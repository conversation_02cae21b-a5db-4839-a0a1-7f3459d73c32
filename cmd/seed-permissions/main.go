package main

import (
	"fmt"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/cobra"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/commands"
	mysqlrepo "wnapi/modules/rbac/repository/mysql"
	"wnapi/modules/rbac/service"
)

func main() {
	// Initialize console
	if err := console.InitializeConsole(false); err != nil {
		fmt.Printf("Failed to initialize console: %v\n", err)
		os.Exit(1)
	}

	console.Info("🚀 WNAPI Permission Seeding Tool")
	console.Info("=================================")

	// Load configuration
	cfg, err := viperconfig.NewConfigLoader().Load("@.env")
	if err != nil {
		console.Error(fmt.Sprintf("Failed to load config: %v", err))
		os.Exit(1)
	}

	// Initialize logger
	logLevelStr := cfg.GetStringWithDefault("LOG_LEVEL", "debug")
	var logLevel logger.Level

	switch strings.ToLower(logLevelStr) {
	case "debug":
		logLevel = logger.LevelDebug
	case "info":
		logLevel = logger.LevelInfo
	case "warn", "warning":
		logLevel = logger.LevelWarn
	case "error":
		logLevel = logger.LevelError
	case "fatal":
		logLevel = logger.LevelFatal
	default:
		logLevel = logger.LevelDebug
	}

	// Create console logger
	log := logger.NewConsoleLogger("wnapi-seed-permissions", logLevel)

	// Initialize database connections
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=true",
		cfg.GetString("db_username"),
		cfg.GetString("db_password"),
		cfg.GetString("db_host"),
		cfg.GetString("db_port"),
		cfg.GetString("db_database"),
	)

	// Initialize sqlx connection
	sqlxDB, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		console.Error(fmt.Sprintf("Failed to connect to database with sqlx: %v", err))
		os.Exit(1)
	}

	// Configure connection pool
	sqlxDB.SetMaxOpenConns(25)
	sqlxDB.SetMaxIdleConns(5)
	sqlxDB.SetConnMaxLifetime(5 * time.Minute)

	// Initialize GORM connection from sqlx
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		console.Error(fmt.Sprintf("Failed to connect to database with GORM: %v", err))
		os.Exit(1)
	}

	// Create DBManager with both connections
	dbManager := database.NewDBManager(gormDB, gormDB, log)
	dbManager.SetSqlxDB(sqlxDB)

	// Initialize RBAC repositories
	permissionRepo := mysqlrepo.NewPermissionRepository(gormDB)
	roleRepo := mysqlrepo.NewRoleRepository(gormDB)
	userRoleRepo := mysqlrepo.NewUserRoleRepository(gormDB)
	permissionGroupRepo := mysqlrepo.NewPermissionGroupRepository(gormDB)

	// Initialize RBAC services
	permissionService := service.NewPermissionService(
		permissionRepo,
		roleRepo,
		userRoleRepo,
		permissionGroupRepo,
		log,
	)

	// Create and run the seed permissions command
	seedCmd := commands.NewSeedPermissionsCommand(permissionService)

	// Parse command line arguments
	args := os.Args[1:]

	// Run the seeding
	console.Info("Starting permission seeding...")

	// Create the command and execute it
	if err := runSeedPermissions(seedCmd, args); err != nil {
		console.Error(fmt.Sprintf("Failed to seed permissions: %v", err))
		os.Exit(1)
	}

	console.Success("✅ Permission seeding completed successfully!")
}

// runSeedPermissions executes the seed permissions command with given arguments
func runSeedPermissions(cmd *cobra.Command, args []string) error {
	// Set default flags if not provided
	if !cmd.Flags().Changed("dry-run") {
		cmd.Flags().Set("dry-run", "false")
	}
	if !cmd.Flags().Changed("force-update") {
		cmd.Flags().Set("force-update", "false")
	}
	if !cmd.Flags().Changed("default-group-id") {
		cmd.Flags().Set("default-group-id", "1")
	}

	// Parse arguments for flags
	for i, arg := range args {
		switch arg {
		case "--module":
			if i+1 < len(args) {
				cmd.Flags().Set("module", args[i+1])
			}
		case "--dry-run":
			cmd.Flags().Set("dry-run", "true")
		case "--force-update":
			cmd.Flags().Set("force-update", "true")
		case "--default-group-id":
			if i+1 < len(args) {
				cmd.Flags().Set("default-group-id", args[i+1])
			}
		}
	}

	// Execute the command
	return cmd.RunE(cmd, args)
}
