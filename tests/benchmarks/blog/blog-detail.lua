-- blog-post-advanced.lua
wrk.method = "GET"
wrk.headers["Accept"] = "application/json, text/plain, */*"
wrk.headers["Accept-Language"] = "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7,fr-FR;q=0.6,fr;q=0.5"
wrk.headers["Authorization"] = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoyLCJ0ZW5hbnRfaWQiOjAsImVtYWlsIjoiYWRtaW4iLCJyb2xlIjoiIiwidG9rZW5fdHlwZSI6ImFjY2VzcyIsImV4cCI6MTc1MTA3Njk2NiwianRpIjoiNzE3OGFlMzctMTg3Yi00MmZhLThmMDUtM2ZhZDQyYTgzYmE0IiwiaWF0IjoxNzUwNDcyMTY2LCJpc3MiOiJ3bmFwaSIsIm5iZiI6MTc1MDQ3MjE2Niwic3ViIjoiMiJ9.oSl3MvGbx6RN1yd4ij_Px93kM1TV6F-LiY07DiH02o0"
wrk.headers["Connection"] = "keep-alive"
wrk.headers["Origin"] = "http://localhost:9200"
wrk.headers["Referer"] = "http://localhost:9200/"
wrk.headers["User-Agent"] = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
wrk.headers["X-Tenant-ID"] = "1"

-- Counters cho response tracking
local success_count = 0
local error_count = 0
local auth_error_count = 0

function response(status, headers, body)
    if status == 200 then
        success_count = success_count + 1
    elseif status == 401 or status == 403 then
        auth_error_count = auth_error_count + 1
    else
        error_count = error_count + 1
    end
end

function done(summary, latency, requests)
    print("=== Custom Results ===")
    print("Success responses (200): " .. success_count)
    print("Auth errors (401/403): " .. auth_error_count)
    print("Other errors: " .. error_count)
    print("Success rate: " .. string.format("%.2f%%", (success_count / (success_count + error_count + auth_error_count)) * 100))
end