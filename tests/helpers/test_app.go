package helpers

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/fx"

	fxapp "wnapi/internal/fx"
)

// TestApp wraps FX application for testing
type TestApp struct {
	app    *fx.App
	engine *gin.Engine
	ctx    context.Context
	cancel context.CancelFunc
}

// NewTestApp creates a new test application with FX
func NewTestApp(modules []string) (*TestApp, error) {
	ctx, cancel := context.WithCancel(context.Background())

	var ginEngine *gin.Engine

	// Create FX app with additional provider to capture Gin engine
	app := fxapp.NewAppWithModules(modules, fx.Populate(&ginEngine))

	// Start the app
	if err := app.Start(ctx); err != nil {
		cancel()
		return nil, err
	}

	// Give it a moment to initialize
	time.Sleep(100 * time.Millisecond)

	return &TestApp{
		app:    app,
		engine: ginEngine,
		ctx:    ctx,
		cancel: cancel,
	}, nil
}

// GetGinEngine returns the Gin engine
func (ta *TestApp) GetGinEngine() *gin.Engine {
	return ta.engine
}

// Stop gracefully stops the test application
func (ta *TestApp) Stop() error {
	defer ta.cancel()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return ta.app.Stop(ctx)
}
