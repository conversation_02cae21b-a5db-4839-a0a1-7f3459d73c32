# Testing Infrastructure - Blog API v1

Hệ thống testing toàn diện cho Blog API v1 với support cho unit tests, integration tests, và performance benchmarks.

## 📁 Structure

```
tests/
├── go.mod                   # Independent test module
├── README.md               # This file
├── integration/
│   └── api_test.go         # Integration test suite
└── helpers/
    └── test_app.go         # FX test utilities
```

## 🚀 Quick Start

### Prerequisites

1. **Database**: MySQL 8.0 đang chạy trên port 3306
2. **Cache**: Redis 7.0 đang chạy trên port 6379
3. **Environment**: Go 1.21+ và các dependencies đã install

### Running Tests

```bash
# Chạy tất cả tests
./scripts/test-runner.sh all

# Chỉ unit tests
./scripts/test-runner.sh unit

# Chỉ integration tests  
./scripts/test-runner.sh integration

# Performance benchmarks
./scripts/test-runner.sh benchmark

# Tests với coverage report
./scripts/test-runner.sh coverage
```

## 🧪 Test Types

### 1. Unit Tests

**Location**: `./internal/...`, `./modules/...`  
**Purpose**: Test individual functions và components isolation  
**Environment**: Mocked dependencies, no external services

```bash
# Run unit tests
go test -v -race ./internal/... ./modules/...

# Với coverage
go test -v -race -coverprofile=unit.out ./internal/... ./modules/...
```

**Features**:
- ✅ Fast execution (< 30 seconds)
- ✅ No external dependencies
- ✅ Mocked database và cache
- ✅ Race condition detection
- ✅ Code coverage tracking

### 2. Integration Tests

**Location**: `./tests/integration/`  
**Purpose**: Test API endpoints và module interactions  
**Environment**: Real database và Redis services

```bash
# Setup integration environment
./scripts/setup-integration-test.sh

# Run integration tests
cd tests
go test -v -tags=integration ./integration/...
```

**Test Cases**:
- 🔐 **Authentication**: Login, logout, token validation
- 🛡️ **Authorization**: RBAC, permissions, protected endpoints
- 📝 **Blog API**: CRUD operations, categories, tags
- 🔔 **Notifications**: Multi-channel notification system
- 🏥 **Health Checks**: Service availability
- 📋 **API Format**: Response structure validation

**Mock API Endpoints**:
```
GET  /api/v1/hello/                    # Basic health check
POST /api/admin/v1/auth/login          # Authentication
GET  /api/admin/v1/auth/profile        # User profile
GET  /api/admin/v1/blog/posts          # Blog posts
GET  /api/admin/v1/auth/health         # Auth module health
GET  /api/admin/v1/blog/healthy        # Blog module health
GET  /api/admin/v1/rbac/healthy        # RBAC module health
GET  /api/admin/v1/notifications/health # Notification health
```

### 3. Performance Benchmarks

**Purpose**: Measure performance và detect regressions  
**Metrics**: Memory usage, execution time, allocations

```bash
# Run benchmarks
go test -bench=. -benchmem ./internal/... ./modules/...

# Specific module benchmarks
go test -bench=. -benchmem ./modules/blog/...
```

## 🔧 Configuration

### Environment Files

#### `.env.test` (Unit Tests)
```env
APP_ENV=test
DB_TYPE=mock
REDIS_ADDR=mock
LOG_LEVEL=error
```

#### `.env.integration` (Integration Tests)
```env
APP_ENV=integration
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wnapi_integration
REDIS_ADDR=127.0.0.1:6379
LOG_LEVEL=error
```

### Test Configuration

**Test Suite Setup**:
```go
type IntegrationTestSuite struct {
    suite.Suite
    app     *gin.Engine
    server  *httptest.Server
    client  *http.Client
    token   string
}
```

**Custom Assertions**:
- Response status code validation
- JSON structure verification  
- Authentication token handling
- Error message validation

## 🔄 CI/CD Integration

### GitHub Actions Workflow

**Jobs**:
1. **Lint**: Code quality checks
2. **Unit Test**: Fast isolated tests
3. **Integration Test**: Full environment tests
4. **Build**: Multi-platform compilation
5. **Security Scan**: Vulnerability detection

**Services**:
```yaml
services:
  mysql:
    image: mysql:8.0
    env:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: wnapi_integration
    ports:
      - 3306:3306

  redis:
    image: redis:7-alpine
    ports:
      - 6379:6379
```

### Test Execution Flow

```mermaid
graph LR
    A[Lint Code] --> B[Unit Tests]
    B --> C[Integration Setup]
    C --> D[Integration Tests]
    D --> E[Build]
    E --> F[Security Scan]
```

## 📊 Coverage Reports

### Generating Coverage

```bash
# Combined coverage (unit + integration)
./scripts/test-runner.sh coverage

# View HTML report
open coverage.html

# Terminal coverage summary
go tool cover -func=combined.out
```

### Coverage Targets

- **Minimum**: 80% overall coverage
- **Critical Modules**: 90%+ coverage
- **New Code**: 100% coverage requirement

## 🛠️ Development Workflow

### Before Committing

```bash
# Run full test suite
./scripts/test-runner.sh all

# Check code quality
golangci-lint run

# Verify builds
make build-all
```

### Adding New Tests

1. **Unit Tests**: Add alongside source code
2. **Integration Tests**: Add to `tests/integration/`
3. **Benchmarks**: Add `Benchmark` functions
4. **Update Documentation**: Update this README

### Test Data Management

**Setup**: `scripts/setup-integration-test.sh`
- Creates test database
- Runs migrations
- Seeds test data
- Configures Redis

**Cleanup**: Automatic after test completion
- Database reset
- Cache clearing
- Temporary file removal

## 🐛 Troubleshooting

### Common Issues

#### Database Connection
```bash
# Check MySQL status
mysql -h127.0.0.1 -P3306 -uroot -proot -e "SELECT 1"

# Reset test database
mysql -h127.0.0.1 -P3306 -uroot -proot -e "DROP DATABASE IF EXISTS wnapi_integration; CREATE DATABASE wnapi_integration;"
```

#### Redis Connection
```bash
# Check Redis status
redis-cli -h 127.0.0.1 -p 6379 ping

# Clear Redis data
redis-cli -h 127.0.0.1 -p 6379 FLUSHALL
```

#### Test Failures
```bash
# Verbose test output
go test -v -tags=integration ./tests/integration/

# Debug specific test
go test -v -run=TestAuth ./tests/integration/
```

### Log Analysis

**Test Logs**: `logs/test-*.log`
**Error Logs**: `logs/error-*.log`
**Debug Mode**: Set `LOG_LEVEL=debug` trong test environment

## 📈 Performance Monitoring

### Benchmark Results Format

```
BenchmarkFunction-8    	    1000	      1234 ns/op	     456 B/op	       7 allocs/op
```

**Metrics**:
- **Iterations**: Number of test runs
- **ns/op**: Nanoseconds per operation
- **B/op**: Bytes allocated per operation
- **allocs/op**: Memory allocations per operation

### Performance Targets

- **API Response**: < 100ms average
- **Database Query**: < 50ms average
- **Memory Usage**: < 100MB peak
- **Throughput**: > 1000 requests/second

## 🔗 Related Documentation

- **Development Guide**: `docs/system/development.md`
- **API Documentation**: `docs-api/README.md`
- **Database Schema**: `docs/system/database.md`
- **Deployment Guide**: `docs/system/deployment.md`

## 📝 Contributing

1. Write tests for new features
2. Maintain >= 80% coverage
3. Run full test suite before PR
4. Update documentation
5. Follow Go testing conventions

---

**Last Updated**: July 4, 2025  
**Maintainer**: Development Team  
**Status**: ✅ Production Ready
