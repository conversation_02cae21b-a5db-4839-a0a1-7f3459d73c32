meta {
  name: Create Notification
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/admin/v1/notifications
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "title": "New Product Launch",
    "content": "We are excited to announce our new product...",
    "type": "email",
    "priority": "high",
    "recipient": {
      "user_id": 1,
      "email": "<EMAIL>",
      "phone": "+84123456789",
      "name": "<PERSON>"
    },
    "channel_id": 1,
    "template_id": 5,
    "variables": {
      "user_name": "<PERSON>",
      "product_name": "New Product",
      "launch_date": "2024-02-01"
    },
    "schedule_at": "2024-01-15T09:00:00Z",
    "metadata": {
      "campaign_id": "camp_002",
      "tags": ["product_launch", "announcement"]
    }
  }
}

docs {
  # Create Notification
  
  Tạo một notification mới để gửi đến user.
  
  ## Request Body
  ```json
  {
    "title": "string",                    // Tiêu đề notification (required)
    "content": "string",                  // Nội dung notification (required)
    "type": "string",                     // Loại: email|sms|push|in_app (required)
    "priority": "string",                 // Độ ưu tiên: low|medium|high|urgent (default: medium)
    "recipient": {                        // Thông tin người nhận (required)
      "user_id": "number",                // ID user (optional nếu có email/phone)
      "email": "string",                  // Email (required for email type)
      "phone": "string",                  // Phone (required for sms type)
      "name": "string",                   // Tên người nhận (optional)
      "device_token": "string"            // Device token (required for push type)
    },
    "channel_id": "number",               // ID channel gửi (optional)
    "template_id": "number",              // ID template sử dụng (optional)
    "variables": "object",                // Variables cho template (optional)
    "schedule_at": "datetime",            // Thời gian lên lịch gửi (optional, gửi ngay nếu không có)
    "metadata": {                         // Metadata bổ sung (optional)
      "campaign_id": "string",
      "tracking_id": "string", 
      "tags": ["string"],
      "custom_data": "object"
    }
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "title": "New Product Launch",
      "content": "We are excited to announce our new product...",
      "type": "email",
      "status": "pending",
      "priority": "high",
      "recipient": {
        "user_id": 1,
        "email": "<EMAIL>",
        "name": "John Doe"
      },
      "channel": {
        "id": 1,
        "name": "Email Channel"
      },
      "template": {
        "id": 5,
        "name": "Product Launch Template"
      },
      "variables": {
        "user_name": "John Doe",
        "product_name": "New Product",
        "launch_date": "2024-02-01"
      },
      "schedule_at": "2024-01-15T09:00:00Z",
      "metadata": {
        "campaign_id": "camp_002",
        "tracking_id": "track_002",
        "tags": ["product_launch", "announcement"]
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `notifications.create`
  
  ## Error Responses
  - 422: Validation Error - Dữ liệu không hợp lệ
  - 404: Channel/Template not found
  - 400: Bad Request - Thiếu thông tin người nhận phù hợp với loại notification
  
  ## Notes
  - Nếu `schedule_at` không được cung cấp, notification sẽ được gửi ngay lập tức
  - Với type "email", cần có `recipient.email`
  - Với type "sms", cần có `recipient.phone`
  - Với type "push", cần có `recipient.device_token`
  - Với type "in_app", cần có `recipient.user_id`
}

tests {
  test("Status code is 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Notification created successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.title).to.equal("New Product Launch");
    expect(data.data.type).to.equal("email");
  });
}
