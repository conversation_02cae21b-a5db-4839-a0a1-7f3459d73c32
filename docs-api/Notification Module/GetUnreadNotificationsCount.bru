meta {
  name: Get Unread Notifications Count
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/admin/v1/notification-users/unread-count
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  type: in_app
  priority: high
}

docs {
  # Get Unread Notifications Count
  
  <PERSON><PERSON><PERSON> số lượng notifications ch<PERSON>a đọc của user hiệ<PERSON> tại.
  
  ## Query Parameters
  - `type`: Lọ<PERSON> theo lo<PERSON> notification (optional)
  - `priority`: <PERSON><PERSON><PERSON> theo độ ưu tiên (optional)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "total_unread": 8,
      "by_type": {
        "in_app": 5,
        "email": 2,
        "push": 1,
        "sms": 0
      },
      "by_priority": {
        "urgent": 1,
        "high": 2,
        "medium": 4,
        "low": 1
      },
      "latest_unread": {
        "id": 25,
        "title": "New message received",
        "created_at": "2024-01-01T11:00:00Z"
      }
    }
  }
  ```
  
  ## Required Permission
  - `notifications.read_own` hoặc chỉ áp dụng cho notifications của user hiệ<PERSON> tạ<PERSON>
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has unread count", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("total_unread");
    expect(data.data.total_unread).to.be.a("number");
    expect(data.data).to.have.property("by_type");
    expect(data.data).to.have.property("by_priority");
  });
}
