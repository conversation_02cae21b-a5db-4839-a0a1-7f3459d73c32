meta {
  name: List Templates
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/admin/v1/notifications/templates
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 20
  search: welcome
  type: email
  status: active
  sort: created_at
  order: desc
}

docs {
  # List Notification Templates
  
  L<PERSON>y danh sách các template notification với các tùy chọn lọc và phân trang.
  
  ## Query Parameters
  - `page`: <PERSON><PERSON> trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 20, max: 100)
  - `search`: <PERSON><PERSON><PERSON> kiếm theo name, code, subject
  - `type`: <PERSON><PERSON><PERSON> theo lo<PERSON> (email, sms, push, in_app)
  - `status`: <PERSON><PERSON><PERSON> theo tr<PERSON> thái (active, inactive)
  - `category`: Lọc theo category
  - `sort`: <PERSON><PERSON><PERSON> xế<PERSON> theo (created_at, updated_at, name)
  - `order`: <PERSON><PERSON><PERSON> tự (asc, desc)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "name": "Welcome Email Template",
        "code": "welcome_email",
        "type": "email",
        "category": "onboarding",
        "subject": "Welcome to {{app_name}}!",
        "content": "Hello {{user_name}}, welcome to our platform...",
        "variables": [
          {
            "name": "user_name",
            "type": "string",
            "required": true,
            "description": "User's full name"
          },
          {
            "name": "app_name",
            "type": "string",
            "required": true,
            "description": "Application name"
          },
          {
            "name": "activation_link",
            "type": "url",
            "required": false,
            "description": "Account activation link"
          }
        ],
        "metadata": {
          "version": "1.2",
          "layout": "default",
          "tags": ["onboarding", "welcome"]
        },
        "status": "active",
        "usage_count": 1250,
        "last_used": "2024-01-01T10:00:00Z",
        "created_by": {
          "id": 1,
          "name": "Admin User"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 20,
      "total": 50,
      "last_page": 3,
      "has_more": true,
      "next_cursor": "eyJpZCI6MjB9"
    },
    "summary": {
      "total_templates": 50,
      "active_templates": 42,
      "type_counts": {
        "email": 25,
        "sms": 10,
        "push": 10,
        "in_app": 5
      }
    }
  }
  ```
  
  ## Required Permission
  - `notifications.templates.list`
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has templates list", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.meta).to.have.property("current_page");
  });
}
