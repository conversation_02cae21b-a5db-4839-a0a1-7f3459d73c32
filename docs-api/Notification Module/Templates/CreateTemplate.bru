meta {
  name: Create Template
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/admin/v1/notifications/templates
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Product Launch Email",
    "code": "product_launch_email",
    "type": "email",
    "category": "marketing",
    "subject": "🚀 New Product Launch: {{product_name}}",
    "content": "<html><body><h1>Hello {{user_name}}!</h1><p>We're excited to announce our new product: <strong>{{product_name}}</strong></p><p>Launch Date: {{launch_date}}</p><a href=\"{{product_url}}\">Learn More</a></body></html>",
    "variables": [
      {
        "name": "user_name",
        "type": "string",
        "required": true,
        "description": "User's full name"
      },
      {
        "name": "product_name",
        "type": "string",
        "required": true,
        "description": "Name of the new product"
      },
      {
        "name": "launch_date",
        "type": "date",
        "required": true,
        "description": "Product launch date"
      },
      {
        "name": "product_url",
        "type": "url",
        "required": false,
        "description": "Link to product page"
      }
    ],
    "metadata": {
      "layout": "marketing",
      "version": "1.0",
      "tags": ["marketing", "product", "launch"]
    },
    "status": "active"
  }
}

docs {
  # Create Notification Template
  
  Tạo một template notification mới.
  
  ## Request Body
  ```json
  {
    "name": "string",                     // Tên template (required)
    "code": "string",                     // Code định danh (required, unique)
    "type": "string",                     // Loại: email|sms|push|in_app (required)
    "category": "string",                 // Danh mục (optional)
    "subject": "string",                  // Tiêu đề (required for email)
    "content": "string",                  // Nội dung template (required)
    "variables": [                        // Danh sách variables (optional)
      {
        "name": "string",                 // Tên variable
        "type": "string",                 // Kiểu: string|number|date|url|boolean
        "required": "boolean",            // Bắt buộc hay không
        "description": "string",          // Mô tả variable
        "default_value": "any"            // Giá trị mặc định (optional)
      }
    ],
    "metadata": {                         // Metadata bổ sung (optional)
      "layout": "string",                 // Layout sử dụng
      "version": "string",                // Phiên bản template
      "tags": ["string"],                 // Tags phân loại
      "preview_data": "object"            // Dữ liệu để preview
    },
    "status": "string"                    // Trạng thái: active|inactive (default: active)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "name": "Product Launch Email",
      "code": "product_launch_email",
      "type": "email",
      "category": "marketing",
      "subject": "🚀 New Product Launch: {{product_name}}",
      "content": "<html>...</html>",
      "variables": [...],
      "metadata": {...},
      "status": "active",
      "usage_count": 0,
      "created_by": {
        "id": 1,
        "name": "Admin User"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `notifications.templates.create`
  
  ## Error Responses
  - 422: Validation Error - Dữ liệu không hợp lệ
  - 409: Conflict - Code đã tồn tại
  
  ## Template Syntax
  - Variables: `{{variable_name}}`
  - Conditionals: `{{#if condition}}...{{/if}}`
  - Loops: `{{#each items}}...{{/each}}`
  - Includes: `{{> partial_name}}`
}

tests {
  test("Status code is 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Template created successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.name).to.equal("Product Launch Email");
    expect(data.data.code).to.equal("product_launch_email");
  });
}
