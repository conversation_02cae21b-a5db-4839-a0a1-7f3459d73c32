meta {
  name: Template - List Templates
  type: http
  seq: 21
}

get {
  url: {{api_url}}/api/v1/notifications/templates?limit=20&notification_type=email&is_active=true
  body: none
  auth: none
}

params:query {
  limit: 20
  notification_type: email
  is_active: true
  ~cursor: 
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
  Authorization: Bearer {{access_token}}
}

docs {
  # L<PERSON><PERSON>ch Templates
  
  API này cho phép lấy danh sách các template thông báo với filtering và pagination.
  
  ## Query Parameters
  
  - `limit` (integer, optional): <PERSON><PERSON> lượng template trên mỗi trang (mặc định: 20, tối đa: 100)
  - `cursor` (string, optional): Cursor cho pagination
  - `notification_type` (string, optional): L<PERSON><PERSON> theo loại thông báo (email, sms, push, in_app)
  - `is_active` (boolean, optional): <PERSON><PERSON><PERSON> theo trạng thái kích ho<PERSON>t
  
  ## Response
  
  ### Success (200 OK)
  ```json
  {
    "status": {
      "success": true,
      "message": "Templates retrieved successfully"
    },
    "data": [
      {
        "template_id": 1,
        "template_code": "welcome_email",
        "template_name": "welcome_email",
        "subject": "Chào mừng bạn đến với {{app_name}}!",
        "content": "<h1>Xin chào {{user_name}}!</h1>...",
        "channel_id": 1,
        "channel_code": "email",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      },
      {
        "template_id": 2,
        "template_code": "password_reset",
        "template_name": "password_reset",
        "subject": "Đặt lại mật khẩu cho {{app_name}}",
        "content": "<h1>Đặt lại mật khẩu</h1>...",
        "channel_id": 1,
        "channel_code": "email",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "next_cursor": "eyJpZCI6Mn0=",
      "has_more": true
    }
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to list templates",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "error": "database connection failed"
    }
  }
  ```
  
  ## Pagination
  
  API sử dụng cursor-based pagination:
  - Sử dụng `cursor` từ response trước để lấy trang tiếp theo
  - `has_more` cho biết còn dữ liệu hay không
  - `next_cursor` là cursor cho trang tiếp theo
  
  ## Filtering
  
  - `notification_type`: Lọc theo loại kênh thông báo
  - `is_active`: Chỉ lấy template đang hoạt động hoặc không hoạt động
  
  ## Ví dụ sử dụng
  
  1. Lấy tất cả template email đang hoạt động:
     `GET /api/admin/v1/notifications/templates?notification_type=email&is_active=true`
  
  2. Lấy 10 template đầu tiên:
     `GET /api/admin/v1/notifications/templates?limit=10`
  
  3. Lấy trang tiếp theo:
     `GET /api/admin/v1/notifications/templates?cursor=eyJpZCI6Mn0=&limit=10`
}
