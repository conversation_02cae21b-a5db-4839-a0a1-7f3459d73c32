meta {
  name: Get User Notifications
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/notification-users/notifications?limit=20&is_read=false
  body: none
  auth: none
}

params:query {
  limit: 20
  is_read: false
  ~cursor:
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log(`Found ${responseJson.data.length} notifications`);
    
    // Save first notification ID for other requests
    if (responseJson.data.length > 0) {
      const firstNotificationId = responseJson.data[0].notification_id;
      bru.setEnvVar("notification_id", firstNotificationId);
      console.log(`First notification ID saved: ${firstNotificationId}`);
    }
  }
}
