meta {
  name: Create Notification
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/admin/v1/notification-users/notifications
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

body:json {
  {
    "user_id": {{user_id}},
    "title": "Test Notification",
    "content": "This is a test notification created via Bruno API"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract notification ID
    const notificationId = responseJson.data.notification_id;
    
    // Save to environment variables
    bru.setEnvVar("notification_id", notificationId);
    
    console.log(`Notification created with ID: ${notificationId}`);
  }
}
