meta {
  name: Update Notification
  type: http
  seq: 7
}

put {
  url: {{api_url}}/api/admin/v1/notification-users/notifications/{{notification_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

body:json {
  {
    "title": "Updated Test Notification",
    "content": "This notification has been updated via Bruno API",
    "is_read": false
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log(`Notification ${responseJson.data.notification_id} updated successfully`);
  }
}
