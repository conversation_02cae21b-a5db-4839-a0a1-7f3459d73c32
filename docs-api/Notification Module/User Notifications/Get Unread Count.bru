meta {
  name: Get Unread Count
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/notification-users/unread-count
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract unread count
    const unreadCount = responseJson.data.count;
    
    // Save to environment variables
    bru.setEnvVar("unread_count", unreadCount);
    
    console.log(`Unread notifications count: ${unreadCount}`);
  }
}
