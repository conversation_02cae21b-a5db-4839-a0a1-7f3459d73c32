meta {
  name: Mark All As Read
  type: http
  seq: 5
}

put {
  url: {{api_url}}/api/admin/v1/notification-users/read-all
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log(`All notifications marked as read for tenant ${bru.getEnvVar("tenant_id")}`);
  }
}
