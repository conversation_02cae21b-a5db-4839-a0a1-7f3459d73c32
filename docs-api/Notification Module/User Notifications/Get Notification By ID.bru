meta {
  name: Get Notification By ID
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/admin/v1/notification-users/notifications/{{notification_id}}
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
  Content-Type: application/json
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log(`Notification details: ${JSON.stringify(responseJson.data, null, 2)}`);
  }
}
