meta {
  name: Mark All Notifications as Read
  type: http
  seq: 5
}

put {
  url: {{base_url}}/api/admin/v1/notification-users/read-all
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Mark All Notifications as Read
  
  <PERSON><PERSON><PERSON> dấu tất cả notifications của user hiện tại là đã đọc.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "total_marked": 8,
      "marked_at": "2024-01-01T12:00:00Z"
    },
    "message": "All notifications marked as read successfully"
  }
  ```
  
  ## Required Permission
  - `notifications.mark_all_read` hoặc chỉ áp dụng cho notifications của user hiện t<PERSON><PERSON>
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("All notifications marked as read", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("total_marked");
    expect(data.data.total_marked).to.be.a("number");
  });
}
