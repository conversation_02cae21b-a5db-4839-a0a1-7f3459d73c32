meta {
  name: Get User Notifications
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/admin/v1/notification-users
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 20
  type: in_app
  status: unread
  priority: high
  sort: created_at
  order: desc
}

docs {
  # Get User Notifications
  
  L<PERSON><PERSON> danh sách notifications của user hiện tại.
  
  ## Query Parameters
  - `page`: <PERSON><PERSON> trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 20, max: 50)
  - `type`: <PERSON><PERSON><PERSON> theo <PERSON> (email, sms, push, in_app)
  - `status`: <PERSON><PERSON><PERSON> theo trạng thái (read, unread)
  - `priority`: <PERSON>ọ<PERSON> theo độ <PERSON>u tiên (low, medium, high, urgent)
  - `sort`: <PERSON><PERSON><PERSON> x<PERSON><PERSON> theo (created_at, updated_at, read_at)
  - `order`: <PERSON><PERSON><PERSON> (asc, desc)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "notification_id": 15,
        "title": "Welcome to our platform!",
        "content": "Thank you for joining us...",
        "type": "in_app",
        "priority": "medium",
        "status": "unread",
        "metadata": {
          "action_url": "/dashboard",
          "icon": "welcome",
          "campaign_id": "camp_001"
        },
        "read_at": null,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
      },
      {
        "id": 2,
        "notification_id": 16,
        "title": "New message received",
        "content": "You have a new message from John Doe",
        "type": "in_app",
        "priority": "high",
        "status": "read",
        "metadata": {
          "action_url": "/messages/123",
          "icon": "message",
          "sender_id": 5
        },
        "read_at": "2024-01-01T11:30:00Z",
        "created_at": "2024-01-01T11:00:00Z",
        "updated_at": "2024-01-01T11:30:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 20,
      "total": 25,
      "last_page": 2,
      "has_more": true,
      "next_cursor": "eyJpZCI6MjB9"
    },
    "summary": {
      "total_notifications": 25,
      "unread_count": 8,
      "status_counts": {
        "read": 17,
        "unread": 8
      },
      "priority_counts": {
        "low": 5,
        "medium": 12,
        "high": 6,
        "urgent": 2
      }
    }
  }
  ```
  
  ## Required Permission
  - `notifications.read_own` hoặc user phải là owner của notifications
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has user notifications", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.summary).to.have.property("unread_count");
  });
}
