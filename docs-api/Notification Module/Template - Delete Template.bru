meta {
  name: Template - Delete Template
  type: http
  seq: 24
}

delete {
  url: {{api_url}}/api/v1/notifications/templates/1
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Xóa Template
  
  API này cho phép xóa một template thông báo khỏi hệ thống.
  
  ## Path Parameters
  
  - `id` (integer, required): ID của template cần xóa
  
  ## Response
  
  ### Success (200 OK)
  ```json
  {
    "status": {
      "success": true,
      "message": "Template deleted successfully"
    },
    "data": {
      "deleted": true
    }
  }
  ```
  
  ### Error (400 Bad Request)
  ```json
  {
    "status": {
      "success": false,
      "message": "Invalid template ID",
      "error_code": "INVALID_REQUEST"
    },
    "data": null,
    "details": {
      "template_id": "abc"
    }
  }
  ```
  
  ### Error (404 Not Found)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template not found",
      "error_code": "NOT_FOUND"
    },
    "data": null,
    "details": {
      "template_id": 999
    }
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to delete template",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "template_id": 1,
      "error": "database connection failed"
    }
  }
  ```
  
  ## Cảnh báo
  
  ⚠️ **Thao tác này không thể hoàn tác!**
  
  - Template sẽ bị xóa vĩnh viễn khỏi hệ thống
  - Các thông báo đã gửi sử dụng template này sẽ không bị ảnh hưởng
  - Các thông báo đang chờ gửi sử dụng template này có thể gặp lỗi
  
  ## Khuyến nghị
  
  Thay vì xóa template, hãy cân nhắc:
  1. Vô hiệu hóa template bằng cách set `is_active: false`
  2. Đổi tên template để đánh dấu không sử dụng
  3. Lưu trữ backup template trước khi xóa
  
  ## Kiểm tra trước khi xóa
  
  Trước khi xóa template, hãy đảm bảo:
  - Không có thông báo nào đang chờ gửi sử dụng template này
  - Không có automation/workflow nào đang tham chiếu đến template
  - Đã backup nội dung template nếu cần thiết
  
  ## Ví dụ workflow an toàn
  
  1. Vô hiệu hóa template trước:
     `PUT /api/v1/notifications/templates/1` với `{"is_active": false}`

  2. Chờ một thời gian để đảm bảo không có thông báo nào đang sử dụng

  3. Xóa template:
     `DELETE /api/v1/notifications/templates/1`
}
