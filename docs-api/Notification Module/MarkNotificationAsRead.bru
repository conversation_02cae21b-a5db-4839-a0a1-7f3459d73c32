meta {
  name: Mark Notification as Read
  type: http
  seq: 4
}

put {
  url: {{base_url}}/api/admin/v1/notification-users/1/read
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Mark Notification as Read
  
  <PERSON><PERSON><PERSON> dấu một notification của user là đã đọc.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "notification_id": 15,
      "title": "Welcome to our platform!",
      "status": "read",
      "read_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `notifications.mark_read` hoặc user phải là owner của notification
  
  ## Error Responses
  - 404: Notification not found
  - 403: Forbidden - Không có quyền mark notification này
  - 409: Conflict - Notification đã đư<PERSON><PERSON> đ<PERSON> dấu đọ<PERSON> r<PERSON>
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Notification marked as read", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.status).to.equal("read");
    expect(data.data.read_at).to.not.be.null;
  });
}
