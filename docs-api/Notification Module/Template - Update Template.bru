meta {
  name: Template - Update Template
  type: http
  seq: 23
}

put {
  url: {{api_url}}/api/v1/notifications/templates/1
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "subject": "Chào mừng bạn đến với {{app_name}} - Phiên bản cập nhật!",
    "content": "<h1>Xin chào {{user_name}}!</h1><p>Chào mừng bạn đến với {{app_name}}. Chúng tôi rất vui khi có bạn tham gia cộng đồng của chúng tôi.</p><p>Đ<PERSON> bắt đầu hành trình, hãy <a href=\"{{login_url}}\">đăng nhập vào tài khoản</a> của bạn và khám phá các tính năng mới.</p><p><PERSON><PERSON>u bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi.</p><p>Trân trọng,<br>Đội ngũ {{app_name}}</p>",
    "is_active": true
  }
}

docs {
  # Cập Nhật Template
  
  API này cho phép cập nhật thông tin của một template thông báo đã tồn tại.
  
  ## Path Parameters
  
  - `id` (integer, required): ID của template cần cập nhật
  
  ## Request Body
  
  - `subject` (string, optional): Tiêu đề template mới
  - `content` (string, optional): Nội dung template mới
  - `is_active` (boolean, optional): Trạng thái kích hoạt template
  
  ## Response
  
  ### Success (200 OK)
  ```json
  {
    "status": {
      "success": true,
      "message": "Template updated successfully"
    },
    "data": {
      "template_id": 1,
      "template_code": "welcome_email",
      "template_name": "welcome_email",
      "subject": "Chào mừng bạn đến với {{app_name}} - Phiên bản cập nhật!",
      "content": "<h1>Xin chào {{user_name}}!</h1><p>Chào mừng bạn đến với {{app_name}}...",
      "channel_id": 1,
      "channel_code": "email",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T01:00:00Z"
    }
  }
  ```
  
  ### Error (400 Bad Request)
  ```json
  {
    "status": {
      "success": false,
      "message": "Invalid template ID",
      "error_code": "INVALID_REQUEST"
    },
    "data": null,
    "details": {
      "template_id": "abc"
    }
  }
  ```
  
  ### Error (404 Not Found)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template not found",
      "error_code": "NOT_FOUND"
    },
    "data": null,
    "details": {
      "template_id": 999
    }
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to update template",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "template_id": 1,
      "error": "database connection failed"
    }
  }
  ```
  
  ## Lưu ý
  
  - Chỉ có thể cập nhật template thuộc tenant hiện tại
  - Template code không thể thay đổi sau khi tạo
  - Tất cả các trường trong request body đều là optional
  - Nếu không truyền trường nào, template sẽ không thay đổi
  - Thời gian `updated_at` sẽ được cập nhật tự động
  
  ## Ví dụ sử dụng
  
  1. Chỉ cập nhật subject:
  ```json
  {
    "subject": "Tiêu đề mới"
  }
  ```
  
  2. Vô hiệu hóa template:
  ```json
  {
    "is_active": false
  }
  ```
  
  3. Cập nhật toàn bộ nội dung:
  ```json
  {
    "subject": "Tiêu đề mới",
    "content": "Nội dung mới",
    "is_active": true
  }
  ```
}
