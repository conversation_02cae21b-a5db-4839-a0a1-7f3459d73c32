meta {
  name: Template - Create Template
  type: http
  seq: 20
}

post {
  url: {{api_url}}/api/v1/notifications/templates
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "template_code": "welcome_email",
    "subject": "Chào mừng bạn đến với {{app_name}}!",
    "content": "<h1>Xin chào {{user_name}}!</h1><p>Chào mừng bạn đến với {{app_name}}. Chúng tôi rất vui khi có bạn tham gia.</p><p><PERSON><PERSON> bắt đầu, hãy <a href=\"{{login_url}}\">đăng nhập vào tài khoản</a> của bạn.</p><p>Trân trọng,<br><PERSON><PERSON><PERSON> {{app_name}}</p>",
    "is_active": true
  }
}

docs {
  # Tạo Template Thông Báo
  
  API này cho phép tạo một template thông báo mới trong hệ thống.
  
  ## Request Body
  
  - `template_code` (string, required): Mã định danh duy nhất cho template
  - `subject` (string, required): Tiêu đề template (có thể chứa biến {{variable}})
  - `content` (string, required): Nội dung template (có thể chứa biến {{variable}})
  - `is_active` (boolean, optional): Trạng thái kích hoạt template (mặc định: true)
  
  ## Response
  
  ### Success (201 Created)
  ```json
  {
    "status": {
      "success": true,
      "message": "Template created successfully"
    },
    "data": {
      "template_id": 1,
      "template_code": "welcome_email",
      "template_name": "welcome_email",
      "subject": "Chào mừng bạn đến với {{app_name}}!",
      "content": "<h1>Xin chào {{user_name}}!</h1>...",
      "channel_id": 1,
      "channel_code": "email",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ### Error (400 Bad Request)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template code is required",
      "error_code": "INVALID_REQUEST"
    },
    "data": null
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to create template",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "error": "database connection failed"
    }
  }
  ```
  
  ## Biến Template
  
  Template hỗ trợ các biến động được bao quanh bởi dấu ngoặc nhọn đôi:
  - `{{user_name}}`: Tên người dùng
  - `{{app_name}}`: Tên ứng dụng
  - `{{login_url}}`: URL đăng nhập
  - `{{verification_url}}`: URL xác thực
  - `{{reset_password_url}}`: URL đặt lại mật khẩu
  
  ## Lưu ý
  
  - Template code phải là duy nhất trong hệ thống
  - Subject và content có thể chứa HTML và biến template
  - Template được tạo sẽ có notification_type mặc định là "email"
}
