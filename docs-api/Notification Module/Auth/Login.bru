meta {
  name: Login
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/auth/login
  body: json
  auth: none
}

headers {
  Content-Type: application/json
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "12345678"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract access token
    const accessToken = responseJson.data.access_token;
    
    // Save to environment variables
    bru.setEnvVar("access_token", accessToken);
    
    console.log(`Access token saved: ${accessToken}`);
  }
}
