meta {
  name: Template - Get Template
  type: http
  seq: 22
}

get {
  url: {{api_url}}/api/v1/notifications/templates/1
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Lấy Chi Tiết Template
  
  API này cho phép lấy thông tin chi tiết của một template thông báo theo ID.
  
  ## Path Parameters
  
  - `id` (integer, required): ID của template cần lấy thông tin
  
  ## Response
  
  ### Success (200 OK)
  ```json
  {
    "status": {
      "success": true,
      "message": "Template retrieved successfully"
    },
    "data": {
      "template_id": 1,
      "template_code": "welcome_email",
      "template_name": "welcome_email",
      "subject": "Chào mừng bạn đến với {{app_name}}!",
      "content": "<h1><PERSON>n chào {{user_name}}!</h1><p><PERSON><PERSON><PERSON> mừng bạn đến với {{app_name}}. Chúng tôi rất vui khi có bạn tham gia.</p><p><PERSON><PERSON> bắt đầu, h<PERSON><PERSON> <a href=\"{{login_url}}\">đăng nhập vào tài khoản</a> của bạn.</p><p>Trân trọng,<br>Đội ngũ {{app_name}}</p>",
      "channel_id": 1,
      "channel_code": "email",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ### Error (400 Bad Request)
  ```json
  {
    "status": {
      "success": false,
      "message": "Invalid template ID",
      "error_code": "INVALID_REQUEST"
    },
    "data": null,
    "details": {
      "template_id": "abc"
    }
  }
  ```
  
  ### Error (404 Not Found)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template not found",
      "error_code": "NOT_FOUND"
    },
    "data": null,
    "details": {
      "template_id": 999
    }
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to get template",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "template_id": 1,
      "error": "database connection failed"
    }
  }
  ```
  
  ## Sử dụng
  
  API này thường được sử dụng để:
  - Hiển thị chi tiết template trong giao diện quản trị
  - Lấy template để chỉnh sửa
  - Xem preview template trước khi gửi thông báo
  - Kiểm tra cấu hình template
  
  ## Lưu ý
  
  - Template ID phải là số nguyên dương
  - Chỉ có thể lấy template thuộc tenant hiện tại
  - Template bị xóa sẽ trả về lỗi 404
}
