meta {
  name: List Notifications
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/admin/v1/notifications
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 20
  search: welcome
  type: email
  status: sent
  user_id: 1
  channel_id: 1
  sort: created_at
  order: desc
  from_date: 2024-01-01
  to_date: 2024-12-31
}

docs {
  # List Notifications
  
  Lấy danh sách các notification với các tùy chọn lọc và phân trang.
  
  ## Query Parameters
  - `page`: Số trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 20, max: 100)
  - `search`: T<PERSON><PERSON> kiếm theo title, content
  - `type`: Lọc theo loại notification (email, sms, push, in_app)
  - `status`: Lọc theo trạng thái (pending, sent, failed, delivered, read)
  - `user_id`: <PERSON><PERSON><PERSON> theo user ID
  - `channel_id`: Lọ<PERSON> theo channel ID
  - `template_id`: <PERSON><PERSON><PERSON> theo template ID
  - `priority`: <PERSON><PERSON><PERSON> theo độ <PERSON>u tiên (low, medium, high, urgent)
  - `sort`: <PERSON><PERSON><PERSON> xếp theo (created_at, updated_at, sent_at, title)
  - `order`: Thứ tự (asc, desc)
  - `from_date`: Lọc từ ngày (YYYY-MM-DD)
  - `to_date`: Lọc đến ngày (YYYY-MM-DD)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "title": "Welcome to our platform!",
        "content": "Thank you for joining us...",
        "type": "email",
        "status": "sent",
        "priority": "medium",
        "recipient": {
          "user_id": 1,
          "email": "<EMAIL>",
          "phone": "+84123456789",
          "name": "John Doe"
        },
        "channel": {
          "id": 1,
          "name": "Email Channel",
          "type": "email"
        },
        "template": {
          "id": 1,
          "name": "Welcome Email",
          "code": "welcome_email"
        },
        "metadata": {
          "campaign_id": "camp_001",
          "tracking_id": "track_001",
          "variables": {
            "user_name": "John Doe",
            "activation_link": "https://example.com/activate"
          }
        },
        "delivery_info": {
          "sent_at": "2024-01-01T10:00:00Z",
          "delivered_at": "2024-01-01T10:01:00Z",
          "read_at": "2024-01-01T11:30:00Z",
          "attempts": 1,
          "last_error": null
        },
        "created_at": "2024-01-01T09:59:00Z",
        "updated_at": "2024-01-01T11:30:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 20,
      "total": 150,
      "last_page": 8,
      "has_more": true,
      "next_cursor": "eyJpZCI6MjB9"
    },
    "summary": {
      "total_notifications": 150,
      "status_counts": {
        "pending": 10,
        "sent": 120,
        "failed": 15,
        "delivered": 100,
        "read": 80
      },
      "type_counts": {
        "email": 80,
        "sms": 30,
        "push": 25,
        "in_app": 15
      }
    }
  }
  ```
  
  ## Required Permission
  - `notifications.list`
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has notifications list", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.meta).to.have.property("current_page");
  });
}
