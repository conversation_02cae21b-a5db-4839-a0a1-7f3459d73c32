# Notification Module API Documentation

Tài liệu API đầy đủ cho Notification Module bao gồm quản lý thông báo, template và queue system.

## 📋 Danh Sách API Endpoints

### 🔔 Notification Management
- `GetAllNotifications.bru` - <PERSON><PERSON><PERSON> danh sách thông báo
- `GetNotificationById.bru` - <PERSON> tiết thông báo theo ID
- `DeleteNotification.bru` - X<PERSON>a thông báo
- `MarkAsRead.bru` - <PERSON><PERSON>h dấu thông báo đã đọc
- `MarkAllAsRead.bru` - Đ<PERSON>h dấu tất cả thông báo đã đọc
- `GetUnreadCount.bru` - <PERSON><PERSON><PERSON> số thông báo chưa đọc
- `UpdateNotificationSettings.bru` - Cập nhật cài đặt thông báo

### 👤 User Notifications (JWT-based)
- `User Notifications/Get User Notifications.bru` - L<PERSON>y thông báo của user hiện tại
- `User Notifications/Get Unread Count.bru` - <PERSON><PERSON><PERSON> thông báo chưa đọc của user
- `User Notifications/Create Notification.bru` - <PERSON><PERSON><PERSON> thông báo mới
- `User Notifications/Get Notification By ID.bru` - Chi tiết thông báo theo ID
- `User Notifications/Update Notification.bru` - Cập nhật thông báo
- `User Notifications/Mark As Read.bru` - Đánh dấu thông báo đã đọc
- `User Notifications/Mark All As Read.bru` - Đánh dấu tất cả đã đọc
- `User Notifications/Delete Notification.bru` - Xóa thông báo

### 📝 Template Management
- `Template - Create Template.bru` - Tạo template thông báo mới
- `Template - List Templates.bru` - Danh sách templates với pagination
- `Template - Get Template.bru` - Chi tiết template theo ID
- `Template - Update Template.bru` - Cập nhật template
- `Template - Delete Template.bru` - Xóa template
- `Template - Get Template by Code.bru` - Lấy template theo code

### 🚀 Queue & Testing
- `Test Queue System.bru` - Test hệ thống queue
- `Test Email Template Handler.bru` - Test gửi email với template
- `Test Direct Email Handler.bru` - Test gửi email trực tiếp
- `Test SMS Handler.bru` - Test gửi SMS
- `Test Push Notification Handler.bru` - Test push notification

## 🔧 Cấu hình

### Environment Variables
Đảm bảo các biến môi trường sau được cấu hình:

```
{{api_url}} - URL base của API
{{access_token}} - JWT access token
{{tenant_id}} - ID của tenant
```

### Authentication
Tất cả API endpoints yêu cầu:
- **Bearer Token**: JWT access token trong header Authorization
- **Tenant ID**: X-Tenant-ID header để xác định tenant

#### 🆕 JWT-based User Notifications
Các endpoint trong folder `User Notifications/` sử dụng JWT authentication:
- **User ID tự động**: Được lấy từ JWT token, không cần truyền trong URL
- **Bảo mật cao**: User chỉ có thể truy cập thông báo của chính mình
- **Multi-tenant**: Hỗ trợ filter theo tenant_id qua header `X-Tenant-ID`
- **Header-based**: Tenant ID được truyền qua header thay vì query parameter
- **Dedicated API Path**: Sử dụng `/api/admin/v1/notification-users` riêng biệt

#### 📋 Header Requirements
```
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

#### 🔗 API Endpoints Structure
- **General Notifications**: `/api/admin/v1/notifications/*`
- **User Notifications**: `/api/admin/v1/notification-users/*`

## 📚 Tài Liệu Bổ Sung

- `README-Queue-Testing.md` - Hướng dẫn chi tiết về testing queue system

## 🎯 Template Variables

Template system hỗ trợ các biến động:

```
{{user_name}} - Tên người dùng
{{app_name}} - Tên ứng dụng
{{login_url}} - URL đăng nhập
{{verification_url}} - URL xác thực
{{reset_password_url}} - URL đặt lại mật khẩu
```

## 🔄 Pagination

API sử dụng cursor-based pagination:
- `limit`: Số lượng items trên mỗi trang (mặc định: 20, tối đa: 100)
- `cursor`: Cursor cho trang tiếp theo
- `has_more`: Có còn dữ liệu hay không

## 📊 Response Format

Tất cả API responses tuân theo format chuẩn:

```json
{
  "status": {
    "success": true,
    "message": "Operation completed successfully"
  },
  "data": { ... },
  "meta": { ... }
}
```

## 🚨 Error Handling

Các mã lỗi phổ biến:
- `400` - Bad Request (dữ liệu không hợp lệ)
- `401` - Unauthorized (chưa xác thực)
- `403` - Forbidden (không có quyền)
- `404` - Not Found (không tìm thấy)
- `500` - Internal Server Error (lỗi server)

## 🔗 Liên Kết

- [Notification Module Documentation](../../docs/modules/notification.md)
- [Queue System Documentation](../../docs/features/queue.md)
- [Template System Documentation](../../docs/features/templates.md)
