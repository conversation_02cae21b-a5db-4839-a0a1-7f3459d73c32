meta {
  name: Template - Get Template by Code
  type: http
  seq: 25
}

get {
  url: {{api_url}}/api/v1/notifications/templates/code/welcome_email
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Lấy Template Theo Code
  
  API này cho phép lấy thông tin chi tiết của một template thông báo theo template code thay vì ID.
  
  ## Path Parameters
  
  - `code` (string, required): Template code của template cần lấy thông tin
  
  ## Response
  
  ### Success (200 OK)
  ```json
  {
    "status": {
      "success": true,
      "message": "Template retrieved successfully"
    },
    "data": {
      "template_id": 1,
      "template_code": "welcome_email",
      "template_name": "welcome_email",
      "subject": "Chào mừng bạn đến với {{app_name}}!",
      "content": "<h1>Xin chào {{user_name}}!</h1><p><PERSON><PERSON>o mừng bạn đến với {{app_name}}. Chúng tôi rất vui khi có bạn tham gia.</p><p><PERSON><PERSON> bắt đầu, hãy <a href=\"{{login_url}}\">đăng nhập vào tài khoản</a> của bạn.</p><p>Trân trọng,<br>Đội ngũ {{app_name}}</p>",
      "channel_id": 1,
      "channel_code": "email",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ### Error (400 Bad Request)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template code is required",
      "error_code": "INVALID_REQUEST"
    },
    "data": null
  }
  ```
  
  ### Error (404 Not Found)
  ```json
  {
    "status": {
      "success": false,
      "message": "Template not found",
      "error_code": "NOT_FOUND"
    },
    "data": null,
    "details": {
      "template_code": "non_existent_template"
    }
  }
  ```
  
  ### Error (500 Internal Server Error)
  ```json
  {
    "status": {
      "success": false,
      "message": "Failed to get template",
      "error_code": "INTERNAL_ERROR"
    },
    "data": null,
    "details": {
      "template_code": "welcome_email",
      "error": "database connection failed"
    }
  }
  ```
  
  ## Sử dụng
  
  API này thường được sử dụng khi:
  - Hệ thống cần lấy template theo code thay vì ID
  - Tích hợp với các service khác sử dụng template code
  - Automation/workflow cần tham chiếu template bằng code
  - API gửi thông báo cần lấy template theo code
  
  ## Ưu điểm của việc sử dụng code
  
  - **Stable reference**: Template code không thay đổi, trong khi ID có thể thay đổi khi migrate data
  - **Human readable**: Code có ý nghĩa, dễ hiểu hơn ID số
  - **Cross-environment**: Code giống nhau giữa các môi trường (dev, staging, prod)
  - **Integration friendly**: Dễ tích hợp với các hệ thống khác
  
  ## Các template code phổ biến
  
  - `welcome_email`: Email chào mừng người dùng mới
  - `password_reset`: Email đặt lại mật khẩu
  - `email_verification`: Email xác thực địa chỉ email
  - `order_confirmation`: Email xác nhận đơn hàng
  - `payment_success`: Email thông báo thanh toán thành công
  - `account_locked`: Email thông báo tài khoản bị khóa
  
  ## Lưu ý
  
  - Template code phân biệt chữ hoa/thường
  - Chỉ có thể lấy template thuộc tenant hiện tại
  - Template bị xóa sẽ trả về lỗi 404
  - Template không hoạt động (`is_active: false`) vẫn có thể lấy được
}
