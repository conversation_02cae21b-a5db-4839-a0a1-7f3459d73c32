meta {
  name: Broadcast To User
  type: http
  seq: 6
}

post {
  url: {{api_url}}/socket/connections/users/1/broadcast
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "user_id": 1,
    "tenant_id": 1,
    "website_id": 1,
    "type": "notification",
    "event": "new_message",
    "data": {
      "title": "New Message",
      "content": "You have a new message from <PERSON><PERSON>",
      "sender": "Admin",
      "timestamp": "2024-01-01T00:00:00Z",
      "action_url": "/messages/123"
    },
    "room_id": "room_123"
  }
}

assert {
  res.status: eq 200
}

tests {
  test("Should successfully broadcast message", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should return broadcast result", function() {
    const body = res.getBody();
    expect(body.success || body.message).to.exist;
    expect(body.connections_reached).to.be.a("number");
  });
}

docs {
  Gửi message broadcast tới tất cả connections của một user.
  
  Path Parameters:
  - user_id: ID của user cần gửi message
  
  Request Body:
  - user_id (required): ID của user
  - tenant_id (required): ID của tenant
  - website_id (optional): ID của website
  - type (required): Loại message (notification, message, event)
  - event (required): Tên event
  - data (required): Dữ liệu message
  - room_id (optional): ID của room (nếu message thuộc về room)
  
  Response:
  - success: Trạng thái thành công
  - message: Thông báo
  - connections_reached: Số lượng connections đã nhận được message
  - failed_connections: Số lượng connections gửi thất bại
  
  Message Types:
  - notification: Thông báo cho user
  - message: Tin nhắn chat
  - event: Event hệ thống
  - alert: Cảnh báo quan trọng
}