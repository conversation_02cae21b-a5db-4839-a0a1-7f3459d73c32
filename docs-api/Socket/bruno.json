{"version": "1", "name": "Socket API", "type": "collection", "ignore": ["node_modules", ".git"], "environments": {"development": {"api_url": "http://localhost:9033/api/v1", "auth_token": "your_jwt_token_here", "tenant_id": "1", "website_id": "1", "user_id": "1"}, "staging": {"api_url": "https://staging-api.example.com/api/v1", "auth_token": "your_jwt_token_here", "tenant_id": "1", "website_id": "1", "user_id": "1"}, "production": {"api_url": "https://api.example.com/api/v1", "auth_token": "your_jwt_token_here", "tenant_id": "1", "website_id": "1", "user_id": "1"}}}