meta {
  name: Send Notification
  type: http
  seq: 12
}

post {
  url: {{api_url}}/socket/notifications/send
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "type": "system",
    "title": "System Maintenance",
    "message": "The system will undergo maintenance from 2:00 AM to 4:00 AM UTC.",
    "priority": "high",
    "targets": {
      "type": "users",
      "user_ids": [1, 2, 3],
      "room_ids": [],
      "broadcast_all": false
    },
    "data": {
      "maintenance_start": "2024-01-01T02:00:00Z",
      "maintenance_end": "2024-01-01T04:00:00Z",
      "affected_services": ["chat", "file_upload"],
      "action_required": false
    },
    "delivery_options": {
      "immediate": true,
      "persistent": true,
      "push_notification": true,
      "email_notification": false,
      "expires_at": "2024-01-01T06:00:00Z"
    },
    "ui_options": {
      "show_popup": true,
      "popup_duration": 10000,
      "icon": "warning",
      "color": "orange",
      "sound": "notification"
    }
  }
}

assert {
  res.status: eq 200
}

tests {
  test("Should send notification successfully", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should return notification result", function() {
    const body = res.getBody();
    expect(body.success).to.be.true;
    expect(body.notification_id).to.be.a("string");
    expect(body.targets_reached).to.be.a("number");
  });
  
  test("Should have delivery stats", function() {
    const body = res.getBody();
    expect(body.delivery_stats).to.be.an("object");
    expect(body.delivery_stats.total_targets).to.be.a("number");
    expect(body.delivery_stats.delivered).to.be.a("number");
    expect(body.delivery_stats.failed).to.be.a("number");
  });
}

docs {
  Gửi notification tới users hoặc rooms.
  
  Request Body:
  - type (required): Loại notification (system, user, room, broadcast)
  - title (required): Tiêu đề notification
  - message (required): Nội dung notification
  - priority (required): Mức độ ưu tiên (low, normal, high, urgent)
  - targets (required): Đối tượng nhận notification
    - type: Loại target (users, rooms, all)
    - user_ids: Danh sách user IDs (nếu type = users)
    - room_ids: Danh sách room IDs (nếu type = rooms)
    - broadcast_all: Gửi cho tất cả users (nếu type = all)
  - data (optional): Dữ liệu bổ sung
  - delivery_options (optional): Tùy chọn gửi
    - immediate: Gửi ngay lập tức
    - persistent: Lưu notification để gửi khi user online
    - push_notification: Gửi push notification
    - email_notification: Gửi email
    - expires_at: Thời gian hết hạn
  - ui_options (optional): Tùy chọn hiển thị UI
    - show_popup: Hiển thị popup
    - popup_duration: Thời gian hiển thị popup (ms)
    - icon: Icon notification
    - color: Màu sắc
    - sound: Âm thanh
  
  Response:
  - success: Trạng thái thành công
  - notification_id: ID của notification
  - targets_reached: Số lượng targets đã nhận
  - delivery_stats: Thống kê gửi
    - total_targets: Tổng số targets
    - delivered: Số đã gửi thành công
    - failed: Số gửi thất bại
    - pending: Số đang chờ gửi
  
  Notification Types:
  - system: Thông báo hệ thống
  - user: Thông báo cá nhân
  - room: Thông báo room
  - broadcast: Thông báo toàn hệ thống
  
  Priority Levels:
  - low: Ưu tiên thấp
  - normal: Ưu tiên bình thường
  - high: Ưu tiên cao
  - urgent: Khẩn cấp
}