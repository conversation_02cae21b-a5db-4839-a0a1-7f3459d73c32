meta {
  name: Get Connections
  type: http
  seq: 3
}

get {
  url: {{api_url}}/socket/connections?page=1&limit=20&status=active
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

params:query {
  page: 1
  limit: 20
  status: active
  ~tenant_id: 1
  ~website_id: 1
  ~user_id: 1
  ~sort_by: connected_at
  ~sort_order: desc
}

assert {
  res.status: eq 200
  res.body.data: isArray
}

tests {
  test("Should return connections list", function() {
    expect(res.getBody().data).to.be.an("array");
  });
  
  test("Should have pagination info", function() {
    const body = res.getBody();
    expect(body.pagination).to.be.an("object");
    expect(body.pagination.page).to.be.a("number");
    expect(body.pagination.limit).to.be.a("number");
    expect(body.pagination.total).to.be.a("number");
  });
  
  test("Connection should have required fields", function() {
    const connections = res.getBody().data;
    if (connections.length > 0) {
      const connection = connections[0];
      expect(connection.id).to.be.a("string");
      expect(connection.user_id).to.be.a("number");
      expect(connection.tenant_id).to.be.a("number");
      expect(connection.status).to.be.a("string");
      expect(connection.connected_at).to.be.a("string");
      expect(connection.is_active).to.be.a("boolean");
    }
  });
}

docs {
  Lấy danh sách tất cả connections đang hoạt động.
  
  Query Parameters:
  - page: Số trang (default: 1)
  - limit: Số lượng items per page (default: 20, max: 100)
  - tenant_id: Filter theo tenant ID
  - website_id: Filter theo website ID
  - user_id: Filter theo user ID
  - status: Filter theo status (active, inactive, disconnected)
  - sort_by: Sắp xếp theo field (connected_at, last_activity)
  - sort_order: Thứ tự sắp xếp (asc, desc)
  
  Response bao gồm:
  - data: Mảng các connection objects
  - pagination: Thông tin phân trang
}