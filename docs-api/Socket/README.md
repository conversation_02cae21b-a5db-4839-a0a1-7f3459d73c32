# Socket Module API Documentation

## Tổng quan
Socket Module cung cấp các API để quản lý WebSocket connections, rooms, notifications và thống kê real-time.

## Base URL
```
{{api_url}}/socket
```

## Authentication
H<PERSON>u hết các endpoint yêu cầu authentication thông qua JWT token hoặc query parameters.

### Headers
```
Authorization: Bearer <jwt_token>
X-Tenant-Id: <tenant_id>
Content-Type: application/json
```

## WebSocket Connection

### Kết nối WebSocket
```
GET {{api_url}}/socket/ws/connect?user_id=1&tenant_id=1&website_id=1
```

**Query Parameters:**
- `user_id` (required): ID của user
- `tenant_id` (required): ID của tenant
- `website_id` (optional): ID của website

**Response khi kết nối thành công:**
```json
{
  "type": "event",
  "event": "system.connected",
  "data": {
    "connection_id": "1_1_1234567890",
    "user_id": 1,
    "tenant_id": 1,
    "website_id": 1,
    "server_time": "2024-01-01T00:00:00Z",
    "features": {
      "rooms": true,
      "notifications": true,
      "file_sharing": false,
      "video_calls": false
    }
  }
}
```

### WebSocket Health Check
```
GET {{api_url}}/socket/ws/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "connections": 150,
  "rooms": 25,
  "uptime": "2h30m45s",
  "version": "1.0.0"
}
```

## Connection Management

### Lấy danh sách connections
```
GET {{api_url}}/socket/connections
```

**Query Parameters:**
- `page` (default: 1): Số trang
- `limit` (default: 20, max: 100): Số lượng items per page
- `tenant_id`: Filter theo tenant
- `website_id`: Filter theo website
- `user_id`: Filter theo user
- `status`: Filter theo status (active, inactive, disconnected)
- `sort_by`: Sắp xếp theo field (connected_at, last_activity)
- `sort_order`: Thứ tự sắp xếp (asc, desc)

**Response:**
```json
{
  "data": [
    {
      "id": "1_1_1234567890",
      "user_id": 1,
      "tenant_id": 1,
      "website_id": 1,
      "status": "active",
      "connected_at": "2024-01-01T00:00:00Z",
      "last_activity": "2024-01-01T00:05:00Z",
      "ip_address": "***********",
      "user_agent": "Mozilla/5.0...",
      "is_active": true,
      "duration": "5m30s"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

### Lấy thông tin connection theo ID
```
GET {{api_url}}/socket/connections/:id
```

### Ngắt kết nối
```
DELETE {{api_url}}/socket/connections/:id
```

**Request Body:**
```json
{
  "reason": "Admin disconnect",
  "force": true
}
```

### Broadcast message tới user
```
POST {{api_url}}/socket/connections/users/:user_id/broadcast
```

**Request Body:**
```json
{
  "user_id": 1,
  "tenant_id": 1,
  "website_id": 1,
  "type": "notification",
  "event": "new_message",
  "data": {
    "title": "New Message",
    "content": "You have a new message",
    "sender": "Admin"
  },
  "room_id": "room_123"
}
```

### Lấy connections của user
```
GET {{api_url}}/socket/connections/users/:user_id
```

## Room Management

### Tạo room mới
```
POST {{api_url}}/socket/rooms
```

**Request Body:**
```json
{
  "name": "General Chat",
  "description": "General discussion room",
  "type": "public",
  "max_members": 100,
  "settings": {
    "allow_file_sharing": true,
    "moderated": false
  }
}
```

### Lấy danh sách rooms
```
GET {{api_url}}/socket/rooms
```

### Lấy thông tin room
```
GET {{api_url}}/socket/rooms/:id
```

### Cập nhật room
```
PUT {{api_url}}/socket/rooms/:id
```

### Xóa room
```
DELETE {{api_url}}/socket/rooms/:id
```

### Tham gia room
```
POST {{api_url}}/socket/rooms/:id/join
```

### Rời khỏi room
```
POST {{api_url}}/socket/rooms/:id/leave
```

### Lấy danh sách members
```
GET {{api_url}}/socket/rooms/:id/members
```

### Broadcast message tới room
```
POST {{api_url}}/socket/rooms/:id/broadcast
```

**Request Body:**
```json
{
  "type": "message",
  "event": "room_message",
  "data": {
    "message": "Hello everyone!",
    "sender_id": 1,
    "sender_name": "John Doe"
  }
}
```

### Cập nhật role member
```
PUT {{api_url}}/socket/rooms/:id/members/:member_id/role
```

**Request Body:**
```json
{
  "role": "moderator"
}
```

### Xóa member khỏi room
```
DELETE {{api_url}}/socket/rooms/:id/members/:member_id
```

## Notifications

### Gửi notification
```
POST {{api_url}}/socket/notifications/send
```

**Request Body:**
```json
{
  "user_id": 1,
  "title": "New Notification",
  "content": "You have a new notification",
  "type": "info",
  "data": {
    "action_url": "/dashboard",
    "priority": "normal"
  }
}
```

### Gửi bulk notifications
```
POST {{api_url}}/socket/notifications/send-bulk
```

**Request Body:**
```json
{
  "user_ids": [1, 2, 3],
  "title": "System Maintenance",
  "content": "System will be under maintenance",
  "type": "warning"
}
```

### Lấy notifications của user
```
GET {{api_url}}/socket/notifications
```

**Query Parameters:**
- `page`: Số trang
- `limit`: Số lượng items
- `status`: Filter theo status (read, unread)
- `type`: Filter theo type

### Đánh dấu đã đọc
```
PUT {{api_url}}/socket/notifications/:id/read
```

### Đánh dấu tất cả đã đọc
```
PUT {{api_url}}/socket/notifications/read-all
```

### Xóa notification
```
DELETE {{api_url}}/socket/notifications/:id
```

### Gửi lại notification
```
POST {{api_url}}/socket/notifications/:id/resend
```

### Lấy cài đặt notification
```
GET {{api_url}}/socket/notifications/settings
```

### Cập nhật cài đặt notification
```
PUT {{api_url}}/socket/notifications/settings
```

**Request Body:**
```json
{
  "email_notifications": true,
  "push_notifications": true,
  "sms_notifications": false,
  "notification_types": {
    "messages": true,
    "mentions": true,
    "system": false
  }
}
```

### Lấy lịch sử gửi
```
GET {{api_url}}/socket/notifications/delivery-history
```

## Statistics

### Thống kê tổng quan
```
GET {{api_url}}/socket/stats/overall
```

**Response:**
```json
{
  "total_connections": 1500,
  "active_connections": 150,
  "total_rooms": 25,
  "active_rooms": 20,
  "total_notifications_sent": 5000,
  "notifications_today": 150,
  "uptime": "2h30m45s",
  "last_updated": "2024-01-01T00:00:00Z"
}
```

### Thống kê connections
```
GET {{api_url}}/socket/stats/connections
```

### Thống kê rooms
```
GET {{api_url}}/socket/stats/rooms
```

### Thống kê notifications
```
GET {{api_url}}/socket/stats/notifications
```

### Kiểm tra sức khỏe hệ thống
```
GET {{api_url}}/socket/stats/health
```

### Metrics hiệu suất
```
GET {{api_url}}/socket/stats/performance
```

### Logs hoạt động
```
GET {{api_url}}/socket/stats/logs
```

### Export thống kê
```
GET {{api_url}}/socket/stats/export
```

**Query Parameters:**
- `format`: Format export (json, csv, xlsx)
- `date_from`: Từ ngày
- `date_to`: Đến ngày
- `type`: Loại thống kê (connections, rooms, notifications)

## WebSocket Message Types

### System Messages
- `system.connected`: Khi kết nối thành công
- `system.ping`: Ping message để duy trì kết nối
- `system.error`: Thông báo lỗi
- `system.disconnect`: Thông báo ngắt kết nối

### Room Messages
- `room.joined`: Khi tham gia room
- `room.left`: Khi rời room
- `room.message`: Tin nhắn trong room
- `room.member_joined`: Có member mới tham gia
- `room.member_left`: Có member rời khỏi room

### Notification Messages
- `notification.new`: Notification mới
- `notification.read`: Notification đã đọc
- `notification.deleted`: Notification đã xóa

### User Messages
- `user.status_changed`: Trạng thái user thay đổi
- `user.typing`: User đang gõ
- `user.stop_typing`: User ngừng gõ

## Error Codes

- `AUTH_REQUIRED`: Yêu cầu authentication
- `INVALID_USER_ID`: User ID không hợp lệ
- `INVALID_TENANT_ID`: Tenant ID không hợp lệ
- `INVALID_WEBSITE_ID`: Website ID không hợp lệ
- `CONNECTION_NOT_FOUND`: Không tìm thấy connection
- `ROOM_NOT_FOUND`: Không tìm thấy room
- `PERMISSION_DENIED`: Không có quyền truy cập
- `RATE_LIMIT_EXCEEDED`: Vượt quá giới hạn request
- `INTERNAL_ERROR`: Lỗi hệ thống

## Rate Limiting

Các endpoint có giới hạn request:
- WebSocket connection: 10 connections/minute per IP
- Broadcast messages: 100 messages/minute per user
- Notification sending: 1000 notifications/hour per tenant

## Best Practices

1. **Kết nối WebSocket:**
   - Luôn xử lý reconnection khi mất kết nối
   - Implement exponential backoff cho reconnection
   - Xử lý ping/pong messages để duy trì kết nối

2. **Room Management:**
   - Kiểm tra quyền trước khi tham gia room
   - Cleanup resources khi rời room
   - Implement proper error handling

3. **Notifications:**
   - Batch notifications khi có thể
   - Implement proper retry logic
   - Respect user notification preferences

4. **Performance:**
   - Monitor connection count và resource usage
   - Implement proper pagination cho large datasets
   - Use appropriate timeouts