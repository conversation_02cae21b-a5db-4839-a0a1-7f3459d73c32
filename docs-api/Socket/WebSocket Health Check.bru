meta {
  name: WebSocket Health Check
  type: http
  seq: 2
}

get {
  url: {{api_url}}/socket/ws/health
  body: none
  auth: none
}

headers {
  X-Tenant-Id: 1
}

assert {
  res.status: eq 200
  res.body.status: eq healthy
}

tests {
  test("Status should be healthy", function() {
    expect(res.getBody().status).to.equal("healthy");
  });
  
  test("Should have timestamp", function() {
    expect(res.getBody().timestamp).to.be.a("string");
  });
  
  test("Should have connections count", function() {
    expect(res.getBody().connections).to.be.a("number");
  });
  
  test("Should have rooms count", function() {
    expect(res.getBody().rooms).to.be.a("number");
  });
}

docs {
  Kiểm tra trạng thái sức khỏe của WebSocket service.
  
  Response bao gồm:
  - status: Trạng thái service (healthy/unhealthy)
  - timestamp: Thời gian hiện tại
  - connections: <PERSON><PERSON> lượng kết nối đang hoạt động
  - rooms: <PERSON><PERSON> lượng room đang hoạt động
  - uptime: Thời gian service đã chạy
  - version: Phiên bản service
}