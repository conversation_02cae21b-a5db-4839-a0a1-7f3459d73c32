meta {
  name: Get Connection Stats
  type: http
  seq: 13
}

get {
  url: {{api_url}}/socket/stats/connections?period=24h&group_by=hour
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

params:query {
  period: 24h
  group_by: hour
  ~start_time: 
  ~end_time: 
  ~website_id: 
  ~user_id: 
}

assert {
  res.status: eq 200
  res.body.current_connections: isNumber
}

tests {
  test("Should return connection stats", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should have current stats", function() {
    const body = res.getBody();
    expect(body.current_connections).to.be.a("number");
    expect(body.total_connections_today).to.be.a("number");
    expect(body.peak_connections_today).to.be.a("number");
    expect(body.average_duration).to.be.a("number");
  });
  
  test("Should have historical data", function() {
    const body = res.getBody();
    expect(body.historical_data).to.be.an("array");
    if (body.historical_data.length > 0) {
      const dataPoint = body.historical_data[0];
      expect(dataPoint.timestamp).to.be.a("string");
      expect(dataPoint.connections).to.be.a("number");
      expect(dataPoint.new_connections).to.be.a("number");
      expect(dataPoint.disconnections).to.be.a("number");
    }
  });
  
  test("Should have breakdown by status", function() {
    const body = res.getBody();
    expect(body.by_status).to.be.an("object");
    expect(body.by_status.active).to.be.a("number");
    expect(body.by_status.idle).to.be.a("number");
    expect(body.by_status.disconnected).to.be.a("number");
  });
}

docs {
  Lấy thống kê về connections.
  
  Query Parameters:
  - period: Khoảng thời gian thống kê (1h, 6h, 24h, 7d, 30d)
  - group_by: Nhóm dữ liệu theo (minute, hour, day)
  - start_time: Thời gian bắt đầu (ISO 8601)
  - end_time: Thời gian kết thúc (ISO 8601)
  - website_id: Filter theo website
  - user_id: Filter theo user
  
  Response:
  - current_connections: Số connections hiện tại
  - total_connections_today: Tổng connections hôm nay
  - peak_connections_today: Peak connections hôm nay
  - average_duration: Thời gian kết nối trung bình (seconds)
  - historical_data: Dữ liệu lịch sử
    - timestamp: Thời gian
    - connections: Số connections tại thời điểm
    - new_connections: Số connections mới
    - disconnections: Số ngắt kết nối
    - average_duration: Thời gian kết nối trung bình
  - by_status: Phân bố theo trạng thái
    - active: Đang hoạt động
    - idle: Không hoạt động
    - disconnected: Đã ngắt kết nối
  - by_website: Phân bố theo website
  - by_user_type: Phân bố theo loại user
    - registered: User đã đăng ký
    - guest: Guest user
    - admin: Admin user
  - performance_metrics: Metrics hiệu suất
    - average_response_time: Thời gian phản hồi trung bình
    - message_throughput: Throughput tin nhắn
    - error_rate: Tỷ lệ lỗi
  
  Period Options:
  - 1h: 1 giờ qua
  - 6h: 6 giờ qua
  - 24h: 24 giờ qua
  - 7d: 7 ngày qua
  - 30d: 30 ngày qua
  
  Group By Options:
  - minute: Nhóm theo phút
  - hour: Nhóm theo giờ
  - day: Nhóm theo ngày
}