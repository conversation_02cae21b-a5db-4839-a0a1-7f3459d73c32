meta {
  name: Join Room
  type: http
  seq: 10
}

post {
  url: {{api_url}}/socket/rooms/room_123/join
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "password": "",
    "message": "Hello everyone! I'm joining this room."
  }
}

assert {
  res.status: eq 200
}

tests {
  test("Should join room successfully", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should return join result", function() {
    const body = res.getBody();
    expect(body.success).to.be.true;
    expect(body.room_id).to.be.a("string");
    expect(body.member_role).to.be.a("string");
  });
  
  test("Should have room info", function() {
    const body = res.getBody();
    expect(body.room).to.be.an("object");
    expect(body.room.name).to.be.a("string");
    expect(body.room.current_members).to.be.a("number");
  });
}

docs {
  Tham gia một room.
  
  Path Parameters:
  - id: Room ID cần tham gia
  
  Request Body:
  - password (optional): <PERSON><PERSON><PERSON> kh<PERSON>u room (nếu room có password)
  - message (optional): Tin nhắn chào khi tham gia
  
  Response:
  - success: Trạng thái thành công
  - room_id: ID của room
  - member_role: Role của user trong room (member, moderator, admin)
  - joined_at: Thời gian tham gia
  - room: Thông tin chi tiết room
    - id: Room ID
    - name: Tên room
    - description: Mô tả
    - type: Loại room
    - current_members: Số member hiện tại
    - max_members: Số member tối đa
    - settings: Cài đặt room
  
  Errors:
  - 404: Room không tồn tại
  - 403: Không có quyền tham gia (room private, banned, etc.)
  - 400: Room đã đầy
  - 409: Đã là member của room
  
  Notes:
  - Với room public: Tham gia ngay lập tức
  - Với room private: Cần approval từ admin/moderator
  - Với room có password: Cần cung cấp password đúng
}