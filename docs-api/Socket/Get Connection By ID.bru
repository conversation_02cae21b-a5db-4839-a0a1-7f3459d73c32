meta {
  name: Get Connection By ID
  type: http
  seq: 4
}

get {
  url: {{api_url}}/socket/connections/1_1_1234567890
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

assert {
  res.status: eq 200
  res.body.id: isString
}

tests {
  test("Should return connection details", function() {
    const body = res.getBody();
    expect(body.id).to.be.a("string");
    expect(body.user_id).to.be.a("number");
    expect(body.tenant_id).to.be.a("number");
    expect(body.status).to.be.a("string");
    expect(body.connected_at).to.be.a("string");
    expect(body.last_activity).to.be.a("string");
    expect(body.ip_address).to.be.a("string");
    expect(body.is_active).to.be.a("boolean");
    expect(body.duration).to.be.a("string");
  });
  
  test("Should have valid status", function() {
    const validStatuses = ["active", "inactive", "disconnected"];
    expect(validStatuses).to.include(res.getBody().status);
  });
}

docs {
  L<PERSON><PERSON> thông tin chi tiết của một connection theo ID.
  
  Path Parameters:
  - id: Connection ID (string)
  
  Response bao gồm:
  - id: Connection ID
  - user_id: ID của user
  - tenant_id: ID của tenant
  - website_id: ID của website
  - status: Trạng thái connection
  - connected_at: Thời gian kết nối
  - last_activity: Thời gian hoạt động cuối
  - ip_address: Địa chỉ IP
  - user_agent: User agent string
  - metadata: Thông tin bổ sung
  - is_active: Trạng thái hoạt động
  - duration: Thời gian đã kết nối
}