# WebSocket Integration Guide

Hướng dẫn tích hợp WebSocket cho Frontend Developers.

## 1. Kết n<PERSON>i WebSocket

### JavaScript/TypeScript

```javascript
class SocketClient {
  constructor(config) {
    this.config = {
      url: 'ws://localhost:9033/api/v1/socket/ws/connect',
      tenantId: '1',
      websiteId: '1',
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      ...config
    };
    this.socket = null;
    this.reconnectAttempts = 0;
    this.listeners = new Map();
  }

  connect(token, userId) {
    const url = new URL(this.config.url);
    url.searchParams.append('token', token);
    url.searchParams.append('user_id', userId);
    url.searchParams.append('tenant_id', this.config.tenantId);
    url.searchParams.append('website_id', this.config.websiteId);

    this.socket = new WebSocket(url.toString());
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.socket.onopen = (event) => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.emit('connected', event);
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse message:', error);
      }
    };

    this.socket.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.emit('disconnected', event);
      this.handleReconnect();
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    };
  }

  handleMessage(message) {
    const { type, event, data } = message;
    
    // Emit specific event
    this.emit(event, data);
    
    // Emit general message event
    this.emit('message', message);
  }

  send(type, event, data) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      const message = {
        type,
        event,
        data,
        timestamp: new Date().toISOString()
      };
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in event callback:', error);
        }
      });
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(this.lastToken, this.lastUserId);
      }, this.config.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached');
      this.emit('reconnect_failed');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }
  }

  // Room methods
  joinRoom(roomId) {
    this.send('room', 'join_room', { room_id: roomId });
  }

  leaveRoom(roomId) {
    this.send('room', 'leave_room', { room_id: roomId });
  }

  sendMessage(roomId, message) {
    this.send('message', 'room_message', {
      room_id: roomId,
      message: message,
      message_type: 'text'
    });
  }

  // User status methods
  updateStatus(status) {
    this.send('user', 'status_update', { status });
  }

  sendTyping(roomId, isTyping) {
    this.send('user', 'typing', {
      room_id: roomId,
      is_typing: isTyping
    });
  }
}
```

### React Hook

```javascript
import { useEffect, useRef, useState } from 'react';

export function useSocket(config) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const socketRef = useRef(null);

  useEffect(() => {
    socketRef.current = new SocketClient({
      ...config,
      onConnect: () => setIsConnected(true),
      onDisconnect: () => setIsConnected(false),
      onMessage: (message) => setLastMessage(message)
    });

    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, []);

  const connect = (token, userId) => {
    socketRef.current?.connect(token, userId);
  };

  const disconnect = () => {
    socketRef.current?.disconnect();
  };

  const sendMessage = (type, event, data) => {
    socketRef.current?.send(type, event, data);
  };

  const subscribe = (event, callback) => {
    socketRef.current?.on(event, callback);
  };

  const unsubscribe = (event, callback) => {
    socketRef.current?.off(event, callback);
  };

  return {
    isConnected,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    subscribe,
    unsubscribe,
    socket: socketRef.current
  };
}
```

### Vue.js Composable

```javascript
import { ref, onMounted, onUnmounted } from 'vue';

export function useSocket(config) {
  const isConnected = ref(false);
  const lastMessage = ref(null);
  let socket = null;

  onMounted(() => {
    socket = new SocketClient({
      ...config,
      onConnect: () => isConnected.value = true,
      onDisconnect: () => isConnected.value = false,
      onMessage: (message) => lastMessage.value = message
    });
  });

  onUnmounted(() => {
    if (socket) {
      socket.disconnect();
    }
  });

  const connect = (token, userId) => {
    socket?.connect(token, userId);
  };

  const disconnect = () => {
    socket?.disconnect();
  };

  const sendMessage = (type, event, data) => {
    socket?.send(type, event, data);
  };

  return {
    isConnected,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    socket
  };
}
```

## 2. Message Types

### Incoming Messages

```javascript
// Welcome message
{
  "type": "system",
  "event": "welcome",
  "data": {
    "connection_id": "conn_123",
    "user_id": 1,
    "server_time": "2024-01-01T00:00:00Z"
  }
}

// Room message
{
  "type": "message",
  "event": "room_message",
  "data": {
    "room_id": "room_123",
    "message_id": "msg_456",
    "sender_id": 2,
    "sender_name": "John Doe",
    "message": "Hello everyone!",
    "message_type": "text",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}

// User joined room
{
  "type": "room",
  "event": "user_joined",
  "data": {
    "room_id": "room_123",
    "user_id": 3,
    "user_name": "Jane Smith",
    "joined_at": "2024-01-01T00:00:00Z"
  }
}

// Notification
{
  "type": "notification",
  "event": "system_notification",
  "data": {
    "title": "System Maintenance",
    "message": "System will be down for maintenance",
    "priority": "high",
    "expires_at": "2024-01-01T06:00:00Z"
  }
}

// Ping/Pong
{
  "type": "system",
  "event": "ping",
  "data": {
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### Outgoing Messages

```javascript
// Join room
socket.send('room', 'join_room', {
  room_id: 'room_123'
});

// Send message
socket.send('message', 'room_message', {
  room_id: 'room_123',
  message: 'Hello everyone!',
  message_type: 'text'
});

// Update status
socket.send('user', 'status_update', {
  status: 'online' // online, away, busy, offline
});

// Typing indicator
socket.send('user', 'typing', {
  room_id: 'room_123',
  is_typing: true
});

// Pong response
socket.send('system', 'pong', {
  timestamp: new Date().toISOString()
});
```

## 3. Error Handling

```javascript
socket.on('error', (error) => {
  console.error('Socket error:', error);
  
  switch (error.code) {
    case 'AUTH_FAILED':
      // Redirect to login
      window.location.href = '/login';
      break;
    case 'RATE_LIMITED':
      // Show rate limit message
      showNotification('Too many requests. Please slow down.');
      break;
    case 'ROOM_NOT_FOUND':
      // Handle room not found
      showNotification('Room not found.');
      break;
    default:
      showNotification('Connection error occurred.');
  }
});
```

## 4. Best Practices

### Connection Management

```javascript
// Always check connection state before sending
if (socket.readyState === WebSocket.OPEN) {
  socket.send(message);
} else {
  // Queue message or show error
  queueMessage(message);
}

// Implement heartbeat
setInterval(() => {
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      type: 'system',
      event: 'ping',
      data: { timestamp: new Date().toISOString() }
    }));
  }
}, 30000); // Every 30 seconds
```

### Message Queuing

```javascript
class MessageQueue {
  constructor() {
    this.queue = [];
    this.isProcessing = false;
  }

  add(message) {
    this.queue.push(message);
    this.process();
  }

  async process() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const message = this.queue.shift();
      
      if (socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify(message));
        await new Promise(resolve => setTimeout(resolve, 100)); // Rate limiting
      } else {
        // Re-queue if not connected
        this.queue.unshift(message);
        break;
      }
    }

    this.isProcessing = false;
  }
}
```

### State Management

```javascript
// Redux/Zustand store example
const useSocketStore = create((set, get) => ({
  isConnected: false,
  rooms: new Map(),
  messages: new Map(),
  users: new Map(),
  
  setConnected: (connected) => set({ isConnected: connected }),
  
  addMessage: (roomId, message) => {
    const messages = get().messages;
    if (!messages.has(roomId)) {
      messages.set(roomId, []);
    }
    messages.get(roomId).push(message);
    set({ messages: new Map(messages) });
  },
  
  updateUserStatus: (userId, status) => {
    const users = get().users;
    const user = users.get(userId) || {};
    users.set(userId, { ...user, status });
    set({ users: new Map(users) });
  }
}));
```

## 5. Testing

```javascript
// Mock WebSocket for testing
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.onopen?.({ type: 'open' });
    }, 100);
  }

  send(data) {
    console.log('Mock send:', data);
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    this.onclose?.({ type: 'close', code: 1000 });
  }
}

// Test example
describe('SocketClient', () => {
  beforeEach(() => {
    global.WebSocket = MockWebSocket;
  });

  test('should connect successfully', (done) => {
    const client = new SocketClient();
    client.on('connected', () => {
      expect(client.socket.readyState).toBe(WebSocket.OPEN);
      done();
    });
    client.connect('token', '1');
  });
});
```

## 6. Security Considerations

- Luôn sử dụng WSS (WebSocket Secure) trong production
- Validate tất cả incoming messages
- Implement rate limiting ở client side
- Không gửi sensitive data qua WebSocket
- Sử dụng JWT tokens với expiration time ngắn
- Implement proper error handling để tránh expose internal errors

## 7. Performance Tips

- Sử dụng message batching cho high-frequency updates
- Implement message compression cho large payloads
- Debounce typing indicators và user status updates
- Cleanup event listeners khi component unmount
- Sử dụng Web Workers cho heavy message processing