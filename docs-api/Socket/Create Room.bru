meta {
  name: Create Room
  type: http
  seq: 8
}

post {
  url: {{api_url}}/socket/rooms
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "name": "General Chat",
    "description": "General discussion room for all members",
    "type": "public",
    "max_members": 100,
    "settings": {
      "allow_file_sharing": true,
      "moderated": false,
      "require_approval": false,
      "allow_guest_access": true,
      "message_history_enabled": true,
      "auto_delete_messages": false
    },
    "metadata": {
      "category": "general",
      "tags": ["chat", "general", "public"]
    }
  }
}

assert {
  res.status: eq 201
  res.body.id: isString
}

tests {
  test("Should create room successfully", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Should return room details", function() {
    const body = res.getBody();
    expect(body.id).to.be.a("string");
    expect(body.name).to.equal("General Chat");
    expect(body.type).to.equal("public");
    expect(body.max_members).to.equal(100);
    expect(body.created_by).to.be.a("number");
    expect(body.created_at).to.be.a("string");
  });
  
  test("Should have settings", function() {
    const settings = res.getBody().settings;
    expect(settings).to.be.an("object");
    expect(settings.allow_file_sharing).to.be.a("boolean");
    expect(settings.moderated).to.be.a("boolean");
  });
}

docs {
  Tạo một room mới.
  
  Request Body:
  - name (required): Tên room
  - description (optional): Mô tả room
  - type (required): Loại room (public, private, direct)
  - max_members (optional): Số lượng member tối đa (default: 50)
  - settings (optional): Cài đặt room
    - allow_file_sharing: Cho phép chia sẻ file
    - moderated: Room có moderator
    - require_approval: Yêu cầu phê duyệt để tham gia
    - allow_guest_access: Cho phép guest truy cập
    - message_history_enabled: Lưu lịch sử tin nhắn
    - auto_delete_messages: Tự động xóa tin nhắn cũ
  - metadata (optional): Thông tin bổ sung
  
  Response:
  - id: Room ID
  - name: Tên room
  - description: Mô tả
  - type: Loại room
  - max_members: Số member tối đa
  - current_members: Số member hiện tại
  - created_by: ID người tạo
  - created_at: Thời gian tạo
  - settings: Cài đặt room
  - metadata: Thông tin bổ sung
}