meta {
  name: Get Rooms
  type: http
  seq: 9
}

get {
  url: {{api_url}}/socket/rooms?page=1&limit=20&type=public
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

params:query {
  page: 1
  limit: 20
  type: public
  ~search: 
  ~sort_by: created_at
  ~sort_order: desc
  ~member_count_min: 
  ~member_count_max: 
}

assert {
  res.status: eq 200
  res.body.data: isArray
}

tests {
  test("Should return rooms list", function() {
    expect(res.getBody().data).to.be.an("array");
  });
  
  test("Should have pagination info", function() {
    const body = res.getBody();
    expect(body.pagination).to.be.an("object");
    expect(body.pagination.page).to.be.a("number");
    expect(body.pagination.limit).to.be.a("number");
    expect(body.pagination.total).to.be.a("number");
  });
  
  test("Each room should have required fields", function() {
    const rooms = res.getBody().data;
    if (rooms.length > 0) {
      const room = rooms[0];
      expect(room.id).to.be.a("string");
      expect(room.name).to.be.a("string");
      expect(room.type).to.be.a("string");
      expect(room.current_members).to.be.a("number");
      expect(room.max_members).to.be.a("number");
      expect(room.created_at).to.be.a("string");
    }
  });
  
  test("Should filter by type if specified", function() {
    const rooms = res.getBody().data;
    rooms.forEach(room => {
      expect(room.type).to.equal("public");
    });
  });
}

docs {
  Lấy danh sách rooms mà user có thể truy cập.
  
  Query Parameters:
  - page: Số trang (default: 1)
  - limit: Số lượng items per page (default: 20, max: 100)
  - type: Filter theo loại room (public, private, direct)
  - search: Tìm kiếm theo tên room
  - sort_by: Sắp xếp theo field (name, created_at, member_count)
  - sort_order: Thứ tự sắp xếp (asc, desc)
  - member_count_min: Số member tối thiểu
  - member_count_max: Số member tối đa
  
  Response:
  - data: Mảng các room objects
  - pagination: Thông tin phân trang
  
  Room Object:
  - id: Room ID
  - name: Tên room
  - description: Mô tả
  - type: Loại room (public, private, direct)
  - current_members: Số member hiện tại
  - max_members: Số member tối đa
  - created_by: ID người tạo
  - created_at: Thời gian tạo
  - last_activity: Hoạt động cuối cùng
  - is_member: User có phải member không
  - member_role: Role của user trong room (nếu là member)
  - settings: Cài đặt room
}