meta {
  name: Get User Connections
  type: http
  seq: 7
}

get {
  url: {{api_url}}/socket/connections/users/1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

assert {
  res.status: eq 200
  res.body.data: isArray
}

tests {
  test("Should return user connections", function() {
    expect(res.getBody().data).to.be.an("array");
  });
  
  test("Should have user info", function() {
    const body = res.getBody();
    expect(body.user_id).to.be.a("number");
    expect(body.total_connections).to.be.a("number");
    expect(body.active_connections).to.be.a("number");
  });
  
  test("Each connection should have required fields", function() {
    const connections = res.getBody().data;
    if (connections.length > 0) {
      const connection = connections[0];
      expect(connection.id).to.be.a("string");
      expect(connection.user_id).to.be.a("number");
      expect(connection.tenant_id).to.be.a("number");
      expect(connection.status).to.be.a("string");
      expect(connection.connected_at).to.be.a("string");
      expect(connection.is_active).to.be.a("boolean");
    }
  });
}

docs {
  Lấy danh sách tất cả connections của một user cụ thể.
  
  Path Parameters:
  - user_id: ID của user cần lấy connections
  
  Response:
  - user_id: ID của user
  - total_connections: Tổng số connections
  - active_connections: Số connections đang hoạt động
  - data: Mảng các connection objects
  - last_activity: Thời gian hoạt động cuối cùng
  
  Connection Object:
  - id: Connection ID
  - user_id: ID của user
  - tenant_id: ID của tenant
  - website_id: ID của website
  - status: Trạng thái (active, inactive, disconnected)
  - connected_at: Thời gian kết nối
  - last_activity: Thời gian hoạt động cuối
  - ip_address: Địa chỉ IP
  - user_agent: User agent
  - is_active: Trạng thái hoạt động
  - duration: Thời gian đã kết nối
}