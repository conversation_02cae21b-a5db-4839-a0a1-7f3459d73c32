meta {
  name: WebSocket Connection
  type: http
  seq: 1
}

get {  
  url: {{api_url}}/socket/ws/connect?user_id=1&tenant_id=1&website_id=1
  body: none
  auth: none
}

doc {
  name: WebSocket Connection
  url: /socket/ws/connect
  method: GET
  description: |
    Endpoint để thiết lập kết nối WebSocket. Khi gọi endpoint này, kết nối HTTP sẽ được nâng cấp lên WebSocket.
    
    **Lưu ý:** Endpoint này không thể được gọi trực tiế<PERSON> t<PERSON>, vì nó yêu cầu WebSocket client. Sử dụng WebSocket client như browser, Postman, hoặc thư viện WebSocket trong code để kết nối.
  
  params: [
    {
      name: user_id,
      type: number,
      required: true,
      description: ID của user
    },
    {
      name: tenant_id,
      type: number,
      required: true,
      description: ID của tenant
    },
    {
      name: website_id,
      type: number,
      required: false,
      description: ID của website (optional)
    }
  ]
  
  response: {
    type: WebSocket,
    description: |
      <PERSON><PERSON> kết nối thành công, client sẽ nhận được message chào mừng với thông tin kết nối.
      
      Sau đó, client có thể gửi và nhận các message theo định dạng JSON.
    
    example: |
      ```json
      {
        "type": "event",
        "event": "system.connected",
        "data": {
          "connection_id": "1_1_1234567890",
          "user_id": 1,
          "tenant_id": 1,
          "website_id": 1,
          "server_time": "2024-01-01T00:00:00Z",
          "features": {
            "rooms": true,
            "notifications": true,
            "file_sharing": false,
            "video_calls": false
          }
        }
      }
      ```
  }
  
  errors: [
    {
      code: 401,
      description: Authentication required
    },
    {
      code: 400,
      description: Invalid parameters
    }
  ]
}