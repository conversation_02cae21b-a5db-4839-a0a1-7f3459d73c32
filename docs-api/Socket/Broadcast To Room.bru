meta {
  name: Broadcast To Room
  type: http
  seq: 11
}

post {
  url: {{api_url}}/socket/rooms/room_123/broadcast
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "type": "message",
    "event": "room_message",
    "data": {
      "message": "Hello everyone! This is a broadcast message to the room.",
      "sender_id": 1,
      "sender_name": "<PERSON>",
      "message_type": "text",
      "timestamp": "2024-01-01T00:00:00Z",
      "metadata": {
        "priority": "normal",
        "mentions": [],
        "attachments": []
      }
    },
    "exclude_sender": false,
    "target_roles": ["member", "moderator", "admin"]
  }
}

assert {
  res.status: eq 200
}

tests {
  test("Should broadcast message successfully", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should return broadcast result", function() {
    const body = res.getBody();
    expect(body.success).to.be.true;
    expect(body.room_id).to.be.a("string");
    expect(body.members_reached).to.be.a("number");
    expect(body.message_id).to.be.a("string");
  });
  
  test("Should have delivery info", function() {
    const body = res.getBody();
    expect(body.delivery_info).to.be.an("object");
    expect(body.delivery_info.total_members).to.be.a("number");
    expect(body.delivery_info.online_members).to.be.a("number");
    expect(body.delivery_info.delivered).to.be.a("number");
  });
}

docs {
  Gửi message broadcast tới tất cả members trong room.
  
  Path Parameters:
  - id: Room ID cần gửi message
  
  Request Body:
  - type (required): Loại message (message, notification, event, announcement)
  - event (required): Tên event
  - data (required): Dữ liệu message
    - message: Nội dung tin nhắn
    - sender_id: ID người gửi
    - sender_name: Tên người gửi
    - message_type: Loại tin nhắn (text, image, file, etc.)
    - timestamp: Thời gian gửi
    - metadata: Thông tin bổ sung
  - exclude_sender (optional): Có loại trừ người gửi không (default: false)
  - target_roles (optional): Chỉ gửi cho các role cụ thể
  
  Response:
  - success: Trạng thái thành công
  - room_id: ID của room
  - message_id: ID của message
  - members_reached: Số lượng members đã nhận message
  - delivery_info: Thông tin chi tiết về việc gửi
    - total_members: Tổng số members trong room
    - online_members: Số members đang online
    - delivered: Số message đã gửi thành công
    - failed: Số message gửi thất bại
  
  Message Types:
  - message: Tin nhắn chat thông thường
  - notification: Thông báo hệ thống
  - event: Event trong room
  - announcement: Thông báo quan trọng
  
  Permissions:
  - member: Có thể gửi message thông thường
  - moderator: Có thể gửi tất cả loại message
  - admin: Có thể gửi tất cả loại message và announcement
}