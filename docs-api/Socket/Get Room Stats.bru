meta {
  name: Get Room Stats
  type: http
  seq: 14
}

get {
  url: {{api_url}}/socket/stats/rooms?period=24h&include_inactive=false
  body: none
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
}

params:query {
  period: 24h
  include_inactive: false
  ~room_type: 
  ~min_members: 
  ~max_members: 
}

assert {
  res.status: eq 200
  res.body.total_rooms: isNumber
}

tests {
  test("Should return room stats", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should have room counts", function() {
    const body = res.getBody();
    expect(body.total_rooms).to.be.a("number");
    expect(body.active_rooms).to.be.a("number");
    expect(body.public_rooms).to.be.a("number");
    expect(body.private_rooms).to.be.a("number");
  });
  
  test("Should have member stats", function() {
    const body = res.getBody();
    expect(body.member_stats).to.be.an("object");
    expect(body.member_stats.total_members).to.be.a("number");
    expect(body.member_stats.average_members_per_room).to.be.a("number");
    expect(body.member_stats.most_popular_room).to.be.an("object");
  });
  
  test("Should have activity stats", function() {
    const body = res.getBody();
    expect(body.activity_stats).to.be.an("object");
    expect(body.activity_stats.messages_today).to.be.a("number");
    expect(body.activity_stats.most_active_room).to.be.an("object");
  });
}

docs {
  Lấy thống kê về rooms.
  
  Query Parameters:
  - period: Khoảng thời gian thống kê (1h, 6h, 24h, 7d, 30d)
  - include_inactive: Bao gồm rooms không hoạt động
  - room_type: Filter theo loại room (public, private, direct)
  - min_members: Số members tối thiểu
  - max_members: Số members tối đa
  
  Response:
  - total_rooms: Tổng số rooms
  - active_rooms: Số rooms đang hoạt động
  - public_rooms: Số public rooms
  - private_rooms: Số private rooms
  - direct_rooms: Số direct message rooms
  - member_stats: Thống kê members
    - total_members: Tổng số members
    - average_members_per_room: Trung bình members/room
    - most_popular_room: Room có nhiều members nhất
      - id: Room ID
      - name: Tên room
      - member_count: Số members
    - least_popular_room: Room có ít members nhất
  - activity_stats: Thống kê hoạt động
    - messages_today: Số tin nhắn hôm nay
    - messages_this_week: Số tin nhắn tuần này
    - most_active_room: Room hoạt động nhiều nhất
      - id: Room ID
      - name: Tên room
      - message_count: Số tin nhắn
      - last_activity: Hoạt động cuối
    - least_active_room: Room hoạt động ít nhất
  - creation_stats: Thống kê tạo rooms
    - created_today: Số rooms tạo hôm nay
    - created_this_week: Số rooms tạo tuần này
    - created_this_month: Số rooms tạo tháng này
  - by_type: Phân bố theo loại
    - public: Số public rooms
    - private: Số private rooms
    - direct: Số direct rooms
  - top_rooms: Top rooms theo activity
    - id: Room ID
    - name: Tên room
    - type: Loại room
    - member_count: Số members
    - message_count: Số tin nhắn
    - last_activity: Hoạt động cuối
    - created_at: Thời gian tạo
  
  Period Options:
  - 1h: 1 giờ qua
  - 6h: 6 giờ qua
  - 24h: 24 giờ qua
  - 7d: 7 ngày qua
  - 30d: 30 ngày qua
}