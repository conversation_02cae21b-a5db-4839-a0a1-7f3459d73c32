meta {
  name: Disconnect Connection
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/socket/connections/1_1_1234567890
  body: json
  auth: bearer
}

auth:bearer {
  token: {{auth_token}}
}

headers {
  X-Tenant-Id: 1
  Content-Type: application/json
}

body:json {
  {
    "reason": "Admin disconnect",
    "force": true
  }
}

assert {
  res.status: eq 200
}

tests {
  test("Should successfully disconnect", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Should return success message", function() {
    const body = res.getBody();
    expect(body.message || body.success).to.exist;
  });
}

docs {
  Ngắt kết nối một connection cụ thể.
  
  Path Parameters:
  - id: Connection ID cần ngắt kết nối
  
  Request Body:
  - reason (required): <PERSON><PERSON> do ngắt kết nối
  - force (optional): Có force ngắt kết nối hay không (default: false)
  
  Response:
  - Thông báo thành công hoặc lỗi
  
  Lưu ý:
  - Chỉ admin hoặc moderator mới có quyền ngắt kết nối
  - Khi force = true, connection sẽ bị ngắt ngay lập tức
  - Khi force = false, sẽ gửi thông báo cho client trước khi ngắt
}