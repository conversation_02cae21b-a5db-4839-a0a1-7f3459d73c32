meta {
  name: Delete SEO Meta by ID
  type: http
  seq: 7
}

delete {
  url: {{api_url}}/api/admin/v1/seo/meta/1
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

docs {
  # Delete SEO Meta by ID
  
  Deletes SEO metadata by its unique meta_id.
  
  ## URL Parameters
  - `id`: The meta_id of the SEO metadata record to delete
  
  ## Response
  Returns success confirmation when the metadata is deleted.
  
  ```json
  {
    "meta": {
      "status": "success",
      "code": 200,
      "message": "SEO metadata deleted successfully"
    }
  }
  ```
}
