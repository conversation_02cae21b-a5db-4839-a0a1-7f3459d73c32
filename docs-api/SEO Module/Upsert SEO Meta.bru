meta {
  name: Upsert SEO Meta
  type: http
  seq: 5
}

post {
  url: {{api_url}}/api/admin/v1/seo/meta/upsert
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "table_id": 2,
    "table_name": "products",
    "meta_title": "Premium Wireless Headphones - High Quality Audio",
    "meta_description": "Experience superior sound quality with our premium wireless headphones. Perfect for music lovers and professionals.",
    "keywords": "headphones, audio, premium, wireless",
    "canonical_url": "https://example.com/products/premium-headphones",
    "og_title": "Premium Wireless Headphones - High Quality Audio",
    "og_description": "Experience superior sound quality with our premium wireless headphones.",
    "og_image": "https://example.com/images/headphones-product.jpg",
    "twitter_title": "Premium Wireless Headphones - High Quality Audio",
    "twitter_description": "Experience superior sound quality with our premium wireless headphones.",
    "twitter_image": "https://example.com/images/headphones-twitter.jpg",
    "robots_index": 1,
    "robots_follow": 1,
    "robots_advanced": "max-snippet:-1,max-image-preview:large",
    "seo_score": 88,
    "readability_score": 82,
    "schema_data": "{\"@type\": \"Product\", \"name\": \"Premium Wireless Headphones\", \"brand\": \"AudioTech\"}"
  }
}

docs {
  # Upsert SEO Meta
  
  Creates new SEO metadata or updates existing metadata for a table record.
  If metadata already exists for the given table_id and table_name, it will be updated.
  Otherwise, new metadata will be created.
  
  ## Request Body
  Same as Create SEO Meta endpoint.
  
  ## Response
  Returns the created or updated SEO metadata.
  
  ```json
  {
    "data": {
      "meta_id": 2,
      "table_id": 2,
      "table_name": "products",
      "meta_title": "Premium Wireless Headphones - High Quality Audio",
      "meta_description": "Experience superior sound quality with our premium wireless headphones. Perfect for music lovers and professionals.",
      "keywords": "headphones, audio, premium, wireless",
      "canonical_url": "https://example.com/products/premium-headphones",
      "og_title": "Premium Wireless Headphones - High Quality Audio",
      "og_description": "Experience superior sound quality with our premium wireless headphones.",
      "og_image": "https://example.com/images/headphones-product.jpg",
      "twitter_title": "Premium Wireless Headphones - High Quality Audio",
      "twitter_description": "Experience superior sound quality with our premium wireless headphones.",
      "twitter_image": "https://example.com/images/headphones-twitter.jpg",
      "robots_index": 1,
      "robots_follow": 1,
      "robots_advanced": "max-snippet:-1,max-image-preview:large",
      "seo_score": 88,
      "readability_score": 82,
      "schema_data": "{\"@type\": \"Product\", \"name\": \"Premium Wireless Headphones\", \"brand\": \"AudioTech\"}",
      "created_at": "2025-06-20T14:15:00Z",
      "updated_at": "2025-06-20T14:15:00Z"
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "SEO metadata created/updated successfully"
    }
  }
  ```
}
