meta {
  name: Get SEO Meta by ID
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/admin/v1/seo/meta/1
  body: none
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

docs {
  # Get SEO Meta by ID
  
  Retrieves SEO metadata by its unique meta_id.
  
  ## URL Parameters
  - `id`: The meta_id of the SEO metadata record
  
  ## Response
  Returns the complete SEO metadata record.
  
  ```json
  {
    "data": {
      "meta_id": 1,
      "table_id": 1,
      "table_name": "posts",
      "meta_title": "Sample Blog Post - Best Practices",
      "meta_description": "Learn about the best practices for writing effective blog posts that engage readers and improve SEO rankings.",
      "keywords": "blog, seo, writing, best practices",
      "canonical_url": "https://example.com/blog/sample-post",
      "og_title": "Sample Blog Post - Best Practices",
      "og_description": "Learn about the best practices for writing effective blog posts.",
      "og_image": "https://example.com/images/blog-post.jpg",
      "twitter_title": "Sample Blog Post - Best Practices",
      "twitter_description": "Learn about the best practices for writing effective blog posts.",
      "twitter_image": "https://example.com/images/blog-post-twitter.jpg",
      "robots_index": 1,
      "robots_follow": 1,
      "robots_advanced": "max-snippet:-1,max-image-preview:large",
      "seo_score": 85,
      "readability_score": 78,
      "schema_data": "{\"@type\": \"Article\", \"headline\": \"Sample Blog Post\"}",
      "created_at": "2025-06-20T10:30:00Z",
      "updated_at": "2025-06-20T10:30:00Z"
    },
    "meta": {
      "status": "success",
      "code": 200
    }
  }
  ```
}
