meta {
  name: Update SEO Meta
  type: http
  seq: 3
}

put {
  url: {{api_url}}/api/admin/v1/seo/meta/1
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "meta_title": "Updated Blog Post - Advanced Best Practices",
    "meta_description": "Discover advanced techniques and best practices for creating high-quality blog posts that drive traffic and engagement.",
    "keywords": "advanced, blog, writing, seo",
    "canonical_url": "https://example.com/blog/advanced-post",
    "og_title": "Updated Blog Post - Advanced Best Practices",
    "og_description": "Discover advanced techniques for creating high-quality blog posts.",
    "og_image": "https://example.com/images/advanced-blog-post.jpg",
    "twitter_title": "Updated Blog Post - Advanced Best Practices",
    "twitter_description": "Discover advanced techniques for creating high-quality blog posts.",
    "twitter_image": "https://example.com/images/advanced-blog-post-twitter.jpg",
    "robots_index": 1,
    "robots_follow": 1,
    "robots_advanced": "max-snippet:-1,max-image-preview:large,max-video-preview:-1",
    "seo_score": 92,
    "readability_score": 85,
    "schema_data": "{\"@type\": \"Article\", \"headline\": \"Updated Blog Post\", \"author\": {\"@type\": \"Person\", \"name\": \"John Doe\"}}"
  }
}

docs {
  # Update SEO Meta
  
  Updates existing SEO metadata by meta ID.
  
  ## URL Parameters
  - `id`: The meta_id of the SEO metadata record to update
  
  ## Request Body
  All fields are optional. Only provided fields will be updated.
  
  - `meta_title`: SEO title for the page
  - `meta_description`: Meta description for search engines
  - `keywords`: SEO keywords (comma separated)
  - `canonical_url`: Canonical URL for the content
  - `og_title`: Open Graph title for social sharing
  - `og_description`: Open Graph description for social sharing
  - `og_image`: Open Graph image URL for social sharing
  - `twitter_title`: Twitter card title
  - `twitter_description`: Twitter card description
  - `twitter_image`: Twitter card image URL
  - `robots_index`: Whether search engines should index the page (1 for yes, 0 for no)
  - `robots_follow`: Whether search engines should follow links (1 for yes, 0 for no)
  - `robots_advanced`: Additional robots directives
  - `seo_score`: SEO score (0-100)
  - `readability_score`: Readability score (0-100)
  - `schema_data`: JSON structured data for schema markup
  
  ## Response
  ```json
  {
    "data": {
      "meta_id": 1,
      "table_id": 1,
      "table_name": "posts",
      "meta_title": "Updated Blog Post - Advanced Best Practices",
      "meta_description": "Discover advanced techniques and best practices for creating high-quality blog posts that drive traffic and engagement.",
      "keywords": "advanced, blog, writing, seo",
      "canonical_url": "https://example.com/blog/advanced-post",
      "og_title": "Updated Blog Post - Advanced Best Practices",
      "og_description": "Discover advanced techniques for creating high-quality blog posts.",
      "og_image": "https://example.com/images/advanced-blog-post.jpg",
      "twitter_title": "Updated Blog Post - Advanced Best Practices",
      "twitter_description": "Discover advanced techniques for creating high-quality blog posts.",
      "twitter_image": "https://example.com/images/advanced-blog-post-twitter.jpg",
      "robots_index": 1,
      "robots_follow": 1,
      "robots_advanced": "max-snippet:-1,max-image-preview:large,max-video-preview:-1",
      "seo_score": 92,
      "readability_score": 85,
      "schema_data": "{\"@type\": \"Article\", \"headline\": \"Updated Blog Post\", \"author\": {\"@type\": \"Person\", \"name\": \"John Doe\"}}",
      "created_at": "2025-06-20T10:30:00Z",
      "updated_at": "2025-06-20T11:45:00Z"
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "SEO metadata updated successfully"
    }
  }
  ```
}
