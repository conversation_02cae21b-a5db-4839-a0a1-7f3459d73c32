meta {
  name: Delete SEO Meta by Table
  type: http
  seq: 4
}

delete {
  url: {{api_url}}/api/admin/v1/seo/meta/table?table_id=1&table_name=posts
  body: none
  auth: none
}

params:query {
  table_id: 1
  table_name: posts
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

docs {
  # Delete SEO Meta by Table
  
  Deletes SEO metadata for a specific table record.
  
  ## Parameters
  - `table_id` (required): The ID of the record in the target table
  - `table_name` (required): The name of the target table
  
  ## Response
  Returns success confirmation when the metadata is deleted.
  
  ```json
  {
    "meta": {
      "status": "success",
      "code": 200,
      "message": "SEO metadata deleted successfully"
    }
  }
  ```
}
