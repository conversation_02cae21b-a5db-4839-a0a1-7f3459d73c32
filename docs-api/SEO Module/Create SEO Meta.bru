meta {
  name: Create SEO Meta
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/seo/meta
  body: json
  auth: none
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "table_id": 1,
    "table_name": "posts",
    "meta_title": "Sample Blog Post - Best Practices",
    "meta_description": "Learn about the best practices for writing effective blog posts that engage readers and improve SEO rankings.",
    "keywords": "blog, seo, writing, best practices",
    "canonical_url": "https://example.com/blog/sample-post",
    "og_title": "Sample Blog Post - Best Practices",
    "og_description": "Learn about the best practices for writing effective blog posts.",
    "og_image": "https://example.com/images/blog-post.jpg",
    "twitter_title": "Sample Blog Post - Best Practices",
    "twitter_description": "Learn about the best practices for writing effective blog posts.",
    "twitter_image": "https://example.com/images/blog-post-twitter.jpg",
    "robots_index": 1,
    "robots_follow": 1,
    "robots_advanced": "max-snippet:-1,max-image-preview:large",
    "seo_score": 85,
    "readability_score": 78,
    "schema_data": "{\"@type\": \"Article\", \"headline\": \"Sample Blog Post\"}"
  }
}

docs {
  # Create SEO Meta
  
  Creates new SEO metadata for a table record.
  
  ## Request Body
  - `table_id` (required): The ID of the record in the target table
  - `table_name` (required): The name of the target table
  - `meta_title`: SEO title for the page
  - `meta_description`: Meta description for search engines
  - `keywords`: SEO keywords (comma separated)
  - `canonical_url`: Canonical URL for the content
  - `og_title`: Open Graph title for social sharing
  - `og_description`: Open Graph description for social sharing
  - `og_image`: Open Graph image URL for social sharing
  - `twitter_title`: Twitter card title
  - `twitter_description`: Twitter card description
  - `twitter_image`: Twitter card image URL
  - `robots_index`: Whether search engines should index the page (1 for yes, 0 for no)
  - `robots_follow`: Whether search engines should follow links (1 for yes, 0 for no)
  - `robots_advanced`: Additional robots directives
  - `seo_score`: SEO score (0-100)
  - `readability_score`: Readability score (0-100)
  - `schema_data`: JSON structured data for schema markup
  
  ## Response
  ```json
  {
    "data": {
      "meta_id": 1,
      "table_id": 1,
      "table_name": "posts",
      "meta_title": "Sample Blog Post - Best Practices",
      "meta_description": "Learn about the best practices for writing effective blog posts that engage readers and improve SEO rankings.",
      "keywords": "blog, seo, writing, best practices",
      "canonical_url": "https://example.com/blog/sample-post",
      "og_title": "Sample Blog Post - Best Practices",
      "og_description": "Learn about the best practices for writing effective blog posts.",
      "og_image": "https://example.com/images/blog-post.jpg",
      "twitter_title": "Sample Blog Post - Best Practices",
      "twitter_description": "Learn about the best practices for writing effective blog posts.",
      "twitter_image": "https://example.com/images/blog-post-twitter.jpg",
      "robots_index": 1,
      "robots_follow": 1,
      "robots_advanced": "max-snippet:-1,max-image-preview:large",
      "seo_score": 85,
      "readability_score": 78,
      "schema_data": "{\"@type\": \"Article\", \"headline\": \"Sample Blog Post\"}",
      "created_at": "2025-06-20T10:30:00Z",
      "updated_at": "2025-06-20T10:30:00Z"
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "SEO metadata created successfully"
    }
  }
  ```
}