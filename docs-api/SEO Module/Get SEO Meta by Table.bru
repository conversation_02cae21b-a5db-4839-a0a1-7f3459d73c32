meta {
  name: Get SEO Meta by Table
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/seo/meta/table?table_id=1&table_name=posts
  body: none
  auth: none
}

params:query {
  table_id: 1
  table_name: posts
}

headers {
  Content-Type: application/json
  Authorization: Bearer {{access_token}}
}

docs {
  # Get SEO Meta by Table
  
  Retrieves SEO metadata for a specific table record.
  
  ## Parameters
  - `table_id` (required): The ID of the record in the target table
  - `table_name` (required): The name of the target table (e.g., "posts", "products")
  
  ## Response
  Returns the SEO metadata including title, description, Open Graph data, etc.
  
  ```json
  {
    "data": {
      "meta_id": 1,
      "table_id": 1,
      "table_name": "posts",
      "meta_title": "Sample Blog Post - Best Practices",
      "meta_description": "Learn about the best practices for writing effective blog posts that engage readers and improve SEO rankings.",
      "keywords": "blog, seo, writing, best practices",
      "canonical_url": "https://example.com/blog/sample-post",
      "og_title": "Sample Blog Post - Best Practices",
      "og_description": "Learn about the best practices for writing effective blog posts.",
      "og_image": "https://example.com/images/blog-post.jpg",
      "twitter_title": "Sample Blog Post - Best Practices",
      "twitter_description": "Learn about the best practices for writing effective blog posts.",
      "twitter_image": "https://example.com/images/blog-post-twitter.jpg",
      "robots_index": 1,
      "robots_follow": 1,
      "robots_advanced": "max-snippet:-1,max-image-preview:large",
      "seo_score": 85,
      "readability_score": 78,
      "schema_data": "{\"@type\": \"Article\", \"headline\": \"Sample Blog Post\"}",
      "created_at": "2025-06-20T10:30:00Z",
      "updated_at": "2025-06-20T10:30:00Z"
    },
    "meta": {
      "status": "success",
      "code": 200
    }
  }
  ```
}
