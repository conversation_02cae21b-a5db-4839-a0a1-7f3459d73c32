meta {
  name: Create User
  type: http
  seq: 12
}

post {
  url: {{base_url}}/api/v1/users
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "email": "<EMAIL>",
    "username": "newuser",
    "password": "password123",
    "password_confirmation": "password123",
    "full_name": "New User",
    "phone": "+84123456789",
    "roles": ["user"],
    "status": "active"
  }
}

docs {
  # Create New User
  
  Tạo user mới trong hệ thống.
  
  ## Request Body
  ```json
  {
    "email": "string",                     // Email (required, unique)
    "username": "string",                  // Username (required, unique) 
    "password": "string",                  // Password (required, min: 8 chars)
    "password_confirmation": "string",     // Password confirmation (required)
    "full_name": "string",                 // Full name (optional)
    "phone": "string",                     // Phone number (optional)
    "roles": ["string"],                   // Array of role names (optional)
    "status": "string"                     // Status: active/inactive (optional, default: active)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 2,
      "email": "<EMAIL>",
      "username": "newuser",
      "full_name": "New User",
      "phone": "+84123456789",
      "status": "active",
      "roles": ["user"],
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `auth.users.create`
  
  ## Error Responses
  - 422: Validation Error - Email/username đã tồn tại hoặc dữ liệu không hợp lệ
  - 403: Forbidden - Không có quyền tạo user
}

tests {
  test("Status code is 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("User created successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.email).to.equal("<EMAIL>");
  });
}
