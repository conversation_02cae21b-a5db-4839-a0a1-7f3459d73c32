meta {
  name: Get User
  type: http
  seq: 11
}

get {
  url: {{base_url}}/api/v1/users/1
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get User by ID
  
  L<PERSON><PERSON> thông tin chi tiết của một user theo ID.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "joh<PERSON><PERSON>",
      "full_name": "<PERSON>",
      "phone": "+84123456789",
      "avatar": "https://example.com/avatar.jpg",
      "status": "active",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "roles": ["user"],
      "permissions": ["read_posts"],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `auth.users.read`
  
  ## Error Responses
  - 404: User not found
  - 403: For<PERSON> - <PERSON><PERSON><PERSON><PERSON> c<PERSON> quyền xem user này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has user data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("id");
    expect(data.data).to.have.property("email");
  });
}
