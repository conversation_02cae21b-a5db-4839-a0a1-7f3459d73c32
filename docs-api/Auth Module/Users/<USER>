meta {
  name: Update User
  type: http
  seq: 13
}

put {
  url: {{base_url}}/api/v1/users/1
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "full_name": "Updated User Name",
    "phone": "+84987654321",
    "status": "active",
    "roles": ["user", "moderator"]
  }
}

docs {
  # Update User
  
  Cập nhật thông tin của một user.
  
  ## Request Body
  ```json
  {
    "full_name": "string",        // Full name (optional)
    "phone": "string",            // Phone number (optional)
    "status": "string",           // Status: active/inactive/banned (optional)
    "roles": ["string"],          // Array of role names (optional)
    "avatar": "string"            // Avatar URL (optional)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "johndo<PERSON>",
      "full_name": "Updated User Name",
      "phone": "+84987654321",
      "status": "active",
      "roles": ["user", "moderator"],
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `auth.users.update`
  
  ## Error Responses
  - 404: User not found
  - 422: Validation Error
  - 403: Forbidden - Không có quyền cập nhật user này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("User updated successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.full_name).to.equal("Updated User Name");
  });
}
