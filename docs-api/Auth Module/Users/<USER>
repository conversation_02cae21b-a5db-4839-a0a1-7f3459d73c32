meta {
  name: List Users
  type: http
  seq: 10
}

get {
  url: {{base_url}}/api/v1/users?page=1&limit=10&search=john
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 10
  search: john
  status: active
  role: user
}

docs {
  # List Users
  
  L<PERSON>y danh sách users với phân trang và lọc.
  
  ## Query Parameters
  - `page`: Số trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 10, max: 100)
  - `search`: <PERSON><PERSON><PERSON> kiếm theo email, username, full_name
  - `status`: <PERSON><PERSON><PERSON> theo trạng thái (active, inactive, banned)
  - `role`: <PERSON><PERSON><PERSON> theo role
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "username": "johndo<PERSON>",
        "full_name": "<PERSON>",
        "status": "active",
        "roles": ["user"],
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 10,
      "total": 100,
      "last_page": 10
    }
  }
  ```
  
  ## Required Permission
  - `auth.users.list`
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has users list", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.meta).to.have.property("current_page");
  });
}
