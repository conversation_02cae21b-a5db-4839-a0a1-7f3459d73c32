meta {
  name: Delete User
  type: http
  seq: 14
}

delete {
  url: {{base_url}}/api/v1/users/1
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Delete User
  
  Xóa một user khỏi hệ thống.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "message": "User deleted successfully"
  }
  ```
  
  ## Required Permission
  - `auth.users.delete`
  
  ## Error Responses
  - 404: User not found
  - 403: Forbidden - Không có quyền xóa user này hoặc không thể xóa chính mình
  - 409: Conflict - Không thể xóa user có dữ liệu liên quan
  
  ## Notes
  - Không thể xóa user hiện tại đang đăng nhập
  - Nếu user có dữ liệu liên quan (posts, comments, etc.), có thể sẽ được soft delete thay vì hard delete
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("User deleted successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.message).to.contain("deleted successfully");
  });
}
