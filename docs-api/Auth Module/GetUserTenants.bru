meta {
  name: GetUserTenants
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/admin/v1/users/me/tenants
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}



docs {
  title: "<PERSON><PERSON><PERSON> danh sách tenant của người dùng hiện tại"
  description: "API này trả về danh sách các tenant mà người dùng hiện tại có quyền truy cập"
  
  response: [
    {
      "tenants": [
        {
          "tenant_id": 1,
          "tenant_name": "Tenant 1",
          "role_id": 1,
          "role_name": "Admin",
          "status": "active",
          "created_at": "2023-01-01T00:00:00Z",
          "updated_at": "2023-01-01T00:00:00Z"
        }
      ]
    }
  ]
}