meta {
  name: Get Me
  type: http
  seq: 6
}

get {
  url: {{base_url}}/api/v1/users/me
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Current User Info
  
  L<PERSON>y thông tin chi tiết của user hi<PERSON><PERSON> t<PERSON>, b<PERSON> gồm cả thông tin tenant.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "johndo<PERSON>",
      "full_name": "<PERSON>",
      "phone": "+84123456789",
      "avatar": "https://example.com/avatar.jpg",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "roles": ["admin", "user"],
      "permissions": ["read_posts", "write_posts"],
      "current_tenant": {
        "id": 1,
        "name": "Default Tenant",
        "domain": "default.example.com"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Error Responses
  - 401: Unauthorized - Token không hợp lệ
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has user data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("id");
    expect(data.data).to.have.property("email");
    expect(data.data).to.have.property("current_tenant");
  });
}
