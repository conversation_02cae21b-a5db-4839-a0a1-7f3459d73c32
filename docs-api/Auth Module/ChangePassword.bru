meta {
  name: Change Password
  type: http
  seq: 5
}

put {
  url: {{base_url}}/api/v1/auth/change-password
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "current_password": "oldpassword123",
    "new_password": "newpassword123",
    "new_password_confirmation": "newpassword123"
  }
}

docs {
  # Change Password
  
  Thay đổi mật khẩu của user hiện tại.
  
  ## Request Body
  ```json
  {
    "current_password": "string",              // Mật khẩu hiện tại (required)
    "new_password": "string",                  // M<PERSON>t khẩu mới (required, min: 8 chars)
    "new_password_confirmation": "string"      // Xác nhận mật khẩu mới (required)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "message": "Password changed successfully"
  }
  ```
  
  ## Error Responses
  - 400: Bad Request - Dữ liệu đầu vào không hợp lệ
  - 401: Unauthorized - Token không hợp lệ hoặc mật khẩu hiện tại sai
  - 422: Validation Error - <PERSON><PERSON><PERSON> khẩu mới không đáp ứng yêu cầu
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Password changed successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.message).to.contain("Password changed successfully");
  });
}
