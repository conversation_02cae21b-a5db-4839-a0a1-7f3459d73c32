meta {
  name: Get Profile
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/v1/auth/profile
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get User Profile
  
  Lấy thông tin profile của user hiện tại đã đăng nhập.
  
  ## Response Format
  ```json
  {
    "status": "success", 
    "data": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "joh<PERSON><PERSON>",
      "full_name": "<PERSON>",
      "phone": "+84123456789",
      "avatar": "https://example.com/avatar.jpg",
      "email_verified_at": "2024-01-01T00:00:00Z",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Error Responses
  - 401: Unauthorized - Token không hợp lệ
  - 404: User not found
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has profile data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("id");
    expect(data.data).to.have.property("email");
  });
}
