meta {
  name: Update Profile
  type: http
  seq: 4
}

put {
  url: {{base_url}}/api/v1/auth/profile
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "full_name": "<PERSON>",
    "phone": "+84987654321",
    "avatar": "https://example.com/new-avatar.jpg"
  }
}

docs {
  # Update User Profile
  
  Cập nhật thông tin profile của user hiện tại.
  
  ## Request Body
  ```json
  {
    "full_name": "string", // Tên đầy đủ (optional)
    "phone": "string",     // Số điện thoại (optional)
    "avatar": "string"     // URL avatar (optional)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "johndo<PERSON>", 
      "full_name": "<PERSON>",
      "phone": "+84987654321",
      "avatar": "https://example.com/new-avatar.jpg",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Error Responses
  - 400: Bad Request - Dữ liệu đầu vào không hợp lệ
  - 401: Unauthorized - Token không hợp lệ
  - 422: Validation Error - Lỗi validation
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Profile updated successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.full_name).to.equal("John Doe Updated");
  });
}
