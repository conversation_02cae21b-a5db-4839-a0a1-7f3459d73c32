# Marketing Module API Collection

## M<PERSON> tả
Module Marketing quản lý các vị trí quảng cáo (Ads Positions) và banner quảng cáo (Ads Banners) cho hệ thống.

## Cấu trúc API

### 1. Ads Positions APIs
Quản lý các vị trí đặt quảng cáo trên website.

#### Public Routes (Không cần xác thực)
- `GET /api/admin/v1/marketing/ads-positions/active` - Lấy danh sách vị trí quảng cáo đang hoạt động

#### Protected Routes (Cần xác thực)
- `GET /api/admin/v1/marketing/ads-positions` - <PERSON><PERSON><PERSON> danh sách tất cả vị trí quảng cáo
- `POST /api/admin/v1/marketing/ads-positions` - Tạo vị trí quảng cáo mới
- `GET /api/admin/v1/marketing/ads-positions/:id` - <PERSON><PERSON><PERSON> chi tiết vị trí quảng cáo
- `PUT /api/admin/v1/marketing/ads-positions/:id` - <PERSON><PERSON><PERSON> nhật vị trí quảng cáo
- `DELETE /api/admin/v1/marketing/ads-positions/:id` - Xóa vị trí quảng cáo

### 2. Ads Banners APIs
Quản lý các banner quảng cáo.

#### Public Routes (Không cần xác thực)
- `GET /api/admin/v1/marketing/ads-banners/active` - Lấy danh sách banner đang hoạt động

#### Protected Routes (Cần xác thực)
- `GET /api/admin/v1/marketing/ads-banners` - Lấy danh sách tất cả banner
- `POST /api/admin/v1/marketing/ads-banners` - Tạo banner mới
- `GET /api/admin/v1/marketing/ads-banners/:id` - Lấy chi tiết banner
- `PUT /api/admin/v1/marketing/ads-banners/:id` - Cập nhật banner
- `DELETE /api/admin/v1/marketing/ads-banners/:id` - Xóa banner
- `PATCH /api/admin/v1/marketing/ads-banners/:id/status` - Cập nhật trạng thái banner

### 3. Public Marketing APIs
APIs công khai cho frontend.

#### Public Routes
- `GET /api/admin/v1/public/marketing/ads-banners/position/:position` - Lấy banner theo vị trí

## Cách sử dụng

### 1. Xác thực
Các API protected cần header Authorization:
```
Authorization: Bearer {{access_token}}
```

### 2. Tham số đường dẫn
- `:id` - ID của ads position hoặc ads banner
- `:position` - Tên vị trí quảng cáo (ví dụ: header_banner, sidebar_banner)

### 3. Biến môi trường Bruno
- `{{api_url}}` - URL base của API
- `{{access_token}}` - Token xác thực
- `{{position_name}}` - Tên vị trí quảng cáo

## Ví dụ Request Body

### Tạo Ads Position
```json
{
  "name": "Header Banner Position",
  "code": "header_banner",
  "description": "Banner position at the top of the page",
  "status": "active"
}
```

### Tạo Ads Banner
```json
{
  "title": "Summer Sale Banner",
  "description": "Banner for summer sale campaign",
  "image_url": "https://example.com/banner.jpg",
  "link_url": "https://example.com/sale",
  "position": "header_banner",
  "status": "active",
  "start_date": "2025-07-01T00:00:00Z",
  "end_date": "2025-07-31T23:59:59Z"
}
```

### Cập nhật Banner Status
```json
{
  "status": "active"
}
```

## Phân quyền (RBAC)
Các API protected sử dụng hệ thống phân quyền:
- `marketing.ads_positions.read` - Đọc vị trí quảng cáo
- `marketing.ads_positions.create` - Tạo vị trí quảng cáo
- `marketing.ads_positions.update` - Cập nhật vị trí quảng cáo
- `marketing.ads_positions.delete` - Xóa vị trí quảng cáo
- `marketing.ads_banners.read` - Đọc banner
- `marketing.ads_banners.create` - Tạo banner
- `marketing.ads_banners.update` - Cập nhật banner
- `marketing.ads_banners.delete` - Xóa banner

## Lưu ý
- Tất cả API trả về JSON với format chuẩn
- Các API public không cần xác thực
- Các API protected cần token hợp lệ
- Sử dụng PATCH cho việc cập nhật trạng thái banner
