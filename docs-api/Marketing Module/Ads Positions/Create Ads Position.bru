meta {
  name: Create Ads Position
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Header Banner Position {{$timestamp}}",
    "code": "header_banner_{{$timestamp}}",
    "description": "Banner position at the top of the page - created via Bruno test",
    "status": "active"
  }
}

script:pre-request {
  // Generate timestamp for unique data
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract created position details
    const positionId = responseJson.data.id;
    const positionName = responseJson.data.name;
    const positionCode = responseJson.data.code;
    
    // Save to environment variables
    bru.setEnvVar("created_ads_position_id", positionId);
    bru.setEnvVar("created_ads_position_name", positionName);
    bru.setEnvVar("created_ads_position_code", positionCode);
    
    console.log("Ads position created successfully:");
    console.log(`Position ID: ${positionId}`);
    console.log(`Name: ${positionName}`);
    console.log(`Code: ${positionCode}`);
  } else {
    console.log("Failed to create ads position");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
