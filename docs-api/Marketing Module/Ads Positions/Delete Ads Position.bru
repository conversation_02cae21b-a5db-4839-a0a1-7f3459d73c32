meta {
  name: Delete Ads Position
  type: http
  seq: 6
}

delete {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions/{{created_ads_position_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Ads position deleted successfully");
    
    // Clear the environment variable
    bru.setEnvVar("created_ads_position_id", "");
  } else {
    console.log("Failed to delete ads position");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
