meta {
  name: List Ads Positions
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions?cursor=&limit=10&status=active&search=&sort_by=created_at&sort_desc=true
  body: none
  auth: inherit
}

params:query {
  cursor: 
  limit: 10
  status: active
  search: 
  sort_by: created_at
  sort_desc: true
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Ads positions listed successfully");
    console.log("Has more:", responseJson.data.meta.has_more);
    console.log("Next cursor:", responseJson.data.meta.next_cursor);
    
    // Save first position ID and next cursor for further tests
    if (responseJson.data.data && responseJson.data.data.length > 0) {
      const firstPosition = responseJson.data.data[0];
      bru.setEnvVar("ads_position_id", firstPosition.id);
      console.log("Saved position ID:", firstPosition.id);
    }
    
    // Save next cursor for pagination
    if (responseJson.data.meta.next_cursor) {
      bru.setEnvVar("ads_position_next_cursor", responseJson.data.meta.next_cursor);
      console.log("Saved next cursor:", responseJson.data.meta.next_cursor);
    }
  } else {
    console.log("Failed to list ads positions");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
