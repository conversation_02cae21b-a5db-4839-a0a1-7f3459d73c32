meta {
  name: Get Ads Position
  type: http
  seq: 4
}

get {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions/{{created_ads_position_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Ads position retrieved successfully:");
    console.log(`Position ID: ${responseJson.data.id}`);
    console.log(`Name: ${responseJson.data.name}`);
    console.log(`Code: ${responseJson.data.code}`);
    console.log(`Status: ${responseJson.data.status}`);
  } else {
    console.log("Failed to retrieve ads position");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
