meta {
  name: Update Ads Position
  type: http
  seq: 5
}

put {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions/{{created_ads_position_id}}
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Updated Header Banner Position {{$timestamp}}",
    "description": "Updated banner position description - modified via Bruno test",
    "status": "active"
  }
}

script:pre-request {
  // Generate timestamp for unique data
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Ads position updated successfully:");
    console.log(`Position ID: ${responseJson.data.id}`);
    console.log(`Updated Name: ${responseJson.data.name}`);
    console.log(`Updated Description: ${responseJson.data.description}`);
  } else {
    console.log("Failed to update ads position");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
