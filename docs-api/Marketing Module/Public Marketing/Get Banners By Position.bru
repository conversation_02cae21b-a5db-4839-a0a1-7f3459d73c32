meta {
  name: Get Banners By Position
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/public/marketing/ads-banners/position/{{position_name}}
  body: none
  auth: none
}

params:path {
  position_name: header_banner
}

script:pre-request {
  // You can change the position name here
  // Available positions: header_banner, sidebar_banner, footer_banner, etc.
  const positionName = bru.getEnvVar("position_name") || "header_banner";
  bru.setVar("position_name", positionName);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const banners = responseJson.data;
    
    console.log("Banners retrieved successfully:");
    console.log(`Total banners: ${banners.length}`);
    
    if (banners.length > 0) {
      // Save first banner details for potential use
      const firstBanner = banners[0];
      bru.setEnvVar("public_banner_id", firstBanner.id);
      bru.setEnvVar("public_banner_title", firstBanner.title);
      
      console.log("First banner details:");
      console.log(`ID: ${firstBanner.id}`);
      console.log(`Title: ${firstBanner.title}`);
      console.log(`Position: ${firstBanner.position}`);
    }
  } else {
    console.log("Failed to retrieve banners");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
