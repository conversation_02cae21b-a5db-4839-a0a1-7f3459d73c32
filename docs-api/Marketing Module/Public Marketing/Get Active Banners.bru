meta {
  name: Get Active Banners
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/public/marketing/ads-banners/active?position={{position_name}}&platform={{platform_name}}
  body: none
  auth: none
}

params:query {
  position: {{position_name}}
  platform: {{platform_name}}
}

params:path {
  position_name: header
  platform_name: web
}

script:pre-request {
  // You can change the position and platform here
  const positionName = bru.getEnvVar("position_name") || "header";
  const platformName = bru.getEnvVar("platform_name") || "web";
  bru.setVar("position_name", positionName);
  bru.setVar("platform_name", platformName);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("✅ Get Active Banners API successful");
    console.log("📊 Number of banners:", responseJson.data.length);
    
    // Log banner details
    responseJson.data.forEach((banner, index) => {
      console.log(`📋 Banner ${index + 1}:`, {
        id: banner.id,
        name: banner.name,
        type: banner.type,
        position: banner.position,
        platform: banner.platform,
        status: banner.status
      });
    });
  } else {
    console.log("❌ Get Active Banners API failed");
    console.log("📄 Response:", responseJson);
  }
}