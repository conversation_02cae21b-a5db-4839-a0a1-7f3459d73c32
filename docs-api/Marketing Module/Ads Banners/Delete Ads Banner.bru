meta {
  name: Delete Ads Banner
  type: http
  seq: 7
}

delete {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{created_ads_banner_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Ads banner deleted successfully");
    
    // Clear the environment variable
    bru.setEnvVar("created_ads_banner_id", "");
  } else {
    console.log("Failed to delete ads banner");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
