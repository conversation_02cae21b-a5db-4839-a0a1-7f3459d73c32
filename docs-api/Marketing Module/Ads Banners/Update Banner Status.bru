meta {
  name: Update Banner Status
  type: http
  seq: 6
}

patch {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{created_ads_banner_id}}/status
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "status": "inactive"
  }
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Banner status updated successfully:");
    console.log(`Banner ID: ${responseJson.data.id}`);
    console.log(`New Status: ${responseJson.data.status}`);
  } else {
    console.log("Failed to update banner status");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
