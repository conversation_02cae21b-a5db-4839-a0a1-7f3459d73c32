meta {
  name: Get Ads Banner
  type: http
  seq: 4
}

get {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{created_ads_banner_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Ads banner retrieved successfully:");
    console.log(`Banner ID: ${responseJson.data.id}`);
    console.log(`Name: ${responseJson.data.name}`);
    console.log(`Type: ${responseJson.data.type}`);
    console.log(`Position: ${responseJson.data.position}`);
    console.log(`Status: ${responseJson.data.status}`);
    console.log(`Platform: ${responseJson.data.platform}`);
  } else {
    console.log("Failed to retrieve ads banner");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
