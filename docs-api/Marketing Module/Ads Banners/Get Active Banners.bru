meta {
  name: Get Active Banners
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/active
  body: none
  auth: none
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Active banners retrieved successfully");
    console.log("Active banners:", responseJson.data);
  } else {
    console.log("Failed to retrieve active banners");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
