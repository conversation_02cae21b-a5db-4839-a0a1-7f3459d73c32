meta {
  name: List Ads Banners
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners?cursor=&limit=10&status=active&platform=web&position=&type=&search=&sort_by=sort_order&sort_desc=false
  body: none
  auth: inherit
}

params:query {
  cursor: 
  limit: 10
  status: active
  platform: web
  position: 
  type: 
  search: 
  sort_by: sort_order
  sort_desc: false
}

headers {
  Authorization: Bearer {{access_token}}
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("Ads banners listed successfully");
    console.log("Has more:", responseJson.data.meta.has_more);
    console.log("Next cursor:", responseJson.data.meta.next_cursor);
    
    // Save first banner ID and next cursor for further tests
    if (responseJson.data.data && responseJson.data.data.length > 0) {
      const firstBanner = responseJson.data.data[0];
      bru.setEnvVar("ads_banner_id", firstBanner.id);
      console.log("Saved banner ID:", firstBanner.id);
    }
    
    // Save next cursor for pagination
    if (responseJson.data.meta.next_cursor) {
      bru.setEnvVar("ads_banner_next_cursor", responseJson.data.meta.next_cursor);
      console.log("Saved next cursor:", responseJson.data.meta.next_cursor);
    }
  } else {
    console.log("Failed to list ads banners");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
