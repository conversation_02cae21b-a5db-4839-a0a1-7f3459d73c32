meta {
  name: Create Ads Banner
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Summer Sale Banner {{$timestamp}}",
    "type": "image",
    "position": "header",
    "image": "/uploads/banner-{{$timestamp}}.jpg",
    "link": "https://example.com/summer-sale",
    "content": "<h1>Summer Sale 50% Off</h1><p>Limited time offer - created via Bruno test</p>",
    "open_in_new_tab": true,
    "platform": "web",
    "from_date": "2024-06-01T00:00:00Z",
    "to_date": "2024-08-31T23:59:59Z",
    "sort_order": 1,
    "status": "pending"
  }
}

script:pre-request {
  // Generate timestamp for unique data
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract created banner details
    const bannerId = responseJson.data.id;
    const bannerName = responseJson.data.name;
    const bannerPosition = responseJson.data.position;
    
    // Save to environment variables
    bru.setEnvVar("created_ads_banner_id", bannerId);
    bru.setEnvVar("created_ads_banner_name", bannerName);
    bru.setEnvVar("created_ads_banner_position", bannerPosition);
    
    console.log("Ads banner created successfully:");
    console.log(`Banner ID: ${bannerId}`);
    console.log(`Name: ${bannerName}`);
    console.log(`Position: ${bannerPosition}`);
  } else {
    console.log("Failed to create ads banner");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
