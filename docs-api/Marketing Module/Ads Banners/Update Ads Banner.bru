meta {
  name: Update Ads Banner
  type: http
  seq: 5
}

put {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{created_ads_banner_id}}
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Updated Winter Sale Banner {{$timestamp}}",
    "type": "video",
    "position": "sidebar",
    "image": "/uploads/updated-banner-{{$timestamp}}.jpg",
    "link": "https://example.com/winter-sale",
    "content": "<h1>Winter Sale 30% Off</h1><p>Updated content - modified via Bruno test</p>",
    "open_in_new_tab": false,
    "platform": "mobile",
    "from_date": "2024-12-01T00:00:00Z",
    "to_date": "2024-12-31T23:59:59Z",
    "sort_order": 2,
    "status": "active"
  }
}

script:pre-request {
  // Generate timestamp for unique data
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    console.log("Ads banner updated successfully:");
    console.log(`Banner ID: ${responseJson.data.id}`);
    console.log(`Updated Name: ${responseJson.data.name}`);
    console.log(`Updated Type: ${responseJson.data.type}`);
    console.log(`Updated Position: ${responseJson.data.position}`);
    console.log(`Updated Status: ${responseJson.data.status}`);
  } else {
    console.log("Failed to update ads banner");
    console.log("Error:", responseJson.status ? responseJson.status.message : "Unknown error");
  }
}
