meta {
  name: Cleanup Workflow Data
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{workflow_banner_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:pre-request {
  // Check if we have data to cleanup
  const bannerId = bru.getEnvVar("workflow_banner_id");
  const positionId = bru.getEnvVar("workflow_position_id");
  
  if (!bannerId || !positionId) {
    console.log("⚠️  No workflow data found to cleanup");
    console.log("This is normal if cleanup was already done or workflow wasn't completed");
    return;
  }
  
  console.log("Step 5: Cleaning up workflow test data...");
  console.log(`Deleting banner ID: ${bannerId}`);
  console.log(`Will delete position ID: ${positionId} in next step`);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if banner deletion was successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("✅ Banner deleted successfully");
    
    // Now delete the position
    const positionId = bru.getEnvVar("workflow_position_id");
    if (positionId) {
      // We'll need to make another request to delete the position
      // For now, just log the instruction
      console.log("");
      console.log("⚠️  Manual step required:");
      console.log(`Please run the 'Delete Ads Position' API with ID: ${positionId}`);
      console.log("Or run the 'Final Cleanup Position' request in this collection");
    }
    
    // Clear workflow environment variables
    bru.setEnvVar("workflow_banner_id", "");
    bru.setEnvVar("workflow_position_id", "");
    bru.setEnvVar("workflow_position_name", "");
    bru.setEnvVar("workflow_position_code", "");
    bru.setEnvVar("workflow_banner_title", "");
    
    console.log("");
    console.log("🎉 WORKFLOW TEST COMPLETED!");
    console.log("All test data has been cleaned up.");
    
  } else {
    console.log("❌ Failed to delete banner");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
