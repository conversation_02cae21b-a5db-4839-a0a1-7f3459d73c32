# Marketing Module Workflow Testing

## <PERSON><PERSON> tả
Collection này chứa các API test workflow hoàn chỉnh cho Marketing Module, bao gồm việ<PERSON> tạo, cậ<PERSON> nh<PERSON>t, kiểm tra và xóa dữ liệu test.

## Cách sử dụng

### Workflow tự động (Khuyến nghị)
Chạy các API theo thứ tự:

1. **01 - Create Position** - Tạo vị trí quảng cáo test
2. **02 - Create Banner** - Tạo banner cho vị trí vừa tạo
3. **03 - Test Public API** - Kiểm tra API công khai
4. **04 - Test Status Update** - Test cập nhật trạng thái banner
5. **05 - Cleanup Banner** - Xóa banner test
6. **06 - Final Cleanup Position** - Xóa vị trí test

### Lưu ý quan trọng
- **Phải chạy theo thứ tự** - Mỗi bước phụ thuộc vào bước trước
- **Cần token xác thực** - <PERSON><PERSON><PERSON> b<PERSON> `{{access_token}}` có giá trị hợp lệ
- **Tự động cleanup** - Workflow sẽ tự động xóa dữ liệu test
- **Console logs** - Kiểm tra console để theo dõi tiến trình

### Biến môi trường được sử dụng
- `{{api_url}}` - Base URL của API
- `{{access_token}}` - Token xác thực
- `{{workflow_position_id}}` - ID vị trí test (tự động tạo)
- `{{workflow_position_code}}` - Code vị trí test (tự động tạo)
- `{{workflow_banner_id}}` - ID banner test (tự động tạo)

### Kết quả mong đợi
Workflow sẽ test các tính năng sau:
- ✅ Tạo ads position mới
- ✅ Tạo ads banner cho position
- ✅ Truy cập public API để lấy banner theo position
- ✅ Cập nhật trạng thái banner
- ✅ Xóa dữ liệu test

### Troubleshooting
- **Token hết hạn**: Chạy lại API login để lấy token mới
- **Permission denied**: Đảm bảo user có quyền marketing
- **API không phản hồi**: Kiểm tra `{{api_url}}` và server status
- **Workflow bị gián đoạn**: Chạy cleanup manual để xóa dữ liệu test

## Test Cases được cover

### 1. CRUD Operations
- Create ads position ✅
- Read ads position ✅
- Update ads position ✅
- Delete ads position ✅
- Create ads banner ✅
- Read ads banner ✅
- Update ads banner ✅
- Delete ads banner ✅

### 2. Public API Access
- Get active positions ✅
- Get active banners ✅
- Get banners by position ✅

### 3. Status Management
- Update banner status ✅
- Filter by status in public API ✅

### 4. Data Integrity
- Position-Banner relationship ✅
- Cleanup test data ✅
