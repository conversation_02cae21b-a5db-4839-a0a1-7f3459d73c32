meta {
  name: Final Cleanup Position
  type: http
  seq: 6
}

delete {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions/{{workflow_position_id}}
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

script:pre-request {
  // Check if we have position to cleanup
  const positionId = bru.getEnvVar("workflow_position_id");
  
  if (!positionId) {
    console.log("⚠️  No position ID found for cleanup");
    console.log("This is normal if cleanup was already done");
    return;
  }
  
  console.log("Final Step: Cleaning up ads position...");
  console.log(`Deleting position ID: ${positionId}`);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if position deletion was successful
  if (responseJson.status && responseJson.status.success === true) {
    console.log("✅ Position deleted successfully");
    
    // Clear remaining workflow environment variables
    bru.setEnvVar("workflow_position_id", "");
    bru.setEnvVar("workflow_position_name", "");
    bru.setEnvVar("workflow_position_code", "");
    
    console.log("");
    console.log("🎉 COMPLETE CLEANUP FINISHED!");
    console.log("All workflow test data has been removed.");
    console.log("");
    console.log("=== MARKETING WORKFLOW TEST COMPLETED ===");
    
  } else {
    console.log("❌ Failed to delete position");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
    console.log("");
    console.log("You may need to manually delete the position from the admin interface");
  }
}
