meta {
  name: Complete Marketing Workflow
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/marketing/ads-positions
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Test Workflow Position {{$timestamp}}",
    "code": "test_workflow_{{$timestamp}}",
    "description": "Test position for complete workflow - created via <PERSON>",
    "status": "active"
  }
}

script:pre-request {
  // Generate timestamp for unique data
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
  
  console.log("=== MARKETING WORKFLOW TEST STARTED ===");
  console.log("Step 1: Creating ads position...");
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract created position details
    const positionId = responseJson.data.id;
    const positionName = responseJson.data.name;
    const positionCode = responseJson.data.code;
    
    // Save to environment variables for next steps
    bru.setEnvVar("workflow_position_id", positionId);
    bru.setEnvVar("workflow_position_name", positionName);
    bru.setEnvVar("workflow_position_code", positionCode);
    
    console.log("✅ Step 1 COMPLETED: Ads position created successfully");
    console.log(`Position ID: ${positionId}`);
    console.log(`Position Name: ${positionName}`);
    console.log(`Position Code: ${positionCode}`);
    console.log("");
    console.log("Next steps:");
    console.log("- Run 'Create Banner for Position' to create banner");
    console.log("- Run 'Test Public API' to test public access");
    console.log("- Run 'Cleanup Workflow Data' to clean up test data");
    
  } else {
    console.log("❌ Step 1 FAILED: Could not create ads position");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
