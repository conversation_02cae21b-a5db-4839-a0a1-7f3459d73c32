meta {
  name: Create Banner for Position
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "title": "Workflow Test Banner {{$timestamp}}",
    "description": "Test banner for workflow - created via <PERSON>",
    "image_url": "https://picsum.photos/800/200?random={{$timestamp}}",
    "link_url": "https://example.com/test-campaign",
    "position": "{{workflow_position_code}}",
    "status": "active",
    "start_date": "{{$isoTimestamp}}",
    "end_date": "2025-12-31T23:59:59Z",
    "sort_order": 1
  }
}

script:pre-request {
  // Check if we have position from previous step
  const positionCode = bru.getEnvVar("workflow_position_code");
  if (!positionCode) {
    console.log("❌ ERROR: No position code found!");
    console.log("Please run 'Create Position' first");
    return;
  }
  
  // Generate timestamp
  const timestamp = Date.now();
  bru.setEnvVar("timestamp", timestamp);
  
  console.log("Step 2: Creating banner for position...");
  console.log(`Using position code: ${positionCode}`);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    // Extract created banner details
    const bannerId = responseJson.data.id;
    const bannerTitle = responseJson.data.title;
    const bannerPosition = responseJson.data.position;
    
    // Save to environment variables
    bru.setEnvVar("workflow_banner_id", bannerId);
    bru.setEnvVar("workflow_banner_title", bannerTitle);
    
    console.log("✅ Step 2 COMPLETED: Banner created successfully");
    console.log(`Banner ID: ${bannerId}`);
    console.log(`Banner Title: ${bannerTitle}`);
    console.log(`Banner Position: ${bannerPosition}`);
    console.log("");
    console.log("Next steps:");
    console.log("- Run 'Test Public API' to verify public access");
    console.log("- Run 'Update Banner Status' to test status update");
    console.log("- Run 'Cleanup Workflow Data' when done");
    
  } else {
    console.log("❌ Step 2 FAILED: Could not create banner");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
