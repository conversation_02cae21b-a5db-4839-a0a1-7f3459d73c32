meta {
  name: Test Banner Status Update
  type: http
  seq: 4
}

patch {
  url: {{api_url}}/api/admin/v1/marketing/ads-banners/{{workflow_banner_id}}/status
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "status": "inactive"
  }
}

script:pre-request {
  // Check if we have banner from previous steps
  const bannerId = bru.getEnvVar("workflow_banner_id");
  if (!bannerId) {
    console.log("❌ ERROR: No banner ID found!");
    console.log("Please run workflow steps 1 and 2 first");
    return;
  }
  
  console.log("Step 4: Testing banner status update...");
  console.log(`Updating status for banner ID: ${bannerId}`);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const updatedBanner = responseJson.data;
    
    console.log("✅ Step 4 COMPLETED: Banner status updated successfully");
    console.log(`Banner ID: ${updatedBanner.id}`);
    console.log(`New Status: ${updatedBanner.status}`);
    console.log(`Updated At: ${updatedBanner.updated_at}`);
    
    console.log("");
    console.log("Workflow almost complete!");
    console.log("- You can now test the public API again to see the banner is no longer returned");
    console.log("- Run 'Cleanup Workflow Data' to clean up test data");
    
  } else {
    console.log("❌ Step 4 FAILED: Could not update banner status");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
