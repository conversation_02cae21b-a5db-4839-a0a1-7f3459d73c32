meta {
  name: Test Public API Access
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/public/marketing/ads-banners/position/{{workflow_position_code}}
  body: none
  auth: none
}

script:pre-request {
  // Check if we have position from previous steps
  const positionCode = bru.getEnvVar("workflow_position_code");
  if (!positionCode) {
    console.log("❌ ERROR: No position code found!");
    console.log("Please run workflow steps 1 and 2 first");
    return;
  }
  
  console.log("Step 3: Testing public API access...");
  console.log(`Looking for banners in position: ${positionCode}`);
}

script:post-response {
  // Parse response
  const response = res;
  const responseJson = response.body;
  
  // Check if response is successful
  if (responseJson.status && responseJson.status.success === true && responseJson.data) {
    const banners = responseJson.data;
    const workflowBannerId = bru.getEnvVar("workflow_banner_id");
    
    console.log("✅ Step 3 COMPLETED: Public API access successful");
    console.log(`Total banners found: ${banners.length}`);
    
    // Check if our workflow banner is in the results
    const ourBanner = banners.find(banner => banner.id == workflowBannerId);
    if (ourBanner) {
      console.log("✅ Workflow banner found in public API results");
      console.log(`Banner Title: ${ourBanner.title}`);
      console.log(`Banner Status: ${ourBanner.status}`);
    } else {
      console.log("⚠️  Workflow banner not found in results (might be normal if status is inactive)");
    }
    
    console.log("");
    console.log("Next steps:");
    console.log("- Run 'Test Status Update' to test banner status changes");
    console.log("- Run 'Cleanup Workflow Data' to clean up test data");
    
  } else {
    console.log("❌ Step 3 FAILED: Public API access failed");
    if (responseJson.status && responseJson.status.message) {
      console.log(`Error: ${responseJson.status.message}`);
    }
  }
}
