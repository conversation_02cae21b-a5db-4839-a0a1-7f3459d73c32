meta {
  name: Get Media File
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/admin/v1/media/1
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Media File by ID
  
  L<PERSON>y thông tin chi tiết của một file media theo ID.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "filename": "sample.jpg",
      "original_filename": "my-image.jpg",
      "title": "Sample Image",
      "alt_text": "Sample image description",
      "description": "Detailed description of the file",
      "mime_type": "image/jpeg",
      "file_size": 1024576,
      "file_extension": "jpg",
      "storage_path": "/uploads/2024/01/sample.jpg",
      "url": "https://example.com/uploads/2024/01/sample.jpg",
      "thumbnail_url": "https://example.com/uploads/2024/01/thumbs/sample.jpg",
      "is_public": true,
      "folder": {
        "id": 1,
        "name": "Images",
        "path": "/Images",
        "description": "Image files folder"
      },
      "metadata": {
        "width": 1920,
        "height": 1080,
        "duration": null,
        "bit_rate": null,
        "frame_rate": null,
        "color_profile": "sRGB",
        "exif": {
          "camera": "Canon EOS 5D Mark IV",
          "lens": "EF24-70mm f/2.8L II USM",
          "iso": 100,
          "aperture": "f/8.0",
          "shutter_speed": "1/125",
          "focal_length": "50mm",
          "taken_at": "2024-01-01T10:30:00Z"
        }
      },
      "stats": {
        "downloads": 25,
        "views": 150,
        "last_accessed": "2024-01-01T12:00:00Z"
      },
      "usage": [
        {
          "type": "blog_post",
          "id": 5,
          "title": "Sample Blog Post",
          "url": "/blog/sample-post"
        },
        {
          "type": "page",
          "id": 2,
          "title": "About Us",
          "url": "/about"
        }
      ],
      "versions": [
        {
          "id": 1,
          "size": "original",
          "width": 1920,
          "height": 1080,
          "url": "https://example.com/uploads/2024/01/sample.jpg"
        },
        {
          "id": 2,
          "size": "large",
          "width": 1024,
          "height": 576,
          "url": "https://example.com/uploads/2024/01/sample-large.jpg"
        },
        {
          "id": 3,
          "size": "medium",
          "width": 512,
          "height": 288,
          "url": "https://example.com/uploads/2024/01/sample-medium.jpg"
        },
        {
          "id": 4,
          "size": "thumbnail",
          "width": 150,
          "height": 150,
          "url": "https://example.com/uploads/2024/01/sample-thumb.jpg"
        }
      ],
      "created_by": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `media.read`
  
  ## Error Responses
  - 404: Media file not found
  - 403: Forbidden - Không có quyền xem file này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has media data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("id");
    expect(data.data).to.have.property("filename");
    expect(data.data).to.have.property("url");
  });
}
