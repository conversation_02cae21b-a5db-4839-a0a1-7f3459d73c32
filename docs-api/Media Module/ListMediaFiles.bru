meta {
  name: List Media Files
  type: http
  seq: 2
}

get {
  url: {{base_url}}/api/admin/v1/media
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 20
  search: image
  folder_id: 1
  mime_type: image/jpeg
  sort: created_at
  order: desc
  from_date: 2024-01-01
  to_date: 2024-12-31
}

docs {
  # List Media Files
  
  Lấy danh sách các file media với các tùy chọn lọc và phân trang.
  
  ## Query Parameters
  - `page`: <PERSON><PERSON> trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 20, max: 100)
  - `search`: T<PERSON><PERSON> kiếm theo filename, title, alt_text
  - `folder_id`: Lọc theo folder ID
  - `mime_type`: Lọ<PERSON> theo MIME type (image/jpeg, video/mp4, etc.)
  - `file_type`: <PERSON><PERSON><PERSON> theo lo<PERSON> file (image, video, document, audio)
  - `sort`: <PERSON><PERSON><PERSON> xếp theo (created_at, updated_at, filename, file_size)
  - `order`: <PERSON><PERSON><PERSON> tự (asc, desc)
  - `from_date`: <PERSON><PERSON><PERSON> t<PERSON> ng<PERSON> (YYYY-MM-DD)
  - `to_date`: Lọc đến ngày (YYYY-MM-DD)
  - `is_public`: Lọc file public/private (true/false)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "filename": "sample.jpg",
        "original_filename": "my-image.jpg",
        "title": "Sample Image",
        "alt_text": "Sample image description",
        "description": "File description",
        "mime_type": "image/jpeg",
        "file_size": 1024576,
        "file_extension": "jpg",
        "url": "https://example.com/uploads/sample.jpg",
        "thumbnail_url": "https://example.com/uploads/thumbs/sample.jpg",
        "is_public": true,
        "folder": {
          "id": 1,
          "name": "Images",
          "path": "/Images"
        },
        "metadata": {
          "width": 1920,
          "height": 1080,
          "duration": null
        },
        "stats": {
          "downloads": 25,
          "views": 150
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "last_page": 5,
      "has_more": true,
      "next_cursor": "eyJpZCI6MjB9"
    },
    "summary": {
      "total_files": 100,
      "total_size": 52428800,
      "file_types": {
        "images": 60,
        "videos": 25,
        "documents": 15
      }
    }
  }
  ```
  
  ## Required Permission
  - `media.list`
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has media list", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.meta).to.have.property("current_page");
  });
}
