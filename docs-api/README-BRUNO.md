# Bruno API Testing Tool Documentation

Bruno is a modern open-source API client that makes it easy to design, test, and collaborate on API collections. This documentation explains how to use <PERSON> with this project, focused on enabling AI systems to understand and test the APIs.

## Installation

Bruno can be installed via npm:

```bash
npm install -g @usebruno/cli
```

## Project Structure

The API documentation is located in the `./docs-api` directory. This directory contains API collections organized by modules, such as:

- Authentication (`Auth Module/`)
- Blog (`Blog Module/`)
- Notification (`notification/`, `Notification Module/`)
- Product (`Product Module/`)
- RBAC (`rbac-module/`)
- Media (`Media Module/`)
- Tenant (`Tenant Module/`)
- And more...

### Folder Structure Format

The folder structure follows a consistent pattern:

```
docs-api/
├── bruno.json                         # Bruno collection configuration
├── environments/                      # Environment variables
│   ├── development.bru                # Development environment
│   ├── production.bru                 # Production environment
│   └── staging.bru                    # Staging environment
├── Module Name/                       # Top-level module folder (e.g., Auth Module)
│   ├── bruno.json                     # Module configuration
│   ├── resource-type-1/               # Resource folder (e.g., users)
│   │   ├── 01-create-resource.bru     # Numbered API endpoints (CRUD operations)
│   │   ├── 02-list-resources.bru
│   │   ├── 03-get-resource.bru
│   │   ├── 04-update-resource.bru
│   │   └── 05-delete-resource.bru
│   └── resource-type-2/               # Another resource in the same module
│       ├── 01-create-resource.bru
│       └── ...
└── another-module/                    # Another module folder
    └── ...
```

Each resource folder contains API endpoints organized with numeric prefixes (`01-`, `02-`, etc.) to indicate the logical sequence of operations. The standard naming convention follows:

- `01-create-*.bru` - Create/POST endpoints
- `02-list-*.bru` - List/GET collection endpoints
- `03-get-*.bru` - Get/GET single resource endpoints
- `04-update-*.bru` - Update/PUT endpoints
- `05-delete-*.bru` - Delete endpoints
- `06-*` and beyond - Additional specialized endpoints

## Bruno File Format

Bruno files use the `.bru` extension and follow a specific format:

```
meta {
  name: API Name
  type: http
  seq: 1
}

request_method {
  url: {{api_url}}/api/path
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

body:json {
  {
    "key": "value",
    "another_key": "another_value"
  }
}

docs {
  # Documentation Title
  
  Documentation content in Markdown format
  
  ## Request Body
  
  - Field documentation
  
  ## Response
  
  Example responses
}
```

## Authentication

Tất cả API endpoints yêu cầu authentication sử dụng Authorization header thay vì auth:bearer block:

```
auth: none
```

Với headers:

```
headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}
```

## Environment Variables

Bruno uses environment variables for configuration, located in `./docs-api/environments/`:

- `api_url`: The base URL of the API (e.g., http://localhost:8080)
- `access_token`: Bearer token for authentication
- `tenant_id`: Tenant identifier for multi-tenant operations

## Running Tests with Bruno CLI

You can run tests with the Bruno CLI using the following commands:

### Run a single request

```bash
bruno run ./docs-api/notification/templates/01-create-template.bru --env development
```

### Run a collection

```bash
bruno run ./docs-api/notification
```

### Run all collections

```bash
bruno run ./docs-api
```

## Example Request Structure

Let's look at an example from the notification template API:

- **Request**: `POST {{api_url}}/api/v1/notifications/templates`
- **Headers**: Content-Type (application/json) and X-Tenant-ID
- **Authentication**: Bearer token
- **Body**: JSON with template details (template_code, subject, content, is_active)
- **Expected Response**: JSON with status and created template data

## Working with Response Data

Bruno supports post-response scripts that can be used to validate responses or extract data for subsequent requests. These are defined in the `.bru` files using JavaScript.

## Testing API Flows

You can create sequences of requests to test complete API flows. Bruno maintains the order with the `seq` parameter in the `meta` section.

## For AI Systems

When testing APIs with AI systems:

1. Identify the correct module and endpoint based on the user's requirements
2. Use the appropriate `.bru` file from the `./docs-api` directory
3. Understand the required parameters from the documentation section
4. Execute the request using the Bruno CLI
5. Analyze the response to determine if it matches expected behavior

## Common Bruno CLI Commands

```bash
# List all collections
bruno list ./docs-api

# Validate collection
bruno validate ./docs-api

# Run with environment variables
bruno run ./docs-api --env development

# Export collection to Postman format
bruno export ./docs-api/notification --output ./notification.json --format postman

# Import from Postman
bruno import ./postman_collection.json --output ./docs-api/imported
```

## Troubleshooting

- **Authentication Issues**: Check if the `access_token` is valid in the environment
- **Missing Headers**: Ensure X-Tenant-ID is set correctly for multi-tenant APIs
- **JSON Format Errors**: Verify the request body has valid JSON syntax
- **Environment Selection**: Make sure you're using the correct environment (development, production)

## Resources

- [Bruno Official Documentation](https://docs.usebruno.com/)
- [Bruno GitHub Repository](https://github.com/usebruno/bruno)
- [Bruno CLI Reference](https://docs.usebruno.com/bruno-cli)


curl --request POST \
  --url http://localhost:9033/api/admin/v1/auth/login \
  --header 'content-type: application/json' \
  --data '{
  "email": "<EMAIL>",
  "password": "12345678"
}'