meta {
  name: Get Comments
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}/comments
  query: {
    page: 1,
    limit: 10,
    status: all
  },
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Comments
  
  Retrieves a list of comments for a specific blog post.
  
  ## Path Parameters
  | Parameter | Description                          |
  |-----------|--------------------------------------|
  | post_id   | ID of the post to get comments from  |
  
  ## Query Parameters
  | Parameter   | Type    | Required | Default | Description                                           |
  |-------------|---------|----------|---------|-------------------------------------------------------|
  | page        | integer | No       | 1       | Page number for pagination                            |
  | limit       | integer | No       | 10      | Number of items per page                              |
  | status      | string  | No       | all     | Filter by status (all, approved, pending, spam)       |
  
  ## Response
  Returns a paginated list of comments for the post.
  
  ```json
  {
    "data": [
      {
        "id": 1,
        "post_id": 5,
        "content": "This is a very informative article. Thanks for sharing!",
        "author_name": "Guest User",
        "author_email": "<EMAIL>",
        "author_website": "https://example.com",
        "parent_id": null,
        "status": "approved",
        "created_at": "2024-01-31T08:30:00Z",
        "updated_at": "2024-01-31T09:15:00Z",
        "replies": [
          {
            "id": 2,
            "post_id": 5,
            "content": "Thank you for your feedback!",
            "author_name": "Admin",
            "author_email": "<EMAIL>",
            "author_website": null,
            "parent_id": 1,
            "status": "approved",
            "created_at": "2024-01-31T09:15:00Z",
            "updated_at": "2024-01-31T09:15:00Z"
          }
        ]
      }
    ],
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Comments retrieved successfully",
      "pagination": {
        "total": 5,
        "count": 1,
        "per_page": 10,
        "current_page": 1,
        "total_pages": 1
      }
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain comments array", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('array');
  });
}
