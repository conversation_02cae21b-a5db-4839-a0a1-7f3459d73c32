meta {
  name: Approve Comment
  type: http
  seq: 3
}

put {
  url: {{api_url}}/api/admin/v1/blog/comments/{{comment_id}}/approve
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Approve Comment
  
  Approves a pending comment, making it visible on the blog.
  
  ## Path Parameters
  | Parameter  | Description               |
  |------------|---------------------------|
  | comment_id | ID of the comment to approve |
  
  ## Response
  Returns the updated comment with approved status.
  
  ```json
  {
    "data": {
      "id": 1,
      "post_id": 5,
      "content": "This is a very informative article. Thanks for sharing!",
      "author_name": "Guest User",
      "author_email": "<EMAIL>",
      "author_website": "https://example.com",
      "parent_id": null,
      "status": "approved",
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T09:15:00Z",
      "created_by": null,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Comment approved successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain approved comment", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.status).to.equal("approved");
  });
}
