meta {
  name: Create Comment
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}/comments
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "content": "This is a very informative article. Thanks for sharing!",
    "author_name": "Guest User",
    "author_email": "<EMAIL>",
    "author_website": "https://example.com",
    "parent_id": null
  }
}

docs {
  # Create Comment
  
  Creates a new comment for a specific blog post.
  
  ## Path Parameters
  | Parameter | Description                          |
  |-----------|--------------------------------------|
  | post_id   | ID of the post to add the comment to |
  
  ## Request Body
  | Field          | Type     | Required | Description                                        |
  |----------------|----------|----------|----------------------------------------------------|
  | content        | string   | Yes      | Comment content                                    |
  | author_name    | string   | No       | Name of the commenter (for guest comments)         |
  | author_email   | string   | No       | Email of the commenter (for guest comments)        |
  | author_website | string   | No       | Website of the commenter                           |
  | parent_id      | integer  | No       | ID of parent comment (for reply comments)          |
  
  ## Response
  Returns the created comment with all details including generated ID.
  
  ```json
  {
    "data": {
      "id": 1,
      "post_id": 5,
      "content": "This is a very informative article. Thanks for sharing!",
      "author_name": "Guest User",
      "author_email": "<EMAIL>",
      "author_website": "https://example.com",
      "parent_id": null,
      "status": "pending",
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": null,
      "updated_by": null,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "Comment created successfully"
    }
  }
  ```
}

tests {
  test("Status should be 201 Created", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(201);
  });

  test("Response should contain comment data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.content).to.be.a('string');
    expect(responseJson.data.post_id).to.be.a('number');
  });
}
