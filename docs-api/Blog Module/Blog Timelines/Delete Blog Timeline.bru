meta {
  name: Delete Blog Timeline
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines/1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain success message", function() {
    const body = res.getBody();
    expect(body).to.have.property('message');
  });
}