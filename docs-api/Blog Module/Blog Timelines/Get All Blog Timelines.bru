meta {
  name: Get All Blog Timelines
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines?page=1&limit=10&search=&active=true
  body: none
  auth: bearer
}

params:query {
  page: 1
  limit: 10
  search: 
  active: true
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain pagination data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body).to.have.property('pagination');
    expect(body.pagination).to.have.property('page');
    expect(body.pagination).to.have.property('limit');
    expect(body.pagination).to.have.property('total');
  });
}