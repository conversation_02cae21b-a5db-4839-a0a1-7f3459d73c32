meta {
  name: Get Timeline Posts
  type: http
  seq: 7
}

get {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines/1/posts?page=1&limit=10&sort_by=priority&sort_order=desc
  body: none
  auth: bearer
}

params:query {
  page: 1
  limit: 10
  sort_by: priority
  sort_order: desc
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain timeline posts data", function() {
    const body = res.getBody();
    expect(body).to.have.property('data');
    expect(body).to.have.property('pagination');
    expect(body.pagination).to.have.property('page');
    expect(body.pagination).to.have.property('limit');
    expect(body.pagination).to.have.property('total');
  });
}