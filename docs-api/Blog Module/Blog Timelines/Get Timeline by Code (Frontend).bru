meta {
  name: Get Timeline by Code (Frontend)
  type: http
  seq: 10
}

get {
  url: {{api_url}}/api/v1/blog/blog-timelines/tech-news?page=1&limit=10
  body: none
  auth: none
}

params:query {
  page: 1
  limit: 10
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain timeline data with posts", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('timeline');
    expect(data).to.have.property('posts');
    expect(data).to.have.property('pagination');
    expect(data.timeline).to.have.property('id');
    expect(data.timeline).to.have.property('name');
    expect(data.timeline).to.have.property('code');
    expect(data.posts).to.be.an('array');
  });
}