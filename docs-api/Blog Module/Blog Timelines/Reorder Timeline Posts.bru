meta {
  name: Reorder Timeline Posts
  type: http
  seq: 9
}

put {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines/1/posts/reorder
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "post_orders": [
      {
        "post_id": 1,
        "priority": 100
      },
      {
        "post_id": 2,
        "priority": 90
      },
      {
        "post_id": 3,
        "priority": 80
      }
    ]
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain success message", function() {
    const body = res.getBody();
    expect(body).to.have.property('message');
  });
}