meta {
  name: Update Blog Timeline
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines/1
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Updated Tech News Timeline",
    "code": "updated-tech-news",
    "active": false
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain updated blog timeline data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('name');
    expect(data).to.have.property('code');
    expect(data).to.have.property('active');
    expect(data.name).to.equal("Updated Tech News Timeline");
  });
}