meta {
  name: Create Blog Timeline
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/blog-timelines
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Tech News Timeline",
    "code": "tech-news",
    "active": true
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog timeline data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('name');
    expect(data).to.have.property('code');
    expect(data).to.have.property('active');
  });
}