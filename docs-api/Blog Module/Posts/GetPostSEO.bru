meta {
  name: Get Post SEO
  type: http
  seq: 8
}

get {
  url: {{base_url}}/api/admin/v1/blog/posts/1/seo
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Post SEO Metadata
  
  Lấy thông tin SEO metadata của một bài viết.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "post_id": 1,
      "meta_title": "SEO Title for Post",
      "meta_description": "SEO Description for Post",
      "meta_keywords": "keyword1, keyword2, keyword3",
      "og_title": "Open Graph Title",
      "og_description": "Open Graph Description", 
      "og_image": "https://example.com/og-image.jpg",
      "og_type": "article",
      "twitter_title": "Twitter Card Title",
      "twitter_description": "Twitter Card Description",
      "twitter_image": "https://example.com/twitter-image.jpg",
      "twitter_card": "summary_large_image",
      "canonical_url": "https://example.com/posts/sample-post",
      "robots": "index,follow",
      "schema_markup": {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "Article Headline",
        "description": "Article Description",
        "author": {
          "@type": "Person",
          "name": "John Doe"
        },
        "datePublished": "2024-01-01T00:00:00Z",
        "dateModified": "2024-01-01T00:00:00Z"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.read`
  
  ## Error Responses
  - 404: Post not found
  - 403: Forbidden - Không có quyền xem post này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has SEO data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("post_id");
    expect(data.data).to.have.property("meta_title");
    expect(data.data).to.have.property("meta_description");
  });
}
