meta {
  name: List Posts
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/blog/posts
  query: {
    page: 1,
    limit: 10,
    status: all,
    search: "",
    sort: created_at,
    order: desc,
    author_id: "",
    category_id: "",
    tag_id: ""
  },
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # List Posts
  
  Retrieves a paginated list of blog posts with filtering options.
  
  ## Query Parameters
  | Parameter   | Type    | Required | Default | Description                                       |
  |-------------|---------|----------|---------|---------------------------------------------------|
  | page        | integer | No       | 1       | Page number for pagination                        |
  | limit       | integer | No       | 10      | Number of items per page                          |
  | status      | string  | No       | all     | Filter by status (all, draft, published, pending) |
  | search      | string  | No       | ""      | Search term for title/content                     |
  | sort        | string  | No       | created_at | Sort field (created_at, updated_at, title)     |
  | order       | string  | No       | desc    | Sort order (asc, desc)                           |
  | author_id   | integer | No       | ""      | Filter by author ID                               |
  | category_id | integer | No       | ""      | Filter by category ID                             |
  | tag_id      | integer | No       | ""      | Filter by tag ID                                  |
  
  ## Response
  Returns a paginated list of posts with metadata.
  
  ```json
  {
    "data": [
      {
        "id": 1,
        "title": "Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
        "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react",
        "description": "Bài viết này sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh...",
        "image": "https://example.com/images/go-react-tutorial.jpg",
        "status": "draft",
        "visibility": "public",
        "published_at": "2024-02-15T10:00:00Z",
        "author": {
          "id": 1,
          "name": "John Doe"
        },
        "created_at": "2024-01-31T08:30:00Z",
        "updated_at": "2024-01-31T08:30:00Z"
      },
      {
        "id": 2,
        "title": "Tối ưu hiệu suất ứng dụng Go với các kỹ thuật tiên tiến",
        "slug": "toi-uu-hieu-suat-ung-dung-go-voi-cac-ky-thuat-tien-tien",
        "description": "Khám phá các kỹ thuật tối ưu hiệu suất nâng cao cho ứng dụng Go...",
        "image": "https://example.com/images/go-performance.jpg",
        "status": "published",
        "visibility": "public",
        "published_at": "2024-01-20T14:30:00Z",
        "author": {
          "id": 2,
          "name": "Jane Smith"
        },
        "created_at": "2024-01-19T11:45:00Z",
        "updated_at": "2024-01-20T14:30:00Z"
      }
    ],
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Posts retrieved successfully",
      "pagination": {
        "total": 25,
        "count": 2,
        "per_page": 10,
        "current_page": 1,
        "total_pages": 3,
        "links": {
          "next": "/api/admin/v1/blog/posts?page=2&limit=10"
        }
      }
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain pagination data", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta.pagination).to.be.an('object');
    expect(responseJson.meta.pagination.total).to.be.a('number');
    expect(responseJson.meta.pagination.current_page).to.be.a('number');
  });

  test("Response should contain post list", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('array');
  });
}
