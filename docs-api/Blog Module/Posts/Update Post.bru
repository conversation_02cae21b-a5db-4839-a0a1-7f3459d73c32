meta {
  name: Update Post
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "title": "Updated: Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
    "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react-updated",
    "description": "Bài viết cập nhật sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh sử dụng Go cho backend và React cho frontend, bao gồm authentication, database design và deployment.",
    "content": "<h1>Giới thiệu (Updated)</h1><p>Trong thời đại công nghệ 4.0, việc phát triển ứng dụng web hiện đại đòi hỏi sự kết hợp hoàn hảo gi<PERSON>a backend mạnh mẽ và frontend thân thiện với người dùng...</p><h2>Thiết lập môi trường phát triển</h2><p>Trước khi bắt đầu, chúng ta cần chuẩn bị các công cụ cần thiết...</p>",
    "image": "https://example.com/images/go-react-tutorial-updated.jpg",
    "status": "published",
    "visibility": "public",
    "comment_status": "open",
    "published_at": "2024-02-15T10:00:00Z",
    "category_ids": [1, 3, 5],
    "tag_ids": [5, 8, 12, 15, 20]
  }
}

docs {
  # Update Post
  
  Updates an existing blog post with the specified details.
  
  ## Path Parameters
  | Parameter | Description             |
  |-----------|-------------------------|
  | id        | ID of the post to update |
  
  ## Request Body
  | Field           | Type     | Required | Description                                        |
  |-----------------|----------|----------|----------------------------------------------------|
  | title           | string   | No       | Post title                                         |
  | slug            | string   | No       | SEO-friendly URL slug                              |
  | description     | string   | No       | Brief description or excerpt of the post           |
  | content         | string   | No       | HTML content of the post                           |
  | image           | string   | No       | Featured image URL                                 |
  | status          | string   | No       | Post status (draft, published, pending)            |
  | visibility      | string   | No       | Visibility (public, private, password)             |
  | password        | string   | No       | Password for password-protected posts              |
  | comment_status  | string   | No       | Comment status (open, closed)                      |
  | published_at    | datetime | No       | Publication date and time                          |
  | schedule_at     | datetime | No       | Scheduled publication date and time                |
  | author_id       | integer  | No       | Author ID                                          |
  | category_ids    | array    | No       | Array of category IDs                              |
  | tag_ids         | array    | No       | Array of tag IDs                                   |
  
  ## Response
  Returns the updated post with all details.
  
  ```json
  {
    "data": {
      "id": 1,
      "title": "Updated: Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
      "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react-updated",
      "description": "Bài viết cập nhật sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh...",
      "content": "<h1>Giới thiệu (Updated)</h1><p>Trong thời đại công nghệ 4.0, việc phát triển ứng dụng web hiện đại...</p>",
      "image": "https://example.com/images/go-react-tutorial-updated.jpg",
      "status": "published",
      "visibility": "public",
      "password": null,
      "comment_status": "open",
      "published_at": "2024-02-15T10:00:00Z",
      "schedule_at": null,
      "author": {
        "id": 1,
        "name": "John Doe",
        "bio": "Web Developer and Technical Writer",
        "avatar": "https://example.com/authors/johndoe.jpg"
      },
      "categories": [
        {
          "id": 1,
          "name": "Web Development",
          "slug": "web-development"
        },
        {
          "id": 3,
          "name": "Tutorials",
          "slug": "tutorials"
        },
        {
          "id": 5,
          "name": "Programming",
          "slug": "programming"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "Go",
          "slug": "go"
        },
        {
          "id": 8,
          "name": "React",
          "slug": "react"
        },
        {
          "id": 12,
          "name": "Web",
          "slug": "web"
        },
        {
          "id": 15,
          "name": "Tutorial",
          "slug": "tutorial"
        },
        {
          "id": 20,
          "name": "Frontend",
          "slug": "frontend"
        }
      ],
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-02-01T14:25:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Post updated successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain updated post data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.title).to.include("Updated");
  });
}
