meta {
  name: Get Post by Slug
  type: http
  seq: 4
}

get {
  url: {{base_url}}/api/admin/v1/blog/posts/slug/sample-blog-post
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Blog Post by Slug
  
  L<PERSON><PERSON> thông tin chi tiết của một bài viết blog theo slug.
  
  ## Response Format
  Giống như Get Post by ID.
  
  ## Required Permission
  - `blog.posts.read`
  
  ## Error Responses
  - 404: Post not found
  - 403: Forbidden - <PERSON><PERSON>ông có quyền xem post này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has post data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("slug");
    expect(data.data.slug).to.equal("sample-blog-post");
  });
}
