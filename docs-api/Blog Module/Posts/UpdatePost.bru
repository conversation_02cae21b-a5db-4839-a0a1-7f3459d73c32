meta {
  name: Update Post
  type: http
  seq: 5
}

put {
  url: {{base_url}}/api/admin/v1/blog/posts/1
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "title": "Updated Blog Post Title",
    "excerpt": "Updated excerpt...",
    "content": "Updated content...",
    "status": "published",
    "featured_image": "https://example.com/updated-image.jpg",
    "category_id": 2,
    "tag_ids": [1, 3, 5],
    "published_at": "2024-01-02T00:00:00Z",
    "seo": {
      "meta_title": "Updated SEO Title",
      "meta_description": "Updated SEO Description",
      "meta_keywords": "updated, keywords"
    },
    "settings": {
      "allow_comments": false,
      "is_featured": true,
      "reading_time": 7
    }
  }
}

docs {
  # Update Blog Post
  
  Cập nhật thông tin của một bài viết blog.
  
  ## Request Body
  T<PERSON>ơng tự nh<PERSON> Post, tất cả các fields đều optional khi update.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "title": "Updated Blog Post Title",
      "slug": "updated-blog-post-title",
      "excerpt": "Updated excerpt...",
      "content": "Updated content...",
      "status": "published",
      "featured_image": "https://example.com/updated-image.jpg",
      "published_at": "2024-01-02T00:00:00Z",
      "author": {...},
      "category": {...},
      "tags": [...],
      "seo": {...},
      "settings": {...},
      "updated_at": "2024-01-02T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.update`
  
  ## Error Responses
  - 404: Post not found
  - 422: Validation Error
  - 403: Forbidden - Không có quyền cập nhật post này
  
  ## Notes
  - Slug sẽ được tự động tạo lại nếu title thay đổi
  - Nếu status chuyển từ draft sang published, published_at sẽ được set về thời gian hiện tại nếu không được cung cấp
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Post updated successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.title).to.equal("Updated Blog Post Title");
  });
}
