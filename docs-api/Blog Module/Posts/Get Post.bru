meta {
  name: Get Post
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Post
  
  Retrieves a specific blog post by ID with all details.
  
  ## Path Parameters
  | Parameter | Description             |
  |-----------|-------------------------|
  | id        | ID of the post to fetch |
  
  ## Response
  Returns the complete post object with all relationships.
  
  ```json
  {
    "data": {
      "id": 1,
      "title": "Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
      "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react",
      "description": "Bài viết này sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh...",
      "content": "<h1><PERSON><PERSON><PERSON><PERSON> thi<PERSON></h1><p>Trong thời đại công nghệ 4.0, vi<PERSON><PERSON> phát triển ứng dụng web hiện đại...</p>",
      "image": "https://example.com/images/go-react-tutorial.jpg",
      "status": "draft",
      "visibility": "public",
      "password": null,
      "comment_status": "open",
      "published_at": "2024-02-15T10:00:00Z",
      "schedule_at": "2024-02-15T10:00:00Z",
      "author": {
        "id": 1,
        "name": "John Doe",
        "bio": "Web Developer and Technical Writer",
        "avatar": "https://example.com/authors/johndoe.jpg"
      },
      "categories": [
        {
          "id": 1,
          "name": "Web Development",
          "slug": "web-development"
        },
        {
          "id": 3,
          "name": "Tutorials",
          "slug": "tutorials"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "Go",
          "slug": "go"
        },
        {
          "id": 8,
          "name": "React",
          "slug": "react"
        },
        {
          "id": 12,
          "name": "Web",
          "slug": "web"
        },
        {
          "id": 15,
          "name": "Tutorial",
          "slug": "tutorial"
        }
      ],
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Post retrieved successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain post data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.title).to.be.a('string');
  });
}
