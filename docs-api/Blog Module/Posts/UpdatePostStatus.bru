meta {
  name: Update Post Status
  type: http
  seq: 6
}

put {
  url: {{base_url}}/api/admin/v1/blog/posts/1/status
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "status": "published",
    "published_at": "2024-01-01T00:00:00Z"
  }
}

docs {
  # Update Post Status
  
  Cập nhật trạng thái xuất bản của một bài viết.
  
  ## Request Body
  ```json
  {
    "status": "string",        // Trạng thái: draft|published|archived|scheduled (required)
    "published_at": "datetime" // Thời gian xuất bản (optional, required if status=scheduled)
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "title": "Post Title",
      "status": "published",
      "published_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.update`
  
  ## Error Responses
  - 404: Post not found
  - 422: Validation Error - Status không hợp lệ
  - 403: Forbidden - Không có quyền cập nhật post này
  
  ## Status Transitions
  - `draft` → `published`: Post sẽ được xuất bản ngay lập tức
  - `draft` → `scheduled`: Post sẽ được lên lịch xuất bản
  - `published` → `archived`: Post sẽ được lưu trữ
  - `published` → `draft`: Post sẽ được chuyển về draft
  - `scheduled` → `published`: Post sẽ được xuất bản ngay lập tức
  - `archived` → `published`: Post sẽ được xuất bản lại
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Post status updated successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.status).to.equal("published");
  });
}
