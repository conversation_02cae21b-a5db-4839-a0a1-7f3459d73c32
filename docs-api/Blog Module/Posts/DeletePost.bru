meta {
  name: Delete Post
  type: http
  seq: 7
}

delete {
  url: {{base_url}}/api/admin/v1/blog/posts/1
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Delete Blog Post
  
  Xóa một bài viết blog.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "message": "Post deleted successfully"
  }
  ```
  
  ## Required Permission
  - `blog.posts.delete`
  
  ## Error Responses
  - 404: Post not found
  - 403: Forbidden - Không có quyền xóa post này
  - 409: Conflict - Post có dữ liệu liên quan không thể xóa
  
  ## Notes
  - Việc xóa post có thể là soft delete hoặc hard delete tùy thuộc vào cấu hình hệ thống
  - N<PERSON><PERSON> post có comments, related posts, hoặc dữ liệu liên quan khác, có thể sẽ được chuyể<PERSON> sang trạng thái archived thay vì xóa hoàn toàn
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Post deleted successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.message).to.contain("deleted successfully");
  });
}
