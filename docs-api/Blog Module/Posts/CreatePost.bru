meta {
  name: Create Post
  type: http
  seq: 2
}

post {
  url: {{base_url}}/api/admin/v1/blog/posts
  body: json
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "title": "New Blog Post",
    "slug": "new-blog-post",
    "excerpt": "This is a new blog post excerpt...",
    "content": "Full content of the blog post goes here...",
    "status": "draft",
    "featured_image": "https://example.com/featured-image.jpg",
    "category_id": 1,
    "author_id": 1,
    "tag_ids": [1, 2, 3],
    "published_at": "2024-01-01T00:00:00Z",
    "seo": {
      "meta_title": "SEO Title for New Post",
      "meta_description": "SEO Description for New Post",
      "meta_keywords": "blog, new, post",
      "og_title": "Open Graph Title",
      "og_description": "Open Graph Description",
      "og_image": "https://example.com/og-image.jpg",
      "twitter_title": "Twitter Title",
      "twitter_description": "Twitter Description",
      "twitter_image": "https://example.com/twitter-image.jpg"
    },
    "settings": {
      "allow_comments": true,
      "is_featured": false,
      "reading_time": 5
    }
  }
}

docs {
  # Create Blog Post
  
  Tạo một bài viết blog mới.
  
  ## Request Body
  ```json
  {
    "title": "string",                    // Tiêu đề (required)
    "slug": "string",                     // URL slug (optional, auto-generated from title)
    "excerpt": "string",                  // Tóm tắt (optional)
    "content": "string",                  // Nội dung đầy đủ (required)
    "status": "string",                   // Trạng thái: draft|published|archived|scheduled (default: draft)
    "featured_image": "string",           // URL ảnh đại diện (optional)
    "category_id": "number",              // ID danh mục (required)
    "author_id": "number",                // ID tác giả (optional, default: current user)
    "tag_ids": ["number"],                // Mảng tag IDs (optional)
    "published_at": "datetime",           // Thời gian xuất bản (optional, required if status=scheduled)
    "seo": {                              // SEO metadata (optional)
      "meta_title": "string",
      "meta_description": "string", 
      "meta_keywords": "string",
      "og_title": "string",
      "og_description": "string",
      "og_image": "string",
      "twitter_title": "string",
      "twitter_description": "string",
      "twitter_image": "string"
    },
    "settings": {                         // Post settings (optional)
      "allow_comments": "boolean",        // Cho phép comment (default: true)
      "is_featured": "boolean",           // Bài viết nổi bật (default: false)
      "reading_time": "number"            // Thời gian đọc (phút, optional)
    }
  }
  ```
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "title": "New Blog Post",
      "slug": "new-blog-post",
      "excerpt": "This is a new blog post excerpt...",
      "content": "Full content of the blog post goes here...",
      "status": "draft",
      "featured_image": "https://example.com/featured-image.jpg",
      "published_at": "2024-01-01T00:00:00Z",
      "author": {
        "id": 1,
        "name": "John Doe"
      },
      "category": {
        "id": 1,
        "name": "Technology"
      },
      "tags": [...],
      "seo": {...},
      "settings": {...},
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.create`
  
  ## Error Responses
  - 422: Validation Error - Dữ liệu không hợp lệ
  - 404: Category/Author/Tag not found
}

tests {
  test("Status code is 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Post created successfully", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.title).to.equal("New Blog Post");
    expect(data.data.slug).to.equal("new-blog-post");
  });
}
