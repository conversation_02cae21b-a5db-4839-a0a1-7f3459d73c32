meta {
  name: Create Post
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/posts
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  // Note: tenant_id and created_by are automatically obtained from JWT token context
  // author_id is optional - if not provided, current user will be used as author
  {
    "title": "Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
    "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react",
    "description": "Bài viết này sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh sử dụng Go cho backend và React cho frontend, bao gồm authentication, database design và deployment.",
    "content": "<h1>Giới thiệu</h1><p>Trong thời đại công nghệ 4.0, vi<PERSON><PERSON> phát triển ứng dụng web hiện đại đòi hỏi sự kết hợp hoàn hảo giữa backend mạnh mẽ và frontend thân thiện với người dùng...</p><h2>Thiết lập môi trường phát triển</h2><p>Trước khi bắt đầu, chúng ta cần chuẩn bị các công cụ cần thiết...</p>",
    "image": "https://example.com/images/go-react-tutorial.jpg",
    "status": "draft",
    "visibility": "public",
    "password": null,
    "comment_status": "open",
    "published_at": "2024-02-15T10:00:00Z",
    "schedule_at": "2024-02-15T10:00:00Z",
    "author_id": {{author_id}},
    "category_ids": [1, 3],
    "tag_ids": [5, 8, 12, 15]
  }
}

docs {
  # Create Post
  
  Creates a new blog post with the specified details.
  
  ## Request Body
  | Field           | Type     | Required | Description                                        |
  |-----------------|----------|----------|----------------------------------------------------|
  | title           | string   | Yes      | Post title                                         |
  | slug            | string   | No       | SEO-friendly URL slug (generated if not provided)  |
  | description     | string   | No       | Brief description or excerpt of the post           |
  | content         | string   | Yes      | HTML content of the post                           |
  | image           | string   | No       | Featured image URL                                 |
  | status          | string   | No       | Post status (draft, published, pending) default: draft |
  | visibility      | string   | No       | Visibility (public, private, password) default: public |
  | password        | string   | No       | Password for password-protected posts              |
  | comment_status  | string   | No       | Comment status (open, closed) default: open        |
  | published_at    | datetime | No       | Publication date and time                          |
  | schedule_at     | datetime | No       | Scheduled publication date and time                |
  | author_id       | integer  | No       | Author ID (uses current user if not specified)     |
  | category_ids    | array    | No       | Array of category IDs                              |
  | tag_ids         | array    | No       | Array of tag IDs                                   |
  
  ## Response
  Returns the created post with all details including generated ID.
  
  ```json
  {
    "data": {
      "id": 1,
      "title": "Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
      "slug": "huong-dan-phat-trien-ung-dung-web-hien-dai-go-react",
      "description": "Bài viết này sẽ hướng dẫn bạn cách xây dựng một ứng dụng web hoàn chỉnh...",
      "content": "<h1>Giới thiệu</h1><p>Trong thời đại công nghệ 4.0, việc phát triển ứng dụng web hiện đại...</p>",
      "image": "https://example.com/images/go-react-tutorial.jpg",
      "status": "draft",
      "visibility": "public",
      "password": null,
      "comment_status": "open",
      "published_at": "2024-02-15T10:00:00Z",
      "schedule_at": "2024-02-15T10:00:00Z",
      "author": {
        "id": 1,
        "name": "John Doe",
        "bio": "Web Developer and Technical Writer",
        "avatar": "https://example.com/authors/johndoe.jpg"
      },
      "categories": [
        {
          "id": 1,
          "name": "Web Development",
          "slug": "web-development"
        },
        {
          "id": 3,
          "name": "Tutorials",
          "slug": "tutorials"
        }
      ],
      "tags": [
        {
          "id": 5,
          "name": "Go",
          "slug": "go"
        },
        {
          "id": 8,
          "name": "React",
          "slug": "react"
        },
        {
          "id": 12,
          "name": "Web",
          "slug": "web"
        },
        {
          "id": 15,
          "name": "Tutorial",
          "slug": "tutorial"
        }
      ],
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "Post created successfully"
    }
  }
  ```
}

tests {
  test("Status should be 201 Created", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(201);
  });

  test("Response should contain post data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.title).to.equal("Hướng dẫn phát triển ứng dụng web hiện đại với Go và React");
  });
}
