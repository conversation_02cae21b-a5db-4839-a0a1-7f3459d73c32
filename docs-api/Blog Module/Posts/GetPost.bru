meta {
  name: Get Post
  type: http
  seq: 3
}

get {
  url: {{base_url}}/api/admin/v1/blog/posts/1
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Blog Post by ID
  
  L<PERSON><PERSON> thông tin chi tiết của một bài viết blog theo ID.
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": {
      "id": 1,
      "title": "Sample Blog Post",
      "slug": "sample-blog-post",
      "excerpt": "This is a sample excerpt...",
      "content": "Full content here...",
      "status": "published",
      "featured_image": "https://example.com/image.jpg",
      "published_at": "2024-01-01T00:00:00Z",
      "author": {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "avatar": "https://example.com/avatar.jpg"
      },
      "category": {
        "id": 1,
        "name": "Technology",
        "slug": "technology",
        "description": "Technology related posts"
      },
      "tags": [
        {
          "id": 1,
          "name": "React",
          "slug": "react",
          "color": "#61DAFB"
        }
      ],
      "seo": {
        "meta_title": "SEO Title",
        "meta_description": "SEO Description",
        "meta_keywords": "keyword1, keyword2",
        "og_title": "Open Graph Title",
        "og_description": "Open Graph Description",
        "og_image": "https://example.com/og-image.jpg",
        "twitter_title": "Twitter Title",
        "twitter_description": "Twitter Description",
        "twitter_image": "https://example.com/twitter-image.jpg"
      },
      "settings": {
        "allow_comments": true,
        "is_featured": true,
        "reading_time": 5
      },
      "stats": {
        "views": 1250,
        "likes": 45,
        "comments": 12,
        "shares": 8
      },
      "related_posts": [
        {
          "id": 2,
          "title": "Related Post",
          "slug": "related-post",
          "featured_image": "https://example.com/related.jpg"
        }
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.read`
  
  ## Error Responses
  - 404: Post not found
  - 403: Forbidden - Không có quyền xem post này
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has post data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.have.property("id");
    expect(data.data).to.have.property("title");
    expect(data.data).to.have.property("author");
    expect(data.data).to.have.property("category");
  });
}
