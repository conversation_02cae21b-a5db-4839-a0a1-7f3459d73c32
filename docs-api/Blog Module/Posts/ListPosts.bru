meta {
  name: List Posts
  type: http
  seq: 1
}

get {
  url: {{base_url}}/api/admin/v1/blog/posts
  body: none
  auth: inherit
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

query {
  page: 1
  limit: 10
  search: example
  status: published
  category_id: 1
  author_id: 1
  sort: created_at
  order: desc
}

docs {
  # List Blog Posts
  
  Lấy danh sách bài viết blog với các tùy chọn lọc và phân trang.
  
  ## Query Parameters
  - `page`: <PERSON><PERSON> trang (mặc định: 1)
  - `limit`: Số lượng items per page (mặc định: 10, max: 100)
  - `search`: T<PERSON><PERSON> kiếm theo title, slug, content
  - `status`: <PERSON><PERSON><PERSON> theo trạng thái (draft, published, archived, scheduled)
  - `category_id`: Lọc theo category ID
  - `author_id`: Lọ<PERSON> theo author ID
  - `tag_ids`: L<PERSON><PERSON> theo tag IDs (cách nhau bởi dấu phẩy)
  - `sort`: <PERSON><PERSON><PERSON> xế<PERSON> theo (created_at, updated_at, title, published_at)
  - `order`: <PERSON><PERSON><PERSON> tự (asc, desc)
  - `from_date`: <PERSON><PERSON><PERSON> (YYYY-MM-DD)
  - `to_date`: Lọc đến ngày (YYYY-MM-DD)
  
  ## Response Format
  ```json
  {
    "status": "success",
    "data": [
      {
        "id": 1,
        "title": "Sample Blog Post",
        "slug": "sample-blog-post",
        "excerpt": "This is a sample excerpt...",
        "content": "Full content here...",
        "status": "published",
        "featured_image": "https://example.com/image.jpg",
        "published_at": "2024-01-01T00:00:00Z",
        "author": {
          "id": 1,
          "name": "John Doe",
          "email": "<EMAIL>"
        },
        "category": {
          "id": 1,
          "name": "Technology",
          "slug": "technology"
        },
        "tags": [
          {
            "id": 1,
            "name": "React",
            "slug": "react"
          }
        ],
        "seo": {
          "meta_title": "SEO Title",
          "meta_description": "SEO Description",
          "meta_keywords": "keyword1, keyword2"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "current_page": 1,
      "per_page": 10,
      "total": 50,
      "last_page": 5,
      "has_more": true,
      "next_cursor": "eyJpZCI6MTB9"
    }
  }
  ```
  
  ## Required Permission
  - `blog.posts.list`
}

tests {
  test("Status code is 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response has posts list", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data).to.be.an("array");
    expect(data.meta).to.have.property("current_page");
  });
}
