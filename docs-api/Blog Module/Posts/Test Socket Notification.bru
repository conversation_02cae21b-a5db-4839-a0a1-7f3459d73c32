meta {
  name: Test Socket Notification
  type: http
  seq: 7
}

put {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}/status
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "status": "published"
  }
}

docs {
  # Test Socket Notification for Post Publishing
  
  This endpoint is specifically for testing the socket notification feature when a post is published.
  
  ## Prerequisites
  1. Ensure WebSocket server is running
  2. Have a post in "draft" or "pending" status to test the transition to "published"
  3. Set up WebSocket client to listen for notifications
  
  ## Testing Steps
  
  ### 1. Connect to WebSocket
  ```javascript
  const ws = new WebSocket('ws://localhost:8080/ws');
  
  ws.onopen = function() {
    console.log('WebSocket connected');
    
    // Subscribe to notification channels
    ws.send(JSON.stringify({
      type: 'subscribe',
      channels: ['user', 'notification'],
      tenant_id: 1
    }));
  };
  
  ws.onmessage = function(event) {
    const notification = JSON.parse(event.data);
    console.log('Received notification:', notification);
    
    // Check for post published notification
    if (notification.type === 'blog.post.published') {
      console.log('✅ Post published notification received!');
      console.log('Post ID:', notification.data.post_id);
      console.log('Post Title:', notification.data.post_title);
    }
  };
  ```
  
  ### 2. Execute API Call
  Run this Bruno request to change post status to "published"
  
  ### 3. Verify Notification
  Check WebSocket client console for notification with:
  - `type`: "blog.post.published"
  - `title`: "Bài viết mới được xuất bản"
  - `message`: "Bài viết '[Post Title]' đã được xuất bản"
  - `data.post_id`: The ID of the published post
  
  ## Expected Socket Notification Format
  ```json
  {
    "type": "blog.post.published",
    "title": "Bài viết mới được xuất bản",
    "message": "Bài viết 'Sample Post Title' đã được xuất bản",
    "priority": "normal",
    "category": "blog",
    "channels": ["user", "notification"],
    "data": {
      "post_id": 1,
      "post_title": "Sample Post Title",
      "post_slug": "sample-post-title",
      "author_id": 1,
      "tenant_id": 1,
      "timestamp": "2024-02-15T10:00:00Z"
    }
  }
  ```
  
  ## Troubleshooting
  - If no notification is received, check WebSocket connection
  - Ensure post was not already in "published" status
  - Check server logs for any socket service errors
  - Verify tenant_id matches between API call and WebSocket subscription
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
  });

  test("Post status should be published", function() {
    const responseJson = res.getBody();
    expect(responseJson.data.status).to.equal("published");
  });

  test("Post should have published_at timestamp", function() {
    const responseJson = res.getBody();
    expect(responseJson.data.published_at).to.not.be.null;
    expect(responseJson.data.published_at).to.not.be.undefined;
  });

  // Manual verification required for socket notification
  // Check WebSocket client console for notification receipt
}