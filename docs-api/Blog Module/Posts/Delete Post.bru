meta {
  name: Delete Post
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Delete Post
  
  Deletes a specific blog post by ID.
  
  ## Path Parameters
  | Parameter | Description             |
  |-----------|-------------------------|
  | id        | ID of the post to delete |
  
  ## Response
  Returns a success message.
  
  ```json
  {
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Post deleted successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
    expect(responseJson.meta.message).to.include("deleted");
  });
}
