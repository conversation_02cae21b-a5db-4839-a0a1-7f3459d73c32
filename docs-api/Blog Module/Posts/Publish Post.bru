meta {
  name: Publish Post
  type: http
  seq: 6
}

put {
  url: {{api_url}}/api/admin/v1/blog/posts/{{post_id}}/status
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "status": "published",
    "published_at": "2024-02-15T10:00:00Z"
  }
}

docs {
  # Publish Post
  
  Updates the status of a post to publish it, schedule it, or change its status.
  
  **🔔 Socket Notification**: When a post is changed from any status to "published", a real-time notification will be sent via WebSocket to all connected clients.
  
  ## Path Parameters
  | Parameter | Description                |
  |-----------|--------------------------|
  | id        | ID of the post to update   |
  
  ## Request Body
  | Field        | Type     | Required | Description                                    |
  |--------------|----------|----------|------------------------------------------------|
  | status       | string   | Yes      | New status (published, draft, pending, schedule) |
  | published_at | datetime | No       | Publication date and time (for published posts) |
  
  ## Socket Notification (When Publishing)
  When status changes to "published", a WebSocket notification is broadcast with:
  
  ```json
  {
    "type": "blog.post.published",
    "title": "Bài viết mới được xuất bản",
    "message": "Bài viết '[Post Title]' đã được xuất bản",
    "priority": "normal",
    "category": "blog",
    "channels": ["user", "notification"],
    "data": {
      "post_id": 1,
      "post_title": "Post Title",
      "post_slug": "post-slug",
      "author_id": 1,
      "tenant_id": 1,
      "timestamp": "2024-02-15T10:00:00Z"
    }
  }
  ```
  
  ## Response
  Returns the updated post with the new status.
  
  ```json
  {
    "data": {
      "id": 1,
      "title": "Hướng dẫn phát triển ứng dụng web hiện đại với Go và React",
      "status": "published",
      "published_at": "2024-02-15T10:00:00Z",
      "updated_at": "2024-02-01T14:25:00Z"
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Post status updated successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain updated status", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.status).to.equal("published");
  });

  test("Post should have published_at when status is published", function() {
    const responseJson = res.getBody();
    if (responseJson.data.status === "published") {
      expect(responseJson.data.published_at).to.not.be.null;
    }
  });

  // Note: Socket notification testing requires WebSocket connection
  // The notification will be sent automatically when status changes to "published"
  // To test socket notifications:
  // 1. Connect to WebSocket endpoint: ws://localhost:8080/ws
  // 2. Subscribe to notification channels
  // 3. Execute this API call
  // 4. Verify notification is received with type "blog.post.published"
}
