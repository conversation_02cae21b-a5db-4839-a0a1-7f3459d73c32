meta {
  name: Get Post Related Posts
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/blog/posts/1/related-posts?limit=10&cursor=
  body: none
  auth: none
}

params:query {
  limit: 10
  cursor: 
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Post Related Posts
  
  Retrieves all related posts for a specific post with cursor-based pagination.
  
  ## Path Parameters
  
  - `post_id`: ID of the post to get related posts for
  
  ## Query Parameters
  
  - `limit` (optional): Maximum number of related posts to return (default: 10, max: 50)
  - `cursor` (optional): Cursor for pagination (post ID to start from)
  
  ## Response
  
  Returns an object with:
  - `data`: Array of post summary objects (simplified post information)
  - `meta`: Object containing:
    - `next_cursor`: Cursor for next page (empty if no more results)
    - `has_more`: <PERSON><PERSON>an indicating if there are more results
    - `total`: Total number of posts in current response
  
  ## Response Format
  
  ```json
  {
    "data": [
      {
        "post_id": 1,
        "title": "Post Title",
        "slug": "post-slug",
        "description": "Post description",
        "image": "image_url",
        "status": "published",
        "visibility": "public",
        "published_at": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "author_id": 1
      }
    ],
    "meta": {
      "next_cursor": "2",
      "has_more": true,
      "total": 1
    }
  }
  ```
  
  ## Notes
  
  - Results are ordered by related_post_id (ascending) for consistent pagination
  - Returns simplified post summaries instead of full post details with relationship metadata
  - Use cursor-based pagination for better performance with large datasets
  - Useful for displaying "Related Articles" sections with pagination support
}
