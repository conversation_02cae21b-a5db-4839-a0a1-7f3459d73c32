meta {
  name: Delete Related Post
  type: http
  seq: 6
}

delete {
  url: {{api_url}}/api/admin/v1/blog/related-posts/1
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Delete Related Post
  
  Deletes a specific related post relationship by its ID.
  
  ## Path Parameters
  
  - `relation_id`: ID of the relationship to delete
  
  ## Response
  
  Returns 204 No Content on successful deletion.
  
  ## Notes
  
  - Only deletes the specific relationship record
  - Does not automatically delete reverse relationships
  - Use "Delete All Related Posts" to remove all relationships for a post
}
