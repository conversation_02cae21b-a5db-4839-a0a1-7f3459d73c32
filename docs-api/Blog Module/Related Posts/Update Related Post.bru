meta {
  name: Update Related Post
  type: http
  seq: 5
}

put {
  url: {{api_url}}/api/admin/v1/blog/related-posts/1
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "priority": 5,
    "is_bidirectional": false
  }
}

docs {
  # Update Related Post
  
  Updates an existing related post relationship.
  
  ## Path Parameters
  
  - `relation_id`: ID of the relationship to update
  
  ## Request Body
  
  - `priority` (optional): New priority for ordering
  - `is_bidirectional` (optional): Whether the relationship is bidirectional
  
  ## Response
  
  Returns the updated related post relationship with enriched post data.
  
  ## Notes
  
  - Only provided fields will be updated
  - Changing `is_bidirectional` does not automatically create/remove reverse relationships
  - Use separate API calls to manage reverse relationships if needed
}
