meta {
  name: Bulk Create Related Posts
  type: http
  seq: 4
}

post {
  url: {{api_url}}/api/admin/v1/blog/related-posts/bulk
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "post_id": 1,
    "is_bidirectional": true,
    "related_posts": [
      {
        "related_post_id": 2,
        "priority": 1
      },
      {
        "related_post_id": 3,
        "priority": 2
      },
      {
        "related_post_id": 4,
        "priority": 3
      }
    ]
  }
}

docs {
  # Bulk Create Related Posts
  
  Creates multiple related post relationships in a single request.
  
  ## Request Body
  
  - `post_id` (required): ID of the main post
  - `is_bidirectional` (optional): Whether to create reverse relationships (default: true)
  - `related_posts` (required): Array of related posts to create (max: 50)
    - `related_post_id` (required): ID of the related post
    - `priority` (optional): Priority for ordering (default: 1)
  
  ## Response
  
  Returns a summary of the bulk operation including:
  - `created`: Array of successfully created relationships
  - `failed`: Array of failed attempts with error details
  - `meta`: Summary statistics
  
  ## Notes
  
  - Validates that all posts exist before creating relationships
  - Skips relationships that already exist
  - Continues processing even if some relationships fail
  - Maximum 50 relationships per request
}
