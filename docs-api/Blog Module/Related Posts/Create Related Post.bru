meta {
  name: Create Related Post
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/related-posts
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "post_id": 1,
    "related_post_id": 2,
    "priority": 1,
    "is_bidirectional": true
  }
}

docs {
  # Create Related Post
  
  Creates a new relationship between two blog posts.
  
  ## Request Body
  
  - `post_id` (required): ID of the main post
  - `related_post_id` (required): ID of the related post
  - `priority` (optional): Priority for ordering (default: 1)
  - `is_bidirectional` (optional): Whether to create reverse relationship (default: true)
  
  ## Response
  
  Returns the created related post relationship with enriched post data.
  
  ## Notes
  
  - If `is_bidirectional` is true, a reverse relationship will also be created
  - Posts cannot be related to themselves
  - Duplicate relationships are not allowed
}
