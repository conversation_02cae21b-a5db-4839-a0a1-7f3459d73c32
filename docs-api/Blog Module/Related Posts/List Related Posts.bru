meta {
  name: List Related Posts
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/related-posts?post_id=1&limit=10&sort_by=priority&sort_order=asc
  body: none
  auth: none
}

params:query {
  post_id: 1
  limit: 10
  sort_by: priority
  sort_order: asc
  ~cursor: 
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # List Related Posts
  
  Retrieves a paginated list of related post relationships.
  
  ## Query Parameters
  
  - `post_id` (optional): Filter by specific post ID
  - `limit` (optional): Number of items per page (1-100, default: 20)
  - `sort_by` (optional): Sort field (priority, created_at, updated_at, default: priority)
  - `sort_order` (optional): Sort order (asc, desc, default: asc)
  - `cursor` (optional): Pagination cursor for next page
  
  ## Response
  
  Returns a paginated list of related post relationships with metadata.
  
  ## Notes
  
  - Results include enriched post data for both main and related posts
  - Supports cursor-based pagination for efficient large dataset handling
}
