meta {
  name: Get Blog Statistics
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/blog/statistics/status
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Blog Statistics
  
  Retrieves statistics about the blog including post counts by status, categories, and tags.
  
  ## Response
  Returns a comprehensive set of blog statistics.
  
  ```json
  {
    "data": {
      "posts": {
        "total": 125,
        "by_status": {
          "published": 78,
          "draft": 35,
          "pending": 12
        }
      },
      "categories": {
        "total": 15
      },
      "tags": {
        "total": 42
      },
      "authors": {
        "total": 8
      }
    },
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Blog statistics retrieved successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain statistics data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.posts).to.be.an('object');
    expect(responseJson.data.posts.total).to.be.a('number');
  });
}
