meta {
  name: Create Author
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/authors
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "<PERSON>",
    "user_id": 1,
    "bio": "Web Developer and Technical Writer with 5+ years of experience",
    "avatar": "https://example.com/authors/johndoe.jpg",
    "email": "<EMAIL>",
    "website": "https://johndoe.com",
    "social_media": {
      "twitter": "johndoe",
      "github": "johndoe",
      "linkedin": "john-doe"
    }
  }
}

docs {
  # Create Author
  
  Creates a new blog author with the specified details.
  
  ## Request Body
  | Field        | Type     | Required | Description                                        |
  |--------------|----------|----------|----------------------------------------------------|
  | name         | string   | Yes      | Author name                                        |
  | user_id      | integer  | Yes      | Associated user ID                                 |
  | bio          | string   | No       | Author biography                                   |
  | avatar       | string   | No       | Author profile image URL                           |
  | email        | string   | No       | Author email                                       |
  | website      | string   | No       | Author website URL                                 |
  | social_media | object   | No       | Social media profiles                              |
  
  ## Response
  Returns the created author with all details including generated ID.
  
  ```json
  {
    "data": {
      "id": 1,
      "name": "John Doe",
      "user_id": 1,
      "bio": "Web Developer and Technical Writer with 5+ years of experience",
      "avatar": "https://example.com/authors/johndoe.jpg",
      "email": "<EMAIL>",
      "website": "https://johndoe.com",
      "social_media": {
        "twitter": "johndoe",
        "github": "johndoe",
        "linkedin": "john-doe"
      },
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "Author created successfully"
    }
  }
  ```
}

tests {
  test("Status should be 201 Created", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(201);
  });

  test("Response should contain author data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.name).to.equal("John Doe");
    expect(responseJson.data.user_id).to.equal(1);
  });
}
