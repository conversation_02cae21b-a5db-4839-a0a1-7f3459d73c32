meta {
  name: Get Category Tree
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/blog/categories/tree
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Category Tree
  
  Retrieves the hierarchical tree structure of all blog categories.
  
  ## Response
  Returns a nested tree structure of categories.
  
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "Web Development",
        "slug": "web-development",
        "description": "Articles about web development technologies and best practices",
        "parent_id": null,
        "image": "https://example.com/images/categories/web-dev.jpg",
        "created_at": "2024-01-31T08:30:00Z",
        "updated_at": "2024-01-31T08:30:00Z",
        "depth": 0,
        "post_count": 15,
        "children": [
          {
            "id": 2,
            "name": "Frontend",
            "slug": "frontend",
            "description": "Articles about frontend technologies",
            "parent_id": 1,
            "image": "https://example.com/images/categories/frontend.jpg",
            "created_at": "2024-01-31T08:35:00Z",
            "updated_at": "2024-01-31T08:35:00Z",
            "depth": 1,
            "post_count": 8,
            "children": [
              {
                "id": 4,
                "name": "React",
                "slug": "react",
                "description": "Articles about React.js",
                "parent_id": 2,
                "image": "https://example.com/images/categories/react.jpg",
                "created_at": "2024-01-31T08:40:00Z",
                "updated_at": "2024-01-31T08:40:00Z",
                "depth": 2,
                "post_count": 5,
                "children": []
              }
            ]
          },
          {
            "id": 3,
            "name": "Backend",
            "slug": "backend",
            "description": "Articles about backend technologies",
            "parent_id": 1,
            "image": "https://example.com/images/categories/backend.jpg",
            "created_at": "2024-01-31T08:36:00Z",
            "updated_at": "2024-01-31T08:36:00Z",
            "depth": 1,
            "post_count": 7,
            "children": []
          }
        ]
      }
    ],
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Category tree retrieved successfully"
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain categories with children", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('array');
    
    // Check if at least one category has children
    if (responseJson.data.length > 0) {
      const hasChildren = responseJson.data.some(category => 
        category.children && category.children.length > 0
      );
      expect(hasChildren).to.equal(true);
    }
  });
}
