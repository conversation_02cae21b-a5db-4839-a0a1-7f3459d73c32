meta {
  name: Get Categories
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/categories
  query: {
    page: 1,
    limit: 10
  },
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Categories
  
  Retrieves a list of all blog categories.
  
  ## Query Parameters
  | Parameter   | Type    | Required | Default | Description                                       |
  |-------------|---------|----------|---------|---------------------------------------------------|
  | page        | integer | No       | 1       | Page number for pagination                        |
  | limit       | integer | No       | 10      | Number of items per page                          |
  
  ## Response
  Returns a paginated list of categories.
  
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "Web Development",
        "slug": "web-development",
        "description": "Articles about web development technologies and best practices",
        "parent_id": null,
        "image": "https://example.com/images/categories/web-dev.jpg",
        "created_at": "2024-01-31T08:30:00Z",
        "updated_at": "2024-01-31T08:30:00Z",
        "depth": 0,
        "post_count": 15
      },
      {
        "id": 2,
        "name": "Frontend",
        "slug": "frontend",
        "description": "Articles about frontend technologies",
        "parent_id": 1,
        "image": "https://example.com/images/categories/frontend.jpg",
        "created_at": "2024-01-31T08:35:00Z",
        "updated_at": "2024-01-31T08:35:00Z",
        "depth": 1,
        "post_count": 8
      }
    ],
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Categories retrieved successfully",
      "pagination": {
        "total": 12,
        "count": 2,
        "per_page": 10,
        "current_page": 1,
        "total_pages": 2,
        "links": {
          "next": "/api/admin/v1/blog/categories?page=2&limit=10"
        }
      }
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain categories array", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('array');
  });
}
