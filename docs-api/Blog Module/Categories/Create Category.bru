meta {
  name: Create Category
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/categories
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "Web Development",
    "slug": "web-development",
    "description": "Articles about web development technologies and best practices",
    "parent_id": null,
    "image": "https://example.com/images/categories/web-dev.jpg"
  }
}

docs {
  # Create Category
  
  Creates a new blog category with the specified details.
  
  ## Request Body
  | Field       | Type     | Required | Description                                        |
  |-------------|----------|----------|----------------------------------------------------|
  | name        | string   | Yes      | Category name                                      |
  | slug        | string   | No       | SEO-friendly URL slug (generated if not provided)  |
  | description | string   | No       | Category description                               |
  | parent_id   | integer  | No       | ID of parent category (null for top-level)         |
  | image       | string   | No       | Image URL for the category                         |
  
  ## Response
  Returns the created category with all details including generated ID.
  
  ```json
  {
    "data": {
      "id": 1,
      "name": "Web Development",
      "slug": "web-development",
      "description": "Articles about web development technologies and best practices",
      "parent_id": null,
      "image": "https://example.com/images/categories/web-dev.jpg",
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1,
      "depth": 0,
      "left": 1,
      "right": 2,
      "post_count": 0
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "Category created successfully"
    }
  }
  ```
}

tests {
  test("Status should be 201 Created", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(201);
  });

  test("Response should contain category data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.name).to.equal("Web Development");
  });
}
