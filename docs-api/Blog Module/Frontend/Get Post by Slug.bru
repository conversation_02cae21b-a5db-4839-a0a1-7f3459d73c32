meta {
  name: Get Post by Slug (Frontend)
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/frontend/posts/sample-post-slug
  body: none
  auth: none
}

params:query {
  ~tenant_id: 
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

vars:pre-request {
  slug: sample-post-slug
}

docs {
  # Get Post by Slug (Frontend API)
  
  Lấy chi tiết bài viết theo slug cho frontend.
  
  ## Path Parameters
  - `slug`: Slug của bài viết
  
  ## Query Parameters
  - `tenant_id`: ID tenant (tù<PERSON> chọn, c<PERSON> thể truyền qua header)
  
  ## Headers
  - `X-Tenant-ID`: ID tenant (tùy chọn)
  
  ## Response
  Trả về thông tin chi tiết của bài viết bao gồm nội dung, danh mục, tags, tác giả.
}
