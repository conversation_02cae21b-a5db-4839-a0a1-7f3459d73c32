meta {
  name: Get Posts (Frontend)
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/frontend/posts?tenant_id=1
  body: none
  auth: none
}

params:query {
  tenant_id: 1
  ~limit: 10
  ~cursor: 
  ~search: 
  ~category_id: 
  ~tag_id: 
  ~order_by: created_at
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Get Posts (Frontend API)
  
  Lấy danh sách bài viết đã xuất bản cho frontend.
  
  ## Query Parameters
  - `limit`: <PERSON><PERSON> lượng bài viết trả về (mặc định: 10, tối đa: 100)
  - `cursor`: Con trỏ phân trang (để lấy trang tiếp theo)
  - `search`: Tìm kiếm theo tiêu đề và nội dung
  - `category_id`: Lọ<PERSON> theo danh mục
  - `tag_id`: Lọ<PERSON> theo tag
  - `order_by`: <PERSON><PERSON><PERSON> xếp (created_at, updated_at, title)
  - `tenant_id`: ID tenant (t<PERSON><PERSON> chọn, c<PERSON> thể truyền qua header)
  
  ## Headers
  - `X-Tenant-ID`: ID tenant (tù<PERSON> chọn)
  
  ## Response
  Trả về danh sách bài viết với thông tin cơ bản và metadata phân trang.
}
