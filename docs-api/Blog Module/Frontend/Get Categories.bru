meta {
  name: Get Categories (Frontend)
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/frontend/categories?tree=false
  body: none
  auth: none
}

params:query {
  tree: false
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Get Categories (Frontend API)
  
  L<PERSON>y danh sách danh mục cho frontend.
  
  ## Query Parameters
  - `tree`: Trả về dạng cây (true) hoặc danh sách phẳng (false, mặc định)
  
  ## Headers
  - `X-Tenant-ID`: ID tenant (bắt buộc)
  
  ## Response Format
  
  ### Khi tree=false (danh sách phẳng):
  ```json
  {
    "success": true,
    "data": [
      {
        "id": 1,
        "name": "Technology",
        "slug": "technology",
        "description": "Tech related posts",
        "image": "https://example.com/tech.jpg",
        "post_count": 15,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "meta": {
      "has_more": false
    }
  }
  ```
  
  ### Khi tree=true (cấu trúc cây):
  ```json
  {
    "success": true,
    "data": [
      {
        "id": 1,
        "name": "Technology",
        "slug": "technology",
        "description": "Tech related posts",
        "image": "https://example.com/tech.jpg",
        "post_count": 15,
        "depth": 0,
        "children": [
          {
            "id": 2,
            "name": "Programming",  
            "slug": "programming",
            "description": "Programming tutorials",
            "image": "https://example.com/programming.jpg",
            "post_count": 8,
            "depth": 1,
            "children": []
          }
        ]
      }
    ],
    "meta": null
  }
  ```
  
  ## Error Responses
  - `400 Bad Request`: Thiếu X-Tenant-ID header
  - `400 Bad Request`: Invalid tenant ID
  - `500 Internal Server Error`: Lỗi server khi lấy dữ liệu
}
