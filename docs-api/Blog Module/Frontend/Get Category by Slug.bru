meta {
  name: Get Category by Slug (Frontend)
  type: http
  seq: 5
}

get {
  url: {{api_url}}/api/frontend/categories/sample-category-slug
  body: none
  auth: none
}

params:query {
  ~tenant_id: 1
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

vars:pre-request {
  slug: sample-category-slug
}

docs {
  # Get Category by Slug (Frontend API)
  
  Lấy chi tiết danh mục theo slug cho frontend.
  
  ## Path Parameters
  - `slug`: Slug của danh mục
  
  ## Query Parameters
  - `tenant_id`: ID tenant (tù<PERSON> chọn, có thể truyền qua header)
  
  ## Headers
  - `X-Tenant-ID`: ID tenant (tùy chọn)
  
  ## Response
  Trả về thông tin chi tiết của danh mục bao gồm mô tả, danh mục cha, và danh mục con.
}
