meta {
  name: Get Category Tree (Frontend)
  type: http
  seq: 4
}

get {
  url: {{api_url}}/api/frontend/categories/tree
  body: none
  auth: none
}

params:query {
  ~tenant_id: 
}

headers {
  Content-Type: application/json
  X-Tenant-ID: {{tenant_id}}
}

docs {
  # Get Category Tree (Frontend API)
  
  Lấy cấu trúc cây danh mục đầy đủ cho frontend.
  
  ## Query Parameters
  - `tenant_id`: ID tenant (tù<PERSON> chọn, có thể truyền qua header)
  
  ## Headers
  - `X-Tenant-ID`: ID tenant (tùy chọn)
  
  ## Response
  Trả về cấu trúc cây danh mục hoàn chỉnh với các danh mục con được lồng nhau.
}
