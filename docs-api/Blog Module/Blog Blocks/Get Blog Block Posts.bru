meta {
  name: Get Blog Block Posts
  type: http
  seq: 7
}

get {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts?limit=10&cursor=
  body: none
  auth: bearer
}

params:query {
  limit: 10
  cursor: 
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog block posts data", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
  });
  
  test("Response should contain meta information", function() {
    const meta = res.getBody().meta;
    expect(meta).to.have.property('has_more');
    expect(meta).to.have.property('next_cursor');
  });
  
  test("Each post should have required fields", function() {
    const data = res.getBody().data;
    if (data.length > 0) {
      const post = data[0];
      expect(post).to.have.property('id');
      expect(post).to.have.property('blog_block_id');
      expect(post).to.have.property('post_id');
      expect(post).to.have.property('priority');
      expect(post).to.have.property('post');
    }
  });
}