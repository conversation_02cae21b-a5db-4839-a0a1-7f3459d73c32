meta {
  name: <PERSON><PERSON><PERSON> Post from Blog Block
  type: http
  seq: 8
}

delete {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts/1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain success message", function() {
    const message = res.getBody().message;
    expect(message).to.be.a('string');
  });
}