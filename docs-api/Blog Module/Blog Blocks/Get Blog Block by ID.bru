meta {
  name: Get Blog Block by ID
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog block data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('name');
    expect(data).to.have.property('type');
    expect(data).to.have.property('config');
    expect(data).to.have.property('is_active');
  });
}