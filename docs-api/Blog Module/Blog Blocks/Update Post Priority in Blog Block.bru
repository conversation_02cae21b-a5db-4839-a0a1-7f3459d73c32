meta {
  name: Update Post Priority in Blog Block
  type: http
  seq: 10
}

put {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts/1
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "priority": 5
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain updated blog block post data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('blog_block_id');
    expect(data).to.have.property('post_id');
    expect(data).to.have.property('priority');
    expect(data.priority).to.equal(5);
  });
}