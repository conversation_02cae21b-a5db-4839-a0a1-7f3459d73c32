meta {
  name: <PERSON><PERSON> Add Posts to Blog Block
  type: http
  seq: 9
}

post {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts/batch
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "posts": [
      {
        "post_id": 1,
        "priority": 1
      },
      {
        "post_id": 2,
        "priority": 2
      },
      {
        "post_id": 3,
        "priority": 3
      }
    ]
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain batch result", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('created_count');
    expect(data).to.have.property('failed_count');
    expect(data).to.have.property('created_posts');
    expect(data).to.have.property('failed_posts');
  });
}