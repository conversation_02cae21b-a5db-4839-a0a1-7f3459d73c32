meta {
  name: Reorder Posts in Blog Block
  type: http
  seq: 11
}

put {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts/reorder
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "post_orders": [
      {
        "post_id": 1,
        "priority": 3
      },
      {
        "post_id": 2,
        "priority": 1
      },
      {
        "post_id": 3,
        "priority": 2
      }
    ]
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain reorder result", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('updated_count');
    expect(data).to.have.property('failed_count');
    expect(data).to.have.property('updated_posts');
    expect(data).to.have.property('failed_posts');
  });
}