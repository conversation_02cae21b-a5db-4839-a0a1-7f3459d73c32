meta {
  name: Create Blog Block
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Featured Posts Block",
    "description": "A block to display featured blog posts",
    "type": "featured",
    "config": {
      "limit": 5,
      "show_excerpt": true,
      "show_author": true,
      "show_date": true
    },
    "is_active": true
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog block data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('name');
    expect(data).to.have.property('type');
    expect(data).to.have.property('config');
  });
}