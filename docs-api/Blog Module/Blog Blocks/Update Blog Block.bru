meta {
  name: Update Blog Block
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Updated Featured Posts Block",
    "description": "An updated block to display featured blog posts",
    "type": "featured",
    "config": {
      "limit": 8,
      "show_excerpt": true,
      "show_author": true,
      "show_date": true,
      "show_thumbnail": true
    },
    "is_active": true
  }
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain updated blog block data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('name');
    expect(data.name).to.equal('Updated Featured Posts Block');
  });
}