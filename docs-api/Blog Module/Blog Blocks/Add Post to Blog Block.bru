meta {
  name: Add Post to Blog Block
  type: http
  seq: 6
}

post {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks/1/posts
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "post_id": 1,
    "priority": 1
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog block post data", function() {
    const data = res.getBody().data;
    expect(data).to.have.property('id');
    expect(data).to.have.property('blog_block_id');
    expect(data).to.have.property('post_id');
    expect(data).to.have.property('priority');
  });
}