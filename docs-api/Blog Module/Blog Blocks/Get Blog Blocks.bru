meta {
  name: Get Blog Blocks
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/blog-blocks?limit=10&cursor=&type=featured&is_active=true
  body: none
  auth: bearer
}

params:query {
  limit: 10
  cursor: 
  type: featured
  is_active: true
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    expect(res.getBody().status).to.equal("success");
  });
  
  test("Response should contain blog blocks data", function() {
    const data = res.getBody().data;
    expect(data).to.be.an('array');
  });
  
  test("Response should contain meta information", function() {
    const meta = res.getBody().meta;
    expect(meta).to.have.property('has_more');
    expect(meta).to.have.property('next_cursor');
  });
}