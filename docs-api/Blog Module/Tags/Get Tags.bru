meta {
  name: Get Tags
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/blog/tags
  query: {
    page: 1,
    limit: 10,
    search: ""
  },
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  # Get Tags
  
  Retrieves a list of all blog tags.
  
  ## Query Parameters
  | Parameter   | Type    | Required | Default | Description                                       |
  |-------------|---------|----------|---------|---------------------------------------------------|
  | page        | integer | No       | 1       | Page number for pagination                        |
  | limit       | integer | No       | 10      | Number of items per page                          |
  | search      | string  | No       | ""      | Search term for tag name                          |
  
  ## Response
  Returns a paginated list of tags.
  
  ```json
  {
    "data": [
      {
        "id": 1,
        "name": "React",
        "slug": "react",
        "description": "React.js frontend library",
        "created_at": "2024-01-31T08:30:00Z",
        "updated_at": "2024-01-31T08:30:00Z"
      },
      {
        "id": 2,
        "name": "Go",
        "slug": "go",
        "description": "Go programming language",
        "created_at": "2024-01-31T08:35:00Z",
        "updated_at": "2024-01-31T08:35:00Z"
      }
    ],
    "meta": {
      "status": "success",
      "code": 200,
      "message": "Tags retrieved successfully",
      "pagination": {
        "total": 25,
        "count": 2,
        "per_page": 10,
        "current_page": 1,
        "total_pages": 3,
        "links": {
          "next": "/api/admin/v1/blog/tags?page=2&limit=10"
        }
      }
    }
  }
  ```
}

tests {
  test("Status should be 200 OK", function() {
    expect(res.getStatus()).to.equal(200);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(200);
  });

  test("Response should contain tags array", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('array');
  });
}
