meta {
  name: Create Tag
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/admin/v1/blog/tags
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "name": "React",
    "slug": "react",
    "description": "React.js frontend library"
  }
}

docs {
  # Create Tag
  
  Creates a new blog tag with the specified details.
  
  ## Request Body
  | Field       | Type     | Required | Description                                        |
  |-------------|----------|----------|----------------------------------------------------|
  | name        | string   | Yes      | Tag name                                           |
  | slug        | string   | No       | SEO-friendly URL slug (generated if not provided)  |
  | description | string   | No       | Tag description                                    |
  
  ## Response
  Returns the created tag with all details including generated ID.
  
  ```json
  {
    "data": {
      "id": 1,
      "name": "React",
      "slug": "react",
      "description": "React.js frontend library",
      "created_at": "2024-01-31T08:30:00Z",
      "updated_at": "2024-01-31T08:30:00Z",
      "created_by": 1,
      "updated_by": 1,
      "tenant_id": 1
    },
    "meta": {
      "status": "success",
      "code": 201,
      "message": "Tag created successfully"
    }
  }
  ```
}

tests {
  test("Status should be 201 Created", function() {
    expect(res.getStatus()).to.equal(201);
  });

  test("Response should have success status", function() {
    const responseJson = res.getBody();
    expect(responseJson.meta).to.be.an('object');
    expect(responseJson.meta.status).to.equal("success");
    expect(responseJson.meta.code).to.equal(201);
  });

  test("Response should contain tag data", function() {
    const responseJson = res.getBody();
    expect(responseJson.data).to.be.an('object');
    expect(responseJson.data.id).to.be.a('number');
    expect(responseJson.data.name).to.equal("React");
  });
}
