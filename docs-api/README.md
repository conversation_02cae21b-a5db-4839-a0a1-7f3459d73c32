# Blog API v1 - <PERSON> Documentation

Đây là tài liệu API sử dụng Bruno cho Blog API v1. T<PERSON>i liệu này bao gồm tất cả các endpoints của các modules chính trong hệ thống.

## Cài đặt và Sử dụng

### 1. <PERSON><PERSON><PERSON> đặt <PERSON>
```bash
# T<PERSON>i <PERSON> từ: https://www.usebruno.com/
# Hoặc cài qua package manager
npm install -g @usebruno/cli
```

### 2. Import Collection
1. Mở Bruno
2. File → Open Collection 
3. Chọn thư mục `docs-api`

### 3. Cấu hình Environment
1. Tạo environment mới trong Bruno
2. Thiết lập các biến:
```
base_url: http://localhost:8080
access_token: your_jwt_token_here
refresh_token: your_refresh_token_here
```

## Modules Overview

### 1. Auth Module (Authentication & Authorization)
- **<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> thực ngườ<PERSON> dùng, qu<PERSON><PERSON> lý token, quản lý users
- **Base Path**: `/api/v1/auth`, `/api/v1/users`
- **Endpoints chính**:
  - Login/Register/Logout
  - Password Reset/Change
  - Profile Management
  - User Management (CRUD)
  - Tenant Management

### 2. Blog Module
- **Mục đích**: Quản lý nội dung blog
- **Base Path**: `/api/admin/v1/blog`, `/api/v1/frontend`
- **Endpoints chính**:
  - Posts Management (CRUD)
  - Categories Management
  - Tags Management
  - Authors Management
  - Blog Blocks & Timelines
  - Related Posts
  - Frontend API (Public)

### 3. Media Module
- **Mục đích**: Quản lý file media (images, videos, documents)
- **Base Path**: `/api/admin/v1/media`
- **Endpoints chính**:
  - File Upload/Download
  - Media Management (CRUD)
  - Folder Management
  - Image Processing
  - Metadata Management

### 4. Notification Module
- **Mục đích**: Hệ thống thông báo đa kênh
- **Base Path**: `/api/admin/v1/notifications`, `/api/admin/v1/notification-users`
- **Endpoints chính**:
  - Notifications Management
  - Templates Management
  - Channels Management
  - User Notifications
  - Mark Read/Unread
  - Push Notifications

## Authentication

Hầu hết các endpoints yêu cầu JWT authentication. 

### Cách sử dụng:
1. Đăng nhập qua endpoint `POST /api/v1/auth/login`
2. Lấy `access_token` từ response
3. Thiết lập token trong environment variable `access_token`
4. Bruno sẽ tự động thêm header `Authorization: Bearer {{access_token}}`

### Token Refresh:
- Khi `access_token` hết hạn, sử dụng endpoint `POST /api/v1/auth/refresh-token`
- Sử dụng `refresh_token` để lấy `access_token` mới

## Permissions

Hệ thống sử dụng RBAC (Role-Based Access Control):
- Mỗi endpoint có permission riêng (ví dụ: `blog.posts.create`)
- User cần có role chứa permission tương ứng
- Permission được kiểm tra bởi middleware

## Response Format

Tất cả API responses đều theo format chuẩn:

### Success Response:
```json
{
  "status": "success",
  "data": {...},
  "meta": {
    "current_page": 1,
    "per_page": 10,
    "total": 100,
    "has_more": true
  }
}
```

### Error Response:
```json
{
  "status": "error",
  "message": "Error description",
  "errors": {
    "field": ["Validation error message"]
  },
  "code": "ERROR_CODE"
}
```

## Pagination

Hệ thống hỗ trợ 2 loại pagination:

### 1. Offset-based (Traditional):
```
?page=1&limit=10
```

### 2. Cursor-based (Recommended):
```
?cursor=eyJpZCI6MTB9&limit=10
```

## File Upload

Endpoints upload file sử dụng `multipart/form-data`:
```javascript
// Body type: multipart-form
{
  file: @file(path/to/file.jpg),
  folder_id: 1,
  alt_text: "Description"
}
```

## Testing

Mỗi endpoint đều có sẵn test cases cơ bản:
- Kiểm tra status code
- Kiểm tra response structure
- Kiểm tra data validity

### Chạy tests:
```bash
# Chạy tất cả tests
bruno run docs-api

# Chạy tests cho module cụ thể
bruno run docs-api/Auth\ Module
```

## Environment Variables

### Required:
- `base_url`: URL base của API server
- `access_token`: JWT access token
- `refresh_token`: JWT refresh token

### Optional:
- `admin_email`: Email admin để test
- `admin_password`: Password admin để test
- `test_user_id`: ID user để test
- `test_post_id`: ID post để test

## Rate Limiting

API có rate limiting:
- **Public endpoints**: 100 requests/minute
- **Authenticated endpoints**: 1000 requests/minute
- **Upload endpoints**: 50 requests/minute

Headers response sẽ bao gồm:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Error Codes

### Common HTTP Status Codes:
- `200`: OK
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Rate Limit Exceeded
- `500`: Internal Server Error

### Custom Error Codes:
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Token expired
- `AUTH_003`: Insufficient permissions
- `BLOG_001`: Post not found
- `MEDIA_001`: File too large
- `NOTIFICATION_001`: Template not found

## Support

Nếu có vấn đề với API:
1. Kiểm tra logs trong Bruno Console
2. Xem response body để biết chi tiết lỗi
3. Kiểm tra environment variables
4. Liên hệ team development

## Updates

Tài liệu này được cập nhật theo:
- Version: v1.0.0
- Last Updated: 2024-01-01
- Modules: Auth, Blog, Media, Notification

---

**Note**: Đây là living document và sẽ được cập nhật liên tục khi có thay đổi API.
