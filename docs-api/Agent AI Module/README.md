# Agent AI Module API Documentation

Module Agent AI cung cấp các API endpoints cho tích hợp với các AI providers, xử lý chat completions và quản lý nội dung được tạo bởi AI.

## Tổng quan

Agent AI Module là một hệ thống tích hợp AI mạnh mẽ cho phép:
- Tích hợp với nhiều AI providers (OpenAI, Anthropic, Google, Azure)
- Thực hiện chat completions với streaming support
- Quản lý và theo dõi nội dung được tạo bởi AI
- Cấu hình và monitoring AI operations

## Endpoints

### 🔧 Configuration Management
- `GET /api/admin/v1/agent-ai/config` - L<PERSON>y cấu hình hiện tại của AI system
- `POST /api/admin/v1/agent-ai/config/provider` - Thiết lập và cấu hình AI provider

### 💬 Chat & AI Completion
- `POST /api/admin/v1/agent-ai/chat/completions` - Thực hiện chat completion với AI
- `POST /api/admin/v1/agent-ai/chat/stream` - Thực hiện chat completion với streaming response real-time

### 📝 Posts Management
- `GET /api/admin/v1/agent-ai/posts` - Lấy danh sách posts được tạo/chỉnh sửa bởi AI
- `GET /api/admin/v1/agent-ai/posts/:id` - Lấy chi tiết post với AI metadata
- `GET /api/admin/v1/agent-ai/posts/:id/status` - Kiểm tra trạng thái generation của post
- `POST /api/admin/v1/agent-ai/posts/:id/cancel` - Hủy quá trình generation đang chạy
- `POST /api/admin/v1/agent-ai/posts/generate` - Tạo blog post mới (queue-based)
- `POST /api/admin/v1/agent-ai/posts/generate/sync` - Tạo blog post mới (synchronous)

### 🛠️ Utilities & Monitoring
- `GET /api/admin/v1/agent-ai/health` - Kiểm tra trạng thái hoạt động của AI service
- `GET /api/admin/v1/agent-ai/mock` - Mock endpoint cho testing và development

## Authentication

Tất cả các endpoints đều yêu cầu authentication thông qua Bearer token:
```
Authorization: Bearer {{access_token}}
```

## Base URL

```
{{api_url}}/api/admin/v1/agent-ai
```

## Supported AI Providers

- **OpenAI** - GPT-3.5, GPT-4, GPT-4 Turbo
- **Anthropic** - Claude 3 Haiku, Claude 3 Sonnet, Claude 3 Opus
- **Google** - Gemini Pro, Gemini Pro Vision
- **Azure OpenAI** - GPT models hosted trên Azure

## Response Format

Tất cả API responses đều tuân theo format chuẩn:
```json
{
  "status": "success|error",
  "data": {},
  "message": "Human readable message",
  "errors": [] // Chỉ có khi status = "error"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Request không hợp lệ |
| 401  | Unauthorized - Không có quyền truy cập |
| 404  | Not Found - Resource không tồn tại |
| 422  | Unprocessable Entity - Validation error |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error - Lỗi server |
| 503  | Service Unavailable - AI provider không khả dụng |

## Rate Limiting

- **Chat Completions**: 100 requests/minute
- **Streaming**: 50 connections đồng thời
- **Configuration**: 10 requests/minute
- **Posts**: 200 requests/minute

## Environment Variables

Để sử dụng Bruno collection này, cần thiết lập các biến môi trường:
- `{{api_url}}` - Base URL của API
- `{{access_token}}` - JWT token để authentication
