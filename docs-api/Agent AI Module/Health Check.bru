meta {
  name: Health Check
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/health
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Health Check
  
  Kiểm tra trạng thái hoạt động của Agent AI module.
  
  ## Description
  Endpoint này dùng để kiểm tra xem Agent AI service có đang hoạt động bình thường hay không.
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "service": "agent-ai",
      "status": "healthy",
      "timestamp": "2025-06-28T10:30:00Z"
    },
    "message": "Agent AI service is healthy"
  }
  ```
  
  ## Status Codes
  - `200 OK` - Service đang hoạt động bình thường
  - `503 Service Unavailable` - Service không khả dụng
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should have success status", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
  });
}
