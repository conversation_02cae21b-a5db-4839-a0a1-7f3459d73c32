meta {
  name: Mock Data
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/mock
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Mock Data
  
  Endpoint mock dùng để testing và development.
  
  ## Description
  T<PERSON><PERSON> về dữ liệu mock để test các tính năng của Agent AI module mà không cần gọi thực tế đến AI providers.
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "message": "This is mock data for Agent AI testing",
      "providers": [
        "openai",
        "anthropic",
        "google"
      ],
      "sample_response": {
        "content": "This is a sample AI response for testing purposes",
        "model": "mock-model",
        "usage": {
          "prompt_tokens": 10,
          "completion_tokens": 15,
          "total_tokens": 25
        }
      }
    },
    "message": "Mock data retrieved successfully"
  }
  ```
  
  ## Status Codes
  - `200 OK` - Mock data trả về thành công
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain mock data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.message).to.be.a("string");
  });
}
