meta {
  name: Get Post Status
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/posts/1/status
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Post Status
  
  Kiểm tra trạng thái generation/processing của một post cụ thể.
  
  ## Description
  Endpoint này cho phép theo dõi trạng thái của quá trình tạo nội dung bởi AI, đặc biệt hữu ích cho các tác vụ chạy background hoặc queue-based generation.
  
  ## Path Parameters
  | Parameter | Type    | Required | Description       |
  |-----------|---------|----------|-------------------|
  | id        | integer | Yes      | ID của post       |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "post_id": 1,
      "generation_status": "processing",
      "progress": {
        "current_step": "content_generation",
        "total_steps": 5,
        "percentage": 60,
        "estimated_remaining": "2 minutes"
      },
      "job_details": {
        "job_id": "job_123456789",
        "queue": "ai_generation",
        "started_at": "2025-06-28T10:30:00Z",
        "processing_time": 180,
        "retry_count": 0,
        "max_retries": 3
      },
      "current_operation": {
        "step": "content_generation",
        "description": "Đang tạo nội dung chính của bài viết",
        "ai_provider": "openai",
        "model": "gpt-3.5-turbo",
        "tokens_used": 1250
      },
      "result": null,
      "error": null,
      "timestamps": {
        "queued_at": "2025-06-28T10:28:00Z",
        "started_at": "2025-06-28T10:30:00Z",
        "updated_at": "2025-06-28T10:33:00Z",
        "estimated_completion": "2025-06-28T10:35:00Z"
      }
    },
    "message": "Post status retrieved successfully"
  }
  ```
  
  ## Status Values
  | Status      | Description                                    |
  |-------------|------------------------------------------------|
  | queued      | Đang chờ trong queue                           |
  | processing  | Đang xử lý/tạo nội dung                        |
  | completed   | Hoàn thành thành công                          |
  | failed      | Thất bại, có lỗi xảy ra                        |
  | cancelled   | Đã bị hủy bởi user                             |
  | paused      | Tạm dừng (có thể resume)                       |
  
  ## Processing Steps
  1. **validation** - Kiểm tra input parameters
  2. **preparation** - Chuẩn bị prompt và context
  3. **content_generation** - Tạo nội dung chính
  4. **seo_optimization** - Tối ưu SEO (title, meta, keywords)
  5. **finalization** - Hoàn thiện và lưu post
  
  ## Status Codes
  - `200 OK` - Trạng thái được lấy thành công
  - `404 Not Found` - Post không tồn tại
  - `401 Unauthorized` - Không có quyền truy cập
  - `500 Internal Server Error` - Lỗi server
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain status data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.post_id).to.be.a("number");
    expect(data.data.generation_status).to.be.a("string");
  });
  
  test("Progress should have percentage", function() {
    const data = res.getBody();
    if (data.data.progress) {
      expect(data.data.progress.percentage).to.be.a("number");
      expect(data.data.progress.percentage).to.be.at.least(0);
      expect(data.data.progress.percentage).to.be.at.most(100);
    }
  });
}
