meta {
  name: Generate Blog (Queue)
  type: http
  seq: 11
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/posts/generate
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "topic": "AI and Web Development",
    "description": "Comprehensive guide about AI in web development",
    "keywords": ["AI", "web development", "automation", "future technology"],
    "model": "gpt-3.5-turbo",
    "language": "vi",
    "tone": "professional",
    "wordCount": 1500,
    "temperature": 0.7
  }
}

tests {
  test("Status should be 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("Response should contain job information", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.job_id).to.be.a("string");
    expect(data.data.post_id).to.be.a("number");
  });
  
  test("Should have queue information", function() {
    const data = res.getBody();
    expect(data.data.queue_info).to.be.an("object");
    expect(data.data.queue_info.position).to.be.a("number");
  });
  
  test("Should have tracking URLs", function() {
    const data = res.getBody();
    expect(data.data.tracking).to.be.an("object");
    expect(data.data.tracking.status_url).to.be.a("string");
    expect(data.data.tracking.cancel_url).to.be.a("string");
  });
}

docs {
  # Generate Blog Post (Queue-based)
  
  Tạo blog post mới bằng AI với xử lý background queue.
  
  ## Description
  Endpoint này khởi tạo quá trình tạo blog post bằng AI và đưa vào queue để xử lý background. Phù hợp cho các tác vụ phức tạp hoặc khi không cần kết quả ngay lập tức.
  
  ## Request Body
  | Field            | Type     | Required | Description                                    |
  |------------------|----------|----------|------------------------------------------------|
  | title            | string   | No       | Tiêu đề bài viết (AI sẽ tạo nếu không có)     |
  | topic            | string   | Yes      | Chủ đề chính của bài viết                      |
  | keywords         | array    | No       | Danh sách keywords cho SEO                     |
  | category_id      | integer  | Yes      | ID category cho post                           |
  | tag_ids          | array    | No       | Danh sách tag IDs                              |
  | target_audience  | string   | No       | Đối tượng mục tiêu                             |
  | content_length   | string   | No       | Độ dài nội dung (short, medium, long)          |
  | tone             | string   | No       | Tone của bài viết (professional, casual, etc)  |
  | language         | string   | No       | Ngôn ngữ (vi, en) - default: vi               |
  | seo_focus        | boolean  | No       | Có tối ưu SEO không - default: true           |
  | include_images   | boolean  | No       | Có tạo/suggest images không - default: false   |
  | priority         | string   | No       | Độ ưu tiên (low, normal, high) - default: normal |
  
  ### Settings Object
  | Field       | Type   | Required | Description                          |
  |-------------|--------|----------|--------------------------------------|
  | provider    | string | No       | AI provider (override default)       |
  | model       | string | No       | Model cụ thể                         |
  | temperature | float  | No       | Độ sáng tạo (0.0 - 1.0)              |
  | max_tokens  | int    | No       | Số token tối đa                      |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "post_id": 123,
      "job_id": "job_123456789",
      "generation_status": "queued",
      "queue_info": {
        "queue_name": "ai_generation",
        "position": 3,
        "estimated_wait_time": "5 minutes",
        "priority": "normal"
      },
      "job_details": {
        "created_at": "2025-06-28T10:30:00Z",
        "estimated_completion": "2025-06-28T10:40:00Z",
        "retry_count": 0,
        "max_retries": 3
      },
      "request_summary": {
        "topic": "AI and Web Development",
        "content_length": "medium",
        "language": "vi",
        "estimated_tokens": 1500,
        "estimated_cost": 0.030
      },
      "tracking": {
        "status_url": "/api/admin/v1/agent-ai/posts/123/status",
        "cancel_url": "/api/admin/v1/agent-ai/posts/123/cancel"
      }
    },
    "message": "Blog generation job queued successfully"
  }
  ```
  
  ## Content Length Options
  | Value  | Description              | Estimated Words | Estimated Tokens |
  |--------|--------------------------|-----------------|------------------|
  | short  | Bài viết ngắn            | 300-500         | 400-700          |
  | medium | Bài viết trung bình      | 800-1200        | 1000-1600        |
  | long   | Bài viết dài             | 1500-2500       | 2000-3500        |
  
  ## Tone Options
  - **professional** - Chuyên nghiệp, formal
  - **casual** - Thân thiện, informal
  - **technical** - Kỹ thuật, chi tiết
  - **educational** - Giáo dục, hướng dẫn
  - **marketing** - Tiếp thị, thuyết phục
  
  ## Priority Levels
  | Priority | Description                    | Queue Position |
  |----------|--------------------------------|----------------|
  | low      | Xử lý khi rảnh                 | Cuối queue     |
  | normal   | Xử lý bình thường              | Theo thứ tự    |
  | high     | Ưu tiên xử lý                  | Đầu queue      |
  
  ## Workflow
  1. **Validation** - Kiểm tra input parameters
  2. **Queue** - Đưa job vào processing queue
  3. **Processing** - AI tạo nội dung
  4. **SEO Optimization** - Tối ưu title, meta, keywords
  5. **Finalization** - Lưu post và notify completion
  
  ## Status Codes
  - `201 Created` - Job được tạo và queue thành công
  - `400 Bad Request` - Request parameters không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `422 Unprocessable Entity` - Validation error
  - `429 Too Many Requests` - Queue đầy hoặc rate limit
  - `500 Internal Server Error` - Lỗi server
}
