meta {
  name: Chat Stream
  type: http
  seq: 6
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/chat/stream
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
  Accept: text/event-stream
  Cache-Control: no-cache
}

body:json {
  {
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "Write a short story about a robot learning to paint."
      }
    ],
    "model": "gpt-3.5-turbo",
    "temperature": 0.8,
    "max_tokens": 500,
    "stream": true
  }
}

docs {
  # Chat Stream
  
  Thực hiện chat completion với streaming response real-time.
  
  ## Description
  Endpoint này tương tự như chat/completions nhưng trả về response dưới dạng Server-Sent Events (SSE) để có thể hiển thị kết quả real-time khi AI đang tạo nội dung.
  
  ## Request Body
  | Field       | Type    | Required | Description                                    |
  |-------------|---------|----------|------------------------------------------------|
  | messages    | array   | Yes      | Mảng các tin nhắn trong cuộc trò chuyện       |
  | model       | string  | No       | Model cụ thể (override default)               |
  | temperature | float   | No       | Độ sáng tạo (0.0 - 1.0)                       |
  | max_tokens  | int     | No       | Số token tối đa cho response                   |
  | stream      | boolean | No       | Luôn là true cho endpoint này                  |
  
  ### Messages Array
  Mỗi message object chứa:
  | Field   | Type   | Required | Description                              |
  |---------|--------|----------|------------------------------------------|
  | role    | string | Yes      | Vai trò (system, user, assistant)       |
  | content | string | Yes      | Nội dung tin nhắn                        |
  
  ## Response (Server-Sent Events)
  Response được trả về dưới dạng SSE stream với format:
  
  ```
  Content-Type: text/event-stream
  Cache-Control: no-cache
  Connection: keep-alive
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Once"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":" upon"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":" a"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":" time"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}
  
  data: [DONE]
  ```
  
  ### Chunk Object Structure
  | Field   | Type   | Description                                    |
  |---------|--------|------------------------------------------------|
  | id      | string | ID duy nhất của completion                     |
  | object  | string | Luôn là "chat.completion.chunk"                |
  | created | int    | Timestamp tạo                                  |
  | model   | string | Model đã sử dụng                               |
  | choices | array  | Mảng các lựa chọn completion                   |
  
  ### Choice Object Structure
  | Field         | Type   | Description                                |
  |---------------|--------|--------------------------------------------|
  | index         | int    | Index của choice                           |
  | delta         | object | Phần nội dung mới trong chunk này         |
  | finish_reason | string | Lý do kết thúc (null, "stop", "length")   |
  
  ### Delta Object Structure
  | Field   | Type   | Description                               |
  |---------|--------|-------------------------------------------|
  | role    | string | Vai trò (chỉ có trong chunk đầu tiên)     |
  | content | string | Nội dung text mới                         |
  
  ## Sử dụng với JavaScript
  ```javascript
  const eventSource = new EventSource('/api/admin/v1/agent-ai/chat/stream', {
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  
  eventSource.onmessage = function(event) {
    if (event.data === '[DONE]') {
      eventSource.close();
      return;
    }
    
    const chunk = JSON.parse(event.data);
    const content = chunk.choices[0]?.delta?.content;
    if (content) {
      // Append content to UI
      displayContent += content;
    }
  };
  
  eventSource.onerror = function(error) {
    console.error('SSE error:', error);
    eventSource.close();
  };
  ```
  
  ## Status Codes
  - `200 OK` - Stream bắt đầu thành công
  - `400 Bad Request` - Request không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `422 Unprocessable Entity` - Validation error
  - `429 Too Many Requests` - Rate limit exceeded
  - `500 Internal Server Error` - Lỗi server
  - `503 Service Unavailable` - AI provider không khả dụng
  
  ## Lưu ý
  - Response sẽ có Content-Type: text/event-stream
  - Connection sẽ được giữ mở cho đến khi stream hoàn thành
  - Client cần xử lý SSE events để nhận dữ liệu real-time
  - Stream sẽ kết thúc với message "data: [DONE]"
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Content-Type should be text/event-stream", function() {
    expect(res.getHeader("Content-Type")).to.include("text/event-stream");
  });
  
  test("Should have Cache-Control no-cache header", function() {
    expect(res.getHeader("Cache-Control")).to.include("no-cache");
  });
}
