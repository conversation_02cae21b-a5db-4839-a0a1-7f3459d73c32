meta {
  name: Get Posts
  type: http
  seq: 7
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/posts
  query: {
    page: 1,
    limit: 10,
    search: "",
    sort: created_at,
    order: desc,
    status: all
  },
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Posts
  
  Lấy danh sách các posts được xử lý bởi Agent AI.
  
  ## Description
  Endpoint này trả về danh sách các blog posts mà Agent AI đã tham gia tạo hoặc chỉnh sửa, với khả năng filter và pagination.
  
  ## Query Parameters
  | Parameter | Type    | Required | Default    | Description                                       |
  |-----------|---------|----------|------------|---------------------------------------------------|
  | page      | integer | No       | 1          | Số trang cho pagination                           |
  | limit     | integer | No       | 10         | Số items mỗi trang (max 100)                     |
  | search    | string  | No       | ""         | Tìm kiếm theo title hoặc content                  |
  | sort      | string  | No       | created_at | Sắp xếp theo field (created_at, updated_at, title) |
  | order     | string  | No       | desc       | Thứ tự sắp xếp (asc, desc)                       |
  | status    | string  | No       | all        | Filter theo status (all, draft, published, pending) |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "posts": [
        {
          "id": 1,
          "title": "AI-Generated Article: The Future of Technology",
          "slug": "ai-generated-article-future-technology",
          "content": "Content generated by AI...",
          "excerpt": "Brief excerpt...",
          "status": "published",
          "ai_metadata": {
            "provider": "openai",
            "model": "gpt-3.5-turbo",
            "generated_at": "2025-06-28T10:30:00Z",
            "tokens_used": 1250,
            "confidence_score": 0.92
          },
          "author": {
            "id": 1,
            "name": "AI Assistant",
            "email": "<EMAIL>"
          },
          "category": {
            "id": 1,
            "name": "Technology",
            "slug": "technology"
          },
          "tags": [
            {
              "id": 1,
              "name": "AI",
              "slug": "ai"
            },
            {
              "id": 2,
              "name": "Future",
              "slug": "future"
            }
          ],
          "created_at": "2025-06-28T10:30:00Z",
          "updated_at": "2025-06-28T10:35:00Z"
        }
      ],
      "pagination": {
        "current_page": 1,
        "total_pages": 5,
        "total_items": 50,
        "items_per_page": 10,
        "has_next": true,
        "has_prev": false
      }
    },
    "message": "Posts retrieved successfully"
  }
  ```
  
  ## Status Codes
  - `200 OK` - Danh sách posts được lấy thành công
  - `400 Bad Request` - Query parameters không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `500 Internal Server Error` - Lỗi server
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain posts array", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.posts).to.be.an("array");
  });
  
  test("Response should contain pagination info", function() {
    const data = res.getBody();
    expect(data.data.pagination).to.be.an("object");
    expect(data.data.pagination.current_page).to.be.a("number");
    expect(data.data.pagination.total_items).to.be.a("number");
  });
}
