meta {
  name: Generate Blog (Sync)
  type: http
  seq: 12
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/posts/generate/sync
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "title": "Quick Guide to Modern JavaScript Frameworks",
    "topic": "JavaScript Frameworks Comparison",
    "keywords": ["javascript", "react", "vue", "angular", "frameworks"],
    "category_id": 2,
    "tag_ids": [4, 5, 6],
    "target_audience": "developers",
    "content_length": "short",
    "tone": "educational",
    "language": "vi",
    "seo_focus": true,
    "include_images": false,
    "settings": {
      "provider": "openai",
      "model": "gpt-3.5-turbo",
      "temperature": 0.6,
      "max_tokens": 1000
    }
  }
}

docs {
  # Generate Blog Post (Synchronous)
  
  Tạo blog post mới bằng AI với xử lý synchronous (real-time).
  
  ## Description
  Endpoint này tạo blog post ngay lập tức và trả về kết quả trong response. <PERSON><PERSON> hợp cho các bài viết ngắn hoặc khi cần kết quả ngay lập tức. Request sẽ block cho đến khi generation hoàn thành.
  
  ## ⚠️ Lưu ý quan trọng
  - **Timeout**: Request có thể mất 30-120 giây tùy vào độ dài content
  - **Blocking**: Client sẽ chờ cho đến khi generation hoàn thành
  - **Phù hợp**: Chỉ cho content ngắn (short/medium length)
  - **Không phù hợp**: Content dài hoặc bulk generation
  
  ## Request Body
  Tương tự như queue-based generation, nhưng khuyến nghị:
  | Field            | Recommended Value     | Lý do                              |
  |------------------|-----------------------|------------------------------------|
  | content_length   | "short" hoặc "medium" | Giảm thời gian xử lý               |
  | max_tokens       | ≤ 1500                | Tránh timeout                      |
  | include_images   | false                 | Image generation tốn thời gian     |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "post": {
        "id": 124,
        "title": "Quick Guide to Modern JavaScript Frameworks",
        "slug": "quick-guide-modern-javascript-frameworks",
        "content": "Full generated content here...",
        "excerpt": "Auto-generated excerpt...",
        "status": "draft",
        "meta_title": "JavaScript Frameworks Guide 2025",
        "meta_description": "Complete comparison of React, Vue, Angular...",
        "featured_image": null,
        "category": {
          "id": 2,
          "name": "Programming",
          "slug": "programming"
        },
        "tags": [
          {
            "id": 4,
            "name": "JavaScript",
            "slug": "javascript"
          }
        ],
        "author": {
          "id": 1,
          "name": "AI Assistant",
          "email": "<EMAIL>"
        }
      },
      "ai_metadata": {
        "provider": "openai",
        "model": "gpt-3.5-turbo",
        "prompt_used": "Write an educational article about...",
        "generation_time": 45.2,
        "tokens_used": 892,
        "cost": 0.018,
        "confidence_score": 0.89,
        "quality_metrics": {
          "readability_score": 85,
          "seo_score": 78,
          "keyword_density": 2.3,
          "content_length": 654
        }
      },
      "generation_stats": {
        "started_at": "2025-06-28T10:30:00Z",
        "completed_at": "2025-06-28T10:30:45Z",
        "total_time": 45.2,
        "steps_completed": [
          "validation",
          "content_generation", 
          "seo_optimization",
          "quality_check",
          "finalization"
        ]
      }
    },
    "message": "Blog post generated successfully"
  }
  ```
  
  ## Performance Expectations
  | Content Length | Est. Time | Max Tokens | Timeout |
  |----------------|-----------|------------|---------|
  | short          | 15-30s    | 500-800    | 60s     |
  | medium         | 30-60s    | 1000-1500  | 120s    |
  | long           | 60-120s   | 2000+      | 180s    |
  
  ## Quality Metrics
  | Metric              | Description                    | Score Range |
  |---------------------|--------------------------------|-------------|
  | readability_score   | Độ dễ đọc của content          | 0-100       |
  | seo_score          | Điểm SEO tổng thể              | 0-100       |
  | keyword_density    | Mật độ keyword chính           | 0-10%       |
  | confidence_score   | Độ tin cậy của AI              | 0.0-1.0     |
  
  ## Best Practices
  1. **Sử dụng cho**: Content ngắn, quick prototyping, testing
  2. **Tránh sử dụng cho**: Content dài, bulk generation, production scale
  3. **Optimize**: Giảm max_tokens, content_length ngắn
  4. **Timeout handling**: Implement proper timeout trên client
  
  ## Error Handling
  ```json
  {
    "status": "error",
    "errors": [
      {
        "code": "GENERATION_TIMEOUT",
        "message": "Content generation took too long",
        "details": {
          "timeout_after": 120,
          "partial_content": "Generated content so far..."
        }
      }
    ]
  }
  ```
  
  ## Status Codes
  - `200 OK` - Post được tạo thành công
  - `400 Bad Request` - Request parameters không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `408 Request Timeout` - Generation timeout
  - `422 Unprocessable Entity` - Validation error
  - `429 Too Many Requests` - Rate limit exceeded
  - `500 Internal Server Error` - Lỗi server
  - `503 Service Unavailable` - AI provider không khả dụng
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain complete post", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.post).to.be.an("object");
    expect(data.data.post.id).to.be.a("number");
    expect(data.data.post.content).to.be.a("string");
  });
  
  test("Should have AI metadata", function() {
    const data = res.getBody();
    expect(data.data.ai_metadata).to.be.an("object");
    expect(data.data.ai_metadata.generation_time).to.be.a("number");
    expect(data.data.ai_metadata.tokens_used).to.be.a("number");
  });
  
  test("Should have quality metrics", function() {
    const data = res.getBody();
    expect(data.data.ai_metadata.quality_metrics).to.be.an("object");
    expect(data.data.ai_metadata.quality_metrics.readability_score).to.be.a("number");
  });
}
