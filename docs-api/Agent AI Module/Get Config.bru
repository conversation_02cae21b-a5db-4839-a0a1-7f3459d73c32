meta {
  name: Get Config
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/config
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Configuration
  
  L<PERSON>y cấu hình hiện tại của Agent AI module.
  
  ## Description
  Endpoint này trả về thông tin cấu hình hiện tại bao gồm AI provider đang được sử dụng, API keys, và các settings khác.
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "current_provider": "openai",
      "available_providers": [
        "openai",
        "anthropic",
        "google",
        "azure"
      ],
      "settings": {
        "default_model": "gpt-3.5-turbo",
        "max_tokens": 2048,
        "temperature": 0.7,
        "timeout": 30
      },
      "api_keys_configured": {
        "openai": true,
        "anthropic": false,
        "google": false,
        "azure": false
      }
    },
    "message": "Configuration retrieved successfully"
  }
  ```
  
  ## Status Codes
  - `200 OK` - C<PERSON>u hình đư<PERSON> l<PERSON>y thành công
  - `401 Unauthorized` - Không có quyền truy cập
  - `500 Internal Server Error` - Lỗi server
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain configuration", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.current_provider).to.be.a("string");
    expect(data.data.available_providers).to.be.an("array");
  });
}
