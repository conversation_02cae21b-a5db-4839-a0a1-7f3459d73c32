meta {
  name: Chat Completion
  type: http
  seq: 5
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/chat/completions
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "messages": [
      {
        "role": "system",
        "content": "You are a helpful assistant."
      },
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "model": "gpt-3.5-turbo",
    "temperature": 0.7,
    "max_tokens": 150,
    "stream": false
  }
}

docs {
  # Chat Completion
  
  Thực hiện chat completion với AI provider được cấu hình.
  
  ## Description
  Endpoint này gửi tin nhắn đến AI provider và trả về phản hồi. Hỗ trợ cả streaming và non-streaming responses.
  
  ## Request Body
  | Field       | Type    | Required | Description                                    |
  |-------------|---------|----------|------------------------------------------------|
  | messages    | array   | Yes      | Mảng các tin nhắn trong cuộc trò chuyện       |
  | model       | string  | No       | Model cụ thể (override default)               |
  | temperature | float   | No       | Độ sáng tạo (0.0 - 1.0)                       |
  | max_tokens  | int     | No       | Số token tối đa cho response                   |
  | stream      | boolean | No       | Có sử dụng streaming response hay không       |
  
  ### Messages Array
  Mỗi message object chứa:
  | Field   | Type   | Required | Description                              |
  |---------|--------|----------|------------------------------------------|
  | role    | string | Yes      | Vai trò (system, user, assistant)       |
  | content | string | Yes      | Nội dung tin nhắn                        |
  
  ## Response (Non-streaming)
  ```json
  {
    "status": "success",
    "data": {
      "id": "chatcmpl-abc123",
      "object": "chat.completion",
      "created": **********,
      "model": "gpt-3.5-turbo",
      "choices": [
        {
          "index": 0,
          "message": {
            "role": "assistant",
            "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
          },
          "finish_reason": "stop"
        }
      ],
      "usage": {
        "prompt_tokens": 20,
        "completion_tokens": 18,
        "total_tokens": 38
      }
    },
    "message": "Chat completion successful"
  }
  ```
  
  ## Response (Streaming)
  Khi `stream: true`, response sẽ được trả về dưới dạng Server-Sent Events (SSE):
  ```
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}
  
  data: {"id":"chatcmpl-abc123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}
  
  data: [DONE]
  ```
  
  ## Status Codes
  - `200 OK` - Chat completion thành công
  - `400 Bad Request` - Request không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `422 Unprocessable Entity` - Validation error
  - `429 Too Many Requests` - Rate limit exceeded
  - `500 Internal Server Error` - Lỗi server
  - `503 Service Unavailable` - AI provider không khả dụng
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain completion data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.choices).to.be.an("array");
    expect(data.data.choices.length).to.be.greaterThan(0);
  });
  
  test("Response should have usage information", function() {
    const data = res.getBody();
    expect(data.data.usage).to.be.an("object");
    expect(data.data.usage.total_tokens).to.be.a("number");
  });
}
