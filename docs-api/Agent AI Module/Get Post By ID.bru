meta {
  name: Get Post By ID
  type: http
  seq: 8
}

get {
  url: {{api_url}}/api/admin/v1/agent-ai/posts/1
  body: none
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

docs {
  # Get Post By ID
  
  Lấy thông tin chi tiết của một post cụ thể được xử lý bởi Agent AI.
  
  ## Description
  Endpoint này trả về thông tin đầy đủ của một blog post bao gồm metadata về việc AI generation, editing history, và analytics.
  
  ## Path Parameters
  | Parameter | Type    | Required | Description       |
  |-----------|---------|----------|-------------------|
  | id        | integer | Yes      | ID của post       |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "post": {
        "id": 1,
        "title": "AI-Generated Article: The Future of Technology",
        "slug": "ai-generated-article-future-technology",
        "content": "Full content of the article generated by AI...",
        "excerpt": "Brief excerpt generated automatically...",
        "status": "published",
        "meta_title": "The Future of Technology - AI Insights",
        "meta_description": "Explore the future of technology through AI-generated insights...",
        "featured_image": "/uploads/ai-tech-future.jpg",
        "ai_metadata": {
          "provider": "openai",
          "model": "gpt-3.5-turbo",
          "prompt_used": "Write an article about the future of technology...",
          "generated_at": "2025-06-28T10:30:00Z",
          "tokens_used": 1250,
          "confidence_score": 0.92,
          "generation_time": 8.5,
          "iterations": 1,
          "human_edited": false
        },
        "author": {
          "id": 1,
          "name": "AI Assistant",
          "email": "<EMAIL>",
          "avatar": "/uploads/ai-avatar.jpg"
        },
        "category": {
          "id": 1,
          "name": "Technology",
          "slug": "technology",
          "description": "Tech-related articles"
        },
        "tags": [
          {
            "id": 1,
            "name": "AI",
            "slug": "ai"
          },
          {
            "id": 2,
            "name": "Future",
            "slug": "future"
          },
          {
            "id": 3,
            "name": "Technology",
            "slug": "technology"
          }
        ],
        "analytics": {
          "views": 1250,
          "shares": 45,
          "likes": 89,
          "comments_count": 12,
          "engagement_rate": 0.068
        },
        "seo_score": {
          "overall": 85,
          "readability": 92,
          "keyword_density": 78,
          "meta_optimization": 88
        },
        "edit_history": [
          {
            "id": 1,
            "action": "created",
            "by": "AI Assistant",
            "at": "2025-06-28T10:30:00Z",
            "changes": "Initial AI generation"
          }
        ],
        "published_at": "2025-06-28T11:00:00Z",
        "created_at": "2025-06-28T10:30:00Z",
        "updated_at": "2025-06-28T10:35:00Z"
      }
    },
    "message": "Post retrieved successfully"
  }
  ```
  
  ## Status Codes
  - `200 OK` - Post được lấy thành công
  - `404 Not Found` - Post không tồn tại
  - `401 Unauthorized` - Không có quyền truy cập
  - `500 Internal Server Error` - Lỗi server
  
  ## AI Metadata Fields
  | Field           | Type    | Description                                    |
  |-----------------|---------|------------------------------------------------|
  | provider        | string  | AI provider đã sử dụng (openai, anthropic...)  |
  | model           | string  | Model cụ thể đã sử dụng                        |
  | prompt_used     | string  | Prompt gốc đã gửi cho AI                       |
  | generated_at    | string  | Thời gian tạo content                          |
  | tokens_used     | integer | Số tokens đã sử dụng                           |
  | confidence_score| float   | Điểm tin cậy của AI (0.0 - 1.0)               |
  | generation_time | float   | Thời gian generation (giây)                    |
  | iterations      | integer | Số lần iteration để tạo content                |
  | human_edited    | boolean | Có được con người chỉnh sửa sau đó không       |
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should contain post data", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.post).to.be.an("object");
    expect(data.data.post.id).to.be.a("number");
  });
  
  test("Post should have AI metadata", function() {
    const data = res.getBody();
    expect(data.data.post.ai_metadata).to.be.an("object");
    expect(data.data.post.ai_metadata.provider).to.be.a("string");
    expect(data.data.post.ai_metadata.tokens_used).to.be.a("number");
  });
  
  test("Post should have analytics data", function() {
    const data = res.getBody();
    expect(data.data.post.analytics).to.be.an("object");
    expect(data.data.post.analytics.views).to.be.a("number");
  });
}
