meta {
  name: Set Provider
  type: http
  seq: 4
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/config/provider
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "provider": "openai",
    "api_key": "your-api-key-here",
    "settings": {
      "model": "gpt-3.5-turbo",
      "temperature": 0.7,
      "max_tokens": 2048
    }
  }
}

docs {
  # Set Provider
  
  Thiết lập AI provider và cấu hình cho Agent AI module.
  
  ## Description
  Endpoint này cho phép thay đổi AI provider hiện tại và cập nhật các cấu hình liên quan.
  
  ## Request Body
  | Field    | Type   | Required | Description                           |
  |----------|--------|----------|---------------------------------------|
  | provider | string | Yes      | Tên provider (openai, anthropic, etc) |
  | api_key  | string | Yes      | API key cho provider                  |
  | settings | object | No       | C<PERSON><PERSON> cấu hình bổ sung                  |
  
  ### Settings Object
  | Field       | Type   | Required | Default | Description              |
  |-------------|--------|----------|---------|--------------------------|
  | model       | string | No       | default | Model cụ thể để sử dụng  |
  | temperature | float  | No       | 0.7     | Độ sáng tạo (0.0 - 1.0)  |
  | max_tokens  | int    | No       | 2048    | Số token tối đa          |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "provider": "openai",
      "model": "gpt-3.5-turbo",
      "settings": {
        "temperature": 0.7,
        "max_tokens": 2048
      }
    },
    "message": "Provider configuration updated successfully"
  }
  ```
  
  ## Status Codes
  - `200 OK` - Cấu hình được cập nhật thành công
  - `400 Bad Request` - Dữ liệu không hợp lệ
  - `401 Unauthorized` - Không có quyền truy cập
  - `422 Unprocessable Entity` - Validation error
  - `500 Internal Server Error` - Lỗi server
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should confirm provider update", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.provider).to.be.a("string");
  });
}
