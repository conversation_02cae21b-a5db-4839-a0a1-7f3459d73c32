meta {
  name: Cancel Post Generation
  type: http
  seq: 10
}

post {
  url: {{api_url}}/api/admin/v1/agent-ai/posts/1/cancel
  body: json
  auth: none
}

headers {
  Authorization: Bearer {{access_token}}
  Content-Type: application/json
}

body:json {
  {
    "reason": "User requested cancellation",
    "force": false
  }
}

docs {
  # Cancel Post Generation
  
  Hủy quá trình generation đang chạy của một post.
  
  ## Description
  Endpoint này cho phép dừng quá trình tạo nội dung AI đang được xử lý. Hữu ích khi user muốn dừng generation do thay đổi yêu cầu hoặc để tiết kiệm resources.
  
  ## Path Parameters
  | Parameter | Type    | Required | Description       |
  |-----------|---------|----------|-------------------|
  | id        | integer | Yes      | ID của post       |
  
  ## Request Body
  | Field  | Type    | Required | Description                                      |
  |--------|---------|----------|--------------------------------------------------|
  | reason | string  | No       | Lý do hủy generation                             |
  | force  | boolean | No       | Có force cancel ngay lập tức không (default: false) |
  
  ## Response
  ```json
  {
    "status": "success",
    "data": {
      "post_id": 1,
      "cancellation_status": "cancelled",
      "job_details": {
        "job_id": "job_123456789",
        "previous_status": "processing",
        "cancelled_at": "2025-06-28T10:35:00Z",
        "processing_time": 300,
        "completion_percentage": 75
      },
      "partial_result": {
        "content_generated": true,
        "seo_optimized": false,
        "draft_saved": true,
        "draft_id": 456
      },
      "resources_used": {
        "tokens_consumed": 1250,
        "processing_time": 300,
        "estimated_cost": 0.025
      },
      "cancellation_reason": "User requested cancellation"
    },
    "message": "Post generation cancelled successfully"
  }
  ```
  
  ## Cancellation Status Values
  | Status        | Description                                    |
  |---------------|------------------------------------------------|
  | cancelled     | Đã hủy thành công                              |
  | cancelling    | Đang trong quá trình hủy                       |
  | cannot_cancel | Không thể hủy (đã hoàn thành hoặc failed)      |
  | force_cancelled | Đã force cancel ngay lập tức                 |
  
  ## Force Cancel
  Khi `force: true`:
  - Dừng ngay lập tức mà không chờ cleanup
  - Có thể mất một phần dữ liệu đã xử lý
  - Sử dụng khi cần dừng khẩn cấp
  
  Khi `force: false` (default):
  - Graceful shutdown, chờ current operation hoàn thành
  - Lưu partial results nếu có thể
  - An toàn hơn cho dữ liệu
  
  ## Partial Results
  Khi generation bị cancel, system sẽ cố gắng lưu:
  - **Draft content** - Nội dung đã tạo được (nếu có)
  - **Metadata** - Thông tin AI đã xử lý
  - **Progress state** - Trạng thái tại thời điểm cancel
  
  ## Status Codes
  - `200 OK` - Cancellation thành công
  - `400 Bad Request` - Không thể cancel (post đã hoàn thành)
  - `404 Not Found` - Post không tồn tại
  - `401 Unauthorized` - Không có quyền truy cập
  - `409 Conflict` - Post không trong trạng thái có thể cancel
  - `500 Internal Server Error` - Lỗi server
}

tests {
  test("Status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("Response should confirm cancellation", function() {
    const data = res.getBody();
    expect(data.status).to.equal("success");
    expect(data.data.cancellation_status).to.be.oneOf(["cancelled", "cancelling", "force_cancelled"]);
  });
  
  test("Should have job details", function() {
    const data = res.getBody();
    expect(data.data.job_details).to.be.an("object");
    expect(data.data.job_details.job_id).to.be.a("string");
  });
}
