meta {
  name: Update Theme
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/website/themes/{{theme_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "name": "Updated Theme Name",
    "description": "Updated theme description",
    "version": "1.1.0",
    "author": "Updated Author",
    "is_active": true,
    "is_public": true,
    "config": {
      "primary_color": "#28a745",
      "secondary_color": "#dc3545"
    }
  }
}

vars {
  theme_id: 1
}