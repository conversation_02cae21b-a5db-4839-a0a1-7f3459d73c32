meta {
  name: Create Theme
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/website/themes
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "name": "Sample Theme",
    "description": "A sample theme for testing",
    "version": "1.0.0",
    "author": "Developer",
    "is_active": true,
    "is_public": false,
    "config": {
      "primary_color": "#007bff",
      "secondary_color": "#6c757d"
    }
  }
}