meta {
  name: Update Page
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/website/website-pages/{{website_id}}/page/{{page_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "title": "Updated Page Title",
    "slug": "updated-page-slug",
    "content": "This is updated page content",
    "meta_title": "Updated Page - Meta Title",
    "meta_description": "Updated page meta description",
    "is_published": true,
    "is_homepage": false
  }
}

vars {
  website_id: 1
  page_id: 1
}