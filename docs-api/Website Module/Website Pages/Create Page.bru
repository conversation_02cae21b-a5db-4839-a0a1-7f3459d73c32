meta {
  name: Create Page
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/website/website-pages/{{website_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "title": "Sample Page",
    "slug": "sample-page",
    "content": "This is a sample page content",
    "meta_title": "Sample Page - Meta Title",
    "meta_description": "Sample page meta description",
    "is_published": true,
    "is_homepage": false
  }
}

vars {
  website_id: 1
}