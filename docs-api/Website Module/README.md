# Website Module API Documentation

Module Website cung cấp các API để quản lý websites, pages, themes và templates.

## Endpoints Overview

### Health Check
- `GET /api/admin/v1/website/health` - <PERSON><PERSON><PERSON> tra trạng thái module

### Websites
- `GET /api/admin/v1/website/websites` - Lấy danh sách websites

### Website Pages
- `GET /api/admin/v1/website/website-pages/{website_id}` - Lấy danh sách pages của website
- `POST /api/admin/v1/website/website-pages/{website_id}` - Tạo page mới
- `GET /api/admin/v1/website/website-pages/{website_id}/page/{id}` - Lấy thông tin page
- `PUT /api/admin/v1/website/website-pages/{website_id}/page/{id}` - Cập nhật page
- `DELETE /api/admin/v1/website/website-pages/{website_id}/page/{id}` - Xóa page
- `GET /api/admin/v1/website/website-pages/{website_id}/homepage` - Lấy homepage
- `GET /api/admin/v1/website/website-pages/{website_id}/by-slug/{slug}` - Lấy page theo slug

### Themes
- `GET /api/admin/v1/website/themes` - Lấy danh sách themes
- `POST /api/admin/v1/website/themes` - Tạo theme mới
- `GET /api/admin/v1/website/themes/{id}` - Lấy thông tin theme
- `PUT /api/admin/v1/website/themes/{id}` - Cập nhật theme
- `DELETE /api/admin/v1/website/themes/{id}` - Xóa theme
- `GET /api/admin/v1/website/themes/public` - Lấy danh sách public themes

### Theme Templates
- `GET /api/admin/v1/website/theme-templates/{theme_id}` - Lấy danh sách templates của theme
- `POST /api/admin/v1/website/theme-templates/{theme_id}` - Tạo template mới
- `GET /api/admin/v1/website/theme-templates/{theme_id}/template/{id}` - Lấy thông tin template
- `PUT /api/admin/v1/website/theme-templates/{theme_id}/template/{id}` - Cập nhật template
- `DELETE /api/admin/v1/website/theme-templates/{theme_id}/template/{id}` - Xóa template
- `GET /api/admin/v1/website/theme-templates/{theme_id}/by-type/{type}` - Lấy templates theo type

## Authentication

Tất cả các API (trừ health check) đều yêu cầu:
- Bearer token trong header Authorization
- X-Tenant-Id trong header

## Variables

Các biến được sử dụng trong Bruno:
- `{{api_url}}` - Base URL của API
- `{{access_token}}` - JWT access token
- `{{tenant_id}}` - ID của tenant
- `{{website_id}}` - ID của website
- `{{page_id}}` - ID của page
- `{{theme_id}}` - ID của theme
- `{{template_id}}` - ID của template
- `{{page_slug}}` - Slug của page
- `{{template_type}}` - Type của template (page, post, etc.)

## Sample Data

Các file Bruno đã được cấu hình với sample data để test. Bạn có thể thay đổi các giá trị trong phần `vars` của mỗi request.