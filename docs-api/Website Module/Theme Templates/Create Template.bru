meta {
  name: Create Template
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/admin/v1/website/theme-templates/{{theme_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "name": "Sample Template",
    "type": "page",
    "content": "<html><head><title>{{title}}</title></head><body><h1>{{title}}</h1><div>{{content}}</div></body></html>",
    "description": "A sample page template",
    "is_active": true
  }
}

vars {
  theme_id: 1
}