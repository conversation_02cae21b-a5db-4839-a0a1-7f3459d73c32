meta {
  name: Update Template
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/admin/v1/website/theme-templates/{{theme_id}}/template/{{template_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

headers {
  Content-Type: application/json
  X-Tenant-Id: {{tenant_id}}
}

body:json {
  {
    "name": "Updated Template Name",
    "type": "page",
    "content": "<html><head><title>{{title}}</title></head><body><header><h1>{{title}}</h1></header><main>{{content}}</main><footer>Footer content</footer></body></html>",
    "description": "Updated template description",
    "is_active": true
  }
}

vars {
  theme_id: 1
  template_id: 1
}