# Redis Configuration for Production
# /usr/local/etc/redis/redis.conf

# Network
bind 0.0.0.0
port 6379
protected-mode yes

# General
daemonize no
pidfile /data/redis.pid
loglevel notice
logfile ""

# Memory
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# AOF
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Security
# requirepass your_redis_password_here

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client connections
maxclients 10000

# Database
databases 16
