# System Architecture - Blog API v1

## Ki<PERSON>n trúc tổng thể
### Dependency Injection Framework: Uber FX
- **Core pattern**: Sử dụng `go.uber.org/fx` cho dependency injection
- **Module system**: Mỗi business logic được tổ chức thành FX modules
- **Lifecycle management**: FX quản lý startup/shutdown tự động
- **Provider pattern**: Tất cả dependencies được tạo qua fx.Provide()

### Multi-Tenant Architecture
- **Tenant isolation**: Mỗi tenant có data riêng biệt
- **Middleware**: `X-Tenant-ID` header để xác định tenant
- **Database**: Shared database với tenant_id discrimination
- **Security**: RBAC system per tenant

## Cấu trúc Module System

### FX Module Interface
```go
type FXModule interface {
    Name() string
    Module() fx.Option
    Dependencies() []string
    Priority() int
    Enabled(config map[string]interface{}) bool
    GetMigrationPath() string
    GetMigrationOrder() int
}
```

### Module Loading Order (dựa trên dependencies):
1. **hello** (no dependencies)
2. **tenant** (priority 1, base module)
3. **auth** (depends on tenant)
4. **rbac** (depends on tenant, auth)
5. **notification** (depends on tenant)
6. **media** (depends on tenant, auth, rbac)
7. **blog** (depends on tenant, auth, rbac)
8. **agent-ai** (depends on tenant, auth)
9. **marketing** (depends on tenant, auth)

## Database Architecture
### Technology Stack:
- **Primary DB**: MySQL
- **Connection**: GORM + SQLX dual support
- **Migrations**: Custom migration system
- **Connection Pool**: Configurable max connections

### Database Configuration:
- Host: 127.0.0.1:3307
- Database: wnapi
- Max connections: 25
- Idle connections: 5
- Connection lifetime: 5m

## Queue System Architecture
### Technology: Redis + Asynq
- **Queue Backend**: Redis (localhost:6379)
- **Worker Framework**: Asynq
- **Task Types**: Email, SMS, Push notifications, AI tasks
- **Concurrency**: 10 workers default
- **Scheduler**: Built-in cron-like scheduling

### Task Categories:
- `notification.send.*` - Email, SMS, Push notifications
- `notification.schedule.*` - Scheduled notifications
- `agent-ai.generate.*` - AI content generation tasks

## HTTP Server Architecture
### Web Framework: Gin
- **Mode**: Debug (development)
- **Address**: 0.0.0.0:9033
- **Timeouts**: Read 15s, Write 15s
- **Max header**: 1MB

### Middleware Stack:
1. **Tenant Middleware**: Extract and validate tenant
2. **Auth Middleware**: JWT token validation
3. **RBAC Middleware**: Permission checking
4. **Logging Middleware**: Request/response logging
5. **CORS Middleware**: Cross-origin support

## Security Architecture
### Authentication:
- **JWT Tokens**: Access + Refresh token pattern
- **Token Storage**: Stateless JWT validation
- **Password Security**: Bcrypt hashing
- **Session Management**: Refresh token rotation

### Authorization (RBAC):
- **Entities**: Users, Roles, Permissions
- **Granular Permissions**: Action-based (create, read, update, delete)
- **Role Hierarchy**: Support for role inheritance
- **Tenant Isolation**: Roles/permissions scoped per tenant

## File Storage Architecture
### Media Management:
- **Storage Backends**: Local filesystem, S3-compatible
- **File Organization**: Tenant-based folder structure
- **Metadata**: Database tracking for all files
- **Upload Security**: File type validation, size limits

## AI Integration Architecture
### Agent AI Module:
- **Provider Support**: Multiple AI providers (OpenAI, etc.)
- **Queue-based Processing**: Async blog generation
- **Content Pipeline**: Prompt → AI → Processing → Blog creation
- **Status Tracking**: Real-time generation status

## Configuration Management
### Environment-based Configuration:
- **File**: `.env` for development
- **Docker**: `.env.docker` for containers
- **Validation**: Required fields validation
- **Hot Reload**: Viper-based config loading

### Key Configuration Areas:
- Database connections
- JWT secrets and expiration
- Email/SMS provider settings
- AI provider configurations
- File storage settings

## Design Patterns và Best Practices
### Repository Pattern:
- Interface-based repositories
- MySQL implementations
- GORM + SQLX hybrid approach

### Service Layer Pattern:
- Business logic encapsulation
- Interface contracts
- Dependency injection

### Handler Pattern:
- HTTP request handling
- Input validation
- Response formatting

### Error Handling:
- Structured error responses
- Proper HTTP status codes
- Logging integration