# Project Overview - Blog API v1

## Tổng quan dự án
**Tên dự án**: Blog API v1 (wnapi)  
**Phiên bản**: 0.1.0  
**Môi trường**: Development  
**Ngôn ngữ**: Go (Golang)  
**Framework chính**: Uber FX (Dependency Injection)  

## Mục tiêu dự án
- Xây dựng một hệ thống Blog API đầy đủ tính năng
- Hỗ trợ multi-tenant architecture
- Sử dụng modern Go patterns với FX dependency injection
- Cung cấp REST API cho quản lý blog, authentication, authorization
- Hỗ trợ media management, notification system

## Phạm vi chức năng
### Core Modules:
- **Auth**: <PERSON><PERSON><PERSON> thực, authorization, JWT token management
- **Blog**: Quản lý bài viết, categories, tags, authors
- **RBAC**: Role-Based Access Control system
- **Tenant**: Multi-tenant support
- **Notification**: Email, SMS, push notifications
- **Media**: File upload, storage management
- **Marketing**: Ads management system
- **Agent AI**: AI-powered content generation

### Tính năng nổi bật:
- Multi-tenant architecture
- Queue-based notification system (Redis/Asynq)
- AI-powered blog generation
- Comprehensive RBAC system
- Media management with multiple storage backends
- Email templates system
- Real-time notifications

## Thông tin triển khai
- **Server**: HTTP REST API server
- **Port**: 9033 (development)
- **Database**: MySQL (default port 3307)
- **Cache/Queue**: Redis (localhost:6379)
- **Environment**: Docker support có sẵn

## Cấu trúc project
```
blog-api-v1/
├── cmd/fx-server/     # Main application entry point
├── internal/          # Internal packages
├── modules/           # Business modules (auth, blog, etc.)
├── migrations/        # Database migrations
├── docs/              # Documentation
├── scripts/          # Build and deployment scripts
└── .memory-bank/     # Memory bank documentation
```

## Trạng thái hiện tại
- ✅ FX infrastructure đã hoàn thành
- ✅ Circular dependency issues đã được giải quyết
- ✅ Tất cả modules đã được migrate sang FX system
- ✅ API endpoints hoạt động bình thường
- ✅ Queue system và notification system hoạt động