# Development Process - Blog API v1

## Workflow & Development Process

### Branch Strategy
- **main**: Production-ready code
- **develop**: Integration branch
- **feature/***: Feature development
- **hotfix/***: Production bug fixes

### Environment Setup
```bash
# Clone repository
git clone <repository-url>
cd blog-api-v1

# Copy environment configuration
cp .env.example .env

# Install dependencies
go mod download

# Run database migrations
make migrate-up

# Start development server
make run
```

## Build System

### Makefile Commands
```makefile
# Development
make run              # Start FX server
make dev              # Development mode with hot reload
make build            # Build application binary

# Database Management  
make migrate-up       # Run all migrations
make migrate-down     # Rollback migrations
make migrate-create   # Create new migration
make seed             # Seed database with test data

# Testing
make test             # Run all tests
make test-unit        # Unit tests only
make test-integration # Integration tests only

# Docker Operations
make docker-build     # Build Docker image
make docker-up        # Start Docker containers
make docker-down      # Stop Docker containers

# Utility
make clean            # Clean build artifacts
make lint             # Run code linting
make format           # Format code
```

### Build Configuration
- **Binary Output**: `build/wnapi`
- **Main Entry Point**: `cmd/fx-server/main.go`
- **Go Version**: 1.21+
- **Module**: `wnapi`

## Docker Development

### Docker Compose Setup
```yaml
# docker-compose.yml
services:
  app:
    build: .
    ports:
      - "9033:9033"
    environment:
      - DB_HOST=mysql
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: wnapi

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

### Container Commands
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Access application container
docker-compose exec app sh

# Stop all services
docker-compose down
```

## Development Guidelines

### Code Organization Standards
```
# Module Structure (strictly enforced)
modules/{module}/
├── fx.go           # FX module definition - REQUIRED
├── providers.go    # FX providers - REQUIRED  
├── routes.go       # Route registration - REQUIRED
├── models/         # Database models
├── repository/     # Data access layer
├── service/        # Business logic
├── api/handlers/   # HTTP handlers
├── dto/            # Data transfer objects
└── internal/       # Internal configurations
```

### FX Module Development Rules
1. **Dependencies**: Only specify module names, not service names
2. **Providers**: All constructors must be FX providers
3. **Routes**: Use `fx.Invoke(RegisterXXXRoutes)` pattern
4. **Interfaces**: Services must implement interfaces
5. **Error Handling**: Proper error types and HTTP status codes

### Code Quality Standards
```bash
# Before commit - run these checks
make lint           # golangci-lint check
make format         # gofmt formatting
make test           # all tests pass
go mod tidy         # clean dependencies
```

## Database Development

### Migration Workflow
```bash
# Create new migration
make migrate-create NAME="add_user_table"

# Generated files:
# migrations/{timestamp}_add_user_table.up.sql
# migrations/{timestamp}_add_user_table.down.sql

# Run migrations
make migrate-up

# Rollback if needed
make migrate-down STEPS=1
```

### Migration Best Practices
- **Naming**: Descriptive names for migration files
- **Reversible**: Always provide down migration
- **Incremental**: Small, focused changes
- **Testing**: Test both up and down migrations

### Database Seeding
```bash
# Seed permissions (required for RBAC)
make seed-permissions

# Seed demo data
make seed-demo

# Custom seeding
go run cmd/seed/main.go
```

## Testing Strategy

### Test Structure
```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
├── benchmarks/     # Performance tests
└── fixtures/       # Test data
```

### Testing Commands
```bash
# Run all tests
make test

# Run specific test category
make test-unit
make test-integration

# Run with coverage
go test -cover ./...

# Run specific module tests
go test ./modules/auth/...

# Benchmark tests
make benchmark
```

### Test Database
- **Separate DB**: Use test-specific database
- **Clean State**: Reset between test suites
- **Fixtures**: Consistent test data setup

## API Development

### API Documentation
- **Bruno Collection**: `docs-api/` directory
- **Environment Setup**: Use Bruno environments
- **Response Scripts**: Automated testing scripts

### API Testing with Bruno
```bash
# Base URL configuration
{{api_url}} = http://localhost:9033

# Authentication
POST {{api_url}}/api/admin/v1/auth/login
{
  "email": "<EMAIL>", 
  "password": "12345678"
}

# Use X-Tenant-ID header for multi-tenant requests
```

### Response Format Standards
```json
{
  "status": "success|error",
  "message": "Human-readable message",
  "data": {}, 
  "meta": {
    "pagination": {},
    "timing": {}
  }
}
```

## Deployment Process

### Production Deployment
1. **Build**: Create production binary
2. **Test**: Run full test suite
3. **Migrate**: Update database schema
4. **Deploy**: Replace application binary
5. **Verify**: Health check endpoints

### Environment Configuration
```bash
# Production environment variables
APP_ENV=production
GIN_MODE=release
LOG_LEVEL=info

# Database (production values)
DB_HOST=prod-mysql-host
DB_PORT=3306

# Security
JWT_ACCESS_SIGNING_KEY=<secure-key>
JWT_REFRESH_SIGNING_KEY=<secure-key>
```

### Health Checks
- **Application**: `/api/v1/hello/` 
- **Database**: Connection check
- **Redis**: Queue system check
- **Modules**: Individual health endpoints

## Monitoring & Debugging

### Logging Strategy
- **Structured Logging**: JSON format in production
- **Log Levels**: Debug, Info, Warn, Error, Fatal
- **Context Tracking**: Request IDs, tenant context
- **Performance**: Query timing, response times

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug make run

# FX debug output
# Shows dependency injection flow
# Provider execution timing
# Module loading order
```

### Development Tools
- **Air**: Hot reload for development
- **golangci-lint**: Code quality checks  
- **gofmt**: Code formatting
- **Bruno**: API testing and documentation

## Git Workflow

### Commit Convention
```
type(scope): description

feat(auth): add JWT refresh token rotation
fix(blog): resolve category tree building issue
docs(readme): update installation instructions
refactor(fx): improve module loading performance
```

### Pre-commit Checks
```bash
# Automated checks before commit
make lint
make test
go mod tidy
```

### Code Review Process
1. **Feature Branch**: Create from develop
2. **Implementation**: Follow coding standards
3. **Testing**: Add appropriate tests
4. **PR Creation**: Detailed description
5. **Review**: Code review and approval
6. **Merge**: Squash merge to develop

## Performance Optimization

### Database Optimization
- **Connection Pooling**: Configured limits
- **Query Optimization**: Use SQLX for complex queries
- **Indexing**: Proper database indexes
- **Caching**: Redis for frequently accessed data

### Application Performance
- **Goroutine Management**: Proper concurrency
- **Memory Usage**: Profile and optimize
- **HTTP Performance**: Connection keep-alive
- **Queue Processing**: Optimal worker count

### Monitoring Metrics
- **Response Times**: HTTP endpoint performance
- **Database Queries**: Query execution time
- **Queue Processing**: Task completion rates
- **Memory Usage**: Application memory patterns