# Components & Modules - Blog API v1

## Core Infrastructure Components

### FX Application (`internal/fx/`)
- **app.go**: Main FX application setup với core providers
- **providers/**: Core service providers (config, logger, database, etc.)
- **lifecycle/**: Server và queue lifecycle management
- **modules/**: Module discovery và loading system

### Configuration System (`internal/pkg/config/`)
- **Viper-based**: Configuration management
- **Multi-source**: Environment variables, files
- **Validation**: Required field checking
- **Module-specific**: Per-module configuration extraction

### Database System (`internal/database/`)
- **Manager**: Database connection management
- **GORM Integration**: ORM operations
- **SQLX Integration**: Raw SQL query support
- **Migration System**: Database schema evolution

### Cron System (`internal/pkg/queue/cron/`)
- **CronScheduler**: Main scheduler with job execution
- **HandlerRegistry**: Registry for cron job handlers
- **CronManager**: High-level coordination
- **CLI Integration**: Commands for job management
- **Local Development**: Testing tools và scripts

## Business Modules

### 1. Tenant Module (`modules/tenant/`)
**Vai trò**: Multi-tenant foundation module
**Dependencies**: None (base module)
**Priority**: 1

**Components**:
- **Models**: Tenant entity definition
- **Repository**: Tenant data access
- **Service**: Tenant business logic
- **Middleware**: Tenant extraction from headers
- **API**: Tenant management endpoints

**Key Features**:
- Tenant creation/management
- Subdomain support
- Tenant-scoped data isolation

### 2. Auth Module (`modules/auth/`)
**Vai trò**: Authentication and user management
**Dependencies**: tenant
**Priority**: 10

**Components**:
- **Models**: User, EmailVerification, PasswordReset
- **Repository**: MySQL-based user data access
- **Service**: Authentication logic, JWT management
- **API**: Login, register, profile, password reset
- **Handlers**: HTTP request processing
- **Cron Jobs**: Session cleanup, password expiry notifications

**Key Features**:
- JWT access/refresh token
- Email verification
- Password reset workflow
- Profile management
- Automated session cleanup

### 3. RBAC Module (`modules/rbac/`)
**Vai trò**: Role-Based Access Control
**Dependencies**: tenant, auth
**Priority**: 20

**Components**:
- **Models**: Role, Permission, UserRole, PermissionGroup
- **Repository**: RBAC data access layer
- **Service**: Permission checking, role management
- **Middleware**: Authorization middleware
- **API**: Role/permission management

**Key Features**:
- Granular permissions
- Role hierarchy
- Permission groups
- User-role assignments

### 4. Notification Module (`modules/notification/`)
**Vai trò**: Multi-channel notification system
**Dependencies**: tenant
**Priority**: 30

**Components**:
- **Models**: Notification, Template, Channel, NotificationUser
- **Repository**: Notification data persistence
- **Service**: Notification delivery logic
- **Queue**: Async notification processing
- **Delivery Services**: Email, SMS, Push, In-app
- **API**: Notification management, template CRUD
- **Cron Jobs**: Digest notifications, cleanup, retry failed

**Key Features**:
- Multi-channel delivery (email, SMS, push, in-app)
- Template-based notifications
- Queue-based processing
- Delivery status tracking
- Batch operations
- Automated cleanup và retry

### 5. Media Module (`modules/media/`)
**Vai trò**: File upload and management
**Dependencies**: tenant, auth, rbac
**Priority**: 50

**Components**:
- **Models**: Media, MediaFolder
- **Service**: File processing, storage management
- **Storage**: Multiple backend support
- **API**: Upload, download, management
- **Cron Jobs**: Image optimization, temp cleanup, orphan cleanup

**Key Features**:
- Multi-storage backend support
- File type validation
- Thumbnail generation
- Folder organization
- URL generation
- Automated optimization và cleanup

### 6. Blog Module (`modules/blog/`)
**Vai trò**: Content management system
**Dependencies**: tenant, auth, rbac
**Priority**: 60

**Components**:
- **Models**: Post, Category, Tag, Author, RelatedPost, Schedule, BlogBlock, BlogTimeline
- **Repository**: Content data access
- **Service**: Content business logic
- **API**: CRUD operations, frontend endpoints
- **Cron Jobs**: Auto-publish, sitemap updates, SEO analysis

**Key Features**:
- Hierarchical categories
- Tag system
- Author management
- Related posts
- Content scheduling
- Blog blocks/timelines
- SEO support
- Frontend API
- Automated publishing và SEO

### 7. Agent AI Module (`modules/agent-ai/`)
**Vai trò**: AI-powered content generation
**Dependencies**: tenant, auth
**Priority**: 70

**Components**:
- **Service**: AI provider integration
- **Queue**: Async blog generation
- **Repository**: AI task tracking
- **API**: Generation requests, status checking

**Key Features**:
- Multiple AI provider support
- Async blog generation
- Generation status tracking
- Queue-based processing

### 8. Marketing Module (`modules/marketing/`)
**Vai trò**: Advertisement management
**Dependencies**: tenant, auth
**Priority**: 80

**Components**:
- **Models**: AdsPosition, AdsBanner
- **Repository**: Marketing data access
- **Service**: Ad management logic
- **API**: Ad CRUD, public ad serving

**Key Features**:
- Ad position management
- Banner management
- Status control
- Public ad serving API

### 9. Hello Module (`modules/hello/`)
**Vai trò**: Demo/testing module
**Dependencies**: None
**Priority**: 100

**Components**:
- **Handler**: Simple HTTP handlers
- **API**: Test endpoints

**Key Features**:
- Health check endpoints
- JSON response testing

## Cron System Integration

### System-Level Cron Jobs
- **system:cleanup** - Clean logs, temp files, cache
- **system:health_check** - Monitor system health
- **system:backup** - Backup system data

### Module-Specific Cron Jobs
- **auth:session_cleanup** - Clean expired sessions
- **auth:password_expiry** - Password expiry notifications
- **media:image_optimization** - Optimize images
- **media:temp_cleanup** - Clean temporary files
- **media:orphan_cleanup** - Remove orphaned files
- **blog:auto_publish** - Auto-publish scheduled posts
- **blog:sitemap_update** - Update sitemap
- **notification:digest** - Send digest notifications
- **notification:cleanup** - Clean old notifications
- **notification:retry_failed** - Retry failed notifications

### Local Development Tools
- **CLI Commands**: `./wnapi cron list|status|run|start`
- **Makefile Targets**: `make cron-*` commands
- **Testing Script**: `./scripts/test-cron-local.sh`
- **Environment Config**: `.env.local` for development
- **Docker Support**: Separate cron worker container

### Production Deployment
- **Separate Container**: Dedicated cron worker service
- **Environment Variables**: Comprehensive configuration
- **Monitoring**: Metrics và health checks
- **Backup Integration**: Automated backup scheduling
- **Scaling Support**: Multiple worker instances

## Module Structure Pattern

### Standard Module Organization:
```
modules/{module_name}/
├── fx.go                    # FX module definition
├── providers.go            # FX providers
├── routes.go               # Route registration
├── models/                 # Data models
├── repository/            
│   └── mysql/              # MySQL implementations
├── service/               # Business logic
├── api/
│   └── handlers/          # HTTP handlers
├── dto/                   # Data transfer objects
├── internal/              # Internal types/configs
├── migrations/            # Database migrations
└── cron/                  # Cron job handlers (nếu có)
```

### FX Integration Pattern:
```go
func (m *ModuleName) Module() fx.Option {
    return fx.Module("module-name",
        // Providers
        fx.Provide(
            NewConfig,
            NewRepository,
            NewService,
            NewHandler,
            NewCronHandler, // Nếu có cron jobs
        ),
        
        // Route registration
        fx.Invoke(RegisterRoutes),
        
        // Cron registration
        fx.Invoke(RegisterCronJobs), // Nếu có cron jobs
    )
}
```

## Inter-Module Communication

### Service Injection:
- Modules expose services via interfaces
- FX handles dependency injection
- Type-safe service resolution

### Event System:
- Queue-based async communication
- Notification system for cross-module events
- Decoupled module interactions

### Cron Job Communication:
- Shared database access
- Redis queue for job coordination
- Cross-module job dependencies

### Shared Resources:
- Database connections
- Logger instances
- Configuration access
- Cache connections
- Cron scheduler

## Module Dependencies Graph
```
tenant (1) ← auth (10) ← rbac (20)
    ↑           ↑           ↑
    └── notification (30)   └── media (50) ← blog (60)
            ↑                       ↑
            └── agent-ai (70)       └── marketing (80)
```

## Module Lifecycle

### Registration Phase:
1. Module implements FXModule interface
2. Registers with GlobalRegistry in init()
3. Module discovery loads enabled modules

### Dependency Resolution:
1. Topological sort based on dependencies
2. Priority-based ordering within dependency groups
3. Module loading in resolved order

### Initialization Phase:
1. FX creates providers in dependency order
2. Services are instantiated with dependencies
3. Routes are registered via fx.Invoke
4. Cron jobs are registered (nếu có)

### Runtime Phase:
1. HTTP server handles requests
2. Services process business logic
3. Queue workers handle async tasks
4. Cron scheduler manages scheduled jobs

## Development Workflow

### Local Development:
1. Setup environment: `make cron-setup`
2. Start services: `docker-compose up -d mysql redis`
3. Build application: `make build`
4. Test cron jobs: `make cron-test`
5. Monitor: `make cron-logs`

### Production Deployment:
1. Use production Docker Compose
2. Separate cron worker container
3. Environment-based configuration
4. Monitoring và alerting

## Error Handling Strategy

### Module-level Error Handling:
- Structured error types
- Proper HTTP status codes
- Error logging and tracking
- Graceful degradation

### Cron-level Error Handling:
- Retry mechanisms
- Error notifications
- Job failure tracking
- Recovery procedures

### Inter-module Error Propagation:
- Interface-based error contracts
- Context-based error passing
- Centralized error handling middleware