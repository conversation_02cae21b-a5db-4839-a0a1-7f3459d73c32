# API Documentation - Blog API v1

## API Structure và Standards

### Base URL
- **Development**: `http://localhost:9033`
- **API Version**: `/api/v1`
- **Authentication**: Bearer JWT Token
- **Content-Type**: `application/json`

### Common Headers
```
Authorization: Bearer <jwt-token>
X-Tenant-ID: <tenant-id>
Content-Type: application/json
```

### Standard Response Format
```json
{
  "success": true,
  "message": "Success message",
  "data": { /* response data */ },
  "meta": { /* pagination, etc */ },
  "errors": null
}
```

## Module APIs

### 1. Auth Module APIs

#### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Refresh access token

#### Password Management
- `POST /api/v1/auth/forgot-password` - Request password reset
- `POST /api/v1/auth/reset-password` - Reset password with token
- `POST /api/v1/auth/change-password` - Change password (authenticated)

#### Email Verification
- `POST /api/v1/auth/verify-email` - Verify email with token
- `POST /api/v1/auth/resend-verification` - Resend verification email

#### Profile Management
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update user profile

### 2. Blog Module APIs

#### Posts Management
- `GET /api/v1/blog/posts` - List posts (with filters, pagination)
- `GET /api/v1/blog/posts/{id}` - Get post by ID
- `POST /api/v1/blog/posts` - Create new post
- `PUT /api/v1/blog/posts/{id}` - Update post
- `DELETE /api/v1/blog/posts/{id}` - Delete post
- `POST /api/v1/blog/posts/{id}/publish` - Publish post
- `POST /api/v1/blog/posts/{id}/unpublish` - Unpublish post

#### Categories Management
- `GET /api/v1/blog/categories` - List categories
- `GET /api/v1/blog/categories/{id}` - Get category by ID
- `POST /api/v1/blog/categories` - Create category
- `PUT /api/v1/blog/categories/{id}` - Update category
- `DELETE /api/v1/blog/categories/{id}` - Delete category

#### Tags Management
- `GET /api/v1/blog/tags` - List tags
- `GET /api/v1/blog/tags/{id}` - Get tag by ID
- `POST /api/v1/blog/tags` - Create tag
- `PUT /api/v1/blog/tags/{id}` - Update tag
- `DELETE /api/v1/blog/tags/{id}` - Delete tag

#### Authors Management
- `GET /api/v1/blog/authors` - List authors
- `GET /api/v1/blog/authors/{id}` - Get author by ID
- `POST /api/v1/blog/authors` - Create author
- `PUT /api/v1/blog/authors/{id}` - Update author
- `DELETE /api/v1/blog/authors/{id}` - Delete author

#### Blog Schedules (Content Scheduling)
- `GET /api/v1/blog/schedules` - List scheduled posts
- `GET /api/v1/blog/schedules/{id}` - Get schedule by ID
- `POST /api/v1/blog/schedules` - Create new schedule
- `POST /api/v1/blog/schedules/{id}/cancel` - Cancel schedule
- `POST /api/v1/blog/schedules/{id}/retry` - Retry failed schedule

**Schedule Features:**
- Auto-publish posts at specified times
- Retry mechanism for failed publications
- Status tracking (pending, published, failed, cancelled)
- Cron job integration for automated processing
- Queue-based execution with Redis

#### Frontend APIs (Public)
- `GET /api/v1/frontend/posts` - Public posts listing
- `GET /api/v1/frontend/posts/{slug}` - Get post by slug
- `GET /api/v1/frontend/categories` - Public categories
- `GET /api/v1/frontend/tags` - Public tags

### 3. RBAC Module APIs

#### Roles Management
- `GET /api/v1/rbac/roles` - List roles
- `GET /api/v1/rbac/roles/{id}` - Get role by ID
- `POST /api/v1/rbac/roles` - Create role
- `PUT /api/v1/rbac/roles/{id}` - Update role
- `DELETE /api/v1/rbac/roles/{id}` - Delete role

#### Permissions Management
- `GET /api/v1/rbac/permissions` - List permissions
- `GET /api/v1/rbac/permissions/{id}` - Get permission by ID
- `POST /api/v1/rbac/permissions` - Create permission
- `PUT /api/v1/rbac/permissions/{id}` - Update permission

#### Permission Groups
- `GET /api/v1/rbac/permission-groups` - List permission groups
- `POST /api/v1/rbac/permission-groups` - Create permission group

#### User Role Assignment
- `GET /api/v1/rbac/user-roles/{user_id}` - Get user roles
- `POST /api/v1/rbac/user-roles` - Assign role to user
- `DELETE /api/v1/rbac/user-roles` - Remove role from user

### 4. Notification Module APIs

#### Notifications Management
- `GET /api/v1/notifications` - List user notifications
- `GET /api/v1/notifications/{id}` - Get notification by ID
- `POST /api/v1/notifications` - Send notification
- `PUT /api/v1/notifications/{id}/read` - Mark as read
- `PUT /api/v1/notifications/mark-all-read` - Mark all as read
- `DELETE /api/v1/notifications/{id}` - Delete notification

#### Templates Management
- `GET /api/v1/notifications/templates` - List templates
- `GET /api/v1/notifications/templates/{id}` - Get template
- `POST /api/v1/notifications/templates` - Create template
- `PUT /api/v1/notifications/templates/{id}` - Update template

#### Channels Management
- `GET /api/v1/notifications/channels` - List channels
- `POST /api/v1/notifications/channels` - Create channel
- `PUT /api/v1/notifications/channels/{id}` - Update channel

### 5. Media Module APIs

#### File Upload
- `POST /api/v1/media/upload` - Upload file
- `POST /api/v1/media/upload/multiple` - Upload multiple files
- `POST /api/v1/media/upload/chunk` - Chunked upload

#### Media Management
- `GET /api/v1/media` - List media files
- `GET /api/v1/media/{id}` - Get media file info
- `PUT /api/v1/media/{id}` - Update media info
- `DELETE /api/v1/media/{id}` - Delete media file

#### Folders Management
- `GET /api/v1/media/folders` - List folders
- `POST /api/v1/media/folders` - Create folder
- `PUT /api/v1/media/folders/{id}` - Update folder
- `DELETE /api/v1/media/folders/{id}` - Delete folder

#### File Operations
- `GET /api/v1/media/{id}/download` - Download file
- `GET /api/v1/media/{id}/thumbnail` - Get thumbnail
- `POST /api/v1/media/{id}/move` - Move to folder

### 6. Marketing Module APIs

#### Ads Positions
- `GET /api/v1/marketing/ads-positions` - List ad positions
- `POST /api/v1/marketing/ads-positions` - Create ad position
- `PUT /api/v1/marketing/ads-positions/{id}` - Update ad position
- `DELETE /api/v1/marketing/ads-positions/{id}` - Delete ad position

#### Ads Banners
- `GET /api/v1/marketing/ads-banners` - List ad banners
- `POST /api/v1/marketing/ads-banners` - Create ad banner
- `PUT /api/v1/marketing/ads-banners/{id}` - Update ad banner
- `DELETE /api/v1/marketing/ads-banners/{id}` - Delete ad banner

#### Public Ads API
- `GET /api/v1/public/ads/{position}` - Get ads for position

### 7. Agent AI Module APIs

#### Blog Generation
- `POST /api/v1/agent-ai/generate/blog` - Generate blog content
- `GET /api/v1/agent-ai/generate/status/{task_id}` - Check generation status
- `GET /api/v1/agent-ai/generate/result/{task_id}` - Get generation result

### 8. Tenant Module APIs

#### Tenant Management
- `GET /api/v1/tenants` - List tenants (admin only)
- `GET /api/v1/tenants/{id}` - Get tenant info
- `POST /api/v1/tenants` - Create tenant (admin only)
- `PUT /api/v1/tenants/{id}` - Update tenant
- `DELETE /api/v1/tenants/{id}` - Delete tenant (admin only)

## API Features

### Pagination
Standard pagination parameters:
- `limit`: Number of items per page (default: 10, max: 100)
- `offset`: Number of items to skip
- `page`: Page number (alternative to offset)

Response includes pagination metadata:
```json
{
  "data": [...],
  "meta": {
    "total": 150,
    "limit": 10,
    "offset": 0,
    "page": 1,
    "total_pages": 15
  }
}
```

### Filtering
Common filter parameters:
- `status`: Filter by status
- `created_at_from`, `created_at_to`: Date range filters
- `search`: Text search across relevant fields
- `sort_by`: Field to sort by
- `sort_order`: `asc` or `desc`

### Multi-tenant Support
- All APIs require `X-Tenant-ID` header
- Data isolation per tenant
- Tenant-specific configurations

### Error Handling
Standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

Error response format:
```json
{
  "success": false,
  "message": "Error message",
  "errors": {
    "field": ["validation error message"]
  },
  "code": "ERROR_CODE"
}
```

### Rate Limiting
- Default: 100 requests per minute per user
- Headers included in response:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining`
  - `X-RateLimit-Reset`

### Authentication Flow
1. Register/Login to get JWT tokens
2. Include `Authorization: Bearer <access-token>` in requests
3. Refresh token when access token expires
4. Handle 401 responses by refreshing tokens

### Content Scheduling System
Blog posts can be scheduled for future publication:

1. **Create Schedule**: Set publication time for blog posts
2. **Auto Processing**: Cron jobs automatically publish scheduled content
3. **Retry Mechanism**: Failed publications are retried automatically
4. **Status Tracking**: Monitor schedule status (pending, published, failed, cancelled)
5. **Manual Controls**: Cancel or retry schedules as needed

**Schedule Workflow:**
```
Create Schedule → Queue Task → Cron Processing → Auto Publish → Update Status
```

**Cron Jobs:**
- `blog_process_pending_schedules`: Every 5 minutes
- `blog_cleanup_old_schedules`: Daily at 2 AM

## API Documentation Tools

### Swagger/OpenAPI
- Available at: `/swagger/` (development)
- Auto-generated from code annotations
- Interactive API testing interface

### Postman Collection
- Import collection from `/docs/postman/`
- Pre-configured environments
- Example requests and responses

## Security Features

### JWT Security
- Short-lived access tokens (15 minutes)
- Long-lived refresh tokens (7 days)
- Token rotation on refresh
- Secure token storage recommendations

### API Security
- CORS configuration
- Rate limiting
- Input validation
- SQL injection prevention
- XSS protection

### Permission-based Access
- Fine-grained permissions
- Role-based access control
- Resource-level permissions
- Tenant isolation

## Development Guidelines

### API Versioning
- URL-based versioning (`/api/v1/`)
- Backward compatibility maintenance
- Deprecation notices
- Migration guides

### Response Consistency
- Consistent naming conventions (snake_case)
- Standard response envelope
- Predictable error formats
- Comprehensive status codes

### Testing
- Unit tests for all endpoints
- Integration tests for workflows
- API contract testing
- Performance testing