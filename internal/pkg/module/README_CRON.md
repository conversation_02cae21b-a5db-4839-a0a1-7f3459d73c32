# Module Cron System

## Mô tả

File `internal/pkg/module/cron.go` cung cấp framework để quản lý cron functionality cho các modules trong hệ thống. Hệ thống này cho phép các modules đăng ký cron handlers và jobs của họ một cách tự động và có tổ chức.

## Các thành phần chính

### 1. `CronModule` Interface

Interface này định nghĩa các phương thức mà một module cần triển khai để hỗ trợ cron functionality:

```go
type CronModule interface {
    // RegisterCronHandlers đăng ký các cron handlers của module
    RegisterCronHandlers(registry *cron.HandlerRegistry) error

    // GetCronJobs trả về danh sách cron jobs của module
    GetCronJobs() ([]*cron.CronJob, error)

    // OnCronEnabled được gọi khi cron system đư<PERSON><PERSON> bật
    OnCronEnabled(ctx context.Context) error

    // OnCronDisabled được gọi khi cron system được tắt
    OnCronDisabled(ctx context.Context) error
}
```

### 2. `CronModuleBase` Struct

Cung cấp implementation mặc định cho `CronModule` interface. Modules có thể embed struct này để có default behavior:

```go
type CronModuleBase struct{}
```

### 3. `CronModuleManager` Struct

Quản lý tất cả các modules có cron functionality:

```go
type CronModuleManager struct {
    modules  map[string]CronModule
    registry *cron.HandlerRegistry
    logger   logger.Logger
}
```

## Cách sử dụng

### 1. Tạo một module với cron functionality

```go
package mymodule

import (
    "context"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/logger"
)

// MyModule triển khai CronModule interface
type MyModule struct {
    module.CronModuleBase // Embed để có default implementations
    db     *gorm.DB
    logger logger.Logger
}

func NewMyModule(db *gorm.DB, logger logger.Logger) *MyModule {
    return &MyModule{
        db:     db,
        logger: logger,
    }
}

// RegisterCronHandlers đăng ký cron handlers của module
func (m *MyModule) RegisterCronHandlers(registry *cron.HandlerRegistry) error {
    // Tạo handler cho cleanup task
    cleanupHandler := &MyModuleCleanupHandler{
        db:     m.db,
        logger: m.logger,
    }
    
    if err := registry.RegisterHandler(cleanupHandler); err != nil {
        return fmt.Errorf("failed to register cleanup handler: %w", err)
    }
    
    m.logger.Info("MyModule cron handlers registered successfully")
    return nil
}

// GetCronJobs trả về danh sách cron jobs của module
func (m *MyModule) GetCronJobs() ([]*cron.CronJob, error) {
    jobs := []*cron.CronJob{
        {
            ID:          "mymodule_cleanup",
            Name:        "MyModule Cleanup",
            TaskType:    "mymodule:cleanup",
            Schedule:    "0 2 * * *", // Chạy lúc 2h sáng hàng ngày
            Enabled:     true,
            Handler:     &MyModuleCleanupHandler{db: m.db, logger: m.logger},
            Description: "Clean up old data in MyModule",
        },
    }
    
    return jobs, nil
}

// OnCronEnabled được gọi khi cron system được bật
func (m *MyModule) OnCronEnabled(ctx context.Context) error {
    m.logger.Info("MyModule cron system enabled")
    // Thực hiện các tác vụ khởi tạo cần thiết
    return nil
}

// OnCronDisabled được gọi khi cron system được tắt
func (m *MyModule) OnCronDisabled(ctx context.Context) error {
    m.logger.Info("MyModule cron system disabled")
    // Thực hiện cleanup nếu cần
    return nil
}
```

### 2. Tạo cron handler

```go
package mymodule

import (
    "context"
    "wnapi/internal/pkg/queue/types"
    "wnapi/internal/pkg/logger"
    "gorm.io/gorm"
)

type MyModuleCleanupHandler struct {
    db     *gorm.DB
    logger logger.Logger
}

func (h *MyModuleCleanupHandler) GetTaskType() types.CronTaskType {
    return "mymodule:cleanup"
}

func (h *MyModuleCleanupHandler) GetDescription() string {
    return "Clean up old data in MyModule"
}

func (h *MyModuleCleanupHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    h.logger.Info("Starting MyModule cleanup task")
    
    // Thực hiện cleanup logic
    result := h.db.Where("created_at < ?", time.Now().AddDate(0, -1, 0)).Delete(&MyData{})
    if result.Error != nil {
        return &types.CronTaskResult{
            TaskType:    h.GetTaskType(),
            Success:     false,
            Message:     fmt.Sprintf("Cleanup failed: %v", result.Error),
            ExecutedAt:  time.Now(),
            CompletedAt: time.Now(),
        }, result.Error
    }
    
    h.logger.Info("MyModule cleanup completed", "deleted_records", result.RowsAffected)
    
    return &types.CronTaskResult{
        TaskType:    h.GetTaskType(),
        Success:     true,
        Message:     fmt.Sprintf("Successfully cleaned up %d old records", result.RowsAffected),
        ExecutedAt:  time.Now(),
        CompletedAt: time.Now(),
    }, nil
}
```

### 3. Sử dụng CronModuleManager

```go
package main

import (
    "context"
    "log/slog"
    "os"
    "wnapi/internal/pkg/module"
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/logger"
)

func main() {
    // Tạo logger
    appLogger := logger.NewConsoleLogger("app", logger.LevelInfo)
    
    // Tạo slog logger cho cron system
    slogLogger := slog.New(slog.NewTextHandler(os.Stdout, nil))
    
    // Tạo handler registry
    registry := cron.NewHandlerRegistry(slogLogger)
    
    // Tạo cron module manager
    cronManager := module.NewCronModuleManager(registry, appLogger)
    
    // Tạo và đăng ký modules
    myModule := NewMyModule(db, appLogger)
    
    if err := cronManager.RegisterModule("mymodule", myModule); err != nil {
        log.Fatal("Failed to register module:", err)
    }
    
    // Bật cron system cho tất cả modules
    ctx := context.Background()
    if err := cronManager.OnCronEnabled(ctx); err != nil {
        log.Fatal("Failed to enable cron system:", err)
    }
    
    // Lấy tất cả cron jobs từ các modules
    jobs, err := cronManager.GetAllCronJobs()
    if err != nil {
        log.Fatal("Failed to get cron jobs:", err)
    }
    
    appLogger.Info("Total cron jobs registered", "count", len(jobs))
    
    // Khi shutdown
    defer func() {
        if err := cronManager.OnCronDisabled(ctx); err != nil {
            appLogger.Error("Failed to disable cron system", "error", err)
        }
    }()
}
```

## Lợi ích

### 1. **Tách biệt concerns**: Mỗi module quản lý cron logic riêng của mình
### 2. **Tái sử dụng**: Base implementation giúp giảm boilerplate code
### 3. **Quản lý tập trung**: Manager cho phép kiểm soát tất cả modules từ một nơi
### 4. **Lifecycle management**: Hỗ trợ enable/disable cron cho toàn bộ hệ thống
### 5. **Logging**: Tích hợp logging để theo dõi hoạt động

## Testing

File test `cron_test.go` cung cấp comprehensive test coverage cho tất cả functionality:

- Test default implementations của `CronModuleBase`
- Test việc tạo và sử dụng `CronModuleManager`
- Test đăng ký modules
- Test error handling
- Test lifecycle methods (OnCronEnabled/OnCronDisabled)

### Chạy tests:

```bash
go test ./internal/pkg/module -v
```

## Kết luận

Hệ thống module cron này cung cấp một framework linh hoạt và mạnh mẽ để quản lý cron functionality trong ứng dụng modular. Nó cho phép:

- Modules tự quản lý cron logic của mình
- Đăng ký handlers và jobs một cách tự động
- Quản lý lifecycle của cron system
- Logging và monitoring tập trung
- Test coverage tốt

Framework này tuân theo các nguyên tắc SOLID và design patterns tốt, giúp code dễ bảo trì và mở rộng.
