package module

import (
	"context"
	"log/slog"
	"os"
	"testing"

	"wnapi/internal/pkg/queue/cron"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockCronModule triển khai CronModule interface cho testing
type MockCronModule struct {
	mock.Mock
}

func (m *MockCronModule) RegisterCronHandlers(registry *cron.HandlerRegistry) error {
	args := m.Called(registry)
	return args.Error(0)
}

func (m *MockCronModule) GetCronJobs() ([]*cron.CronJob, error) {
	args := m.Called()
	return args.Get(0).([]*cron.CronJob), args.Error(1)
}

func (m *MockCronModule) OnCronEnabled(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockCronModule) OnCronDisabled(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// MockHandlerRegistry triển khai HandlerRegistry interface cho testing
// Removed - we'll use real HandlerRegistry for testing

// MockLogger triển khai logger.Logger interface cho testing
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Info(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Warn(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Error(msg string, args ...interface{}) {
	m.Called(msg, args)
}

func (m *MockLogger) Fatal(msg string, args ...interface{}) {
	m.Called(msg, args)
}

// Helper function to create test registry
func createTestRegistry() *cron.HandlerRegistry {
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
	return cron.NewHandlerRegistry(logger)
}

func TestCronModuleBase_DefaultImplementations(t *testing.T) {
	base := &CronModuleBase{}

	// Test default implementations
	err := base.RegisterCronHandlers(nil)
	assert.NoError(t, err)

	jobs, err := base.GetCronJobs()
	assert.NoError(t, err)
	assert.Empty(t, jobs)

	err = base.OnCronEnabled(context.Background())
	assert.NoError(t, err)

	err = base.OnCronDisabled(context.Background())
	assert.NoError(t, err)
}

func TestNewCronModuleManager(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}

	manager := NewCronModuleManager(registry, mockLogger)

	assert.NotNil(t, manager)
	assert.NotNil(t, manager.modules)
	assert.Equal(t, registry, manager.registry)
	assert.Equal(t, mockLogger, manager.logger)
}

func TestCronModuleManager_RegisterModule(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Setup expectations
	mockModule.On("RegisterCronHandlers", registry).Return(nil)

	// Test successful registration
	err := manager.RegisterModule("test-module", mockModule)
	assert.NoError(t, err)

	// Verify module was registered
	retrievedModule, exists := manager.GetModule("test-module")
	assert.True(t, exists)
	assert.Equal(t, mockModule, retrievedModule)

	mockModule.AssertExpectations(t)
}

func TestCronModuleManager_RegisterModule_HandlerError(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Setup expectations - handler registration fails
	mockModule.On("RegisterCronHandlers", registry).Return(assert.AnError)

	// Test failed registration
	err := manager.RegisterModule("test-module", mockModule)
	assert.Error(t, err)
	assert.Equal(t, assert.AnError, err)

	// Verify module was still registered (only handler registration failed)
	retrievedModule, exists := manager.GetModule("test-module")
	assert.True(t, exists)
	assert.Equal(t, mockModule, retrievedModule)

	mockModule.AssertExpectations(t)
}

func TestCronModuleManager_GetAllModules(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule1 := &MockCronModule{}
	mockModule2 := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Setup expectations
	mockModule1.On("RegisterCronHandlers", registry).Return(nil)
	mockModule2.On("RegisterCronHandlers", registry).Return(nil)

	// Register modules
	err := manager.RegisterModule("module1", mockModule1)
	require.NoError(t, err)

	err = manager.RegisterModule("module2", mockModule2)
	require.NoError(t, err)

	// Get all modules
	allModules := manager.GetAllModules()

	assert.Len(t, allModules, 2)
	assert.Contains(t, allModules, "module1")
	assert.Contains(t, allModules, "module2")
	assert.Equal(t, mockModule1, allModules["module1"])
	assert.Equal(t, mockModule2, allModules["module2"])

	mockModule1.AssertExpectations(t)
	mockModule2.AssertExpectations(t)
}

func TestCronModuleManager_GetAllCronJobs(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule1 := &MockCronModule{}
	mockModule2 := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Create mock jobs
	job1 := &cron.CronJob{ID: "job1", Name: "Job 1"}
	job2 := &cron.CronJob{ID: "job2", Name: "Job 2"}
	job3 := &cron.CronJob{ID: "job3", Name: "Job 3"}

	// Setup expectations
	mockModule1.On("RegisterCronHandlers", registry).Return(nil)
	mockModule1.On("GetCronJobs").Return([]*cron.CronJob{job1, job2}, nil)

	mockModule2.On("RegisterCronHandlers", registry).Return(nil)
	mockModule2.On("GetCronJobs").Return([]*cron.CronJob{job3}, nil)

	// Register modules
	err := manager.RegisterModule("module1", mockModule1)
	require.NoError(t, err)

	err = manager.RegisterModule("module2", mockModule2)
	require.NoError(t, err)

	// Get all cron jobs
	allJobs, err := manager.GetAllCronJobs()

	assert.NoError(t, err)
	assert.Len(t, allJobs, 3)
	assert.Contains(t, allJobs, job1)
	assert.Contains(t, allJobs, job2)
	assert.Contains(t, allJobs, job3)

	mockModule1.AssertExpectations(t)
	mockModule2.AssertExpectations(t)
}

func TestCronModuleManager_GetAllCronJobs_Error(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Setup expectations - GetCronJobs returns error
	mockModule.On("RegisterCronHandlers", registry).Return(nil)
	mockModule.On("GetCronJobs").Return([]*cron.CronJob{}, assert.AnError)

	// Register module
	err := manager.RegisterModule("test-module", mockModule)
	require.NoError(t, err)

	// Get all cron jobs should return error
	allJobs, err := manager.GetAllCronJobs()

	assert.Error(t, err)
	assert.Equal(t, assert.AnError, err)
	assert.Nil(t, allJobs)

	mockModule.AssertExpectations(t)
}

func TestCronModuleManager_OnCronEnabled(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule1 := &MockCronModule{}
	mockModule2 := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	ctx := context.Background()

	// Setup expectations
	mockModule1.On("RegisterCronHandlers", registry).Return(nil)
	mockModule1.On("OnCronEnabled", ctx).Return(nil)

	mockModule2.On("RegisterCronHandlers", registry).Return(nil)
	mockModule2.On("OnCronEnabled", ctx).Return(nil)

	// Expect logger calls
	mockLogger.On("Info", "Module cron enabled", []interface{}{"module", "module1"})
	mockLogger.On("Info", "Module cron enabled", []interface{}{"module", "module2"})

	// Register modules
	err := manager.RegisterModule("module1", mockModule1)
	require.NoError(t, err)

	err = manager.RegisterModule("module2", mockModule2)
	require.NoError(t, err)

	// Call OnCronEnabled
	err = manager.OnCronEnabled(ctx)
	assert.NoError(t, err)

	mockModule1.AssertExpectations(t)
	mockModule2.AssertExpectations(t)
	mockLogger.AssertExpectations(t)
}

func TestCronModuleManager_OnCronEnabled_Error(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	ctx := context.Background()

	// Setup expectations - OnCronEnabled returns error
	mockModule.On("RegisterCronHandlers", registry).Return(nil)
	mockModule.On("OnCronEnabled", ctx).Return(assert.AnError)

	// Register module
	err := manager.RegisterModule("test-module", mockModule)
	require.NoError(t, err)

	// Call OnCronEnabled should return error
	err = manager.OnCronEnabled(ctx)
	assert.Error(t, err)
	assert.Equal(t, assert.AnError, err)

	mockModule.AssertExpectations(t)
}

func TestCronModuleManager_OnCronDisabled(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule1 := &MockCronModule{}
	mockModule2 := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	ctx := context.Background()

	// Setup expectations
	mockModule1.On("RegisterCronHandlers", registry).Return(nil)
	mockModule1.On("OnCronDisabled", ctx).Return(nil)

	mockModule2.On("RegisterCronHandlers", registry).Return(nil)
	mockModule2.On("OnCronDisabled", ctx).Return(nil)

	// Expect logger calls
	mockLogger.On("Info", "Module cron disabled", []interface{}{"module", "module1"})
	mockLogger.On("Info", "Module cron disabled", []interface{}{"module", "module2"})

	// Register modules
	err := manager.RegisterModule("module1", mockModule1)
	require.NoError(t, err)

	err = manager.RegisterModule("module2", mockModule2)
	require.NoError(t, err)

	// Call OnCronDisabled
	err = manager.OnCronDisabled(ctx)
	assert.NoError(t, err)

	mockModule1.AssertExpectations(t)
	mockModule2.AssertExpectations(t)
	mockLogger.AssertExpectations(t)
}

func TestCronModuleManager_OnCronDisabled_Error(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}
	mockModule := &MockCronModule{}

	manager := NewCronModuleManager(registry, mockLogger)

	ctx := context.Background()

	// Setup expectations - OnCronDisabled returns error
	mockModule.On("RegisterCronHandlers", registry).Return(nil)
	mockModule.On("OnCronDisabled", ctx).Return(assert.AnError)

	// Register module
	err := manager.RegisterModule("test-module", mockModule)
	require.NoError(t, err)

	// Call OnCronDisabled should return error
	err = manager.OnCronDisabled(ctx)
	assert.Error(t, err)
	assert.Equal(t, assert.AnError, err)

	mockModule.AssertExpectations(t)
}

func TestCronModuleManager_GetModule_NotFound(t *testing.T) {
	registry := createTestRegistry()
	mockLogger := &MockLogger{}

	manager := NewCronModuleManager(registry, mockLogger)

	// Try to get non-existent module
	module, exists := manager.GetModule("non-existent")
	assert.False(t, exists)
	assert.Nil(t, module)
}
