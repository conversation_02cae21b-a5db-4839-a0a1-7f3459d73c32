package socket

import (
	"context"
	"sync"
	"time"

	"wnapi/internal/pkg/logger"
)

// EventRouter implementation của EventRouter interface
type EventRouterImpl struct {
	// Handler registry
	handlers map[string]EventHandler
	handlerMu sync.RWMutex

	// Middleware chain
	middlewares []MiddlewareFunc
	middlewareMu sync.RWMutex

	// Event publisher
	publisher EventPublisher

	// Event metrics
	metrics *EventMetrics

	// Configuration
	config *EventRouterConfig

	// Logger
	logger logger.Logger
}

// EventRouterConfig cấu hình cho EventRouter
type EventRouterConfig struct {
	EnableMetrics       bool
	EnableErrorHandler  bool
	DefaultTimeout      time.Duration
	MaxConcurrentEvents int
}

// DefaultEventRouterConfig trả về cấu hình mặc định
func DefaultEventRouterConfig() *EventRouterConfig {
	return &EventRouterConfig{
		EnableMetrics:       true,
		EnableErrorHandler:  true,
		DefaultTimeout:      30 * time.Second,
		MaxConcurrentEvents: 1000,
	}
}

// NewEventRouter tạo EventRouter mới
func NewEventRouter(
	config *EventRouterConfig,
	publisher EventPublisher,
	logger logger.Logger,
) *EventRouterImpl {
	if config == nil {
		config = DefaultEventRouterConfig()
	}

	return &EventRouterImpl{
		handlers:    make(map[string]EventHandler),
		middlewares: make([]MiddlewareFunc, 0),
		publisher:   publisher,
		metrics:     NewEventMetrics(),
		config:      config,
		logger:      logger,
	}
}

// RegisterHandler đăng ký event handler
func (er *EventRouterImpl) RegisterHandler(eventType string, handler EventHandler) {
	er.handlerMu.Lock()
	defer er.handlerMu.Unlock()

	er.handlers[eventType] = handler
	er.logger.Debug("Event handler registered", "event_type", eventType)
}

// UnregisterHandler hủy đăng ký event handler
func (er *EventRouterImpl) UnregisterHandler(eventType string) {
	er.handlerMu.Lock()
	defer er.handlerMu.Unlock()

	delete(er.handlers, eventType)
	er.logger.Debug("Event handler unregistered", "event_type", eventType)
}

// GetHandler lấy event handler
func (er *EventRouterImpl) GetHandler(eventType string) (EventHandler, bool) {
	er.handlerMu.RLock()
	defer er.handlerMu.RUnlock()

	handler, exists := er.handlers[eventType]
	return handler, exists
}

// Use thêm middleware vào chain
func (er *EventRouterImpl) Use(middleware MiddlewareFunc) {
	er.middlewareMu.Lock()
	defer er.middlewareMu.Unlock()

	er.middlewares = append(er.middlewares, middleware)
	er.logger.Debug("Middleware added to event router")
}

// GetMiddlewares lấy danh sách middlewares
func (er *EventRouterImpl) GetMiddlewares() []MiddlewareFunc {
	er.middlewareMu.RLock()
	defer er.middlewareMu.RUnlock()

	middlewares := make([]MiddlewareFunc, len(er.middlewares))
	copy(middlewares, er.middlewares)
	return middlewares
}

// RouteEvent route event đến handler thích hợp
func (er *EventRouterImpl) RouteEvent(ctx context.Context, conn Connection, event *IncomingMessage) error {
	startTime := time.Now()

	// Update metrics
	if er.config.EnableMetrics {
		er.metrics.IncrementEventCount(event.Type)
	}

	// Get handler
	handler, exists := er.GetHandler(event.Event)
	if !exists {
		er.logger.Warn("No handler found for event", "event_type", event.Event)
		if er.config.EnableMetrics {
			er.metrics.IncrementErrorCount(event.Event)
		}
		return ErrHandlerNotFound
	}

	// Build middleware chain
	finalHandler := er.buildMiddlewareChain(handler)

	// Convert IncomingMessage to data map for handler
	data := event.Data
	if data == nil {
		data = make(map[string]interface{})
	}

	// Add event metadata to data
	data["event_type"] = event.Event
	data["message_type"] = event.Type
	if event.RequestID != "" {
		data["request_id"] = event.RequestID
	}
	if event.RoomID != "" {
		data["room_id"] = event.RoomID
	}

	// Execute handler with timeout
	var err error
	done := make(chan struct{})

	go func() {
		defer close(done)
		err = finalHandler(ctx, conn, data)
	}()

	select {
	case <-done:
		// Handler completed
	case <-time.After(er.config.DefaultTimeout):
		err = ErrHandlerTimeout
	case <-ctx.Done():
		err = ctx.Err()
	}

	// Update metrics
	if er.config.EnableMetrics {
		duration := time.Since(startTime)
		er.metrics.RecordEventDuration(event.Event, duration)

		if err != nil {
			er.metrics.IncrementErrorCount(event.Event)
		}
	}

	if err != nil {
		er.logger.Error("Failed to handle event",
			"event_type", event.Event,
			"connection_id", conn.ID(),
			"error", err)
		return err
	}

	er.logger.Debug("Event handled successfully",
		"event_type", event.Event,
		"connection_id", conn.ID(),
		"duration", time.Since(startTime))

	return nil
}

// PublishEvent publish event đến external systems
func (er *EventRouterImpl) PublishEvent(event *Event) error {
	if er.publisher == nil {
		return nil // No publisher configured
	}

	if err := er.publisher.PublishConnectionEvent(
		event.ConnectionID,
		event.UserID,
		event.Type,
		event.Data,
	); err != nil {
		er.logger.Error("Failed to publish event",
			"event_type", event.Type,
			"error", err)
		return err
	}

	er.logger.Debug("Event published",
		"event_type", event.Type,
		"connection_id", event.ConnectionID)

	return nil
}

// buildMiddlewareChain xây dựng middleware chain
func (er *EventRouterImpl) buildMiddlewareChain(handler EventHandler) EventHandler {
	er.middlewareMu.RLock()
	middlewares := make([]MiddlewareFunc, len(er.middlewares))
	copy(middlewares, er.middlewares)
	er.middlewareMu.RUnlock()

	// Build chain từ cuối lên đầu
	final := handler
	for i := len(middlewares) - 1; i >= 0; i-- {
		final = middlewares[i](final)
	}

	return final
}

// GetEventMetrics lấy event metrics
func (er *EventRouterImpl) GetEventMetrics() *EventMetrics {
	if !er.config.EnableMetrics {
		return nil
	}
	return er.metrics.Copy()
}

// EventMetrics thống kê events
type EventMetrics struct {
	EventCounts    map[string]int64          `json:"eventCounts"`
	ErrorCounts    map[string]int64          `json:"errorCounts"`
	EventDurations map[string]time.Duration  `json:"eventDurations"`
	TotalEvents    int64                     `json:"totalEvents"`
	TotalErrors    int64                     `json:"totalErrors"`
	LastUpdated    time.Time                 `json:"lastUpdated"`

	mu sync.RWMutex
}

// NewEventMetrics tạo EventMetrics mới
func NewEventMetrics() *EventMetrics {
	return &EventMetrics{
		EventCounts:    make(map[string]int64),
		ErrorCounts:    make(map[string]int64),
		EventDurations: make(map[string]time.Duration),
		LastUpdated:    time.Now(),
	}
}

// IncrementEventCount tăng event count
func (em *EventMetrics) IncrementEventCount(eventType string) {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.EventCounts[eventType]++
	em.TotalEvents++
	em.LastUpdated = time.Now()
}

// IncrementErrorCount tăng error count
func (em *EventMetrics) IncrementErrorCount(eventType string) {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.ErrorCounts[eventType]++
	em.TotalErrors++
	em.LastUpdated = time.Now()
}

// RecordEventDuration ghi lại thời gian xử lý event
func (em *EventMetrics) RecordEventDuration(eventType string, duration time.Duration) {
	em.mu.Lock()
	defer em.mu.Unlock()

	// Simple moving average
	if current, exists := em.EventDurations[eventType]; exists {
		em.EventDurations[eventType] = (current + duration) / 2
	} else {
		em.EventDurations[eventType] = duration
	}
	em.LastUpdated = time.Now()
}

// Copy tạo copy của metrics
func (em *EventMetrics) Copy() *EventMetrics {
	em.mu.RLock()
	defer em.mu.RUnlock()

	eventCounts := make(map[string]int64)
	for k, v := range em.EventCounts {
		eventCounts[k] = v
	}

	errorCounts := make(map[string]int64)
	for k, v := range em.ErrorCounts {
		errorCounts[k] = v
	}

	eventDurations := make(map[string]time.Duration)
	for k, v := range em.EventDurations {
		eventDurations[k] = v
	}

	return &EventMetrics{
		EventCounts:    eventCounts,
		ErrorCounts:    errorCounts,
		EventDurations: eventDurations,
		TotalEvents:    em.TotalEvents,
		TotalErrors:    em.TotalErrors,
		LastUpdated:    em.LastUpdated,
	}
}

// EventRegistry registry cho event handlers
type EventRegistry struct {
	handlers map[string]EventHandler
	mu       sync.RWMutex
	logger   logger.Logger
}

// NewEventRegistry tạo EventRegistry mới
func NewEventRegistry(logger logger.Logger) *EventRegistry {
	return &EventRegistry{
		handlers: make(map[string]EventHandler),
		logger:   logger,
	}
}

// Register đăng ký event handler
func (er *EventRegistry) Register(eventType string, handler EventHandler) {
	er.mu.Lock()
	defer er.mu.Unlock()

	er.handlers[eventType] = handler
	er.logger.Debug("Event handler registered in registry", "event_type", eventType)
}

// Get lấy event handler
func (er *EventRegistry) Get(eventType string) (EventHandler, bool) {
	er.mu.RLock()
	defer er.mu.RUnlock()

	handler, exists := er.handlers[eventType]
	return handler, exists
}

// GetAll lấy tất cả handlers
func (er *EventRegistry) GetAll() map[string]EventHandler {
	er.mu.RLock()
	defer er.mu.RUnlock()

	result := make(map[string]EventHandler)
	for k, v := range er.handlers {
		result[k] = v
	}
	return result
}

// Unregister hủy đăng ký handler
func (er *EventRegistry) Unregister(eventType string) {
	er.mu.Lock()
	defer er.mu.Unlock()

	delete(er.handlers, eventType)
	er.logger.Debug("Event handler unregistered from registry", "event_type", eventType)
}

// Clear xóa tất cả handlers
func (er *EventRegistry) Clear() {
	er.mu.Lock()
	defer er.mu.Unlock()

	er.handlers = make(map[string]EventHandler)
	er.logger.Debug("All event handlers cleared from registry")
}

// Common middleware functions

// LoggingMiddleware middleware để log events
func LoggingMiddleware(logger logger.Logger) MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			eventType, _ := data["event_type"].(string)
			connID := conn.ID()

			logger.Debug("Processing event",
				"event_type", eventType,
				"connection_id", connID)

			start := time.Now()
			err := next(ctx, conn, data)
			duration := time.Since(start)

			if err != nil {
				logger.Error("Event processing failed",
					"event_type", eventType,
					"connection_id", connID,
					"duration", duration,
					"error", err)
			} else {
				logger.Debug("Event processed successfully",
					"event_type", eventType,
					"connection_id", connID,
					"duration", duration)
			}

			return err
		}
	}
}

// AuthenticationMiddleware middleware để check authentication
func AuthenticationMiddleware() MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			// Basic authentication check
			if conn.UserID() == "" {
				return ErrAuthRequired
			}

			// Add user info to context data
			data["user_id"] = conn.UserID()
			data["tenant_id"] = conn.TenantID()
			data["website_id"] = conn.WebsiteID()

			return next(ctx, conn, data)
		}
	}
}

// RateLimitMiddleware middleware để rate limiting
func RateLimitMiddleware(limit int, window time.Duration) MiddlewareFunc {
	type rateLimitInfo struct {
		count     int
		window    time.Time
		mu        sync.Mutex
	}

	rateLimits := make(map[string]*rateLimitInfo)
	var globalMu sync.RWMutex

	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			connID := conn.ID()

			globalMu.RLock()
			info, exists := rateLimits[connID]
			globalMu.RUnlock()

			if !exists {
				info = &rateLimitInfo{
					count:  0,
					window: time.Now(),
				}
				globalMu.Lock()
				rateLimits[connID] = info
				globalMu.Unlock()
			}

			info.mu.Lock()
			defer info.mu.Unlock()

			now := time.Now()
			if now.Sub(info.window) > window {
				// Reset window
				info.count = 0
				info.window = now
			}

			if info.count >= limit {
				return ErrRateLimitExceeded
			}

			info.count++
			return next(ctx, conn, data)
		}
	}
}

// ValidationMiddleware middleware để validate data
func ValidationMiddleware() MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			// Basic validation
			eventType, ok := data["event_type"].(string)
			if !ok || eventType == "" {
				return ErrInvalidEvent
			}

			return next(ctx, conn, data)
		}
	}
}

// Error definitions for event router
var (
	ErrHandlerNotFound = NewSocketError("HANDLER_NOT_FOUND", "Event handler not found")
	ErrHandlerTimeout  = NewSocketError("HANDLER_TIMEOUT", "Event handler timeout")
)
