package socket

import (
	"context"
	"fmt"
	"sync"
	"time"

	"wnapi/internal/pkg/logger"
)

// HubConfig cấu hình cho Hub
type HubConfig struct {
	MaxConnections        int
	MaxConnectionsPerUser int
	MaxRoomsPerUser       int
	PingInterval          time.Duration
	WriteTimeout          time.Duration
	ReadTimeout           time.Duration
	CleanupInterval       time.Duration
	EnableStatistics      bool
}

// DefaultHubConfig trả về cấu hình mặc định cho Hub
func DefaultHubConfig() *HubConfig {
	return &HubConfig{
		MaxConnections:        DefaultMaxConnections,
		MaxConnectionsPerUser: DefaultMaxConnectionsPerUser,
		MaxRoomsPerUser:       DefaultMaxRoomsPerUser,
		PingInterval:          DefaultPingInterval,
		WriteTimeout:          DefaultWriteTimeout,
		ReadTimeout:           DefaultReadTimeout,
		CleanupInterval:       DefaultCleanupInterval,
		EnableStatistics:      true,
	}
}

// Hub quản lý tất cả WebSocket connections và rooms
type HubImpl struct {
	// Configuration
	config *HubConfig

	// Connection management
	connections map[string]Connection // connectionID -> Connection
	userConns   map[string][]string   // userID -> []connectionID

	// Room management
	rooms map[string]*Room // roomID -> Room

	// Channels for operations
	register   chan Connection
	unregister chan Connection
	broadcast  chan *BroadcastMessage
	shutdown   chan struct{}

	// Event handling
	eventRouter EventRouter

	// Statistics
	stats *HubStatistics

	// Synchronization
	mu     sync.RWMutex
	userMu sync.RWMutex
	roomMu sync.RWMutex

	// Context
	ctx    context.Context
	cancel context.CancelFunc

	// Logger
	logger logger.Logger
}

// BroadcastMessage tin nhắn để broadcast
type BroadcastMessage struct {
	RoomID  string
	Message *OutgoingMessage
	Exclude []string // Danh sách connectionID để loại trừ
}

// NewHub tạo Hub instance mới
func NewHub(config *HubConfig, eventRouter EventRouter, logger logger.Logger) *HubImpl {
	if config == nil {
		config = DefaultHubConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	hub := &HubImpl{
		config:      config,
		connections: make(map[string]Connection),
		userConns:   make(map[string][]string),
		rooms:       make(map[string]*Room),
		register:    make(chan Connection, 100),
		unregister:  make(chan Connection, 100),
		broadcast:   make(chan *BroadcastMessage, 1000),
		shutdown:    make(chan struct{}),
		eventRouter: eventRouter,
		stats:       NewHubStatistics(),
		ctx:         ctx,
		cancel:      cancel,
		logger:      logger,
	}

	return hub
}

// Start khởi động Hub
func (h *HubImpl) Start() error {
	h.logger.Info("Starting WebSocket Hub")

	// Start background goroutines
	go h.run()
	go h.statisticsReporter()
	go h.cleanupRoutine()

	return nil
}

// Stop dừng Hub
func (h *HubImpl) Stop() error {
	h.logger.Info("Stopping WebSocket Hub")

	h.cancel()
	close(h.shutdown)

	// Đóng tất cả connections
	h.mu.Lock()
	for _, conn := range h.connections {
		conn.Close()
	}
	h.mu.Unlock()

	h.logger.Info("WebSocket Hub stopped")
	return nil
}

// RegisterConnection đăng ký connection mới
func (h *HubImpl) RegisterConnection(conn Connection) error {
	// Kiểm tra limits
	if err := h.checkConnectionLimits(conn.UserID()); err != nil {
		return err
	}

	select {
	case h.register <- conn:
		return nil
	case <-h.ctx.Done():
		return ErrHubShutdown
	default:
		return ErrHubBusy
	}
}

// UnregisterConnection hủy đăng ký connection
func (h *HubImpl) UnregisterConnection(connectionID string) error {
	h.mu.RLock()
	conn, exists := h.connections[connectionID]
	h.mu.RUnlock()

	if !exists {
		return ErrConnectionNotFound
	}

	select {
	case h.unregister <- conn:
		return nil
	case <-h.ctx.Done():
		// Hub đang shutdown, đóng connection trực tiếp
		h.removeConnection(conn)
		return ErrHubShutdown
	default:
		// Channel đầy, xử lý trực tiếp
		h.removeConnection(conn)
		return ErrHubBusy
	}
}

// BroadcastToRoom broadcast message đến room
func (h *HubImpl) BroadcastToRoom(roomID string, message *OutgoingMessage, exclude ...string) error {
	broadcastMsg := &BroadcastMessage{
		RoomID:  roomID,
		Message: message,
		Exclude: exclude,
	}

	select {
	case h.broadcast <- broadcastMsg:
		return nil
	case <-h.ctx.Done():
		return ErrHubShutdown
	default:
		return ErrHubBusy
	}
}

// SendToConnection gửi message đến connection cụ thể
func (h *HubImpl) SendToConnection(connectionID string, message *OutgoingMessage) error {
	h.mu.RLock()
	conn, exists := h.connections[connectionID]
	h.mu.RUnlock()

	if !exists {
		return ErrConnectionNotFound
	}

	return conn.SendMessage(message)
}

// SendToUser gửi message đến tất cả connections của user
func (h *HubImpl) SendToUser(userID string, message *OutgoingMessage) error {
	h.userMu.RLock()
	connectionIDs, exists := h.userConns[userID]
	h.userMu.RUnlock()

	if !exists {
		return ErrUserNotConnected
	}

	var errors []error
	for _, connID := range connectionIDs {
		if err := h.SendToConnection(connID, message); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send to some connections: %v", errors)
	}

	return nil
}

// GetConnection lấy connection theo ID
func (h *HubImpl) GetConnection(connectionID string) (Connection, bool) {
	h.mu.RLock()
	defer h.mu.RUnlock()
	
	conn, exists := h.connections[connectionID]
	return conn, exists
}

// GetUserConnections lấy tất cả connections của user
func (h *HubImpl) GetUserConnections(userID string) []Connection {
	h.userMu.RLock()
	connectionIDs := h.userConns[userID]
	h.userMu.RUnlock()

	if len(connectionIDs) == 0 {
		return nil
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	connections := make([]Connection, 0, len(connectionIDs))
	for _, connID := range connectionIDs {
		if conn, exists := h.connections[connID]; exists {
			connections = append(connections, conn)
		}
	}

	return connections
}

// GetRoom lấy room theo ID
func (h *HubImpl) GetRoom(roomID string) (*Room, bool) {
	h.roomMu.RLock()
	defer h.roomMu.RUnlock()
	
	room, exists := h.rooms[roomID]
	return room, exists
}

// CreateRoom tạo room mới
func (h *HubImpl) CreateRoom(roomID string, config *RoomConfig) (*Room, error) {
	h.roomMu.Lock()
	defer h.roomMu.Unlock()

	if _, exists := h.rooms[roomID]; exists {
		return nil, ErrRoomExists
	}

	room := NewRoom(roomID, config, h.logger)
	h.rooms[roomID] = room

	h.stats.AddRoom()
	h.logger.Debug("Room created", "roomID", roomID)

	return room, nil
}

// DeleteRoom xóa room
func (h *HubImpl) DeleteRoom(roomID string) error {
	h.roomMu.Lock()
	room, exists := h.rooms[roomID]
	if exists {
		delete(h.rooms, roomID)
	}
	h.roomMu.Unlock()

	if !exists {
		return ErrRoomNotFound
	}

	// Disconnect all connections from room
	room.DisconnectAll()
	h.stats.RemoveRoom()
	h.logger.Debug("Room deleted", "roomID", roomID)

	return nil
}

// JoinRoom thêm connection vào room
func (h *HubImpl) JoinRoom(connectionID, roomID string) error {
	// Lấy connection
	h.mu.RLock()
	conn, exists := h.connections[connectionID]
	h.mu.RUnlock()

	if !exists {
		return ErrConnectionNotFound
	}

	// Lấy room
	h.roomMu.RLock()
	room, exists := h.rooms[roomID]
	h.roomMu.RUnlock()

	if !exists {
		return ErrRoomNotFound
	}

	// Kiểm tra room limits cho user
	if err := h.checkRoomLimits(conn.UserID()); err != nil {
		return err
	}

	// Join room
	if err := room.AddConnection(conn); err != nil {
		return err
	}

	// Thêm room vào connection
	if err := conn.JoinRoom(roomID); err != nil {
		return err
	}

	h.logger.Debug("Connection joined room", 
		"connectionID", connectionID, 
		"roomID", roomID,
		"userID", conn.UserID())

	return nil
}

// LeaveRoom xóa connection khỏi room
func (h *HubImpl) LeaveRoom(connectionID, roomID string) error {
	// Lấy connection
	h.mu.RLock()
	conn, exists := h.connections[connectionID]
	h.mu.RUnlock()

	if !exists {
		return ErrConnectionNotFound
	}

	// Lấy room
	h.roomMu.RLock()
	room, exists := h.rooms[roomID]
	h.roomMu.RUnlock()

	if !exists {
		return ErrRoomNotFound
	}

	// Leave room
	room.RemoveConnection(connectionID)
	if err := conn.LeaveRoom(roomID); err != nil {
		return err
	}

	h.logger.Debug("Connection left room", 
		"connectionID", connectionID, 
		"roomID", roomID,
		"userID", conn.UserID())

	return nil
}

// GetStatistics lấy thống kê Hub
func (h *HubImpl) GetStatistics() *HubStatistics {
	return h.stats.Copy()
}

// run chạy main loop của Hub
func (h *HubImpl) run() {
	for {
		select {
		case conn := <-h.register:
			h.handleRegister(conn)

		case conn := <-h.unregister:
			h.handleUnregister(conn)

		case broadcast := <-h.broadcast:
			h.handleBroadcast(broadcast)

		case <-h.shutdown:
			return
		}
	}
}

// handleRegister xử lý đăng ký connection
func (h *HubImpl) handleRegister(conn Connection) {
	h.mu.Lock()
	h.connections[conn.ID()] = conn
	h.mu.Unlock()

	h.userMu.Lock()
	h.userConns[conn.UserID()] = append(h.userConns[conn.UserID()], conn.ID())
	h.userMu.Unlock()

	h.stats.AddConnection()
	h.logger.Debug("Connection registered", 
		"connectionID", conn.ID(), 
		"userID", conn.UserID())

	// Publish event
	if h.eventRouter != nil {
		event := &Event{
			Type: EventTypeConnectionRegistered,
			Data: map[string]interface{}{
				"connectionID": conn.ID(),
				"userID":       conn.UserID(),
			},
		}
		h.eventRouter.PublishEvent(event)
	}
}

// handleUnregister xử lý hủy đăng ký connection
func (h *HubImpl) handleUnregister(conn Connection) {
	h.removeConnection(conn)
}

// removeConnection xóa connection khỏi Hub
func (h *HubImpl) removeConnection(conn Connection) {
	// Remove from connections map
	h.mu.Lock()
	delete(h.connections, conn.ID())
	h.mu.Unlock()

	// Remove from user connections
	h.userMu.Lock()
	if connections, exists := h.userConns[conn.UserID()]; exists {
		for i, connID := range connections {
			if connID == conn.ID() {
				h.userConns[conn.UserID()] = append(connections[:i], connections[i+1:]...)
				break
			}
		}
		// Xóa user nếu không còn connections
		if len(h.userConns[conn.UserID()]) == 0 {
			delete(h.userConns, conn.UserID())
		}
	}
	h.userMu.Unlock()

	// Remove from all rooms
	h.roomMu.RLock()
	for _, roomID := range conn.GetRooms() {
		if room, exists := h.rooms[roomID]; exists {
			room.RemoveConnection(conn.ID())
		}
	}
	h.roomMu.RUnlock()

	h.stats.RemoveConnection()
	h.logger.Debug("Connection unregistered", 
		"connectionID", conn.ID(), 
		"userID", conn.UserID())

	// Publish event
	if h.eventRouter != nil {
		event := &Event{
			Type: EventTypeConnectionUnregistered,
			Data: map[string]interface{}{
				"connectionID": conn.ID(),
				"userID":       conn.UserID(),
			},
		}
		h.eventRouter.PublishEvent(event)
	}
}

// handleBroadcast xử lý broadcast message
func (h *HubImpl) handleBroadcast(broadcast *BroadcastMessage) {
	h.roomMu.RLock()
	room, exists := h.rooms[broadcast.RoomID]
	h.roomMu.RUnlock()

	if !exists {
		h.logger.Warn("Broadcast to non-existent room", "roomID", broadcast.RoomID)
		return
	}

	// Tạo set của excluded connections
	excludeSet := make(map[string]bool)
	for _, connID := range broadcast.Exclude {
		excludeSet[connID] = true
	}

	// Broadcast đến tất cả connections trong room
	room.mu.RLock()
	connectionIDs := make([]string, 0, len(room.connections))
	for connID := range room.connections {
		if !excludeSet[connID] {
			connectionIDs = append(connectionIDs, connID)
		}
	}
	room.mu.RUnlock()

	// Send message to connections
	h.mu.RLock()
	for _, connID := range connectionIDs {
		if conn, exists := h.connections[connID]; exists {
			if err := conn.SendMessage(broadcast.Message); err != nil {
				h.logger.Error("Failed to send message to connection", 
					"error", err, 
					"connectionID", connID)
			}
		}
	}
	h.mu.RUnlock()

	h.stats.AddBroadcast()
}

// checkConnectionLimits kiểm tra giới hạn connections
func (h *HubImpl) checkConnectionLimits(userID string) error {
	// Kiểm tra tổng số connections
	h.mu.RLock()
	totalConnections := len(h.connections)
	h.mu.RUnlock()

	if totalConnections >= h.config.MaxConnections {
		return ErrMaxConnectionsReached
	}

	// Kiểm tra connections per user
	h.userMu.RLock()
	userConnections := len(h.userConns[userID])
	h.userMu.RUnlock()

	if userConnections >= h.config.MaxConnectionsPerUser {
		return ErrMaxConnectionsPerUserReached
	}

	return nil
}

// checkRoomLimits kiểm tra giới hạn rooms cho user
func (h *HubImpl) checkRoomLimits(userID string) error {
	h.userMu.RLock()
	connectionIDs := h.userConns[userID]
	h.userMu.RUnlock()

	if len(connectionIDs) == 0 {
		return nil
	}

	// Đếm số rooms mà user đã join
	h.mu.RLock()
	roomCount := 0
	if len(connectionIDs) > 0 {
		// Lấy connection đầu tiên để đếm rooms
		if conn, exists := h.connections[connectionIDs[0]]; exists {
			roomCount = len(conn.GetRooms())
		}
	}
	h.mu.RUnlock()

	if roomCount >= h.config.MaxRoomsPerUser {
		return ErrMaxRoomsPerUserReached
	}

	return nil
}

// statisticsReporter báo cáo statistics định kỳ
func (h *HubImpl) statisticsReporter() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if h.config.EnableStatistics {
				stats := h.GetStatistics()
				h.logger.Info("Hub Statistics", 
					"connections", stats.TotalConnections,
					"rooms", stats.TotalRooms,
					"messages", stats.TotalMessages,
					"broadcasts", stats.TotalBroadcasts)
			}

		case <-h.ctx.Done():
			return
		}
	}
}

// cleanupRoutine dọn dẹp resources định kỳ
func (h *HubImpl) cleanupRoutine() {
	ticker := time.NewTicker(h.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.performCleanup()

		case <-h.ctx.Done():
			return
		}
	}
}

// performCleanup thực hiện cleanup
func (h *HubImpl) performCleanup() {
	// Cleanup dead connections
	h.mu.RLock()
	deadConnections := make([]Connection, 0)
	for _, conn := range h.connections {
		if !conn.IsAlive() {
			deadConnections = append(deadConnections, conn)
		}
	}
	h.mu.RUnlock()

	for _, conn := range deadConnections {
		h.logger.Debug("Cleaning up dead connection", "connectionID", conn.ID())
		h.UnregisterConnection(conn.ID())
		conn.Close()
	}

	// Cleanup empty rooms
	h.roomMu.RLock()
	emptyRooms := make([]string, 0)
	for roomID, room := range h.rooms {
		if room.IsEmpty() && room.CanAutoDelete() {
			emptyRooms = append(emptyRooms, roomID)
		}
	}
	h.roomMu.RUnlock()

	for _, roomID := range emptyRooms {
		h.logger.Debug("Cleaning up empty room", "roomID", roomID)
		h.DeleteRoom(roomID)
	}
}
