package socket

import (
	"errors"
	"fmt"
)

// Error codes
const (
	// Connection errors
	ErrCodeConnectionFailed    = "CONNECTION_FAILED"
	ErrCodeConnectionClosed    = "CONNECTION_CLOSED"
	ErrCodeConnectionTimeout   = "CONNECTION_TIMEOUT"
	ErrCodeInvalidConnection   = "INVALID_CONNECTION"
	ErrCodeConnectionNotFound  = "CONNECTION_NOT_FOUND"
	ErrCodeConnectionExists    = "CONNECTION_EXISTS"

	// Authentication errors
	ErrCodeAuthRequired        = "AUTH_REQUIRED"
	ErrCodeAuthFailed         = "AUTH_FAILED"
	ErrCodeAuthTimeout        = "AUTH_TIMEOUT"
	ErrCodeInvalidToken       = "INVALID_TOKEN"
	ErrCodeTokenExpired       = "TOKEN_EXPIRED"
	ErrCodeUnauthorized       = "UNAUTHORIZED"

	// Message errors
	ErrCodeInvalidMessage     = "INVALID_MESSAGE"
	ErrCodeMessageTooLarge    = "MESSAGE_TOO_LARGE"
	ErrCodeInvalidMessageType = "INVALID_MESSAGE_TYPE"
	ErrCodeInvalidEvent       = "INVALID_EVENT"
	ErrCodeMissingRequestID   = "MISSING_REQUEST_ID"
	ErrCodeMissingRoomID      = "MISSING_ROOM_ID"
	ErrCodeMessageQueueFull   = "MESSAGE_QUEUE_FULL"

	// Room errors
	ErrCodeRoomNotFound       = "ROOM_NOT_FOUND"
	ErrCodeRoomExists         = "ROOM_EXISTS"
	ErrCodeRoomFull           = "ROOM_FULL"
	ErrCodeRoomClosed         = "ROOM_CLOSED"
	ErrCodeNotInRoom          = "NOT_IN_ROOM"
	ErrCodeAlreadyInRoom      = "ALREADY_IN_ROOM"
	ErrCodeRoomPermissionDenied = "ROOM_PERMISSION_DENIED"

	// Rate limiting errors
	ErrCodeRateLimitExceeded  = "RATE_LIMIT_EXCEEDED"
	ErrCodeTooManyConnections = "TOO_MANY_CONNECTIONS"
	ErrCodeTooManyRequests    = "TOO_MANY_REQUESTS"

	// System errors
	ErrCodeServerError        = "SERVER_ERROR"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	ErrCodeHubShutdown        = "HUB_SHUTDOWN"
	ErrCodeHubBusy           = "HUB_BUSY"
	ErrCodeInternalError     = "INTERNAL_ERROR"

	// Validation errors
	ErrCodeValidationFailed   = "VALIDATION_FAILED"
	ErrCodeInvalidRequest     = "INVALID_REQUEST"
	ErrCodeMissingParameter   = "MISSING_PARAMETER"
	ErrCodeInvalidParameter   = "INVALID_PARAMETER"
)

// SocketError đại diện cho một WebSocket error
type SocketError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Cause   error  `json:"-"`
}

// Error implement error interface
func (e *SocketError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap implement errors.Unwrap interface
func (e *SocketError) Unwrap() error {
	return e.Cause
}

// NewSocketError tạo SocketError mới
func NewSocketError(code, message string, details ...string) *SocketError {
	err := &SocketError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// NewSocketErrorWithCause tạo SocketError với cause
func NewSocketErrorWithCause(code, message string, cause error, details ...string) *SocketError {
	err := &SocketError{
		Code:    code,
		Message: message,
		Cause:   cause,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// Predefined errors
var (
	// Connection errors
	ErrConnectionFailed        = NewSocketError(ErrCodeConnectionFailed, "Connection failed")
	ErrConnectionClosed        = NewSocketError(ErrCodeConnectionClosed, "Connection closed")
	ErrConnectionTimeout       = NewSocketError(ErrCodeConnectionTimeout, "Connection timeout")
	ErrInvalidConnection       = NewSocketError(ErrCodeInvalidConnection, "Invalid connection")
	ErrConnectionNotFound      = NewSocketError(ErrCodeConnectionNotFound, "Connection not found")
	ErrConnectionExists        = NewSocketError(ErrCodeConnectionExists, "Connection already exists")

	// Authentication errors
	ErrAuthRequired           = NewSocketError(ErrCodeAuthRequired, "Authentication required")
	ErrAuthFailed            = NewSocketError(ErrCodeAuthFailed, "Authentication failed")
	ErrAuthTimeout           = NewSocketError(ErrCodeAuthTimeout, "Authentication timeout")
	ErrInvalidToken          = NewSocketError(ErrCodeInvalidToken, "Invalid token")
	ErrTokenExpired          = NewSocketError(ErrCodeTokenExpired, "Token expired")
	ErrUnauthorized          = NewSocketError(ErrCodeUnauthorized, "Unauthorized")

	// Message errors
	ErrInvalidMessage        = NewSocketError(ErrCodeInvalidMessage, "Invalid message")
	ErrMessageTooLarge       = NewSocketError(ErrCodeMessageTooLarge, "Message too large")
	ErrInvalidMessageType    = NewSocketError(ErrCodeInvalidMessageType, "Invalid message type")
	ErrInvalidEvent          = NewSocketError(ErrCodeInvalidEvent, "Invalid event")
	ErrMissingRequestID      = NewSocketError(ErrCodeMissingRequestID, "Missing request ID")
	ErrMissingRoomID         = NewSocketError(ErrCodeMissingRoomID, "Missing room ID")
	ErrMessageQueueFull      = NewSocketError(ErrCodeMessageQueueFull, "Message queue full")

	// Room errors
	ErrRoomNotFound          = NewSocketError(ErrCodeRoomNotFound, "Room not found")
	ErrRoomExists            = NewSocketError(ErrCodeRoomExists, "Room already exists")
	ErrRoomFull              = NewSocketError(ErrCodeRoomFull, "Room is full")
	ErrRoomClosed            = NewSocketError(ErrCodeRoomClosed, "Room is closed")
	ErrNotInRoom             = NewSocketError(ErrCodeNotInRoom, "Not in room")
	ErrAlreadyInRoom         = NewSocketError(ErrCodeAlreadyInRoom, "Already in room")
	ErrRoomPermissionDenied  = NewSocketError(ErrCodeRoomPermissionDenied, "Room permission denied")

	// Additional room errors
	ErrUserNotConnected         = NewSocketError("USER_NOT_CONNECTED", "User not connected")
	ErrUserNotInRoom           = NewSocketError("USER_NOT_IN_ROOM", "User not in room")
	ErrConnectionAlreadyInRoom = NewSocketError("CONNECTION_ALREADY_IN_ROOM", "Connection already in room")
	ErrMaxConnectionsReached   = NewSocketError("MAX_CONNECTIONS_REACHED", "Maximum connections reached")
	ErrMaxConnectionsPerUserReached = NewSocketError("MAX_CONNECTIONS_PER_USER_REACHED", "Maximum connections per user reached")
	ErrMaxRoomsPerUserReached  = NewSocketError("MAX_ROOMS_PER_USER_REACHED", "Maximum rooms per user reached")

	// Rate limiting errors
	ErrRateLimitExceeded     = NewSocketError(ErrCodeRateLimitExceeded, "Rate limit exceeded")
	ErrTooManyConnections    = NewSocketError(ErrCodeTooManyConnections, "Too many connections")
	ErrTooManyRequests       = NewSocketError(ErrCodeTooManyRequests, "Too many requests")

	// System errors
	ErrServerError           = NewSocketError(ErrCodeServerError, "Server error")
	ErrServiceUnavailable    = NewSocketError(ErrCodeServiceUnavailable, "Service unavailable")
	ErrHubShutdown          = NewSocketError(ErrCodeHubShutdown, "Hub is shutting down")
	ErrHubBusy              = NewSocketError(ErrCodeHubBusy, "Hub is busy")
	ErrInternalError        = NewSocketError(ErrCodeInternalError, "Internal error")

	// Validation errors
	ErrValidationFailed     = NewSocketError(ErrCodeValidationFailed, "Validation failed")
	ErrInvalidRequest       = NewSocketError(ErrCodeInvalidRequest, "Invalid request")
	ErrMissingParameter     = NewSocketError(ErrCodeMissingParameter, "Missing parameter")
	ErrInvalidParameter     = NewSocketError(ErrCodeInvalidParameter, "Invalid parameter")
)

// IsSocketError kiểm tra có phải SocketError không
func IsSocketError(err error) bool {
	_, ok := err.(*SocketError)
	return ok
}

// GetSocketError lấy SocketError từ error
func GetSocketError(err error) (*SocketError, bool) {
	var socketErr *SocketError
	if errors.As(err, &socketErr) {
		return socketErr, true
	}
	return nil, false
}

// WrapError wrap error thành SocketError
func WrapError(err error, code, message string) *SocketError {
	return NewSocketErrorWithCause(code, message, err)
}

// ToErrorDetail chuyển đổi error thành ErrorDetail
func ToErrorDetail(err error) *ErrorDetail {
	if socketErr, ok := GetSocketError(err); ok {
		return &ErrorDetail{
			Code:    socketErr.Code,
			Message: socketErr.Message,
			Details: socketErr.Details,
		}
	}
	
	return &ErrorDetail{
		Code:    ErrCodeInternalError,
		Message: err.Error(),
	}
}

// Error messages map
var ErrorMessages = map[string]string{
	ErrCodeConnectionFailed:     "Failed to establish WebSocket connection",
	ErrCodeConnectionClosed:     "WebSocket connection was closed",
	ErrCodeConnectionTimeout:    "WebSocket connection timed out",
	ErrCodeInvalidConnection:    "Invalid WebSocket connection",
	ErrCodeConnectionNotFound:   "WebSocket connection not found",
	ErrCodeConnectionExists:     "WebSocket connection already exists",
	ErrCodeAuthRequired:         "Authentication is required",
	ErrCodeAuthFailed:          "Authentication failed",
	ErrCodeAuthTimeout:         "Authentication timed out",
	ErrCodeInvalidToken:        "Invalid authentication token",
	ErrCodeTokenExpired:        "Authentication token has expired",
	ErrCodeUnauthorized:        "Unauthorized access",
	ErrCodeInvalidMessage:      "Invalid message format",
	ErrCodeMessageTooLarge:     "Message size exceeds limit",
	ErrCodeInvalidMessageType:  "Invalid message type",
	ErrCodeInvalidEvent:        "Invalid event type",
	ErrCodeMissingRequestID:    "Request ID is required",
	ErrCodeMissingRoomID:       "Room ID is required",
	ErrCodeMessageQueueFull:    "Message queue is full",
	ErrCodeRoomNotFound:        "Room not found",
	ErrCodeRoomExists:          "Room already exists",
	ErrCodeRoomFull:            "Room has reached maximum capacity",
	ErrCodeRoomClosed:          "Room is closed",
	ErrCodeNotInRoom:           "Not a member of the room",
	ErrCodeAlreadyInRoom:       "Already a member of the room",
	ErrCodeRoomPermissionDenied: "Insufficient room permissions",
	ErrCodeRateLimitExceeded:   "Rate limit exceeded",
	ErrCodeTooManyConnections:  "Too many active connections",
	ErrCodeTooManyRequests:     "Too many requests",
	ErrCodeServerError:         "Internal server error",
	ErrCodeServiceUnavailable:  "Service temporarily unavailable",
	ErrCodeHubShutdown:        "WebSocket hub is shutting down",
	ErrCodeHubBusy:            "WebSocket hub is busy",
	ErrCodeInternalError:       "Internal system error",
	ErrCodeValidationFailed:    "Request validation failed",
	ErrCodeInvalidRequest:      "Invalid request format",
	ErrCodeMissingParameter:    "Required parameter is missing",
	ErrCodeInvalidParameter:    "Parameter value is invalid",
}

// GetErrorMessage lấy error message từ code
func GetErrorMessage(code string) string {
	if message, exists := ErrorMessages[code]; exists {
		return message
	}
	return "Unknown error"
}
