package socket

import (
	"context"
	"sync"
	"time"

	"wnapi/internal/pkg/logger"
)

// HeartbeatManager quản lý heartbeat cho connections
type HeartbeatManager struct {
	// Configuration
	config *HeartbeatConfig

	// Connection tracking
	connections map[string]*HeartbeatConnection
	connMutex   sync.RWMutex

	// Control channels
	register   chan *HeartbeatConnection
	unregister chan string
	shutdown   chan struct{}

	// Context
	ctx    context.Context
	cancel context.CancelFunc

	// Logger
	logger logger.Logger
}

// HeartbeatConfig cấu hình cho heartbeat
type HeartbeatConfig struct {
	Interval       time.Duration // Khoảng thời gian gửi ping
	Timeout        time.Duration // Timeout cho pong response
	MaxMissed      int           // Số lần miss tối đa trước khi disconnect
	CheckInterval  time.Duration // Khoảng thời gian check health
	EnableReconnect bool         // Cho phép auto reconnect
}

// DefaultHeartbeatConfig trả về cấu hình mặc định
func DefaultHeartbeatConfig() *HeartbeatConfig {
	return &HeartbeatConfig{
		Interval:       DefaultPingInterval,
		Timeout:        DefaultPongTimeout,
		MaxMissed:      3,
		CheckInterval:  10 * time.Second,
		EnableReconnect: false,
	}
}

// HeartbeatConnection tracking cho một connection
type HeartbeatConnection struct {
	ConnectionID   string
	Connection     Connection
	LastPing       time.Time
	LastPong       time.Time
	MissedCount    int
	IsHealthy      bool
	mu             sync.RWMutex
}

// NewHeartbeatManager tạo HeartbeatManager mới
func NewHeartbeatManager(config *HeartbeatConfig, logger logger.Logger) *HeartbeatManager {
	if config == nil {
		config = DefaultHeartbeatConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	hm := &HeartbeatManager{
		config:      config,
		connections: make(map[string]*HeartbeatConnection),
		register:    make(chan *HeartbeatConnection, 100),
		unregister:  make(chan string, 100),
		shutdown:    make(chan struct{}),
		ctx:         ctx,
		cancel:      cancel,
		logger:      logger.Named("heartbeat-manager"),
	}

	return hm
}

// Start bắt đầu heartbeat manager
func (hm *HeartbeatManager) Start() error {
	hm.logger.Info("Starting heartbeat manager")

	go hm.run()
	go hm.healthChecker()

	return nil
}

// Stop dừng heartbeat manager
func (hm *HeartbeatManager) Stop() error {
	hm.logger.Info("Stopping heartbeat manager")

	hm.cancel()
	close(hm.shutdown)

	return nil
}

// RegisterConnection đăng ký connection cho heartbeat tracking
func (hm *HeartbeatManager) RegisterConnection(conn Connection) {
	hbConn := &HeartbeatConnection{
		ConnectionID: conn.ID(),
		Connection:   conn,
		LastPing:     time.Now(),
		LastPong:     time.Now(),
		MissedCount:  0,
		IsHealthy:    true,
	}

	select {
	case hm.register <- hbConn:
	case <-hm.ctx.Done():
	}
}

// UnregisterConnection hủy đăng ký connection
func (hm *HeartbeatManager) UnregisterConnection(connectionID string) {
	select {
	case hm.unregister <- connectionID:
	case <-hm.ctx.Done():
	}
}

// UpdatePongReceived cập nhật khi nhận được pong
func (hm *HeartbeatManager) UpdatePongReceived(connectionID string) {
	hm.connMutex.RLock()
	hbConn, exists := hm.connections[connectionID]
	hm.connMutex.RUnlock()

	if exists {
		hbConn.mu.Lock()
		hbConn.LastPong = time.Now()
		hbConn.MissedCount = 0
		hbConn.IsHealthy = true
		hbConn.mu.Unlock()

		hm.logger.Debug("Pong received", "conn_id", connectionID)
	}
}

// GetConnectionHealth lấy thông tin health của connection
func (hm *HeartbeatManager) GetConnectionHealth(connectionID string) (*ConnectionHealth, bool) {
	hm.connMutex.RLock()
	hbConn, exists := hm.connections[connectionID]
	hm.connMutex.RUnlock()

	if !exists {
		return nil, false
	}

	hbConn.mu.RLock()
	defer hbConn.mu.RUnlock()

	return &ConnectionHealth{
		ConnectionID:   hbConn.ConnectionID,
		LastPing:       hbConn.LastPing,
		LastPong:       hbConn.LastPong,
		MissedCount:    hbConn.MissedCount,
		IsHealthy:      hbConn.IsHealthy,
		ResponseTime:   hbConn.LastPong.Sub(hbConn.LastPing),
		TimeSinceLastPong: time.Since(hbConn.LastPong),
	}, true
}

// GetAllConnectionsHealth lấy health của tất cả connections
func (hm *HeartbeatManager) GetAllConnectionsHealth() []*ConnectionHealth {
	hm.connMutex.RLock()
	defer hm.connMutex.RUnlock()

	healths := make([]*ConnectionHealth, 0, len(hm.connections))
	for _, hbConn := range hm.connections {
		hbConn.mu.RLock()
		health := &ConnectionHealth{
			ConnectionID:      hbConn.ConnectionID,
			LastPing:          hbConn.LastPing,
			LastPong:          hbConn.LastPong,
			MissedCount:       hbConn.MissedCount,
			IsHealthy:         hbConn.IsHealthy,
			ResponseTime:      hbConn.LastPong.Sub(hbConn.LastPing),
			TimeSinceLastPong: time.Since(hbConn.LastPong),
		}
		hbConn.mu.RUnlock()
		healths = append(healths, health)
	}

	return healths
}

// GetHealthStatistics lấy thống kê health
func (hm *HeartbeatManager) GetHealthStatistics() *HeartbeatStatistics {
	hm.connMutex.RLock()
	defer hm.connMutex.RUnlock()

	stats := &HeartbeatStatistics{
		TotalConnections:   len(hm.connections),
		HealthyConnections: 0,
		UnhealthyConnections: 0,
		AverageResponseTime: 0,
		MaxResponseTime:     0,
		Timestamp:          time.Now(),
	}

	var totalResponseTime time.Duration
	var responseCount int

	for _, hbConn := range hm.connections {
		hbConn.mu.RLock()
		if hbConn.IsHealthy {
			stats.HealthyConnections++
		} else {
			stats.UnhealthyConnections++
		}

		responseTime := hbConn.LastPong.Sub(hbConn.LastPing)
		if responseTime > 0 && responseTime < time.Minute {
			totalResponseTime += responseTime
			responseCount++

			if responseTime > stats.MaxResponseTime {
				stats.MaxResponseTime = responseTime
			}
		}
		hbConn.mu.RUnlock()
	}

	if responseCount > 0 {
		stats.AverageResponseTime = totalResponseTime / time.Duration(responseCount)
	}

	return stats
}

// run main loop của heartbeat manager
func (hm *HeartbeatManager) run() {
	for {
		select {
		case hbConn := <-hm.register:
			hm.handleRegister(hbConn)

		case connID := <-hm.unregister:
			hm.handleUnregister(connID)

		case <-hm.shutdown:
			return
		}
	}
}

// handleRegister xử lý đăng ký connection
func (hm *HeartbeatManager) handleRegister(hbConn *HeartbeatConnection) {
	hm.connMutex.Lock()
	hm.connections[hbConn.ConnectionID] = hbConn
	hm.connMutex.Unlock()

	hm.logger.Debug("Connection registered for heartbeat",
		"conn_id", hbConn.ConnectionID)
}

// handleUnregister xử lý hủy đăng ký connection
func (hm *HeartbeatManager) handleUnregister(connectionID string) {
	hm.connMutex.Lock()
	delete(hm.connections, connectionID)
	hm.connMutex.Unlock()

	hm.logger.Debug("Connection unregistered from heartbeat",
		"conn_id", connectionID)
}

// healthChecker kiểm tra health định kỳ
func (hm *HeartbeatManager) healthChecker() {
	ticker := time.NewTicker(hm.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hm.checkHealth()

		case <-hm.ctx.Done():
			return
		}
	}
}

// checkHealth kiểm tra health của tất cả connections
func (hm *HeartbeatManager) checkHealth() {
	hm.connMutex.RLock()
	connections := make([]*HeartbeatConnection, 0, len(hm.connections))
	for _, hbConn := range hm.connections {
		connections = append(connections, hbConn)
	}
	hm.connMutex.RUnlock()

	now := time.Now()
	unhealthyCount := 0

	for _, hbConn := range connections {
		hbConn.mu.Lock()

		// Kiểm tra thời gian từ lần pong cuối
		timeSinceLastPong := now.Sub(hbConn.LastPong)

		if timeSinceLastPong > hm.config.Timeout {
			hbConn.MissedCount++
			hbConn.IsHealthy = false

			if hbConn.MissedCount >= hm.config.MaxMissed {
				// Connection cần được đóng
				hm.logger.Warn("Connection unhealthy, closing",
					"conn_id", hbConn.ConnectionID,
					"missed_count", hbConn.MissedCount,
					"time_since_pong", timeSinceLastPong)

				hbConn.mu.Unlock()
				hbConn.Connection.Close()
				hm.UnregisterConnection(hbConn.ConnectionID)
				continue
			} else {
				hm.logger.Debug("Connection missed heartbeat",
					"conn_id", hbConn.ConnectionID,
					"missed_count", hbConn.MissedCount)
			}

			unhealthyCount++
		}

		hbConn.mu.Unlock()
	}

	if unhealthyCount > 0 {
		hm.logger.Info("Health check completed",
			"total_connections", len(connections),
			"unhealthy_connections", unhealthyCount)
	}
}

// ConnectionHealth thông tin health của connection
type ConnectionHealth struct {
	ConnectionID      string        `json:"connectionId"`
	LastPing          time.Time     `json:"lastPing"`
	LastPong          time.Time     `json:"lastPong"`
	MissedCount       int           `json:"missedCount"`
	IsHealthy         bool          `json:"isHealthy"`
	ResponseTime      time.Duration `json:"responseTime"`
	TimeSinceLastPong time.Duration `json:"timeSinceLastPong"`
}

// HeartbeatStatistics thống kê heartbeat
type HeartbeatStatistics struct {
	TotalConnections     int           `json:"totalConnections"`
	HealthyConnections   int           `json:"healthyConnections"`
	UnhealthyConnections int           `json:"unhealthyConnections"`
	AverageResponseTime  time.Duration `json:"averageResponseTime"`
	MaxResponseTime      time.Duration `json:"maxResponseTime"`
	Timestamp            time.Time     `json:"timestamp"`
}

// HeartbeatService interface cho external usage
type HeartbeatService interface {
	RegisterConnection(conn *Connection)
	UnregisterConnection(connectionID string)
	UpdatePongReceived(connectionID string)
	GetConnectionHealth(connectionID string) (*ConnectionHealth, bool)
	GetAllConnectionsHealth() []*ConnectionHealth
	GetHealthStatistics() *HeartbeatStatistics
}
