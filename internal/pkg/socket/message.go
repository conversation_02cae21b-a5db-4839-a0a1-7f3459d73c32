package socket

import (
	"encoding/json"
	"time"
)

// Message đại diện cho một WebSocket message
type Message struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Event     string                 `json:"event,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source,omitempty"`
	Target    string                 `json:"target,omitempty"`
	RoomID    string                 `json:"roomId,omitempty"`
	UserID    string                 `json:"userId,omitempty"`
	Metadata  map[string]string      `json:"metadata,omitempty"`
}

// IncomingMessage message từ client
type IncomingMessage struct {
	Type      string                 `json:"type"`
	Event     string                 `json:"event"`
	Data      map[string]interface{} `json:"data,omitempty"`
	RequestID string                 `json:"requestId,omitempty"`
	RoomID    string                 `json:"roomId,omitempty"`
	Target    string                 `json:"target,omitempty"`
	Metadata  map[string]string      `json:"metadata,omitempty"`
}

// OutgoingMessage message gửi đến client
type OutgoingMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Event     string                 `json:"event,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source,omitempty"`
	RequestID string                 `json:"requestId,omitempty"`
	Success   bool                   `json:"success"`
	Error     *ErrorDetail           `json:"error,omitempty"`
	Metadata  map[string]string      `json:"metadata,omitempty"`
}

// ErrorDetail chi tiết về error
type ErrorDetail struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// ToJSON chuyển đổi message thành JSON
func (m *Message) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON tạo message từ JSON
func (m *Message) FromJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

// ToJSON chuyển đổi incoming message thành JSON
func (im *IncomingMessage) ToJSON() ([]byte, error) {
	return json.Marshal(im)
}

// FromJSON tạo incoming message từ JSON
func (im *IncomingMessage) FromJSON(data []byte) error {
	return json.Unmarshal(data, im)
}

// ToJSON chuyển đổi outgoing message thành JSON
func (om *OutgoingMessage) ToJSON() ([]byte, error) {
	return json.Marshal(om)
}

// FromJSON tạo outgoing message từ JSON
func (om *OutgoingMessage) FromJSON(data []byte) error {
	return json.Unmarshal(data, om)
}

// NewMessage tạo message mới
func NewMessage(msgType, event string, data map[string]interface{}) *Message {
	return &Message{
		ID:        generateMessageID(),
		Type:      msgType,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// NewOutgoingMessage tạo outgoing message mới
func NewOutgoingMessage(msgType, event string, data map[string]interface{}) *OutgoingMessage {
	return &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      msgType,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
		Success:   true,
	}
}

// NewErrorMessage tạo error message mới
func NewErrorMessage(code, message, details string) *OutgoingMessage {
	return &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      MessageTypeError,
		Timestamp: time.Now(),
		Success:   false,
		Error: &ErrorDetail{
			Code:    code,
			Message: message,
			Details: details,
		},
	}
}

// NewResponseMessage tạo response message cho request
func NewResponseMessage(requestID string, success bool, data map[string]interface{}, err *ErrorDetail) *OutgoingMessage {
	return &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      MessageTypeResponse,
		Data:      data,
		Timestamp: time.Now(),
		RequestID: requestID,
		Success:   success,
		Error:     err,
	}
}

// NewEventMessage tạo event message
func NewEventMessage(event string, data map[string]interface{}) *OutgoingMessage {
	return &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      MessageTypeEvent,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
		Success:   true,
	}
}

// NewNotificationMessage tạo notification message
func NewNotificationMessage(data map[string]interface{}) *OutgoingMessage {
	return &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      MessageTypeNotification,
		Event:     EventTypeNotification,
		Data:      data,
		Timestamp: time.Now(),
		Success:   true,
	}
}

// NewBroadcastMessage tạo broadcast message
func NewBroadcastMessage(roomID, event string, data map[string]interface{}) *OutgoingMessage {
	msg := &OutgoingMessage{
		ID:        generateMessageID(),
		Type:      MessageTypeBroadcast,
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
		Success:   true,
	}
	
	if msg.Metadata == nil {
		msg.Metadata = make(map[string]string)
	}
	msg.Metadata["roomId"] = roomID
	
	return msg
}

// SetSource set source cho message
func (om *OutgoingMessage) SetSource(source string) *OutgoingMessage {
	om.Source = source
	return om
}

// SetRequestID set request ID cho message
func (om *OutgoingMessage) SetRequestID(requestID string) *OutgoingMessage {
	om.RequestID = requestID
	return om
}

// SetMetadata set metadata cho message
func (om *OutgoingMessage) SetMetadata(key, value string) *OutgoingMessage {
	if om.Metadata == nil {
		om.Metadata = make(map[string]string)
	}
	om.Metadata[key] = value
	return om
}

// Validate kiểm tra tính hợp lệ của incoming message
func (im *IncomingMessage) Validate() error {
	if im.Type == "" {
		return ErrInvalidMessageType
	}
	
	if im.Event == "" {
		return ErrInvalidEvent
	}
	
	// Validate specific message types
	switch im.Type {
	case MessageTypeRequest:
		if im.RequestID == "" {
			return ErrMissingRequestID
		}
	case MessageTypeJoinRoom, MessageTypeLeaveRoom:
		if im.RoomID == "" {
			return ErrMissingRoomID
		}
	}
	
	return nil
}

// IsRequest kiểm tra có phải request message không
func (im *IncomingMessage) IsRequest() bool {
	return im.Type == MessageTypeRequest
}

// IsRoomMessage kiểm tra có phải room message không
func (im *IncomingMessage) IsRoomMessage() bool {
	return im.RoomID != ""
}

// GetDataString lấy string data
func (im *IncomingMessage) GetDataString(key string) (string, bool) {
	if im.Data == nil {
		return "", false
	}
	
	value, exists := im.Data[key]
	if !exists {
		return "", false
	}
	
	str, ok := value.(string)
	return str, ok
}

// GetDataInt lấy int data
func (im *IncomingMessage) GetDataInt(key string) (int, bool) {
	if im.Data == nil {
		return 0, false
	}
	
	value, exists := im.Data[key]
	if !exists {
		return 0, false
	}
	
	switch v := value.(type) {
	case int:
		return v, true
	case float64:
		return int(v), true
	default:
		return 0, false
	}
}

// GetDataBool lấy bool data
func (im *IncomingMessage) GetDataBool(key string) (bool, bool) {
	if im.Data == nil {
		return false, false
	}
	
	value, exists := im.Data[key]
	if !exists {
		return false, false
	}
	
	b, ok := value.(bool)
	return b, ok
}

// generateMessageID tạo unique message ID
func generateMessageID() string {
	// Sử dụng timestamp + random string
	return time.Now().Format("20060102150405") + "-" + generateRandomString(8)
}

// generateRandomString tạo random string
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
