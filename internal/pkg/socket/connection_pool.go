package socket

import (
	"sync"
)

// ConnectionPool quản lý pool của connections để tái sử dụng
type ConnectionPool struct {
	pool    chan Connection
	maxSize int
	mu      sync.RWMutex
}

// NewConnectionPool tạo connection pool mới
func NewConnectionPool(maxSize int) *ConnectionPool {
	return &ConnectionPool{
		pool:    make(chan Connection, maxSize),
		maxSize: maxSize,
	}
}

// Get lấy connection từ pool
func (cp *ConnectionPool) Get() Connection {
	select {
	case conn := <-cp.pool:
		return conn
	default:
		return nil // Pool empty
	}
}

// Put trả connection về pool
func (cp *ConnectionPool) Put(conn Connection) bool {
	if conn == nil {
		return false
	}

	// Reset connection state
	conn.Reset()

	select {
	case cp.pool <- conn:
		return true
	default:
		return false // Pool full
	}
}

// Size lấy số lượng connections hiện tại trong pool
func (cp *ConnectionPool) Size() int {
	return len(cp.pool)
}

// Close đóng pool và cleanup tất cả connections
func (cp *ConnectionPool) Close() {
	close(cp.pool)
	
	// Cleanup remaining connections
	for conn := range cp.pool {
		if conn != nil {
			conn.Close()
		}
	}
}

// UserConnectionTracker theo dõi connections theo user
type UserConnectionTracker struct {
	userConnections map[string][]string // userID -> []connectionID
	connectionUsers map[string]string   // connectionID -> userID
	mu              sync.RWMutex
}

// NewUserConnectionTracker tạo user connection tracker mới
func NewUserConnectionTracker() *UserConnectionTracker {
	return &UserConnectionTracker{
		userConnections: make(map[string][]string),
		connectionUsers: make(map[string]string),
	}
}

// AddConnection thêm connection cho user
func (uct *UserConnectionTracker) AddConnection(userID, connectionID string) {
	uct.mu.Lock()
	defer uct.mu.Unlock()
	
	uct.userConnections[userID] = append(uct.userConnections[userID], connectionID)
	uct.connectionUsers[connectionID] = userID
}

// RemoveConnection xóa connection của user
func (uct *UserConnectionTracker) RemoveConnection(connectionID string) {
	uct.mu.Lock()
	defer uct.mu.Unlock()
	
	userID, exists := uct.connectionUsers[connectionID]
	if !exists {
		return
	}
	
	// Remove from connectionUsers
	delete(uct.connectionUsers, connectionID)
	
	// Remove from userConnections
	connections := uct.userConnections[userID]
	for i, connID := range connections {
		if connID == connectionID {
			uct.userConnections[userID] = append(connections[:i], connections[i+1:]...)
			break
		}
	}
	
	// Remove user if no connections left
	if len(uct.userConnections[userID]) == 0 {
		delete(uct.userConnections, userID)
	}
}

// GetUserConnections lấy tất cả connections của user
func (uct *UserConnectionTracker) GetUserConnections(userID string) []string {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	connections := uct.userConnections[userID]
	if connections == nil {
		return []string{}
	}
	
	// Return copy
	result := make([]string, len(connections))
	copy(result, connections)
	return result
}

// GetConnectionUser lấy userID của connection
func (uct *UserConnectionTracker) GetConnectionUser(connectionID string) (string, bool) {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	userID, exists := uct.connectionUsers[connectionID]
	return userID, exists
}

// GetUserCount lấy số lượng users có connections
func (uct *UserConnectionTracker) GetUserCount() int {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	return len(uct.userConnections)
}

// GetConnectionCount lấy tổng số connections
func (uct *UserConnectionTracker) GetConnectionCount() int {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	return len(uct.connectionUsers)
}

// GetUserConnectionCount lấy số connections của user
func (uct *UserConnectionTracker) GetUserConnectionCount(userID string) int {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	return len(uct.userConnections[userID])
}

// HasUser kiểm tra user có connections không
func (uct *UserConnectionTracker) HasUser(userID string) bool {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	_, exists := uct.userConnections[userID]
	return exists
}

// GetAllUsers lấy tất cả userIDs có connections
func (uct *UserConnectionTracker) GetAllUsers() []string {
	uct.mu.RLock()
	defer uct.mu.RUnlock()
	
	users := make([]string, 0, len(uct.userConnections))
	for userID := range uct.userConnections {
		users = append(users, userID)
	}
	
	return users
}

// Clear xóa tất cả tracking data
func (uct *UserConnectionTracker) Clear() {
	uct.mu.Lock()
	defer uct.mu.Unlock()
	
	uct.userConnections = make(map[string][]string)
	uct.connectionUsers = make(map[string]string)
}

// RoomConnectionTracker theo dõi connections trong rooms
type RoomConnectionTracker struct {
	roomConnections    map[string][]string // roomID -> []connectionID
	connectionRooms    map[string][]string // connectionID -> []roomID
	mu                 sync.RWMutex
}

// NewRoomConnectionTracker tạo room connection tracker mới
func NewRoomConnectionTracker() *RoomConnectionTracker {
	return &RoomConnectionTracker{
		roomConnections: make(map[string][]string),
		connectionRooms: make(map[string][]string),
	}
}

// AddConnectionToRoom thêm connection vào room
func (rct *RoomConnectionTracker) AddConnectionToRoom(connectionID, roomID string) {
	rct.mu.Lock()
	defer rct.mu.Unlock()
	
	// Add to room connections
	rct.roomConnections[roomID] = append(rct.roomConnections[roomID], connectionID)
	
	// Add to connection rooms
	rct.connectionRooms[connectionID] = append(rct.connectionRooms[connectionID], roomID)
}

// RemoveConnectionFromRoom xóa connection khỏi room
func (rct *RoomConnectionTracker) RemoveConnectionFromRoom(connectionID, roomID string) {
	rct.mu.Lock()
	defer rct.mu.Unlock()
	
	// Remove from room connections
	if connections, exists := rct.roomConnections[roomID]; exists {
		for i, connID := range connections {
			if connID == connectionID {
				rct.roomConnections[roomID] = append(connections[:i], connections[i+1:]...)
				break
			}
		}
		
		// Remove room if empty
		if len(rct.roomConnections[roomID]) == 0 {
			delete(rct.roomConnections, roomID)
		}
	}
	
	// Remove from connection rooms
	if rooms, exists := rct.connectionRooms[connectionID]; exists {
		for i, rID := range rooms {
			if rID == roomID {
				rct.connectionRooms[connectionID] = append(rooms[:i], rooms[i+1:]...)
				break
			}
		}
		
		// Remove connection if no rooms
		if len(rct.connectionRooms[connectionID]) == 0 {
			delete(rct.connectionRooms, connectionID)
		}
	}
}

// RemoveConnection xóa connection khỏi tất cả rooms
func (rct *RoomConnectionTracker) RemoveConnection(connectionID string) {
	rct.mu.Lock()
	defer rct.mu.Unlock()
	
	rooms, exists := rct.connectionRooms[connectionID]
	if !exists {
		return
	}
	
	// Remove connection from all rooms
	for _, roomID := range rooms {
		if connections, exists := rct.roomConnections[roomID]; exists {
			for i, connID := range connections {
				if connID == connectionID {
					rct.roomConnections[roomID] = append(connections[:i], connections[i+1:]...)
					break
				}
			}
			
			// Remove room if empty
			if len(rct.roomConnections[roomID]) == 0 {
				delete(rct.roomConnections, roomID)
			}
		}
	}
	
	// Remove connection
	delete(rct.connectionRooms, connectionID)
}

// GetRoomConnections lấy tất cả connections trong room
func (rct *RoomConnectionTracker) GetRoomConnections(roomID string) []string {
	rct.mu.RLock()
	defer rct.mu.RUnlock()
	
	connections := rct.roomConnections[roomID]
	if connections == nil {
		return []string{}
	}
	
	// Return copy
	result := make([]string, len(connections))
	copy(result, connections)
	return result
}

// GetConnectionRooms lấy tất cả rooms của connection
func (rct *RoomConnectionTracker) GetConnectionRooms(connectionID string) []string {
	rct.mu.RLock()
	defer rct.mu.RUnlock()
	
	rooms := rct.connectionRooms[connectionID]
	if rooms == nil {
		return []string{}
	}
	
	// Return copy
	result := make([]string, len(rooms))
	copy(result, rooms)
	return result
}

// GetRoomConnectionCount lấy số connections trong room
func (rct *RoomConnectionTracker) GetRoomConnectionCount(roomID string) int {
	rct.mu.RLock()
	defer rct.mu.RUnlock()
	
	return len(rct.roomConnections[roomID])
}

// HasConnectionInRoom kiểm tra connection có trong room không
func (rct *RoomConnectionTracker) HasConnectionInRoom(connectionID, roomID string) bool {
	rct.mu.RLock()
	defer rct.mu.RUnlock()
	
	rooms := rct.connectionRooms[connectionID]
	for _, rID := range rooms {
		if rID == roomID {
			return true
		}
	}
	
	return false
}

// GetAllRooms lấy tất cả roomIDs
func (rct *RoomConnectionTracker) GetAllRooms() []string {
	rct.mu.RLock()
	defer rct.mu.RUnlock()
	
	rooms := make([]string, 0, len(rct.roomConnections))
	for roomID := range rct.roomConnections {
		rooms = append(rooms, roomID)
	}
	
	return rooms
}

// Clear xóa tất cả tracking data
func (rct *RoomConnectionTracker) Clear() {
	rct.mu.Lock()
	defer rct.mu.Unlock()
	
	rct.roomConnections = make(map[string][]string)
	rct.connectionRooms = make(map[string][]string)
}
