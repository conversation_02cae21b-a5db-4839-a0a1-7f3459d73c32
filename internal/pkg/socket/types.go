package socket

import (
	"context"
	"time"
)

// Connection đại diện cho một WebSocket connection
type Connection interface {
	// Connection info
	ID() string
	UserID() string
	TenantID() string
	WebsiteID() string

	// Messaging
	Send(message *Message) error
	SendEvent(eventType string, data map[string]interface{}) error
	SendError(code, message string) error
	SendMessage(message *OutgoingMessage) error

	// Connection management
	Close() error
	IsAlive() bool
	IsActive() bool
	LastActivity() time.Time
	Reset()

	// Room management
	JoinRoom(roomID string) error
	LeaveRoom(roomID string) error
	GetRooms() []string
	AddRoom(roomID string)
	RemoveRoom(roomID string)

	// Context
	Context() context.Context
	RemoteAddr() string
}

// EventHandler xử lý các WebSocket events
type EventHandler func(ctx context.Context, conn Connection, data map[string]interface{}) error

// EventRouter quản lý event routing và handling
type EventRouter interface {
	// Handler registration
	RegisterHandler(eventType string, handler EventHandler)
	UnregisterHandler(eventType string)
	GetHandler(eventType string) (EventHandler, bool)

	// Event processing
	RouteEvent(ctx context.Context, conn Connection, event *IncomingMessage) error
	PublishEvent(event *Event) error

	// Middleware
	Use(middleware MiddlewareFunc)
	GetMiddlewares() []MiddlewareFunc
}

// MiddlewareFunc middleware function cho event processing
type MiddlewareFunc func(next EventHandler) EventHandler

// Event đại diện cho một system event
type Event struct {
	Type        string                 `json:"type"`
	Source      string                 `json:"source"`
	Data        map[string]interface{} `json:"data"`
	Timestamp   time.Time              `json:"timestamp"`
	ConnectionID string                `json:"connectionId,omitempty"`
	UserID      string                 `json:"userId,omitempty"`
	RoomID      string                 `json:"roomId,omitempty"`
}

// ConnectionInfo thông tin về connection
type ConnectionInfo struct {
	ID          string            `json:"id"`
	UserID      string            `json:"userId"`
	TenantID    string            `json:"tenantId"`
	WebsiteID   string            `json:"websiteId"`
	RemoteAddr  string            `json:"remoteAddr"`
	UserAgent   string            `json:"userAgent"`
	ConnectedAt time.Time         `json:"connectedAt"`
	LastActivity time.Time        `json:"lastActivity"`
	Rooms       []string          `json:"rooms"`
	Metadata    map[string]string `json:"metadata"`
}

// Statistics thống kê hệ thống
type Statistics struct {
	TotalConnections   int                    `json:"totalConnections"`
	ActiveConnections  int                    `json:"activeConnections"`
	TotalRooms         int                    `json:"totalRooms"`
	ActiveRooms        int                    `json:"activeRooms"`
	MessagesPerSecond  float64               `json:"messagesPerSecond"`
	ConnectionsByUser  map[string]int        `json:"connectionsByUser"`
	ConnectionsByRoom  map[string]int        `json:"connectionsByRoom"`
	Timestamp          time.Time             `json:"timestamp"`
}

// Config cấu hình cho WebSocket system
type Config struct {
	// Connection limits
	MaxConnections        int           `json:"maxConnections"`
	MaxConnectionsPerUser int           `json:"maxConnectionsPerUser"`
	MaxRoomsPerUser       int           `json:"maxRoomsPerUser"`

	// Timeouts
	ReadTimeout    time.Duration `json:"readTimeout"`
	WriteTimeout   time.Duration `json:"writeTimeout"`
	PingInterval   time.Duration `json:"pingInterval"`
	PongTimeout    time.Duration `json:"pongTimeout"`

	// Message limits
	MaxMessageSize int `json:"maxMessageSize"`
	MessageBuffer  int `json:"messageBuffer"`

	// Room settings
	DefaultRoomMaxMembers int           `json:"defaultRoomMaxMembers"`
	RoomCleanupInterval   time.Duration `json:"roomCleanupInterval"`

	// Security
	RequireAuth     bool     `json:"requireAuth"`
	AllowedOrigins  []string `json:"allowedOrigins"`
	EnableCORS      bool     `json:"enableCors"`

	// Features
	EnableStatistics bool `json:"enableStatistics"`
	EnableMetrics    bool `json:"enableMetrics"`
	EnableLogging    bool `json:"enableLogging"`
}

// Hub quản lý WebSocket connections và rooms
type Hub interface {
	// Connection management
	RegisterConnection(conn Connection) error
	UnregisterConnection(connectionID string) error
	GetConnection(connectionID string) (Connection, bool)
	GetUserConnections(userID string) []Connection

	// Room management
	CreateRoom(roomID string, config *RoomConfig) (*Room, error)
	DeleteRoom(roomID string) error
	GetRoom(roomID string) (*Room, bool)
	JoinRoom(connectionID, roomID string) error
	LeaveRoom(connectionID, roomID string) error

	// Messaging
	SendToConnection(connectionID string, message *OutgoingMessage) error
	SendToUser(userID string, message *OutgoingMessage) error
	BroadcastToRoom(roomID string, message *OutgoingMessage, exclude ...string) error

	// Lifecycle
	Start() error
	Stop() error

	// Statistics
	GetStatistics() *HubStatistics
}

// EventPublisher publish events đến external systems
type EventPublisher interface {
	PublishConnectionEvent(connID, userID, eventType string, metadata map[string]interface{}) error
	PublishRoomEvent(roomID, eventType string, metadata map[string]interface{}) error
	PublishMessageEvent(connID, roomID string, message *Message) error
}

// RoomType định nghĩa type cho room
type RoomType string