package socket

import "time"

// Message types
const (
	MessageTypeRequest      = "request"
	MessageTypeResponse     = "response"
	MessageTypeEvent        = "event"
	MessageTypeNotification = "notification"
	MessageTypeBroadcast    = "broadcast"
	MessageTypeError        = "error"
	MessageTypePing         = "ping"
	MessageTypePong         = "pong"
	MessageTypeAuth         = "auth"
	MessageTypeJoinRoom     = "join_room"
	MessageTypeLeaveRoom    = "leave_room"
)

// Event types
const (
	EventTypeConnected          = "connected"
	EventTypeDisconnected       = "disconnected"
	EventTypeAuthenticated      = "authenticated"
	EventTypeAuthFailed         = "auth_failed"
	EventTypeJoinedRoom         = "joined_room"
	EventTypeLeftRoom           = "left_room"
	EventTypeRoomCreated        = "room_created"
	EventTypeRoomDeleted        = "room_deleted"
	EventTypeUserJoined         = "user_joined"
	EventTypeUserLeft           = "user_left"
	EventTypeMessage            = "message"
	EventTypeNotification       = "notification"
	EventTypeBroadcast          = "broadcast"
	EventTypeError              = "error"
	EventTypeHeartbeat          = "heartbeat"
	EventTypeConnectionRegistered   = "connection_registered"
	EventTypeConnectionUnregistered = "connection_unregistered"
)

// Default configuration values
const (
	// Connection limits
	DefaultMaxConnections        = 10000
	DefaultMaxConnectionsPerUser = 10
	DefaultMaxRoomsPerUser       = 100

	// Timeouts
	DefaultReadTimeout  = 60 * time.Second
	DefaultWriteTimeout = 10 * time.Second
	DefaultPingInterval = 30 * time.Second
	DefaultPongTimeout  = 60 * time.Second

	// Message limits
	DefaultMaxMessageSize = 1024 * 1024 // 1MB
	DefaultMessageBuffer  = 1000

	// Room settings
	DefaultMaxRoomMembers = 1000
	DefaultMessageHistoryLimit = 100
	DefaultRoomAutoDeleteTimeout = 5 * time.Minute

	// Hub settings
	DefaultCleanupInterval = 5 * time.Minute

	// Rate limiting
	DefaultRateLimit = 100 // messages per minute
	DefaultBurstLimit = 10 // burst messages

	// Heartbeat
	DefaultHeartbeatInterval = 30 * time.Second
	DefaultHeartbeatTimeout  = 90 * time.Second
)

// Connection states
const (
	ConnectionStateConnecting    = "connecting"
	ConnectionStateConnected     = "connected"
	ConnectionStateAuthenticated = "authenticated"
	ConnectionStateDisconnecting = "disconnecting"
	ConnectionStateDisconnected  = "disconnected"
	ConnectionStateError         = "error"
)

// Room types
const (
	RoomTypePublic  = "public"
	RoomTypePrivate = "private"
	RoomTypeDirect  = "direct"
)

// Permission levels
const (
	PermissionLevelNone      = 0
	PermissionLevelRead      = 1
	PermissionLevelWrite     = 2
	PermissionLevelModerate  = 3
	PermissionLevelAdmin     = 4
	PermissionLevelOwner     = 5
)

// WebSocket close codes
const (
	CloseCodeNormalClosure   = 1000
	CloseCodeGoingAway       = 1001
	CloseCodeProtocolError   = 1002
	CloseCodeUnsupportedData = 1003
	CloseCodeNoStatusRcvd    = 1005
	CloseCodeAbnormalClosure = 1006
	CloseCodeInvalidFramePayloadData = 1007
	CloseCodePolicyViolation = 1008
	CloseCodeMessageTooBig   = 1009
	CloseCodeMandatoryExtension = 1010
	CloseCodeInternalServerErr = 1011
	CloseCodeServiceRestart  = 1012
	CloseCodeTryAgainLater   = 1013
	CloseCodeBadGateway      = 1014
	CloseCodeTLSHandshake    = 1015
)

// Custom close codes
const (
	CloseCodeAuthRequired    = 4000
	CloseCodeAuthFailed      = 4001
	CloseCodeAuthTimeout     = 4002
	CloseCodeRateLimitExceeded = 4003
	CloseCodeServerShutdown  = 4004
	CloseCodeInvalidMessage  = 4005
	CloseCodePermissionDenied = 4006
)

// HTTP headers
const (
	HeaderUserAgent    = "User-Agent"
	HeaderOrigin       = "Origin"
	HeaderAuthorization = "Authorization"
	HeaderXRealIP      = "X-Real-IP"
	HeaderXForwardedFor = "X-Forwarded-For"
	HeaderXRequestID   = "X-Request-ID"
	HeaderUserID       = "X-User-ID"
	HeaderTenantID     = "X-Tenant-ID"
	HeaderWebsiteID    = "X-Website-ID"
)

// Context keys
const (
	ContextKeyUserID     = "userID"
	ContextKeyTenantID   = "tenantID"
	ContextKeyWebsiteID  = "websiteID"
	ContextKeyConnectionID = "connectionID"
	ContextKeyRequestID  = "requestID"
	ContextKeyClientIP   = "clientIP"
	ContextKeyUserAgent  = "userAgent"
)

// Queue sizes
const (
	DefaultRegisterQueueSize   = 100
	DefaultUnregisterQueueSize = 100
	DefaultBroadcastQueueSize  = 1000
	DefaultEventQueueSize      = 1000
)

// Statistics intervals
const (
	DefaultStatsReportInterval = 30 * time.Second
	DefaultMetricsInterval     = 60 * time.Second
)

// Retry settings
const (
	DefaultMaxRetries    = 3
	DefaultRetryInterval = 1 * time.Second
	DefaultRetryBackoff  = 2.0 // exponential backoff multiplier
)

// Buffer sizes
const (
	DefaultReadBufferSize  = 4096
	DefaultWriteBufferSize = 4096
)

// Log levels
const (
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
)

// Metrics names
const (
	MetricConnectionsTotal    = "websocket_connections_total"
	MetricConnectionsActive   = "websocket_connections_active"
	MetricMessagesTotal       = "websocket_messages_total"
	MetricMessagesSent        = "websocket_messages_sent"
	MetricMessagesReceived    = "websocket_messages_received"
	MetricBroadcastsTotal     = "websocket_broadcasts_total"
	MetricRoomsTotal          = "websocket_rooms_total"
	MetricRoomsActive         = "websocket_rooms_active"
	MetricErrorsTotal         = "websocket_errors_total"
	MetricResponseTime        = "websocket_response_time"
	MetricConnectionDuration  = "websocket_connection_duration"
)

// Status codes
const (
	StatusOK                    = 200
	StatusBadRequest           = 400
	StatusUnauthorized         = 401
	StatusForbidden            = 403
	StatusNotFound             = 404
	StatusConflict             = 409
	StatusTooManyRequests      = 429
	StatusInternalServerError  = 500
	StatusServiceUnavailable   = 503
)

// Default values
var (
	DefaultAllowedOrigins = []string{"*"}
	DefaultConfig = &Config{
		MaxConnections:        DefaultMaxConnections,
		MaxConnectionsPerUser: DefaultMaxConnectionsPerUser,
		MaxRoomsPerUser:       DefaultMaxRoomsPerUser,
		ReadTimeout:          DefaultReadTimeout,
		WriteTimeout:         DefaultWriteTimeout,
		PingInterval:         DefaultPingInterval,
		PongTimeout:          DefaultPongTimeout,
		MaxMessageSize:       DefaultMaxMessageSize,
		MessageBuffer:        DefaultMessageBuffer,
		DefaultRoomMaxMembers: DefaultMaxRoomMembers,
		RoomCleanupInterval:  DefaultCleanupInterval,
		RequireAuth:          true,
		AllowedOrigins:       DefaultAllowedOrigins,
		EnableCORS:           true,
		EnableStatistics:     true,
		EnableMetrics:        true,
		EnableLogging:        true,
	}
)
