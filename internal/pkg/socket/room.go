package socket

import (
	"sync"
	"time"

	"wnapi/internal/pkg/logger"
)

// RoomType loại room
// RoomConfig cấu hình cho Room
type RoomConfig struct {
	Type              RoomType
	MaxMembers        int
	PersistMessages   bool
	MessageHistory    int           // Số lượng messages lưu trữ
	AutoDelete        bool          // Tự động xóa khi empty
	AutoDeleteTimeout time.Duration // Thời gian chờ trước khi auto delete
	CreatedBy         string        // UserID của người tạo
	CreatedAt         time.Time
}

// DefaultRoomConfig trả về cấu hình mặc định cho Room
func DefaultRoomConfig() *RoomConfig {
	return &RoomConfig{
		Type:              RoomTypePublic,
		MaxMembers:        DefaultMaxRoomMembers,
		PersistMessages:   true,
		MessageHistory:    DefaultMessageHistoryLimit,
		AutoDelete:        true,
		AutoDeleteTimeout: DefaultRoomAutoDeleteTimeout,
		CreatedAt:         time.Now(),
	}
}

// Room quản lý một nhóm connections
type Room struct {
	// Basic info
	ID     string
	Config *RoomConfig

	// Connection management
	connections map[string]Connection // connectionID -> Connection
	userCount   map[string]int        // userID -> connection count

	// Message history
	messages []*RoomMessage

	// Synchronization
	mu sync.RWMutex

	// Timestamps
	createdAt     time.Time
	lastActivity  time.Time
	emptyAt       *time.Time // Thời điểm room trống

	// Logger
	logger logger.Logger
}

// RoomMessage tin nhắn trong room
type RoomMessage struct {
	ID           string
	RoomID       string
	UserID       string
	ConnectionID string
	Content      string
	MessageType  string
	Timestamp    time.Time
	Metadata     map[string]interface{}
}

// NewRoom tạo Room instance mới
func NewRoom(id string, config *RoomConfig, logger logger.Logger) *Room {
	if config == nil {
		config = DefaultRoomConfig()
	}

	now := time.Now()
	room := &Room{
		ID:           id,
		Config:       config,
		connections:  make(map[string]Connection),
		userCount:    make(map[string]int),
		messages:     make([]*RoomMessage, 0),
		createdAt:    now,
		lastActivity: now,
		emptyAt:      &now, // Room bắt đầu empty
		logger:       logger,
	}

	return room
}

// AddConnection thêm connection vào room
func (r *Room) AddConnection(conn Connection) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Kiểm tra nếu connection đã có trong room
	if _, exists := r.connections[conn.ID()]; exists {
		return ErrConnectionAlreadyInRoom
	}

	// Kiểm tra max members limit
	if len(r.connections) >= r.Config.MaxMembers {
		return ErrRoomFull
	}

	// Thêm connection
	r.connections[conn.ID()] = conn
	r.userCount[conn.UserID()]++

	// Update timestamps
	r.lastActivity = time.Now()
	r.emptyAt = nil // Room không còn empty

	r.logger.Debug("Connection added to room", 
		"roomID", r.ID,
		"connectionID", conn.ID(), 
		"userID", conn.UserID(),
		"totalConnections", len(r.connections))

	return nil
}

// RemoveConnection xóa connection khỏi room
func (r *Room) RemoveConnection(connectionID string) {
	r.mu.Lock()
	defer r.mu.Unlock()

	conn, exists := r.connections[connectionID]
	if !exists {
		return
	}

	// Xóa connection
	delete(r.connections, connectionID)
	r.userCount[conn.UserID()]--

	// Xóa user nếu không còn connections
	if r.userCount[conn.UserID()] <= 0 {
		delete(r.userCount, conn.UserID())
	}

	// Update timestamps
	r.lastActivity = time.Now()
	if len(r.connections) == 0 {
		now := time.Now()
		r.emptyAt = &now
	}

	r.logger.Debug("Connection removed from room", 
		"roomID", r.ID,
		"connectionID", connectionID, 
		"userID", conn.UserID(),
		"totalConnections", len(r.connections))
}

// GetConnection lấy connection theo ID
func (r *Room) GetConnection(connectionID string) (Connection, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	conn, exists := r.connections[connectionID]
	return conn, exists
}

// GetConnections lấy tất cả connections trong room
func (r *Room) GetConnections() []Connection {
	r.mu.RLock()
	defer r.mu.RUnlock()

	connections := make([]Connection, 0, len(r.connections))
	for _, conn := range r.connections {
		connections = append(connections, conn)
	}

	return connections
}

// GetUserConnections lấy connections của user trong room
func (r *Room) GetUserConnections(userID string) []Connection {
	r.mu.RLock()
	defer r.mu.RUnlock()

	connections := make([]Connection, 0)
	for _, conn := range r.connections {
		if conn.UserID() == userID {
			connections = append(connections, conn)
		}
	}

	return connections
}

// HasUser kiểm tra user có trong room không
func (r *Room) HasUser(userID string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	_, exists := r.userCount[userID]
	return exists
}

// HasConnection kiểm tra connection có trong room không
func (r *Room) HasConnection(connectionID string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	_, exists := r.connections[connectionID]
	return exists
}

// GetMemberCount lấy số lượng members trong room
func (r *Room) GetMemberCount() int {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	return len(r.userCount)
}

// GetConnectionCount lấy số lượng connections trong room
func (r *Room) GetConnectionCount() int {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	return len(r.connections)
}

// IsEmpty kiểm tra room có trống không
func (r *Room) IsEmpty() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	return len(r.connections) == 0
}

// CanAutoDelete kiểm tra room có thể auto delete không
func (r *Room) CanAutoDelete() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.Config.AutoDelete || r.emptyAt == nil {
		return false
	}

	return time.Since(*r.emptyAt) >= r.Config.AutoDeleteTimeout
}

// DisconnectAll ngắt kết nối tất cả connections trong room
func (r *Room) DisconnectAll() {
	r.mu.Lock()
	connections := make([]Connection, 0, len(r.connections))
	for _, conn := range r.connections {
		connections = append(connections, conn)
	}
	
	// Clear maps
	r.connections = make(map[string]Connection)
	r.userCount = make(map[string]int)
	
	now := time.Now()
	r.lastActivity = now
	r.emptyAt = &now
	r.mu.Unlock()

	// Remove room from connections
	for _, conn := range connections {
		conn.RemoveRoom(r.ID)
	}

	r.logger.Debug("Disconnected all connections from room", 
		"roomID", r.ID,
		"disconnectedCount", len(connections))
}

// AddMessage thêm message vào room history
func (r *Room) AddMessage(message *RoomMessage) {
	if !r.Config.PersistMessages {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	// Thêm message
	r.messages = append(r.messages, message)

	// Giới hạn số lượng messages
	if len(r.messages) > r.Config.MessageHistory {
		// Xóa messages cũ
		copy(r.messages, r.messages[len(r.messages)-r.Config.MessageHistory:])
		r.messages = r.messages[:r.Config.MessageHistory]
	}

	r.lastActivity = time.Now()
}

// GetMessages lấy messages trong room
func (r *Room) GetMessages(limit int) []*RoomMessage {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if limit <= 0 || limit > len(r.messages) {
		limit = len(r.messages)
	}

	// Lấy messages mới nhất
	start := len(r.messages) - limit
	if start < 0 {
		start = 0
	}

	messages := make([]*RoomMessage, limit)
	copy(messages, r.messages[start:])

	return messages
}

// GetMessagesSince lấy messages từ timestamp
func (r *Room) GetMessagesSince(since time.Time) []*RoomMessage {
	r.mu.RLock()
	defer r.mu.RUnlock()

	messages := make([]*RoomMessage, 0)
	for _, msg := range r.messages {
		if msg.Timestamp.After(since) {
			messages = append(messages, msg)
		}
	}

	return messages
}

// GetInfo lấy thông tin room
func (r *Room) GetInfo() *RoomInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return &RoomInfo{
		ID:            r.ID,
		Type:          r.Config.Type,
		MemberCount:   len(r.userCount),
		ConnectionCount: len(r.connections),
		MaxMembers:    r.Config.MaxMembers,
		CreatedBy:     r.Config.CreatedBy,
		CreatedAt:     r.createdAt,
		LastActivity:  r.lastActivity,
		MessageCount:  len(r.messages),
	}
}

// RoomInfo thông tin room
type RoomInfo struct {
	ID              string    `json:"id"`
	Type            RoomType  `json:"type"`
	MemberCount     int       `json:"memberCount"`
	ConnectionCount int       `json:"connectionCount"`
	MaxMembers      int       `json:"maxMembers"`
	CreatedBy       string    `json:"createdBy"`
	CreatedAt       time.Time `json:"createdAt"`
	LastActivity    time.Time `json:"lastActivity"`
	MessageCount    int       `json:"messageCount"`
}

// BroadcastMessage broadcast message đến tất cả connections trong room
func (r *Room) BroadcastMessage(message *OutgoingMessage, exclude ...string) {
	r.mu.RLock()
	connections := make([]Connection, 0, len(r.connections))
	excludeSet := make(map[string]bool)
	
	for _, connID := range exclude {
		excludeSet[connID] = true
	}
	
	for connID, conn := range r.connections {
		if !excludeSet[connID] {
			connections = append(connections, conn)
		}
	}
	r.mu.RUnlock()

	// Send message to connections
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			r.logger.Error("Failed to send message to connection in room", 
				"error", err,
				"roomID", r.ID,
				"connectionID", conn.ID())
		}
	}

	r.mu.Lock()
	r.lastActivity = time.Now()
	r.mu.Unlock()
}

// SendToUser gửi message đến tất cả connections của user trong room
func (r *Room) SendToUser(userID string, message *OutgoingMessage) error {
	connections := r.GetUserConnections(userID)
	if len(connections) == 0 {
		return ErrUserNotInRoom
	}

	var errors []error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, err)
			r.logger.Error("Failed to send message to user connection in room", 
				"error", err,
				"roomID", r.ID,
				"connectionID", conn.ID(),
				"userID", userID)
		}
	}

	r.mu.Lock()
	r.lastActivity = time.Now()
	r.mu.Unlock()

	if len(errors) > 0 {
		return errors[0]
	}

	return nil
}

// GetUsers lấy danh sách userIDs trong room
func (r *Room) GetUsers() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()

	users := make([]string, 0, len(r.userCount))
	for userID := range r.userCount {
		users = append(users, userID)
	}

	return users
}

// UpdateLastActivity cập nhật last activity timestamp
func (r *Room) UpdateLastActivity() {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.lastActivity = time.Now()
}
