package socket

import (
	"context"
	"sync"
	"time"

	"wnapi/internal/pkg/logger"
)

// EventRegistry đăng ký và quản lý event handlers
type EventRegistryImpl struct {
	// Handler storage
	handlers map[string]EventHandler
	patterns map[string][]EventHandler // For wildcard patterns
	mu       sync.RWMutex

	// Event groups
	groups map[string][]string // group -> []eventTypes
	groupMu sync.RWMutex

	// Logger
	logger logger.Logger
}

// NewEventRegistry tạo EventRegistry mới
func NewEventRegistryImpl(logger logger.Logger) *EventRegistryImpl {
	return &EventRegistryImpl{
		handlers: make(map[string]EventHandler),
		patterns: make(map[string][]EventHandler),
		groups:   make(map[string][]string),
		logger:   logger,
	}
}

// RegisterHandler đăng ký event handler
func (er *EventRegistryImpl) RegisterHandler(eventType string, handler EventHandler) {
	er.mu.Lock()
	defer er.mu.Unlock()

	// Check if it's a wildcard pattern
	if isWildcardPattern(eventType) {
		er.patterns[eventType] = append(er.patterns[eventType], handler)
	} else {
		er.handlers[eventType] = handler
	}

	er.logger.Debug("Event handler registered", "event_type", eventType)
}

// RegisterHandlerGroup đăng ký nhóm handlers
func (er *EventRegistryImpl) RegisterHandlerGroup(groupName string, eventTypes []string, handler EventHandler) {
	er.groupMu.Lock()
	er.groups[groupName] = eventTypes
	er.groupMu.Unlock()

	// Register handler for each event type
	for _, eventType := range eventTypes {
		er.RegisterHandler(eventType, handler)
	}

	er.logger.Debug("Event handler group registered",
		"group", groupName,
		"event_types", eventTypes)
}

// UnregisterHandler hủy đăng ký event handler
func (er *EventRegistryImpl) UnregisterHandler(eventType string) {
	er.mu.Lock()
	defer er.mu.Unlock()

	if isWildcardPattern(eventType) {
		delete(er.patterns, eventType)
	} else {
		delete(er.handlers, eventType)
	}

	er.logger.Debug("Event handler unregistered", "event_type", eventType)
}

// UnregisterHandlerGroup hủy đăng ký nhóm handlers
func (er *EventRegistryImpl) UnregisterHandlerGroup(groupName string) {
	er.groupMu.RLock()
	eventTypes, exists := er.groups[groupName]
	er.groupMu.RUnlock()

	if !exists {
		return
	}

	// Unregister each handler
	for _, eventType := range eventTypes {
		er.UnregisterHandler(eventType)
	}

	er.groupMu.Lock()
	delete(er.groups, groupName)
	er.groupMu.Unlock()

	er.logger.Debug("Event handler group unregistered", "group", groupName)
}

// GetHandler lấy event handler
func (er *EventRegistryImpl) GetHandler(eventType string) (EventHandler, bool) {
	er.mu.RLock()
	defer er.mu.RUnlock()

	// Check exact match first
	if handler, exists := er.handlers[eventType]; exists {
		return handler, true
	}

	// Check wildcard patterns
	for pattern, handlers := range er.patterns {
		if matchWildcardPattern(pattern, eventType) && len(handlers) > 0 {
			// Return first matching handler
			return handlers[0], true
		}
	}

	return nil, false
}

// GetAllHandlers lấy tất cả handlers cho event type (bao gồm wildcards)
func (er *EventRegistryImpl) GetAllHandlers(eventType string) []EventHandler {
	er.mu.RLock()
	defer er.mu.RUnlock()

	var handlers []EventHandler

	// Add exact match
	if handler, exists := er.handlers[eventType]; exists {
		handlers = append(handlers, handler)
	}

	// Add wildcard matches
	for pattern, patternHandlers := range er.patterns {
		if matchWildcardPattern(pattern, eventType) {
			handlers = append(handlers, patternHandlers...)
		}
	}

	return handlers
}

// GetRegisteredEvents lấy danh sách tất cả events đã đăng ký
func (er *EventRegistryImpl) GetRegisteredEvents() []string {
	er.mu.RLock()
	defer er.mu.RUnlock()

	events := make([]string, 0, len(er.handlers)+len(er.patterns))

	for eventType := range er.handlers {
		events = append(events, eventType)
	}

	for pattern := range er.patterns {
		events = append(events, pattern)
	}

	return events
}

// GetRegisteredGroups lấy danh sách groups đã đăng ký
func (er *EventRegistryImpl) GetRegisteredGroups() map[string][]string {
	er.groupMu.RLock()
	defer er.groupMu.RUnlock()

	result := make(map[string][]string)
	for k, v := range er.groups {
		eventTypes := make([]string, len(v))
		copy(eventTypes, v)
		result[k] = eventTypes
	}

	return result
}

// Clear xóa tất cả handlers
func (er *EventRegistryImpl) Clear() {
	er.mu.Lock()
	er.handlers = make(map[string]EventHandler)
	er.patterns = make(map[string][]EventHandler)
	er.mu.Unlock()

	er.groupMu.Lock()
	er.groups = make(map[string][]string)
	er.groupMu.Unlock()

	er.logger.Debug("All event handlers cleared")
}

// isWildcardPattern kiểm tra có phải wildcard pattern không
func isWildcardPattern(pattern string) bool {
	return len(pattern) > 0 && (pattern[len(pattern)-1] == '*' || 
		pattern[0] == '*' || 
		contains(pattern, "*"))
}

// matchWildcardPattern kiểm tra pattern có match với event type không
func matchWildcardPattern(pattern, eventType string) bool {
	if pattern == "*" {
		return true
	}

	// Simple wildcard matching
	if pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(eventType) >= len(prefix) && eventType[:len(prefix)] == prefix
	}

	if pattern[0] == '*' {
		suffix := pattern[1:]
		return len(eventType) >= len(suffix) && eventType[len(eventType)-len(suffix):] == suffix
	}

	return pattern == eventType
}

// contains kiểm tra string có chứa substring không
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// EventMiddlewareChain quản lý middleware chain
type EventMiddlewareChain struct {
	middlewares []MiddlewareFunc
	mu          sync.RWMutex
	logger      logger.Logger
}

// NewEventMiddlewareChain tạo middleware chain mới
func NewEventMiddlewareChain(logger logger.Logger) *EventMiddlewareChain {
	return &EventMiddlewareChain{
		middlewares: make([]MiddlewareFunc, 0),
		logger:      logger,
	}
}

// Use thêm middleware vào chain
func (emc *EventMiddlewareChain) Use(middleware MiddlewareFunc) {
	emc.mu.Lock()
	defer emc.mu.Unlock()

	emc.middlewares = append(emc.middlewares, middleware)
	emc.logger.Debug("Middleware added to chain")
}

// UseMultiple thêm nhiều middlewares
func (emc *EventMiddlewareChain) UseMultiple(middlewares ...MiddlewareFunc) {
	emc.mu.Lock()
	defer emc.mu.Unlock()

	emc.middlewares = append(emc.middlewares, middlewares...)
	emc.logger.Debug("Multiple middlewares added to chain", "count", len(middlewares))
}

// Build xây dựng middleware chain cho handler
func (emc *EventMiddlewareChain) Build(handler EventHandler) EventHandler {
	emc.mu.RLock()
	defer emc.mu.RUnlock()

	// Build chain từ cuối lên đầu
	final := handler
	for i := len(emc.middlewares) - 1; i >= 0; i-- {
		final = emc.middlewares[i](final)
	}

	return final
}

// GetMiddlewares lấy danh sách middlewares
func (emc *EventMiddlewareChain) GetMiddlewares() []MiddlewareFunc {
	emc.mu.RLock()
	defer emc.mu.RUnlock()

	middlewares := make([]MiddlewareFunc, len(emc.middlewares))
	copy(middlewares, emc.middlewares)
	return middlewares
}

// Clear xóa tất cả middlewares
func (emc *EventMiddlewareChain) Clear() {
	emc.mu.Lock()
	defer emc.mu.Unlock()

	emc.middlewares = make([]MiddlewareFunc, 0)
	emc.logger.Debug("Middleware chain cleared")
}

// Advanced middleware functions

// RecoveryMiddleware middleware để recover từ panic
func RecoveryMiddleware(logger logger.Logger) MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) (err error) {
			defer func() {
				if r := recover(); r != nil {
					logger.Error("Event handler panic recovered",
						"connection_id", conn.ID(),
						"panic", r)
					err = ErrInternalError
				}
			}()

			return next(ctx, conn, data)
		}
	}
}

// TimeoutMiddleware middleware để timeout handling
func TimeoutMiddleware(timeout time.Duration) MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			ctx, cancel := context.WithTimeout(ctx, timeout)
			defer cancel()

			done := make(chan error, 1)
			go func() {
				done <- next(ctx, conn, data)
			}()

			select {
			case err := <-done:
				return err
			case <-ctx.Done():
				return ErrHandlerTimeout
			}
		}
	}
}

// MetricsMiddleware middleware để collect metrics
func MetricsMiddleware(metrics *EventMetrics) MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			eventType, _ := data["event_type"].(string)
			start := time.Now()

			metrics.IncrementEventCount(eventType)

			err := next(ctx, conn, data)
			
			duration := time.Since(start)
			metrics.RecordEventDuration(eventType, duration)

			if err != nil {
				metrics.IncrementErrorCount(eventType)
			}

			return err
		}
	}
}

// CacheMiddleware middleware để cache responses
func CacheMiddleware(cache map[string]interface{}, ttl time.Duration) MiddlewareFunc {
	type cacheEntry struct {
		data    interface{}
		expires time.Time
	}

	cacheData := make(map[string]*cacheEntry)
	var cacheMu sync.RWMutex

	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			// Generate cache key
			eventType, _ := data["event_type"].(string)
			cacheKey := eventType + ":" + conn.UserID()

			// Check cache
			cacheMu.RLock()
			entry, exists := cacheData[cacheKey]
			cacheMu.RUnlock()

			if exists && time.Now().Before(entry.expires) {
				// Return cached result
				if cachedData, ok := entry.data.(map[string]interface{}); ok {
					for k, v := range cachedData {
						data[k] = v
					}
				}
				return nil
			}

			// Execute handler
			err := next(ctx, conn, data)

			// Cache result if successful
			if err == nil && ttl > 0 {
				cacheMu.Lock()
				cacheData[cacheKey] = &cacheEntry{
					data:    data,
					expires: time.Now().Add(ttl),
				}
				cacheMu.Unlock()
			}

			return err
		}
	}
}

// ConditionalMiddleware middleware chỉ chạy khi điều kiện thỏa mãn
func ConditionalMiddleware(condition func(ctx context.Context, conn Connection, data map[string]interface{}) bool, middleware MiddlewareFunc) MiddlewareFunc {
	return func(next EventHandler) EventHandler {
		return func(ctx context.Context, conn Connection, data map[string]interface{}) error {
			if condition(ctx, conn, data) {
				return middleware(next)(ctx, conn, data)
			}
			return next(ctx, conn, data)
		}
	}
}

// EventTypeMiddleware middleware chỉ chạy cho event types cụ thể
func EventTypeMiddleware(eventTypes []string, middleware MiddlewareFunc) MiddlewareFunc {
	eventTypeMap := make(map[string]bool)
	for _, eventType := range eventTypes {
		eventTypeMap[eventType] = true
	}

	return ConditionalMiddleware(
		func(ctx context.Context, conn Connection, data map[string]interface{}) bool {
			eventType, _ := data["event_type"].(string)
			return eventTypeMap[eventType]
		},
		middleware,
	)
}
