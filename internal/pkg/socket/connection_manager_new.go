package socket

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"wnapi/internal/pkg/logger"
)

// ConnectionManager quản lý lifecycle của WebSocket connections
type ConnectionManager struct {
	// Connection storage
	connections map[string]Connection
	connMutex   sync.RWMutex

	// User tracking
	userConnections map[string][]Connection
	userMutex       sync.RWMutex

	// Configuration
	config *ConnectionManagerConfig

	// Event handling
	eventRouter EventRouter

	// Metrics
	connectionCount int64
	totalCreated    int64
	totalClosed     int64

	// Cleanup
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}

	// Logger
	logger logger.Logger
}

// ConnectionManagerConfig cấu hình cho ConnectionManager
type ConnectionManagerConfig struct {
	MaxConnections        int
	MaxConnectionsPerUser int
	CleanupInterval       time.Duration
	ConnectionConfig      *ConnectionConfig
}

// DefaultConnectionManagerConfig trả về cấu hình mặc định
func DefaultConnectionManagerConfig() *ConnectionManagerConfig {
	return &ConnectionManagerConfig{
		MaxConnections:        DefaultMaxConnections,
		MaxConnectionsPerUser: DefaultMaxConnectionsPerUser,
		CleanupInterval:       DefaultCleanupInterval,
		ConnectionConfig:      DefaultConnectionConfig(),
	}
}

// NewConnectionManager tạo ConnectionManager mới
func NewConnectionManager(
	config *ConnectionManagerConfig,
	eventRouter EventRouter,
	logger logger.Logger,
) *ConnectionManager {
	if config == nil {
		config = DefaultConnectionManagerConfig()
	}

	cm := &ConnectionManager{
		connections:     make(map[string]Connection),
		userConnections: make(map[string][]Connection),
		config:          config,
		eventRouter:     eventRouter,
		stopCleanup:     make(chan struct{}),
		logger:          logger.Named("connection-manager"),
	}

	// Start cleanup routine
	cm.startCleanup()

	return cm
}

// CreateConnection tạo connection mới từ WebSocket
func (cm *ConnectionManager) CreateConnection(
	userID, tenantID, websiteID string,
	wsConn *websocket.Conn,
) (Connection, error) {
	// Kiểm tra limits
	if err := cm.checkLimits(userID); err != nil {
		return nil, err
	}

	// Generate connection ID
	connID := cm.generateConnectionID(userID)

	// Tạo connection
	conn := NewWebSocketConnection(
		connID,
		userID,
		tenantID,
		websiteID,
		wsConn.RemoteAddr().String(),
		cm.config.ConnectionConfig,
		cm.logger,
	)

	// Đăng ký connection
	if err := cm.RegisterConnection(conn); err != nil {
		conn.Close()
		return nil, err
	}

	cm.logger.Info("Connection created",
		"conn_id", connID,
		"user_id", userID,
		"tenant_id", tenantID,
		"remote_addr", conn.RemoteAddr())

	return conn, nil
}

// RegisterConnection đăng ký connection
func (cm *ConnectionManager) RegisterConnection(conn Connection) error {
	cm.connMutex.Lock()
	defer cm.connMutex.Unlock()

	// Kiểm tra connection đã tồn tại
	if _, exists := cm.connections[conn.ID()]; exists {
		return ErrConnectionExists
	}

	// Thêm vào connections map
	cm.connections[conn.ID()] = conn

	// Thêm vào user connections
	cm.userMutex.Lock()
	cm.userConnections[conn.UserID()] = append(cm.userConnections[conn.UserID()], conn)
	cm.userMutex.Unlock()

	// Update metrics
	cm.connectionCount++
	cm.totalCreated++

	cm.logger.Debug("Connection registered",
		"conn_id", conn.ID(),
		"user_id", conn.UserID(),
		"total_connections", cm.connectionCount)

	return nil
}

// UnregisterConnection hủy đăng ký connection
func (cm *ConnectionManager) UnregisterConnection(connID string) error {
	cm.connMutex.Lock()
	conn, exists := cm.connections[connID]
	if exists {
		delete(cm.connections, connID)
		cm.connectionCount--
		cm.totalClosed++
	}
	cm.connMutex.Unlock()

	if !exists {
		return ErrConnectionNotFound
	}

	// Remove from user connections
	cm.userMutex.Lock()
	userConns := cm.userConnections[conn.UserID()]
	for i, c := range userConns {
		if c.ID() == connID {
			cm.userConnections[conn.UserID()] = append(userConns[:i], userConns[i+1:]...)
			break
		}
	}
	// Remove user if no connections left
	if len(cm.userConnections[conn.UserID()]) == 0 {
		delete(cm.userConnections, conn.UserID())
	}
	cm.userMutex.Unlock()

	cm.logger.Debug("Connection unregistered",
		"conn_id", connID,
		"user_id", conn.UserID(),
		"total_connections", cm.connectionCount)

	return nil
}

// GetConnection lấy connection theo ID
func (cm *ConnectionManager) GetConnection(connID string) (Connection, bool) {
	cm.connMutex.RLock()
	defer cm.connMutex.RUnlock()

	conn, exists := cm.connections[connID]
	return conn, exists
}

// GetUserConnections lấy tất cả connections của user
func (cm *ConnectionManager) GetUserConnections(userID string) []Connection {
	cm.userMutex.RLock()
	defer cm.userMutex.RUnlock()

	connections := cm.userConnections[userID]
	if connections == nil {
		return []Connection{}
	}

	// Return copy
	result := make([]Connection, len(connections))
	copy(result, connections)
	return result
}

// GetAllConnections lấy tất cả connections
func (cm *ConnectionManager) GetAllConnections() []Connection {
	cm.connMutex.RLock()
	defer cm.connMutex.RUnlock()

	connections := make([]Connection, 0, len(cm.connections))
	for _, conn := range cm.connections {
		connections = append(connections, conn)
	}

	return connections
}

// GetConnectionsByTenant lấy connections theo tenant
func (cm *ConnectionManager) GetConnectionsByTenant(tenantID string) []Connection {
	cm.connMutex.RLock()
	defer cm.connMutex.RUnlock()

	connections := make([]Connection, 0)
	for _, conn := range cm.connections {
		if conn.TenantID() == tenantID {
			connections = append(connections, conn)
		}
	}

	return connections
}

// GetConnectionsByWebsite lấy connections theo website
func (cm *ConnectionManager) GetConnectionsByWebsite(websiteID string) []Connection {
	cm.connMutex.RLock()
	defer cm.connMutex.RUnlock()

	connections := make([]Connection, 0)
	for _, conn := range cm.connections {
		if conn.WebsiteID() == websiteID {
			connections = append(connections, conn)
		}
	}

	return connections
}

// CloseConnection đóng connection
func (cm *ConnectionManager) CloseConnection(connID string) error {
	conn, exists := cm.GetConnection(connID)
	if !exists {
		return ErrConnectionNotFound
	}

	conn.Close()
	return cm.UnregisterConnection(connID)
}

// CloseUserConnections đóng tất cả connections của user
func (cm *ConnectionManager) CloseUserConnections(userID string) error {
	connections := cm.GetUserConnections(userID)
	
	for _, conn := range connections {
		conn.Close()
		cm.UnregisterConnection(conn.ID())
	}

	cm.logger.Info("Closed user connections",
		"user_id", userID,
		"count", len(connections))

	return nil
}

// BroadcastToUser gửi message đến tất cả connections của user
func (cm *ConnectionManager) BroadcastToUser(userID string, message *OutgoingMessage) error {
	connections := cm.GetUserConnections(userID)
	if len(connections) == 0 {
		return ErrUserNotConnected
	}

	var errors []error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send to some connections: %v", errors)
	}

	return nil
}

// BroadcastToTenant gửi message đến tất cả connections của tenant
func (cm *ConnectionManager) BroadcastToTenant(tenantID string, message *OutgoingMessage) error {
	connections := cm.GetConnectionsByTenant(tenantID)
	if len(connections) == 0 {
		return fmt.Errorf("no connections found for tenant: %s", tenantID)
	}

	var errors []error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send to some connections: %v", errors)
	}

	return nil
}

// BroadcastToWebsite gửi message đến tất cả connections của website
func (cm *ConnectionManager) BroadcastToWebsite(websiteID string, message *OutgoingMessage) error {
	connections := cm.GetConnectionsByWebsite(websiteID)
	if len(connections) == 0 {
		return fmt.Errorf("no connections found for website: %s", websiteID)
	}

	var errors []error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send to some connections: %v", errors)
	}

	return nil
}

// BroadcastToAll gửi message đến tất cả connections
func (cm *ConnectionManager) BroadcastToAll(message *OutgoingMessage) error {
	connections := cm.GetAllConnections()
	if len(connections) == 0 {
		return fmt.Errorf("no active connections")
	}

	var errors []error
	for _, conn := range connections {
		if err := conn.SendMessage(message); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send to some connections: %v", errors)
	}

	return nil
}

// GetStatistics lấy thống kê connections
func (cm *ConnectionManager) GetStatistics() map[string]interface{} {
	cm.connMutex.RLock()
	totalConnections := len(cm.connections)
	cm.connMutex.RUnlock()

	cm.userMutex.RLock()
	totalUsers := len(cm.userConnections)
	cm.userMutex.RUnlock()

	return map[string]interface{}{
		"total_connections": totalConnections,
		"total_users":       totalUsers,
		"total_created":     cm.totalCreated,
		"total_closed":      cm.totalClosed,
		"max_connections":   cm.config.MaxConnections,
		"max_per_user":      cm.config.MaxConnectionsPerUser,
	}
}

// GetDetailedStatistics lấy thống kê chi tiết
func (cm *ConnectionManager) GetDetailedStatistics() map[string]interface{} {
	stats := cm.GetStatistics()

	// Connection by tenant
	tenantStats := make(map[string]int)
	websiteStats := make(map[string]int)

	cm.connMutex.RLock()
	for _, conn := range cm.connections {
		tenantStats[conn.TenantID()]++
		websiteStats[conn.WebsiteID()]++
	}
	cm.connMutex.RUnlock()

	stats["by_tenant"] = tenantStats
	stats["by_website"] = websiteStats

	return stats
}

// Cleanup dọn dẹp dead connections
func (cm *ConnectionManager) Cleanup() {
	cm.connMutex.RLock()
	deadConnections := make([]Connection, 0)
	for _, conn := range cm.connections {
		if !conn.IsAlive() {
			deadConnections = append(deadConnections, conn)
		}
	}
	cm.connMutex.RUnlock()

	for _, conn := range deadConnections {
		cm.logger.Debug("Cleaning up dead connection", "conn_id", conn.ID())
		conn.Close()
		cm.UnregisterConnection(conn.ID())
	}

	if len(deadConnections) > 0 {
		cm.logger.Info("Cleaned up dead connections", "count", len(deadConnections))
	}
}

// Shutdown đóng tất cả connections và dừng manager
func (cm *ConnectionManager) Shutdown(ctx context.Context) error {
	cm.logger.Info("Shutting down connection manager")

	// Stop cleanup routine
	close(cm.stopCleanup)
	if cm.cleanupTicker != nil {
		cm.cleanupTicker.Stop()
	}

	// Close all connections
	connections := cm.GetAllConnections()
	for _, conn := range connections {
		conn.Close()
	}

	// Wait for connections to close or timeout
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			cm.logger.Warn("Shutdown timeout, forcing close")
			return ctx.Err()
		case <-ticker.C:
			if len(cm.GetAllConnections()) == 0 {
				cm.logger.Info("All connections closed")
				return nil
			}
		}
	}
}

// checkLimits kiểm tra các giới hạn kết nối
func (cm *ConnectionManager) checkLimits(userID string) error {
	// Check total connections limit
	cm.connMutex.RLock()
	totalConnections := len(cm.connections)
	cm.connMutex.RUnlock()

	if totalConnections >= cm.config.MaxConnections {
		return ErrMaxConnectionsReached
	}

	// Check connections per user limit
	userConnections := cm.GetUserConnections(userID)
	if len(userConnections) >= cm.config.MaxConnectionsPerUser {
		return ErrMaxConnectionsPerUserReached
	}

	return nil
}

// generateConnectionID tạo unique connection ID
func (cm *ConnectionManager) generateConnectionID(userID string) string {
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("conn_%s_%d", userID, timestamp)
}

// startCleanup bắt đầu cleanup routine
func (cm *ConnectionManager) startCleanup() {
	cm.cleanupTicker = time.NewTicker(cm.config.CleanupInterval)

	go func() {
		for {
			select {
			case <-cm.cleanupTicker.C:
				cm.Cleanup()
			case <-cm.stopCleanup:
				return
			}
		}
	}()
}

// IsUserConnected kiểm tra user có connection không
func (cm *ConnectionManager) IsUserConnected(userID string) bool {
	connections := cm.GetUserConnections(userID)
	return len(connections) > 0
}

// GetUserConnectionCount lấy số connections của user
func (cm *ConnectionManager) GetUserConnectionCount(userID string) int {
	connections := cm.GetUserConnections(userID)
	return len(connections)
}

// GetTotalConnectionCount lấy tổng số connections
func (cm *ConnectionManager) GetTotalConnectionCount() int {
	cm.connMutex.RLock()
	defer cm.connMutex.RUnlock()
	return len(cm.connections)
}
