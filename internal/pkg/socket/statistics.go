package socket

import (
	"sync"
	"sync/atomic"
	"time"
)

// HubStatistics thống kê của Hub
type HubStatistics struct {
	// Connection stats
	TotalConnections     int64 `json:"totalConnections"`
	ActiveConnections    int64 `json:"activeConnections"`
	PeakConnections      int64 `json:"peakConnections"`
	ConnectionsCreated   int64 `json:"connectionsCreated"`
	ConnectionsDestroyed int64 `json:"connectionsDestroyed"`

	// Room stats
	TotalRooms     int64 `json:"totalRooms"`
	ActiveRooms    int64 `json:"activeRooms"`
	PeakRooms      int64 `json:"peakRooms"`
	RoomsCreated   int64 `json:"roomsCreated"`
	RoomsDestroyed int64 `json:"roomsDestroyed"`

	// Message stats
	TotalMessages      int64 `json:"totalMessages"`
	TotalBroadcasts    int64 `json:"totalBroadcasts"`
	MessagesPerSecond  int64 `json:"messagesPerSecond"`
	BroadcastsPerSecond int64 `json:"broadcastsPerSecond"`

	// Performance stats
	AverageResponseTime time.Duration `json:"averageResponseTime"`
	MessageQueueSize    int64         `json:"messageQueueSize"`
	ErrorCount          int64         `json:"errorCount"`

	// Timestamps
	StartTime    time.Time `json:"startTime"`
	LastActivity time.Time `json:"lastActivity"`

	// Internal counters for rate calculation
	lastMessageCount   int64
	lastBroadcastCount int64
	lastUpdateTime     time.Time

	// Mutex for thread-safe operations
	mu sync.RWMutex
}

// NewHubStatistics tạo HubStatistics instance mới
func NewHubStatistics() *HubStatistics {
	now := time.Now()
	return &HubStatistics{
		StartTime:        now,
		LastActivity:     now,
		lastUpdateTime:   now,
	}
}

// AddConnection thêm connection mới
func (hs *HubStatistics) AddConnection() {
	atomic.AddInt64(&hs.TotalConnections, 1)
	atomic.AddInt64(&hs.ActiveConnections, 1)
	atomic.AddInt64(&hs.ConnectionsCreated, 1)

	// Update peak connections
	current := atomic.LoadInt64(&hs.ActiveConnections)
	for {
		peak := atomic.LoadInt64(&hs.PeakConnections)
		if current <= peak || atomic.CompareAndSwapInt64(&hs.PeakConnections, peak, current) {
			break
		}
	}

	hs.updateLastActivity()
}

// RemoveConnection xóa connection
func (hs *HubStatistics) RemoveConnection() {
	atomic.AddInt64(&hs.ActiveConnections, -1)
	atomic.AddInt64(&hs.ConnectionsDestroyed, 1)
	hs.updateLastActivity()
}

// AddRoom thêm room mới
func (hs *HubStatistics) AddRoom() {
	atomic.AddInt64(&hs.TotalRooms, 1)
	atomic.AddInt64(&hs.ActiveRooms, 1)
	atomic.AddInt64(&hs.RoomsCreated, 1)

	// Update peak rooms
	current := atomic.LoadInt64(&hs.ActiveRooms)
	for {
		peak := atomic.LoadInt64(&hs.PeakRooms)
		if current <= peak || atomic.CompareAndSwapInt64(&hs.PeakRooms, peak, current) {
			break
		}
	}

	hs.updateLastActivity()
}

// RemoveRoom xóa room
func (hs *HubStatistics) RemoveRoom() {
	atomic.AddInt64(&hs.ActiveRooms, -1)
	atomic.AddInt64(&hs.RoomsDestroyed, 1)
	hs.updateLastActivity()
}

// AddMessage thêm message
func (hs *HubStatistics) AddMessage() {
	atomic.AddInt64(&hs.TotalMessages, 1)
	hs.updateLastActivity()
}

// AddBroadcast thêm broadcast
func (hs *HubStatistics) AddBroadcast() {
	atomic.AddInt64(&hs.TotalBroadcasts, 1)
	hs.updateLastActivity()
}

// AddError thêm error
func (hs *HubStatistics) AddError() {
	atomic.AddInt64(&hs.ErrorCount, 1)
}

// SetMessageQueueSize cập nhật message queue size
func (hs *HubStatistics) SetMessageQueueSize(size int64) {
	atomic.StoreInt64(&hs.MessageQueueSize, size)
}

// UpdateResponseTime cập nhật average response time
func (hs *HubStatistics) UpdateResponseTime(responseTime time.Duration) {
	hs.mu.Lock()
	defer hs.mu.Unlock()

	// Simple moving average
	if hs.AverageResponseTime == 0 {
		hs.AverageResponseTime = responseTime
	} else {
		hs.AverageResponseTime = (hs.AverageResponseTime + responseTime) / 2
	}
}

// UpdateRates cập nhật rates (messages/second, broadcasts/second)
func (hs *HubStatistics) UpdateRates() {
	hs.mu.Lock()
	defer hs.mu.Unlock()

	now := time.Now()
	duration := now.Sub(hs.lastUpdateTime)
	if duration < time.Second {
		return
	}

	currentMessages := atomic.LoadInt64(&hs.TotalMessages)
	currentBroadcasts := atomic.LoadInt64(&hs.TotalBroadcasts)

	messageDiff := currentMessages - hs.lastMessageCount
	broadcastDiff := currentBroadcasts - hs.lastBroadcastCount

	seconds := duration.Seconds()
	hs.MessagesPerSecond = int64(float64(messageDiff) / seconds)
	hs.BroadcastsPerSecond = int64(float64(broadcastDiff) / seconds)

	hs.lastMessageCount = currentMessages
	hs.lastBroadcastCount = currentBroadcasts
	hs.lastUpdateTime = now
}

// GetUptime lấy uptime của Hub
func (hs *HubStatistics) GetUptime() time.Duration {
	return time.Since(hs.StartTime)
}

// Copy tạo copy của statistics (thread-safe)
func (hs *HubStatistics) Copy() *HubStatistics {
	hs.mu.RLock()
	defer hs.mu.RUnlock()

	return &HubStatistics{
		TotalConnections:     atomic.LoadInt64(&hs.TotalConnections),
		ActiveConnections:    atomic.LoadInt64(&hs.ActiveConnections),
		PeakConnections:      atomic.LoadInt64(&hs.PeakConnections),
		ConnectionsCreated:   atomic.LoadInt64(&hs.ConnectionsCreated),
		ConnectionsDestroyed: atomic.LoadInt64(&hs.ConnectionsDestroyed),
		TotalRooms:           atomic.LoadInt64(&hs.TotalRooms),
		ActiveRooms:          atomic.LoadInt64(&hs.ActiveRooms),
		PeakRooms:            atomic.LoadInt64(&hs.PeakRooms),
		RoomsCreated:         atomic.LoadInt64(&hs.RoomsCreated),
		RoomsDestroyed:       atomic.LoadInt64(&hs.RoomsDestroyed),
		TotalMessages:        atomic.LoadInt64(&hs.TotalMessages),
		TotalBroadcasts:      atomic.LoadInt64(&hs.TotalBroadcasts),
		MessagesPerSecond:    hs.MessagesPerSecond,
		BroadcastsPerSecond:  hs.BroadcastsPerSecond,
		AverageResponseTime:  hs.AverageResponseTime,
		MessageQueueSize:     atomic.LoadInt64(&hs.MessageQueueSize),
		ErrorCount:           atomic.LoadInt64(&hs.ErrorCount),
		StartTime:            hs.StartTime,
		LastActivity:         hs.LastActivity,
	}
}

// Reset reset tất cả statistics
func (hs *HubStatistics) Reset() {
	now := time.Now()

	atomic.StoreInt64(&hs.TotalConnections, 0)
	atomic.StoreInt64(&hs.ActiveConnections, 0)
	atomic.StoreInt64(&hs.PeakConnections, 0)
	atomic.StoreInt64(&hs.ConnectionsCreated, 0)
	atomic.StoreInt64(&hs.ConnectionsDestroyed, 0)
	atomic.StoreInt64(&hs.TotalRooms, 0)
	atomic.StoreInt64(&hs.ActiveRooms, 0)
	atomic.StoreInt64(&hs.PeakRooms, 0)
	atomic.StoreInt64(&hs.RoomsCreated, 0)
	atomic.StoreInt64(&hs.RoomsDestroyed, 0)
	atomic.StoreInt64(&hs.TotalMessages, 0)
	atomic.StoreInt64(&hs.TotalBroadcasts, 0)
	atomic.StoreInt64(&hs.MessagesPerSecond, 0)
	atomic.StoreInt64(&hs.BroadcastsPerSecond, 0)
	atomic.StoreInt64(&hs.MessageQueueSize, 0)
	atomic.StoreInt64(&hs.ErrorCount, 0)

	hs.mu.Lock()
	hs.AverageResponseTime = 0
	hs.StartTime = now
	hs.LastActivity = now
	hs.lastMessageCount = 0
	hs.lastBroadcastCount = 0
	hs.lastUpdateTime = now
	hs.mu.Unlock()
}

// updateLastActivity cập nhật last activity timestamp
func (hs *HubStatistics) updateLastActivity() {
	hs.mu.Lock()
	hs.LastActivity = time.Now()
	hs.mu.Unlock()
}

// ConnectionStatistics thống kê cho một connection
type ConnectionStatistics struct {
	ID               string        `json:"id"`
	UserID           string        `json:"userId"`
	ConnectedAt      time.Time     `json:"connectedAt"`
	LastActivity     time.Time     `json:"lastActivity"`
	MessagesSent     int64         `json:"messagesSent"`
	MessagesReceived int64         `json:"messagesReceived"`
	BytesSent        int64         `json:"bytesSent"`
	BytesReceived    int64         `json:"bytesReceived"`
	RoomCount        int           `json:"roomCount"`
	Uptime           time.Duration `json:"uptime"`
	IsAlive          bool          `json:"isAlive"`

	mu sync.RWMutex
}

// NewConnectionStatistics tạo connection statistics mới
func NewConnectionStatistics(id, userID string) *ConnectionStatistics {
	now := time.Now()
	return &ConnectionStatistics{
		ID:           id,
		UserID:       userID,
		ConnectedAt:  now,
		LastActivity: now,
		IsAlive:      true,
	}
}

// AddMessageSent thêm message sent
func (cs *ConnectionStatistics) AddMessageSent(bytes int64) {
	atomic.AddInt64(&cs.MessagesSent, 1)
	atomic.AddInt64(&cs.BytesSent, bytes)
	cs.updateLastActivity()
}

// AddMessageReceived thêm message received
func (cs *ConnectionStatistics) AddMessageReceived(bytes int64) {
	atomic.AddInt64(&cs.MessagesReceived, 1)
	atomic.AddInt64(&cs.BytesReceived, bytes)
	cs.updateLastActivity()
}

// SetRoomCount cập nhật room count
func (cs *ConnectionStatistics) SetRoomCount(count int) {
	cs.mu.Lock()
	cs.RoomCount = count
	cs.mu.Unlock()
}

// SetAlive cập nhật alive status
func (cs *ConnectionStatistics) SetAlive(alive bool) {
	cs.mu.Lock()
	cs.IsAlive = alive
	cs.mu.Unlock()
}

// GetUptime lấy uptime của connection
func (cs *ConnectionStatistics) GetUptime() time.Duration {
	return time.Since(cs.ConnectedAt)
}

// updateLastActivity cập nhật last activity
func (cs *ConnectionStatistics) updateLastActivity() {
	cs.mu.Lock()
	cs.LastActivity = time.Now()
	cs.mu.Unlock()
}

// Copy tạo copy của statistics
func (cs *ConnectionStatistics) Copy() *ConnectionStatistics {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	return &ConnectionStatistics{
		ID:               cs.ID,
		UserID:           cs.UserID,
		ConnectedAt:      cs.ConnectedAt,
		LastActivity:     cs.LastActivity,
		MessagesSent:     atomic.LoadInt64(&cs.MessagesSent),
		MessagesReceived: atomic.LoadInt64(&cs.MessagesReceived),
		BytesSent:        atomic.LoadInt64(&cs.BytesSent),
		BytesReceived:    atomic.LoadInt64(&cs.BytesReceived),
		RoomCount:        cs.RoomCount,
		Uptime:           cs.GetUptime(),
		IsAlive:          cs.IsAlive,
	}
}

// RoomStatistics thống kê cho một room
type RoomStatistics struct {
	ID              string        `json:"id"`
	Type            RoomType      `json:"type"`
	CreatedAt       time.Time     `json:"createdAt"`
	LastActivity    time.Time     `json:"lastActivity"`
	MemberCount     int           `json:"memberCount"`
	ConnectionCount int           `json:"connectionCount"`
	MessageCount    int64         `json:"messageCount"`
	Uptime          time.Duration `json:"uptime"`

	mu sync.RWMutex
}

// NewRoomStatistics tạo room statistics mới
func NewRoomStatistics(id string, roomType RoomType) *RoomStatistics {
	now := time.Now()
	return &RoomStatistics{
		ID:           id,
		Type:         roomType,
		CreatedAt:    now,
		LastActivity: now,
	}
}

// AddMessage thêm message
func (rs *RoomStatistics) AddMessage() {
	atomic.AddInt64(&rs.MessageCount, 1)
	rs.updateLastActivity()
}

// SetMemberCount cập nhật member count
func (rs *RoomStatistics) SetMemberCount(count int) {
	rs.mu.Lock()
	rs.MemberCount = count
	rs.mu.Unlock()
	rs.updateLastActivity()
}

// SetConnectionCount cập nhật connection count
func (rs *RoomStatistics) SetConnectionCount(count int) {
	rs.mu.Lock()
	rs.ConnectionCount = count
	rs.mu.Unlock()
	rs.updateLastActivity()
}

// GetUptime lấy uptime của room
func (rs *RoomStatistics) GetUptime() time.Duration {
	return time.Since(rs.CreatedAt)
}

// updateLastActivity cập nhật last activity
func (rs *RoomStatistics) updateLastActivity() {
	rs.mu.Lock()
	rs.LastActivity = time.Now()
	rs.mu.Unlock()
}

// Copy tạo copy của statistics
func (rs *RoomStatistics) Copy() *RoomStatistics {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	return &RoomStatistics{
		ID:              rs.ID,
		Type:            rs.Type,
		CreatedAt:       rs.CreatedAt,
		LastActivity:    rs.LastActivity,
		MemberCount:     rs.MemberCount,
		ConnectionCount: rs.ConnectionCount,
		MessageCount:    atomic.LoadInt64(&rs.MessageCount),
		Uptime:          rs.GetUptime(),
	}
}

// StatisticsManager quản lý tất cả statistics
type StatisticsManager struct {
	hubStats         *HubStatistics
	connectionStats  map[string]*ConnectionStatistics
	roomStats        map[string]*RoomStatistics
	mu               sync.RWMutex
}

// NewStatisticsManager tạo statistics manager mới
func NewStatisticsManager() *StatisticsManager {
	return &StatisticsManager{
		hubStats:        NewHubStatistics(),
		connectionStats: make(map[string]*ConnectionStatistics),
		roomStats:       make(map[string]*RoomStatistics),
	}
}

// GetHubStatistics lấy hub statistics
func (sm *StatisticsManager) GetHubStatistics() *HubStatistics {
	return sm.hubStats.Copy()
}

// AddConnectionStatistics thêm connection statistics
func (sm *StatisticsManager) AddConnectionStatistics(stats *ConnectionStatistics) {
	sm.mu.Lock()
	sm.connectionStats[stats.ID] = stats
	sm.mu.Unlock()
}

// RemoveConnectionStatistics xóa connection statistics
func (sm *StatisticsManager) RemoveConnectionStatistics(connectionID string) {
	sm.mu.Lock()
	delete(sm.connectionStats, connectionID)
	sm.mu.Unlock()
}

// GetConnectionStatistics lấy connection statistics
func (sm *StatisticsManager) GetConnectionStatistics(connectionID string) *ConnectionStatistics {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if stats, exists := sm.connectionStats[connectionID]; exists {
		return stats.Copy()
	}
	return nil
}

// GetAllConnectionStatistics lấy tất cả connection statistics
func (sm *StatisticsManager) GetAllConnectionStatistics() []*ConnectionStatistics {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := make([]*ConnectionStatistics, 0, len(sm.connectionStats))
	for _, connStats := range sm.connectionStats {
		stats = append(stats, connStats.Copy())
	}
	return stats
}

// AddRoomStatistics thêm room statistics
func (sm *StatisticsManager) AddRoomStatistics(stats *RoomStatistics) {
	sm.mu.Lock()
	sm.roomStats[stats.ID] = stats
	sm.mu.Unlock()
}

// RemoveRoomStatistics xóa room statistics
func (sm *StatisticsManager) RemoveRoomStatistics(roomID string) {
	sm.mu.Lock()
	delete(sm.roomStats, roomID)
	sm.mu.Unlock()
}

// GetRoomStatistics lấy room statistics
func (sm *StatisticsManager) GetRoomStatistics(roomID string) *RoomStatistics {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	if stats, exists := sm.roomStats[roomID]; exists {
		return stats.Copy()
	}
	return nil
}

// GetAllRoomStatistics lấy tất cả room statistics
func (sm *StatisticsManager) GetAllRoomStatistics() []*RoomStatistics {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := make([]*RoomStatistics, 0, len(sm.roomStats))
	for _, roomStats := range sm.roomStats {
		stats = append(stats, roomStats.Copy())
	}
	return stats
}
