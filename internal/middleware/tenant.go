package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/constants"
	"wnapi/internal/pkg/response"
	"wnapi/modules/tenant/models"

	"github.com/gin-gonic/gin"
)

// TenantContextKey là key để lưu thông tin tenant trong context
type TenantContextKey string

const (
	// TenantKey là key để lưu tenant trong context
	TenantKey TenantContextKey = "tenant"
)

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	VerifyUserTenantAccess(ctx context.Context, userID, tenantID uint) (*models.Tenant, error)
}

// TenantMiddleware tạo middleware để xác thực và lưu thông tin tenant vào context
// Middleware này phải được chạy sau JWTAuthMiddleware
// Tenant ID được lấy từ header X-Tenant-ID
func TenantMiddleware(tenantService TenantService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. Lấy user ID từ context (đã được set bởi JWT middleware)
		userIDPtr := auth.GetUserID(c)
		if userIDPtr == nil {
			response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin người dùng", "USER_NOT_FOUND")
			c.Abort()
			return
		}
		userID := *userIDPtr

		// 2. Lấy tenant ID từ header
		tenantIDStr := c.GetHeader(constants.TenantHeaderKey)
		if tenantIDStr == "" {
			response.Error(c, http.StatusBadRequest, "Thiếu thông tin "+constants.TenantHeaderKey, "TENANT_REQUIRED")
			c.Abort()
			return
		}

		// 3. Chuyển đổi tenant ID từ string sang uint
		tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
		if err != nil {
			response.Error(c, http.StatusBadRequest, "Tenant ID không hợp lệ", "INVALID_TENANT_ID")
			c.Abort()
			return
		}

		// 4. Xác thực quyền truy cập của user vào tenant
		tenant, err := tenantService.VerifyUserTenantAccess(c.Request.Context(), userID, uint(tenantID))
		if err != nil {
			response.Error(c, http.StatusForbidden, fmt.Sprintf("Không có quyền truy cập tenant: %v", err), "TENANT_ACCESS_DENIED")
			c.Abort()
			return
		}

		// 5. Lưu thông tin tenant vào context
		SetTenantInContext(c, tenant)

		// 6. Tiếp tục xử lý request
		c.Next()
	}
}

// SetTenantInContext lưu thông tin tenant vào context
// Sử dụng key "tenant" để lưu thông tin tenant
func SetTenantInContext(c *gin.Context, tenant *models.Tenant) {
	c.Set(constants.TenantContextKey, tenant.TenantID)
}

// GetTenantFromContext lấy thông tin tenant từ context
func GetTenantFromContext(c *gin.Context) (*models.Tenant, error) {
	tenantValue, exists := c.Get(constants.TenantContextKey)
	if !exists {
		return nil, fmt.Errorf("không tìm thấy thông tin tenant trong context")
	}

	tenantID, ok := tenantValue.(uint)
	if !ok {
		return nil, fmt.Errorf("thông tin tenant không hợp lệ")
	}

	return &models.Tenant{TenantID: tenantID}, nil
}

// GetTenantID lấy tenant ID từ context
func GetTenantID(c *gin.Context) (uint, error) {
	tenantValue, exists := c.Get(constants.TenantContextKey)
	if !exists {
		return 0, fmt.Errorf("không tìm thấy thông tin tenant trong context")
	}

	tenantID, ok := tenantValue.(uint)
	if !ok {
		return 0, fmt.Errorf("thông tin tenant không hợp lệ")
	}

	return tenantID, nil
}
