package middleware

import (
	"context"
	"wnapi/modules/tenant/models"
)

// NoOpTenantService là một implementation gi<PERSON> của TenantService
// Sử dụng khi không cần xác thực tenant thực sự (ví dụ: trong môi trường test hoặc development)
type NoOpTenantService struct{}

// NewNoOpTenantService tạo một instance mới của NoOpTenantService
func NewNoOpTenantService() TenantService {
	return &NoOpTenantService{}
}

// VerifyUserTenantAccess luôn trả về một tenant giả
// Implementation này chỉ nên được sử dụng trong môi trường phát triển hoặc test
func (s *NoOpTenantService) VerifyUserTenantAccess(ctx context.Context, userID, tenantID uint) (*models.Tenant, error) {
	// Luôn trả về một tenant giả
	tenant := &models.Tenant{
		TenantID:   tenantID,
		TenantName: "NoOp Tenant",
		TenantCode: "noop",
		Status:     models.TenantStatusActive,
	}

	return tenant, nil
}