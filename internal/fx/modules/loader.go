package modules

import (
	"fmt"
	"sort"

	"wnapi/internal/pkg/logger"

	"go.uber.org/fx"
)

// LoaderConfig contains module loading configuration
type LoaderConfig struct {
	EnabledModules []string
	ModuleConfigs  map[string]map[string]interface{}
	Logger         logger.Logger
}

// ModuleLoader handles module loading and dependency resolution
type ModuleLoader struct {
	registry *ModuleRegistry
	config   LoaderConfig
	logger   logger.Logger
}

// NewModuleLoader creates new module loader
func NewModuleLoader(registry *ModuleRegistry, config LoaderConfig) *ModuleLoader {
	return &ModuleLoader{
		registry: registry,
		config:   config,
		logger:   config.Logger,
	}
}

// LoadModules loads enabled modules in dependency order
func (l *ModuleLoader) LoadModules() ([]fx.Option, error) {
	// Get enabled modules
	enabledModules, err := l.getEnabledModules()
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled modules: %w", err)
	}

	// Resolve dependencies
	orderedModules, err := l.resolveDependencies(enabledModules)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve dependencies: %w", err)
	}

	// Create fx options
	var options []fx.Option
	for _, module := range orderedModules {
		l.logger.Info("Loading module", "name", module.Name())
		options = append(options, module.Module())
	}

	return options, nil
}

// getEnabledModules returns list of enabled modules
func (l *ModuleLoader) getEnabledModules() ([]FXModule, error) {
	var enabled []FXModule

	for _, moduleName := range l.config.EnabledModules {
		module, ok := l.registry.Get(moduleName)
		if !ok {
			l.logger.Warn("Module not found", "name", moduleName)
			continue
		}

		// Check if module is enabled
		moduleConfig := l.config.ModuleConfigs[moduleName]
		if !module.Enabled(moduleConfig) {
			l.logger.Info("Module disabled", "name", moduleName)
			continue
		}

		enabled = append(enabled, module)
	}

	return enabled, nil
}

// resolveDependencies resolves module dependencies and returns ordered list
func (l *ModuleLoader) resolveDependencies(modules []FXModule) ([]FXModule, error) {
	// Create dependency graph
	graph := make(map[string][]string)
	moduleMap := make(map[string]FXModule)

	for _, module := range modules {
		name := module.Name()
		graph[name] = module.Dependencies()
		moduleMap[name] = module
	}

	// Topological sort
	ordered, err := l.topologicalSort(graph)
	if err != nil {
		return nil, err
	}

	// Convert to module list
	var result []FXModule
	for _, name := range ordered {
		if module, ok := moduleMap[name]; ok {
			result = append(result, module)
		}
	}

	// Sort by priority within dependency groups
	sort.SliceStable(result, func(i, j int) bool {
		return result[i].Priority() < result[j].Priority()
	})

	return result, nil
}

// topologicalSort performs topological sort on dependency graph
func (l *ModuleLoader) topologicalSort(graph map[string][]string) ([]string, error) {
	// Kahn's algorithm
	inDegree := make(map[string]int)

	// Initialize in-degree
	for node := range graph {
		inDegree[node] = 0
	}

	// Calculate in-degree
	for _, deps := range graph {
		for _, dep := range deps {
			inDegree[dep]++
		}
	}

	// Find nodes with no incoming edges
	var queue []string
	for node, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, node)
		}
	}

	var result []string

	for len(queue) > 0 {
		// Remove node from queue
		node := queue[0]
		queue = queue[1:]
		result = append(result, node)

		// Remove edges from this node
		for _, neighbor := range graph[node] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}

	// Check for cycles
	if len(result) != len(graph) {
		return nil, fmt.Errorf("circular dependency detected")
	}

	return result, nil
}
