package modules

import (
	"fmt"

	"go.uber.org/fx"
)

// FXModule defines interface for fx-based modules
type FXModule interface {
	// Name returns module name
	Name() string

	// Module returns fx.Module for this module
	Module() fx.Option

	// Dependencies returns list of module dependencies
	Dependencies() []string

	// Priority returns loading priority (lower = higher priority)
	Priority() int

	// Enabled checks if module should be loaded
	Enabled(config map[string]interface{}) bool

	// GetMigrationPath returns path to module migrations
	// Returns empty string if module has no migrations
	GetMigrationPath() string

	// GetMigrationOrder returns migration priority order
	// Lower values have higher priority (run first)
	GetMigrationOrder() int
}

// ModuleInfo contains module metadata
type ModuleInfo struct {
	Name         string
	Version      string
	Description  string
	Dependencies []string
	Priority     int
	Module       fx.Option
}

// ModuleRegistry manages fx modules
type ModuleRegistry struct {
	modules map[string]FXModule
	loaded  map[string]bool
}

// NewModuleRegistry creates new module registry
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		modules: make(map[string]FXModule),
		loaded:  make(map[string]bool),
	}
}

// Register registers a module
func (r *ModuleRegistry) Register(module FXModule) error {
	if module == nil {
		return fmt.Errorf("module cannot be nil")
	}

	name := module.Name()
	if name == "" {
		return fmt.Errorf("module name cannot be empty")
	}

	if _, exists := r.modules[name]; exists {
		return fmt.Errorf("module %s already registered", name)
	}

	r.modules[name] = module
	return nil
}

// Get retrieves a module by name
func (r *ModuleRegistry) Get(name string) (FXModule, bool) {
	module, ok := r.modules[name]
	return module, ok
}

// List returns all registered modules
func (r *ModuleRegistry) List() []FXModule {
	modules := make([]FXModule, 0, len(r.modules))
	for _, module := range r.modules {
		modules = append(modules, module)
	}
	return modules
}
