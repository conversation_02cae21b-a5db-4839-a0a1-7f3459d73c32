package modules

import (
	"testing"

	"go.uber.org/fx"
)

// TestModule implements FXModule for testing
type TestModule struct {
	name         string
	dependencies []string
	priority     int
	enabled      bool
}

func (m *TestModule) Name() string                               { return m.name }
func (m *TestModule) Dependencies() []string                     { return m.dependencies }
func (m *TestModule) Priority() int                              { return m.priority }
func (m *TestModule) Enabled(config map[string]interface{}) bool { return m.enabled }
func (m *TestModule) Module() fx.Option                          { return fx.Options() }

// mockLogger implements logger.Logger for testing
type mockLogger struct{}

func (m *mockLogger) Debug(msg string, keysAndValues ...interface{}) {}
func (m *mockLogger) Info(msg string, keysAndValues ...interface{})  {}
func (m *mockLogger) Warn(msg string, keysAndValues ...interface{})  {}
func (m *mockLogger) Error(msg string, keysAndValues ...interface{}) {}
func (m *mockLogger) Fatal(msg string, keysAndValues ...interface{}) {}
func (m *mockLogger) With(keysAndValues ...interface{}) interface{}  { return m }

func TestModuleRegistry(t *testing.T) {
	registry := NewModuleRegistry()

	module := &TestModule{
		name:    "test",
		enabled: true,
	}

	// Test registration
	err := registry.Register(module)
	if err != nil {
		t.Fatalf("Failed to register module: %v", err)
	}

	// Test retrieval
	retrieved, ok := registry.Get("test")
	if !ok {
		t.Fatal("Failed to retrieve module")
	}

	if retrieved.Name() != "test" {
		t.Errorf("Expected module name 'test', got '%s'", retrieved.Name())
	}
}

func TestDependencyResolution(t *testing.T) {
	registry := NewModuleRegistry()

	// Create modules with dependencies
	moduleA := &TestModule{name: "a", enabled: true, priority: 1}
	moduleB := &TestModule{name: "b", dependencies: []string{"a"}, enabled: true, priority: 2}
	moduleC := &TestModule{name: "c", dependencies: []string{"b"}, enabled: true, priority: 3}

	registry.Register(moduleA)
	registry.Register(moduleB)
	registry.Register(moduleC)

	config := LoaderConfig{
		EnabledModules: []string{"c", "a", "b"}, // Intentionally out of order
		ModuleConfigs:  make(map[string]map[string]interface{}),
		Logger:         &mockLogger{},
	}

	loader := NewModuleLoader(registry, config)
	options, err := loader.LoadModules()
	if err != nil {
		t.Fatalf("Failed to load modules: %v", err)
	}

	if len(options) != 3 {
		t.Errorf("Expected 3 modules, got %d", len(options))
	}
}
