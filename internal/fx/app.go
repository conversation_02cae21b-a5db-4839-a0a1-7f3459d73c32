package fx

import (
	"fmt"
	"wnapi/internal/fx/lifecycle"
	"wnapi/internal/fx/modules"
	"wnapi/internal/fx/providers"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"

	"go.uber.org/fx"
)

// <PERSON><PERSON>pp creates a new fx.App with core providers
func NewApp(opts ...fx.Option) *fx.App {
	defaultOpts := []fx.Option{
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
			providers.NewAppCache,
			providers.NewJWTConfig,
			providers.NewJWTService,
			providers.NewPermissionFactory,
			providers.NewGinEngine,
			providers.NewTracingProvider,

			// Queue system providers
			providers.NewQueueConfig,
			providers.NewQueueFactory,
			providers.NewQueueLogger,
			providers.NewQueueManager,
			providers.NewQueueClient,
			providers.NewQueueWorker,
			providers.NewQueueInspector,
			providers.NewQueueScheduler,
		),

		// Module loading will be handled by NewAppWithModules

		// Lifecycle management
		fx.Invoke(
			lifecycle.RegisterServerHooks,
			lifecycle.RegisterQueueHooks,
			func(tp *tracing.Provider) {}, // Ensure tracing is initialized
		),
	}

	// Merge with user options
	allOpts := append(defaultOpts, opts...)

	return fx.New(allOpts...)
}

// AppOptions contains configuration for fx.App
type AppOptions struct {
	// EnableModules specifies which modules to load
	EnableModules []string

	// EnablePlugins specifies which plugins to load
	EnablePlugins []string

	// Development mode settings
	Development bool

	// CLI mode (skip certain services)
	CLIMode bool
}

// NewAppWithOptions creates fx.App with specific options
func NewAppWithOptions(opts AppOptions) *fx.App {
	var fxOpts []fx.Option

	// Add CLI mode configuration
	if opts.CLIMode {
		fxOpts = append(fxOpts, fx.Provide(func() AppOptions { return opts }))
	}

	// Add module loading
	if len(opts.EnableModules) > 0 {
		// Will be implemented in module migration tasks
	}

	return NewApp(fxOpts...)
}

// loadModules loads modules dynamically
func loadModules(cfg config.Config, log logger.Logger) error {
	moduleOptions, err := modules.DiscoverModules(cfg, log)
	if err != nil {
		return fmt.Errorf("failed to discover modules: %w", err)
	}

	log.Info("Discovered modules", "count", len(moduleOptions))
	return nil
}

// NewAppWithModules creates app with specific modules
func NewAppWithModules(moduleNames []string, opts ...fx.Option) *fx.App {
	// Create a simple logger for module loading
	tempLogger := &simpleLogger{}

	// Load modules first to get their fx.Options
	loaderConfig := modules.LoaderConfig{
		EnabledModules: moduleNames,
		ModuleConfigs:  make(map[string]map[string]interface{}), // Will be populated from config
		Logger:         tempLogger,
	}

	loader := modules.NewModuleLoader(modules.GlobalRegistry, loaderConfig)
	moduleOptions, err := loader.LoadModules()
	if err != nil {
		// For now, log the error and continue without modules
		// In production, you might want to handle this differently
		fmt.Printf("Warning: Failed to load modules: %v\n", err)
		moduleOptions = []fx.Option{}
	}

	// Combine core app options with module options
	allOpts := append([]fx.Option{}, opts...)
	allOpts = append(allOpts, moduleOptions...)

	return NewApp(allOpts...)
}

// simpleLogger is a basic logger implementation for module loading
type simpleLogger struct{}

func (l *simpleLogger) Info(msg string, args ...interface{}) {
	fmt.Printf("[INFO] %s", msg)
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			fmt.Printf(" %v=%v", args[i], args[i+1])
		}
	}
	fmt.Println()
}

func (l *simpleLogger) Warn(msg string, args ...interface{}) {
	fmt.Printf("[WARN] %s", msg)
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			fmt.Printf(" %v=%v", args[i], args[i+1])
		}
	}
	fmt.Println()
}

func (l *simpleLogger) Error(msg string, args ...interface{}) {
	fmt.Printf("[ERROR] %s", msg)
	for i := 0; i < len(args); i += 2 {
		if i+1 < len(args) {
			fmt.Printf(" %v=%v", args[i], args[i+1])
		}
	}
	fmt.Println()
}

func (l *simpleLogger) Debug(msg string, args ...interface{}) {
	// Skip debug messages for simplicity
}

func (l *simpleLogger) Fatal(msg string, args ...interface{}) {
	l.Error(msg, args...)
	panic(msg)
}

func (l *simpleLogger) Named(name string) logger.Logger {
	return &simpleLogger{}
}
