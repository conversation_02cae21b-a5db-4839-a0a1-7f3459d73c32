package providers

import (
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
)

// NewJWTConfig provides JWT configuration from environment
func NewJWTConfig(cfg config.Config) auth.JWTConfig {
	return auth.JWTConfig{
		AccessSigningKey:       cfg.GetString("JWT_ACCESS_SIGNING_KEY"),
		RefreshSigningKey:      cfg.GetString("JWT_REFRESH_SIGNING_KEY"),
		AccessTokenExpiration:  cfg.GetDuration("JWT_ACCESS_TOKEN_EXPIRATION"),
		RefreshTokenExpiration: cfg.GetDuration("JWT_REFRESH_TOKEN_EXPIRATION"),
		Issuer:                 cfg.GetString("JWT_ISSUER"),
	}
}

// NewJWTService provides JWT service implementation
func NewJWTService(jwtConfig auth.JWTConfig) *auth.JWTService {
	return auth.NewJWTService(jwtConfig)
}

// AuthParams defines parameters for auth services
type AuthParams struct {
	Config config.Config
}

// NewJWTConfigWithParams creates JWT config with structured params
func NewJWTConfigWithParams(params AuthParams) auth.JWTConfig {
	return NewJWTConfig(params.Config)
}
