package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/tracing"
)

// NewTracingProvider provides tracing configuration
func NewTracingProvider(cfg config.Config) (*tracing.Provider, error) {
	tracingConfig := LoadTracingConfigFromEnv(cfg)
	return tracing.NewProvider(tracingConfig)
}

// LoadTracingConfigFromEnv loads tracing configuration from environment variables
func LoadTracingConfigFromEnv(cfg config.Config) *tracing.Config {
	return &tracing.Config{
		ServiceName:    cfg.GetStringWithDefault("TRACING_SERVICE_NAME", "wnapi"),
		ServiceVersion: cfg.GetStringWithDefault("TRACING_SERVICE_VERSION", "0.1.0"),
		Environment:    cfg.GetStringWithDefault("TRACING_ENVIRONMENT", "development"),
		Enabled:        cfg.GetBoolWithDefault("TRACING_ENABLED", true),
		ExporterType:   cfg.GetStringWithDefault("TRACING_EXPORTER_TYPE", "console"),
		OTLP: tracing.OTLPConfig{
			Endpoint: cfg.GetStringWithDefault("TRACING_OTLP_ENDPOINT", "http://localhost:4317"),
			Insecure: cfg.GetBoolWithDefault("TRACING_OTLP_INSECURE", true),
			Headers:  make(map[string]string),
			Timeout:  cfg.GetDurationWithDefault("TRACING_OTLP_TIMEOUT", 10),
		},
		Jaeger: tracing.JaegerConfig{
			Endpoint: cfg.GetStringWithDefault("TRACING_JAEGER_ENDPOINT", "http://localhost:14268/api/traces"),
			Username: cfg.GetString("TRACING_JAEGER_USERNAME"),
			Password: cfg.GetString("TRACING_JAEGER_PASSWORD"),
		},
		Sampling: tracing.SamplingConfig{
			Type:  cfg.GetStringWithDefault("TRACING_SAMPLING_TYPE", "ratio"),
			Ratio: cfg.GetFloat64WithDefault("TRACING_SAMPLING_RATIO", 0.1),
		},
	}
}

// TracingParams defines parameters for tracing
type TracingParams struct {
	Config config.Config
}

// NewTracingProviderWithParams creates tracing provider with structured params
func NewTracingProviderWithParams(params TracingParams) (*tracing.Provider, error) {
	return NewTracingProvider(params.Config)
}
