package providers

import (
	"time"
	
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"log"
	"os"
	
	"wnapi/internal/database"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// NewDBManager provides *database.Manager (sqlx-based)
func NewDBManager(cfg config.Config, log logger.Logger) (*database.Manager, error) {
	dbConfig := database.Config{
		Type:            cfg.GetString("DB_TYPE"),
		Host:            cfg.GetString("DB_HOST"),
		Port:            cfg.GetInt("DB_PORT"),
		Username:        cfg.GetString("DB_USERNAME"),
		Password:        cfg.GetString("DB_PASSWORD"),
		Database:        cfg.GetString("DB_DATABASE"),
		MaxOpenConns:    cfg.GetIntWithDefault("DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:    cfg.GetIntWithDefault("DB_MAX_IDLE_CONNS", 5),
		ConnMaxLifetime: cfg.GetDurationWithDefault("DB_CONN_MAX_LIFETIME", 5*time.Minute),
	}
	return database.NewManager(dbConfig, log)
}

// NewGormDB provides *gorm.DB using existing database connection
func NewGormDB(dbManager *database.Manager) (*gorm.DB, error) {
	sqlDB := dbManager.GetDB()
	
	// Cấu hình GORM logger để hiển thị SQL queries
	newLogger := gormlogger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		gormlogger.Config{
			SlowThreshold:             time.Second, // Slow SQL threshold
			LogLevel:                  gormlogger.Info, // Log level (Silent, Error, Warn, Info) - Info hiển thị tất cả SQL queries
			IgnoreRecordNotFoundError: false,      // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      false,      // false = hiển thị cả tham số trong SQL log
			Colorful:                  true,       // Enable color
		},
	)
	
	return gorm.Open(mysql.New(mysql.Config{
		Conn: sqlDB.DB,
	}), &gorm.Config{
		Logger: newLogger,
	})
}

// NewSqlxDB provides *sqlx.DB from database manager
func NewSqlxDB(dbManager *database.Manager) *sqlx.DB {
	return dbManager.GetDB()
}

// DBManagerParams defines parameters for database manager
type DBManagerParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewDBManagerWithParams creates database manager with structured params
func NewDBManagerWithParams(params DBManagerParams) (*database.Manager, error) {
	return NewDBManager(params.Config, params.Logger)
}
