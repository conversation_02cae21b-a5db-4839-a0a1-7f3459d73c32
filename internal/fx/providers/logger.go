package providers

import (
	"strings"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// NewLogger provides logger.Logger
func NewLogger(cfg config.Config) (logger.Logger, error) {
	// Parse log level
	logLevelStr := cfg.GetStringWithDefault("LOG_LEVEL", "debug")
	var logLevel logger.Level

	switch strings.ToLower(logLevelStr) {
	case "debug":
		logLevel = logger.LevelDebug
	case "info":
		logLevel = logger.LevelInfo
	case "warn", "warning":
		logLevel = logger.LevelWarn
	case "error":
		logLevel = logger.LevelError
	case "fatal":
		logLevel = logger.LevelFatal
	default:
		logLevel = logger.LevelDebug
	}

	// Create console logger
	consoleLogger := logger.NewConsoleLogger("wnapi", logLevel)
	return consoleLogger, nil
}
