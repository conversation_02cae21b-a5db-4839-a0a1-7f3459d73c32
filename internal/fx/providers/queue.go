package providers

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/logger/adapter"
	"wnapi/internal/pkg/queue"
	"wnapi/internal/pkg/queue/asynq"
)

// QueueParams defines parameters for queue system creation
type QueueParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewQueueConfig creates queue configuration from app config
func NewQueueConfig(cfg config.Config) *queue.Config {
	return &queue.Config{
		Backend: "asynq",
		Redis: queue.RedisConfig{
			Addr:      cfg.GetString("QUEUE_REDIS_ADDR"),
			Password:  cfg.GetString("QUEUE_REDIS_PASSWORD"),
			DB:        cfg.GetInt("QUEUE_REDIS_DB"),
			KeyPrefix: "wnapi:",
		},
		Worker: queue.WorkerConfig{
			Concurrency:     cfg.GetInt("QUEUE_WORKER_CONCURRENCY"),
			ShutdownTimeout: 30 * time.Second,
			Queues: map[string]queue.QueueConfig{
				"emails":    {Name: "emails", Priority: 6, Enabled: true, MaxRetry: 3},
				"sms":       {Name: "sms", Priority: 5, Enabled: true, MaxRetry: 3},
				"push":      {Name: "push", Priority: 5, Enabled: true, MaxRetry: 3},
				"scheduled": {Name: "scheduled", Priority: 3, Enabled: true, MaxRetry: 3},
				"test":      {Name: "test", Priority: 2, Enabled: true, MaxRetry: 3},
				"default":   {Name: "default", Priority: 1, Enabled: true, MaxRetry: 3},
			},
		},
		DefaultQueue: queue.QueueConfig{
			Name:     "default",
			Priority: 10,
			MaxRetry: 3,
		},
		Scheduler: queue.SchedulerConfig{
			Enabled:       true,
			CheckInterval: 1 * time.Minute,
			TimeZone:      "Asia/Ho_Chi_Minh",
		},
	}
}

// NewQueueFactory creates queue factory
func NewQueueFactory() queue.QueueFactory {
	return asynq.NewAsynqFactory()
}

// NewQueueLogger creates queue logger adapter
func NewQueueLogger(logger logger.Logger) queue.Logger {
	return adapter.NewQueueLogger(logger)
}

// NewQueueManager creates queue manager with all components
func NewQueueManager(
	config *queue.Config,
	logger queue.Logger,
	factory queue.QueueFactory,
) (*queue.QueueManager, error) {
	return queue.CreateQueueManager(config, logger, factory)
}

// NewQueueClient creates queue client
func NewQueueClient(manager *queue.QueueManager) queue.QueueClient {
	return manager.GetClient()
}

// NewQueueWorker creates queue worker server
func NewQueueWorker(manager *queue.QueueManager) queue.WorkerServer {
	return manager.GetWorker()
}

// NewQueueInspector creates queue inspector
func NewQueueInspector(manager *queue.QueueManager) queue.QueueInspector {
	return manager.GetInspector()
}

// NewQueueScheduler creates queue scheduler
func NewQueueScheduler(manager *queue.QueueManager) queue.Scheduler {
	return manager.GetScheduler()
}

// QueueManagerParams defines structured parameters for queue manager
type QueueManagerParams struct {
	Config  *queue.Config
	Logger  queue.Logger
	Factory queue.QueueFactory
}

// NewQueueManagerWithParams creates queue manager with structured params
func NewQueueManagerWithParams(params QueueManagerParams) (*queue.QueueManager, error) {
	return NewQueueManager(params.Config, params.Logger, params.Factory)
}

// QueueConfigParams defines parameters for queue config creation
type QueueConfigParams struct {
	Config config.Config
}

// NewQueueConfigWithParams creates queue config with structured params
func NewQueueConfigWithParams(params QueueConfigParams) *queue.Config {
	return NewQueueConfig(params.Config)
}

// QueueLoggerParams defines parameters for queue logger creation
type QueueLoggerParams struct {
	Logger logger.Logger
}

// NewQueueLoggerWithParams creates queue logger with structured params
func NewQueueLoggerWithParams(params QueueLoggerParams) queue.Logger {
	return NewQueueLogger(params.Logger)
}

// SetupGlobalQueueManager sets up the global queue manager
func SetupGlobalQueueManager(manager *queue.QueueManager) {
	queue.SetGlobalManager(manager)
}

// StartQueueWorker starts the queue worker if enabled
func StartQueueWorker(
	manager *queue.QueueManager,
	cfg config.Config,
	log logger.Logger,
) error {
	// Check if queue is enabled (automatically disabled if CLI mode)
	cliMode := cfg.GetBoolWithDefault("CLI_MODE", false)
	queueEnabled := cfg.GetBool("QUEUE_ENABLED") && !cliMode

	if !queueEnabled {
		if cliMode {
			log.Info("Queue system disabled due to CLI mode")
		} else {
			log.Info("Queue system not enabled")
		}
		return nil
	}

	// Start worker if configured
	startWorker := cfg.GetBool("QUEUE_START_WORKER")
	if startWorker {
		ctx := context.Background()
		if err := manager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start queue manager: %w", err)
		}
		log.Info("Queue worker started successfully")
	}

	return nil
}

// QueueStarterParams defines parameters for queue starter
type QueueStarterParams struct {
	Manager *queue.QueueManager
	Config  config.Config
	Logger  logger.Logger
}

// StartQueueWorkerWithParams starts queue worker with structured params
func StartQueueWorkerWithParams(params QueueStarterParams) error {
	return StartQueueWorker(params.Manager, params.Config, params.Logger)
}
