package providers

import (
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	rbacSvc "wnapi/modules/rbac/service"
)

// NewPermissionFactory provides permission middleware factory
// Replaces internal/bootstrap/rbac_bootstrap.go logic for FX architecture
func NewPermissionFactory(
	dbManager *database.Manager,
	cache cache.Cache,
	log logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	log.Info("Initializing RBAC components via FX provider")

	// Create PermissionCheckerService as PermissionChecker
	// This is an adapter that fits the permission.PermissionChecker interface
	permissionChecker := rbacSvc.NewPermissionCheckerService(log)

	// Create CachedPermissionChecker
	permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)
	cachedPermChecker := permission.NewCachedPermissionChecker(
		permissionChecker,
		cache,
		permissionCacheTTL,
		log,
	)

	// Initialize MiddlewareFactory
	middlewareFactory := permission.NewMiddlewareFactory(
		cachedPermChecker,
		log,
	)

	log.Info("RBAC Permission system initialized via FX")
	return middlewareFactory, nil
}

// PermissionParams defines parameters for permission factory
type PermissionParams struct {
	DBManager *database.Manager
	Cache     cache.Cache
	Logger    logger.Logger
	Config    config.Config
}

// NewPermissionFactoryWithParams creates permission factory with structured params
func NewPermissionFactoryWithParams(params PermissionParams) (*permission.MiddlewareFactory, error) {
	return NewPermissionFactory(params.DBManager, params.Cache, params.Logger, params.Config)
}
