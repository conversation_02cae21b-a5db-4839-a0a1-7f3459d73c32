package providers

import (
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// NewGinEngine provides configured Gin engine
func NewGinEngine(cfg config.Config, log logger.Logger) *gin.Engine {
	// Set Gin mode based on environment
	appEnv := cfg.GetString("APP_ENV")
	if appEnv == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// Create engine without default middleware
	engine := gin.New()

	// Disable automatic trailing slash redirects
	engine.RedirectTrailingSlash = false

	// Add custom middleware
	engine.Use(middleware.Logger(log))
	engine.Use(middleware.Recovery(log))

	return engine
}

// ServerParams defines parameters for server creation
type ServerParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewGinEngineWithParams creates Gin engine with structured params
func NewGinEngineWithParams(params ServerParams) *gin.Engine {
	return NewGinEngine(params.Config, params.Logger)
}
