package providers

import (
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// NewAppCache provides cache.Cache implementation
func NewAppCache(cfg config.Config, log logger.Logger) cache.Cache {
	cacheType := cfg.GetStringWithDefault("CACHE_TYPE", "memory")

	switch cacheType {
	case "memory":
		return memorycache.NewMemoryCache()
	case "redis":
		// FUTURE: Redis cache provider can be implemented here
		// For now, using memory cache as fallback
		log.Warn("Redis cache not implemented, falling back to memory cache")
		return memorycache.NewMemoryCache()
	default:
		log.Warn("Unknown cache type, using memory cache", "type", cacheType)
		return memorycache.NewMemoryCache()
	}
}

// CacheParams defines parameters for cache creation
type CacheParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewAppCacheWithParams creates cache with structured params
func NewAppCacheWithParams(params CacheParams) cache.Cache {
	return NewAppCache(params.Config, params.Logger)
}
