package lifecycle

import (
	"context"
	"net/http"
	"time"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// RegisterServerHooks registers HTTP server lifecycle hooks
func RegisterServerHooks(
	lc fx.Lifecycle,
	engine *gin.Engine,
	cfg config.Config,
	log logger.Logger,
) {
	serverAddr := cfg.GetStringWithDefault("SERVER_ADDR", ":8080")
	srv := &http.Server{
		Addr:    serverAddr,
		Handler: engine,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.Info("Starting HTTP server", "addr", serverAddr)
			go func() {
				if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
					log.Fatal("Failed to run server", "error", err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			log.Info("Stopping HTTP server")
			shutdownTimeout := cfg.GetDurationWithDefault("SERVER_SHUTDOWN_TIMEOUT", 10*time.Second)
			shutdownCtx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
			defer cancel()
			return srv.Shutdown(shutdownCtx)
		},
	})
}
