package lifecycle

import (
	"context"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"

	"go.uber.org/fx"
)

// RegisterQueueHooks registers queue system lifecycle hooks
func RegisterQueueHooks(
	lc fx.Lifecycle,
	manager *queue.QueueManager,
	cfg config.Config,
	log logger.Logger,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// Check if queue is enabled (automatically disabled if CLI mode)
			cliMode := cfg.GetBoolWithDefault("CLI_MODE", false)
			queueEnabled := cfg.GetBool("QUEUE_ENABLED") && !cliMode

			if !queueEnabled {
				if cliMode {
					log.Info("Queue system disabled due to CLI mode")
				} else {
					log.Info("Queue system not enabled")
				}
				return nil
			}

			// Set global manager
			queue.SetGlobalManager(manager)
			log.Info("Global queue manager set successfully")

			// Start worker if configured
			startWorker := cfg.GetBool("QUEUE_START_WORKER")
			if startWorker {
				if err := manager.Start(ctx); err != nil {
					log.Error("Failed to start queue manager", "error", err)
					return err
				}
				log.Info("Queue worker started successfully")
			}

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// Check if queue is enabled
			cliMode := cfg.GetBoolWithDefault("CLI_MODE", false)
			queueEnabled := cfg.GetBool("QUEUE_ENABLED") && !cliMode

			if !queueEnabled {
				return nil
			}

			// Stop the queue manager gracefully
			if err := manager.Stop(); err != nil {
				log.Error("Error stopping queue manager", "error", err)
				return err
			}

			log.Info("Queue manager stopped successfully")
			return nil
		},
	})
}

// QueueHookParams defines parameters for queue hooks
type QueueHookParams struct {
	Lifecycle fx.Lifecycle
	Manager   *queue.QueueManager
	Config    config.Config
	Logger    logger.Logger
}

// RegisterQueueHooksWithParams registers queue hooks with structured params
func RegisterQueueHooksWithParams(params QueueHookParams) {
	RegisterQueueHooks(params.Lifecycle, params.Manager, params.Config, params.Logger)
}
