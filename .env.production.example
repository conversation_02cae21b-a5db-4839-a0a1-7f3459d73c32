# Production Environment Configuration for WNAPI
# Copy this file to .env.production và update các values theo environment của bạn

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MYSQL_ROOT_PASSWORD=your_secure_root_password_here
MYSQL_DATABASE=wnapi_prod
MYSQL_USER=wnapi
MYSQL_PASSWORD=your_secure_db_password_here

DB_HOST=mysql
DB_PORT=3306
DB_NAME=wnapi_prod
DB_USER=wnapi
DB_PASSWORD=your_secure_db_password_here
DB_MAX_CONNECTIONS=25
DB_MAX_IDLE_CONNECTIONS=5
DB_CONNECTION_LIFETIME=5m

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10
REDIS_MAX_IDLE_CONNECTIONS=5

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_ENV=production
APP_NAME=WNAPI
APP_VERSION=1.0.0
APP_DEBUG=false
APP_PORT=8080

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=/app/logs/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=10
LOG_MAX_AGE=30

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your_jwt_secret_key_minimum_32_characters_here
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_ISSUER=wnapi

# =============================================================================
# CRON SYSTEM CONFIGURATION
# =============================================================================
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh
CRON_LOG_LEVEL=info
CRON_WORKER_CONCURRENCY=5

# =============================================================================
# SYSTEM CRON JOBS
# =============================================================================
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="0 2 * * *"
CRON_SYSTEM_CLEANUP_RETENTION_DAYS=30
CRON_SYSTEM_CLEANUP_DRY_RUN=false

CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/5 * * * *"
CRON_SYSTEM_HEALTH_CHECK_TIMEOUT=30s

CRON_SYSTEM_BACKUP_ENABLED=true
CRON_SYSTEM_BACKUP_SCHEDULE="0 3 * * 0"
CRON_SYSTEM_BACKUP_S3_ENABLED=false
CRON_SYSTEM_BACKUP_S3_BUCKET=wnapi-backups
CRON_SYSTEM_BACKUP_S3_REGION=ap-southeast-1
CRON_SYSTEM_BACKUP_RETENTION_DAYS=30

# =============================================================================
# AUTH MODULE CRON JOBS
# =============================================================================
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=168h
CRON_AUTH_SESSION_CLEANUP_BATCH_SIZE=1000

CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="14,7,3,1"
CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID=password_expiry
CRON_AUTH_PASSWORD_EXPIRY_MAX_AGE=90d

# =============================================================================
# MEDIA MODULE CRON JOBS
# =============================================================================
CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=true
CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY=85
CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=50
CRON_MEDIA_IMAGE_OPTIMIZATION_MAX_SIZE=2MB

CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 1 * * *"
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=48h
CRON_MEDIA_TEMP_CLEANUP_PATH=/app/uploads/temp

CRON_MEDIA_ORPHAN_CLEANUP_ENABLED=true
CRON_MEDIA_ORPHAN_CLEANUP_SCHEDULE="0 4 * * 0"
CRON_MEDIA_ORPHAN_CLEANUP_GRACE_PERIOD=7d

# =============================================================================
# BLOG MODULE CRON JOBS
# =============================================================================
CRON_BLOG_AUTO_PUBLISH_ENABLED=true
CRON_BLOG_AUTO_PUBLISH_SCHEDULE="*/10 * * * *"
CRON_BLOG_AUTO_PUBLISH_BATCH_SIZE=10

CRON_BLOG_SITEMAP_UPDATE_ENABLED=true
CRON_BLOG_SITEMAP_UPDATE_SCHEDULE="0 4 * * *"
CRON_BLOG_SITEMAP_MAX_URLS=50000

CRON_BLOG_SEO_ANALYSIS_ENABLED=false
CRON_BLOG_SEO_ANALYSIS_SCHEDULE="0 5 * * 0"
CRON_BLOG_SEO_ANALYSIS_BATCH_SIZE=20

# =============================================================================
# NOTIFICATION MODULE CRON JOBS
# =============================================================================
CRON_NOTIFICATION_DIGEST_ENABLED=true
CRON_NOTIFICATION_DIGEST_SCHEDULE="0 8 * * 1"
CRON_NOTIFICATION_DIGEST_TEMPLATE_ID=weekly_digest

CRON_NOTIFICATION_CLEANUP_ENABLED=true
CRON_NOTIFICATION_CLEANUP_SCHEDULE="0 5 * * 0"
CRON_NOTIFICATION_CLEANUP_RETENTION_DAYS=90
CRON_NOTIFICATION_CLEANUP_BATCH_SIZE=1000

CRON_NOTIFICATION_RETRY_FAILED_ENABLED=true
CRON_NOTIFICATION_RETRY_FAILED_SCHEDULE="*/30 * * * *"
CRON_NOTIFICATION_RETRY_MAX_ATTEMPTS=3

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_PROVIDER=smtp
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=your_app_password
EMAIL_FROM_NAME=WNAPI System
EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# SMS CONFIGURATION
# =============================================================================
SMS_PROVIDER=twilio
SMS_TWILIO_ACCOUNT_SID=your_twilio_account_sid
SMS_TWILIO_AUTH_TOKEN=your_twilio_auth_token
SMS_TWILIO_FROM_NUMBER=+**********

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
STORAGE_PROVIDER=local
STORAGE_LOCAL_PATH=/app/uploads
STORAGE_S3_BUCKET=wnapi-uploads
STORAGE_S3_REGION=ap-southeast-1
STORAGE_S3_ACCESS_KEY=your_s3_access_key
STORAGE_S3_SECRET_KEY=your_s3_secret_key

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
MONITORING_ENABLED=true
METRICS_ENABLED=true
METRICS_PATH=/metrics
METRICS_PORT=9090

SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Tenant-ID
CORS_MAX_AGE=86400

RATE_LIMIT_ENABLED=true
RATE_LIMIT_RPS=100
RATE_LIMIT_BURST=200

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
TLS_ENABLED=false
TLS_CERT_FILE=/etc/ssl/certs/server.crt
TLS_KEY_FILE=/etc/ssl/private/server.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_S3_BUCKET=wnapi-backups
BACKUP_S3_REGION=ap-southeast-1
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key_here
BACKUP_RETENTION_DAYS=30

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# AI Services
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Payment Services
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Social Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
