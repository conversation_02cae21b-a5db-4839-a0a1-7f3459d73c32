# Crawl Module

Module crawl cho hệ thống WebNews API, sử dụng thư viện Colly để thu thập dữ liệu từ các trang web.

## Tính năng

- ✅ Thu thập dữ liệu từ các trang web sử dụng Colly
- ✅ Quản lý công việc crawl (CRUD operations)
- ✅ Quản lý bài viết đã crawl
- ✅ Hệ thống queue để xử lý công việc crawl
- ✅ Cron scheduler cho việc crawl định kỳ
- ✅ API endpoints đầy đủ
- ✅ Cấu hình linh hoạt
- ✅ Logging và monitoring
- ✅ Retry mechanism
- ✅ Duplicate detection

## Cấu trúc thư mục

```
crawl/
├── Makefile                    # Build và test commands
├── README.md                   # Tài liệu này
├── fx.go                       # Module definition cho Fx
├── providers.go                # Dependency injection providers
├── routes.go                   # Route registration
├── cron/
│   └── crawl_scheduler.go      # Cron scheduler cho crawl jobs
├── dto/
│   ├── request/                # Request DTOs
│   └── response/               # Response DTOs
├── handlers/
│   └── crawl_handler.go        # HTTP handlers
├── internal/
│   ├── config.go               # Cấu hình module
│   ├── permission.go           # Định nghĩa permissions
│   └── types.go                # Các kiểu dữ liệu internal
├── migrations/
│   └── 001_create_crawl_tables.sql # Database migrations
├── models/
│   ├── crawl_article.go        # Model cho crawl articles
│   └── crawl_job.go            # Model cho crawl jobs
├── queue/
│   └── crawl_worker.go         # Queue worker cho crawl jobs
├── repository/
│   ├── interfaces.go           # Repository interfaces
│   └── mysql/                  # MySQL implementations
├── └── services/
    ├── crawl_article_service.go # Service cho crawl articles
    ├── crawl_job_service.go     # Service cho crawl jobs
    └── crawl_service.go         # Main crawl service
```

## API Endpoints

### Crawl Jobs

- `POST /api/v1/crawl/jobs` - Tạo crawl job mới
- `GET /api/v1/crawl/jobs` - Liệt kê crawl jobs
- `GET /api/v1/crawl/jobs/:id` - Lấy thông tin crawl job
- `PUT /api/v1/crawl/jobs/:id` - Cập nhật crawl job
- `DELETE /api/v1/crawl/jobs/:id` - Xóa crawl job
- `POST /api/v1/crawl/jobs/:id/start` - Bắt đầu crawl job
- `POST /api/v1/crawl/jobs/:id/stop` - Dừng crawl job
- `GET /api/v1/crawl/jobs/:id/progress` - Lấy tiến độ crawl job
- `POST /api/v1/crawl/jobs/test` - Test cấu hình crawl

### Crawl Articles

- `GET /api/v1/crawl/articles` - Liệt kê bài viết đã crawl
- `GET /api/v1/crawl/articles/search` - Tìm kiếm bài viết
- `GET /api/v1/crawl/articles/:id` - Lấy thông tin bài viết
- `PUT /api/v1/crawl/articles/:id` - Cập nhật bài viết
- `DELETE /api/v1/crawl/articles/:id` - Xóa bài viết

### Statistics

- `GET /api/v1/crawl/stats` - Lấy thống kê crawl

## Cách sử dụng

### 1. Build module

```bash
cd modules/crawl
make build
```

### 2. Kiểm tra syntax

```bash
make check-syntax
```

### 3. Chạy tests

```bash
make test
```

### 4. Format code

```bash
make fmt
```

### 5. Chạy tất cả checks

```bash
make all
```

## Cấu hình

Module crawl có thể được cấu hình thông qua các biến môi trường hoặc config file:

```yaml
crawl:
  user_agent: "WebNews Crawler 1.0"
  delay: 1000  # milliseconds
  timeout: 30  # seconds
  max_retries: 3
  concurrency: 2
  respect_robots: true
  follow_redirects: true
  max_depth: 5
  queue:
    name: "crawl_jobs"
    worker_count: 5
  storage:
    enabled: true
    path: "/tmp/crawl_storage"
```

## Tạo Crawl Job

```json
{
  "name": "Example News Site",
  "description": "Crawl news from example.com",
  "start_url": "https://example.com/news",
  "type": "news",
  "rules": {
    "allowed_domains": ["example.com"],
    "disallowed_paths": ["/admin", "/login"],
    "selectors": {
      "title": "h1.title",
      "content": ".article-content",
      "author": ".author",
      "image": ".featured-image img",
      "summary": ".excerpt"
    },
    "max_depth": 3,
    "delay": 2000,
    "respect_robots": true
  },
  "schedule": "0 */6 * * *",  // Chạy mỗi 6 giờ
  "active": true
}
```

## Scheduled Crawling

Module hỗ trợ crawl theo lịch trình sử dụng cron expressions:

- `"0 0 * * *"` - Chạy hàng ngày lúc 00:00
- `"0 */6 * * *"` - Chạy mỗi 6 giờ
- `"0 0 * * 1"` - Chạy mỗi thứ 2 hàng tuần
- `"@hourly"` - Chạy mỗi giờ
- `"@daily"` - Chạy hàng ngày

## Queue System

Module sử dụng hệ thống queue để xử lý các crawl jobs:

- Jobs được thêm vào queue khi được tạo hoặc scheduled
- Workers xử lý jobs từ queue
- Hỗ trợ retry với exponential backoff
- Priority-based processing

## Database Schema

Module tạo các bảng sau:

- `crawl_jobs` - Lưu trữ thông tin crawl jobs
- `crawl_articles` - Lưu trữ bài viết đã crawl
- `crawl_queue` - Queue cho crawl jobs
- `crawl_logs` - Logs của quá trình crawl
- `crawl_settings` - Cấu hình crawl

## Dependencies

Module sử dụng các thư viện sau:

- `github.com/gocolly/colly/v2` - Web scraping framework
- `github.com/robfig/cron/v3` - Cron scheduler
- `github.com/gin-gonic/gin` - HTTP framework
- `gorm.io/gorm` - ORM
- `go.uber.org/zap` - Logging
- `go.uber.org/fx` - Dependency injection

## Troubleshooting

### Build errors

1. Đảm bảo tất cả dependencies đã được cài đặt:
   ```bash
   make deps
   ```

2. Kiểm tra syntax:
   ```bash
   make check-syntax
   ```

3. Format code:
   ```bash
   make fmt
   make imports
   ```

### Runtime errors

1. Kiểm tra logs để xem chi tiết lỗi
2. Đảm bảo database đã được migrate
3. Kiểm tra cấu hình crawl
4. Kiểm tra network connectivity

## Development

### Thêm tính năng mới

1. Tạo tests trước
2. Implement tính năng
3. Chạy all checks:
   ```bash
   make all
   ```

### Code style

- Sử dụng `make fmt` để format code
- Sử dụng `make imports` để fix imports
- Sử dụng `make vet` để check code quality
- Sử dụng `make lint` để run linter

## Contributing

1. Fork repository
2. Tạo feature branch
3. Implement changes
4. Chạy tests và checks
5. Submit pull request

## License

MIT License