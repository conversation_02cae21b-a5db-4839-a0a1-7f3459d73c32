-- Migration: 001_create_crawl_tables.sql
-- Description: Create tables for crawl module
-- Created: 2024-01-01

-- Create crawl_jobs table
CREATE TABLE IF NOT EXISTS crawl_jobs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL,
    website_id BIGINT UNSIGNED NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT NULL,
    start_url VARCHAR(2048) NOT NULL,
    type ENUM('news', 'blog', 'ecommerce', 'general') NOT NULL DEFAULT 'general',
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled', 'paused') NOT NULL DEFAULT 'pending',
    rules JSON NULL,
    progress JSON NULL,
    schedule VARCHAR(255) NULL COMMENT 'Cron expression for scheduled crawling',
    active BOOLEAN NOT NULL DEFAULT true,
    last_run_at TIMESTAMP NULL,
    next_run_at TIMESTAMP NULL,
    error_msg TEXT NULL,
    retry_count INT UNSIGNED NOT NULL DEFAULT 0,
    max_retries INT UNSIGNED NOT NULL DEFAULT 3,
    created_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_crawl_jobs_tenant_id (tenant_id),
    INDEX idx_crawl_jobs_website_id (website_id),
    INDEX idx_crawl_jobs_status (status),
    INDEX idx_crawl_jobs_type (type),
    INDEX idx_crawl_jobs_active (active),
    INDEX idx_crawl_jobs_next_run_at (next_run_at),
    INDEX idx_crawl_jobs_created_at (created_at),
    INDEX idx_crawl_jobs_tenant_status (tenant_id, status),
    INDEX idx_crawl_jobs_tenant_active (tenant_id, active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create crawl_articles table
CREATE TABLE IF NOT EXISTS crawl_articles (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL,
    website_id BIGINT UNSIGNED NULL,
    crawl_job_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) NOT NULL,
    content LONGTEXT NOT NULL,
    summary TEXT NULL,
    url VARCHAR(2048) NOT NULL,
    image_url VARCHAR(2048) NULL,
    author VARCHAR(255) NULL,
    published_at TIMESTAMP NULL,
    status ENUM('pending', 'processed', 'failed', 'duplicate') NOT NULL DEFAULT 'pending',
    metadata JSON NULL,
    tags JSON NULL,
    categories JSON NULL,
    word_count INT UNSIGNED NOT NULL DEFAULT 0,
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    hash_content VARCHAR(64) NOT NULL COMMENT 'SHA256 hash of content for duplicate detection',
    error_msg TEXT NULL,
    retry_count INT UNSIGNED NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_crawl_articles_tenant_id (tenant_id),
    INDEX idx_crawl_articles_website_id (website_id),
    INDEX idx_crawl_articles_crawl_job_id (crawl_job_id),
    INDEX idx_crawl_articles_status (status),
    INDEX idx_crawl_articles_slug (slug),
    INDEX idx_crawl_articles_url (url(255)),
    INDEX idx_crawl_articles_hash_content (hash_content),
    INDEX idx_crawl_articles_published_at (published_at),
    INDEX idx_crawl_articles_created_at (created_at),
    INDEX idx_crawl_articles_tenant_status (tenant_id, status),
    INDEX idx_crawl_articles_tenant_job (tenant_id, crawl_job_id),
    INDEX idx_crawl_articles_language (language),
    INDEX idx_crawl_articles_word_count (word_count),
    
    UNIQUE KEY uk_crawl_articles_tenant_hash (tenant_id, hash_content),
    UNIQUE KEY uk_crawl_articles_tenant_url (tenant_id, url(255)),
    
    FOREIGN KEY (crawl_job_id) REFERENCES crawl_jobs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create crawl_queue table for job queue management
CREATE TABLE IF NOT EXISTS crawl_queue (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL,
    crawl_job_id BIGINT UNSIGNED NOT NULL,
    url VARCHAR(2048) NOT NULL,
    priority INT NOT NULL DEFAULT 0,
    status ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    retry_count INT UNSIGNED NOT NULL DEFAULT 0,
    max_retries INT UNSIGNED NOT NULL DEFAULT 3,
    error_msg TEXT NULL,
    scheduled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_crawl_queue_tenant_id (tenant_id),
    INDEX idx_crawl_queue_crawl_job_id (crawl_job_id),
    INDEX idx_crawl_queue_status (status),
    INDEX idx_crawl_queue_priority (priority),
    INDEX idx_crawl_queue_scheduled_at (scheduled_at),
    INDEX idx_crawl_queue_tenant_status (tenant_id, status),
    INDEX idx_crawl_queue_job_status (crawl_job_id, status),
    
    UNIQUE KEY uk_crawl_queue_job_url (crawl_job_id, url(255)),
    
    FOREIGN KEY (crawl_job_id) REFERENCES crawl_jobs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create crawl_logs table for logging crawl activities
CREATE TABLE IF NOT EXISTS crawl_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL,
    crawl_job_id BIGINT UNSIGNED NOT NULL,
    level ENUM('debug', 'info', 'warning', 'error') NOT NULL DEFAULT 'info',
    message TEXT NOT NULL,
    context JSON NULL,
    url VARCHAR(2048) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_crawl_logs_tenant_id (tenant_id),
    INDEX idx_crawl_logs_crawl_job_id (crawl_job_id),
    INDEX idx_crawl_logs_level (level),
    INDEX idx_crawl_logs_created_at (created_at),
    INDEX idx_crawl_logs_tenant_job (tenant_id, crawl_job_id),
    
    FOREIGN KEY (crawl_job_id) REFERENCES crawl_jobs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create crawl_settings table for crawl configuration
CREATE TABLE IF NOT EXISTS crawl_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL,
    key_name VARCHAR(255) NOT NULL,
    value JSON NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_crawl_settings_tenant_id (tenant_id),
    INDEX idx_crawl_settings_key_name (key_name),
    
    UNIQUE KEY uk_crawl_settings_tenant_key (tenant_id, key_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default crawl settings
INSERT INTO crawl_settings (tenant_id, key_name, value, description) VALUES
(0, 'default_user_agent', '"WebNews Crawler 1.0"', 'Default User-Agent for crawling'),
(0, 'default_delay', '1000', 'Default delay between requests in milliseconds'),
(0, 'default_timeout', '30', 'Default request timeout in seconds'),
(0, 'default_max_retries', '3', 'Default maximum retry attempts'),
(0, 'default_concurrency', '2', 'Default number of concurrent requests'),
(0, 'default_max_depth', '5', 'Default maximum crawl depth'),
(0, 'respect_robots', 'true', 'Whether to respect robots.txt by default'),
(0, 'follow_redirects', 'true', 'Whether to follow redirects by default'),
(0, 'queue_enabled', 'true', 'Whether queue processing is enabled'),
(0, 'queue_worker_count', '5', 'Number of queue workers'),
(0, 'storage_enabled', 'true', 'Whether to enable file storage for crawled content'),
(0, 'storage_path', '"/tmp/crawl_storage"', 'Path for storing crawled files')
ON DUPLICATE KEY UPDATE
    value = VALUES(value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;