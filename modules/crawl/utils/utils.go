package utils

import (
	"fmt"
	"regexp"
	"strings"
)

// GenerateSlug converts a string to URL-friendly slug
func GenerateSlug(input string) string {
	// Convert to lowercase
	slug := strings.ToLower(input)

	// Replace special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}

// StripHTMLTags removes HTML tags from a string
func StripHTMLTags(input string) string {
	// Remove HTML tags
	reg := regexp.MustCompile(`<[^>]*>`)
	cleaned := reg.ReplaceAllString(input, "")

	// Normalize whitespace
	reg = regexp.MustCompile(`\s+`)
	cleaned = reg.ReplaceAllString(cleaned, " ")

	return strings.TrimSpace(cleaned)
}

// TruncateString truncates a string to a maximum length
func TruncateString(input string, maxLength int) string {
	if len(input) <= maxLength {
		return input
	}
	return input[:maxLength] + "..."
}

// GenerateHash generates a simple hash from a string
func GenerateHash(input string) string {
	// Simple hash function for demonstration
	// In production, you might want to use a more robust hashing algorithm
	hash := 0
	for _, char := range input {
		hash = hash*31 + int(char)
	}
	return fmt.Sprintf("%x", hash)
}