package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"wnapi/modules/crawl/internal"
)

// CrawlArticle represents a crawled article in the database
type CrawlArticle struct {
	ID          uint                        `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID    uint                        `json:"tenant_id" gorm:"not null;index:idx_tenant_status"`
	WebsiteID   uint                        `json:"website_id" gorm:"not null;index:idx_website_tenant"`
	CrawlJobID  uint                        `json:"crawl_job_id" gorm:"not null;index:idx_job_status"`
	Title       string                      `json:"title" gorm:"size:500;not null"`
	Slug        string                      `json:"slug" gorm:"size:500;index:idx_slug"`
	Content     string                      `json:"content" gorm:"type:longtext"`
	Summary     string                      `json:"summary" gorm:"type:text"`
	URL         string                      `json:"url" gorm:"size:500;not null;index:idx_url"`
	ImageURL    string                      `json:"image_url" gorm:"size:500"`
	Author      string                      `json:"author" gorm:"size:255"`
	PublishedAt *time.Time                  `json:"published_at"`
	Status      internal.CrawlArticleStatus `json:"status" gorm:"size:50;not null;default:'pending';index:idx_status"`
	Metadata    ArticleMetadataJSON         `json:"metadata" gorm:"type:json"`
	Tags        ArticleTagsJSON             `json:"tags" gorm:"type:json"`
	Categories  ArticleCategoriesJSON       `json:"categories" gorm:"type:json"`
	WordCount   int                         `json:"word_count" gorm:"default:0"`
	Language    string                      `json:"language" gorm:"size:10;default:'vi'"`
	HashContent string                      `json:"hash_content" gorm:"size:64;index:idx_hash"` // For duplicate detection
	ErrorMsg    string                      `json:"error_msg" gorm:"type:text"`
	RetryCount  int                         `json:"retry_count" gorm:"default:0"`
	CreatedAt   time.Time                   `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time                   `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	CrawlJob *CrawlJob `json:"crawl_job,omitempty" gorm:"foreignKey:CrawlJobID"`
}

// ArticleMetadata represents additional metadata for an article
type ArticleMetadata struct {
	Description   string            `json:"description"`
	Keywords      []string          `json:"keywords"`
	CanonicalURL  string            `json:"canonical_url"`
	OGTitle       string            `json:"og_title"`
	OGDescription string            `json:"og_description"`
	OGImage       string            `json:"og_image"`
	TwitterCard   string            `json:"twitter_card"`
	CustomFields  map[string]string `json:"custom_fields"`
	ReadingTime   int               `json:"reading_time"` // in minutes
	Difficulty    string            `json:"difficulty"`   // easy, medium, hard
}

// ArticleMetadataJSON is a wrapper for JSON marshaling/unmarshaling
type ArticleMetadataJSON ArticleMetadata

// Value implements the driver.Valuer interface for database storage
func (a ArticleMetadataJSON) Value() (driver.Value, error) {
	if a.Description == "" && len(a.Keywords) == 0 && len(a.CustomFields) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface for database retrieval
func (a *ArticleMetadataJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, a)
	case string:
		return json.Unmarshal([]byte(v), a)
	default:
		return fmt.Errorf("cannot scan %T into ArticleMetadataJSON", value)
	}
}

// ArticleTagsJSON is a wrapper for JSON marshaling/unmarshaling of tags
type ArticleTagsJSON []string

// Value implements the driver.Valuer interface for database storage
func (a ArticleTagsJSON) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface for database retrieval
func (a *ArticleTagsJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, a)
	case string:
		return json.Unmarshal([]byte(v), a)
	default:
		return fmt.Errorf("cannot scan %T into ArticleTagsJSON", value)
	}
}

// ArticleCategoriesJSON is a wrapper for JSON marshaling/unmarshaling of categories
type ArticleCategoriesJSON []string

// Value implements the driver.Valuer interface for database storage
func (a ArticleCategoriesJSON) Value() (driver.Value, error) {
	if len(a) == 0 {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan implements the sql.Scanner interface for database retrieval
func (a *ArticleCategoriesJSON) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, a)
	case string:
		return json.Unmarshal([]byte(v), a)
	default:
		return fmt.Errorf("cannot scan %T into ArticleCategoriesJSON", value)
	}
}

// TableName returns the table name for CrawlArticle
func (CrawlArticle) TableName() string {
	return "crawl_articles"
}

// IsProcessed checks if the article has been processed
func (c *CrawlArticle) IsProcessed() bool {
	return c.Status == internal.CrawlArticleStatusProcessed
}

// IsFailed checks if the article processing failed
func (c *CrawlArticle) IsFailed() bool {
	return c.Status == internal.CrawlArticleStatusFailed
}

// IsDuplicate checks if the article is a duplicate
func (c *CrawlArticle) IsDuplicate() bool {
	return c.Status == internal.CrawlArticleStatusDuplicate
}

// CanRetry checks if the article can be retried
func (c *CrawlArticle) CanRetry() bool {
	return c.Status == internal.CrawlArticleStatusFailed && c.RetryCount < 3
}

// GetReadingTime calculates estimated reading time in minutes
func (c *CrawlArticle) GetReadingTime() int {
	if c.WordCount == 0 {
		return 0
	}
	// Average reading speed: 200 words per minute
	return (c.WordCount + 199) / 200
}