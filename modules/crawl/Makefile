# Makefile for Crawl Module
# This Makefile allows building and testing the crawl module independently

.PHONY: help build test lint clean deps check-syntax vet fmt imports

# Default target
help:
	@echo "Available targets:"
	@echo "  build        - Build the crawl module"
	@echo "  test         - Run tests for the crawl module"
	@echo "  lint         - Run linter on the crawl module"
	@echo "  vet          - Run go vet on the crawl module"
	@echo "  fmt          - Format code using go fmt"
	@echo "  imports      - Fix imports using goimports"
	@echo "  check-syntax - Check syntax and compilation errors"
	@echo "  deps         - Download dependencies"
	@echo "  clean        - Clean build artifacts"
	@echo "  all          - Run all checks (fmt, imports, vet, lint, build, test)"

# Module information
MODULE_PATH := wnapi/modules/crawl
ROOT_DIR := $(shell pwd)/../..
MODULE_DIR := $(ROOT_DIR)/modules/crawl

# Go commands
GO := go
GOFMT := gofmt
GOIMPORTS := goimports
GOLINT := golangci-lint

# Build the crawl module
build:
	@echo "Building crawl module..."
	@cd $(ROOT_DIR) && $(GO) build -v ./modules/crawl/...
	@echo "✅ Build successful"

# Run tests
test:
	@echo "Running tests for crawl module..."
	@cd $(ROOT_DIR) && $(GO) test -v ./modules/crawl/...
	@echo "✅ Tests completed"

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage for crawl module..."
	@cd $(ROOT_DIR) && $(GO) test -v -coverprofile=coverage.out ./modules/crawl/...
	@cd $(ROOT_DIR) && $(GO) tool cover -html=coverage.out -o coverage.html
	@echo "✅ Coverage report generated: coverage.html"

# Check syntax and compilation errors
check-syntax:
	@echo "Checking syntax for crawl module..."
	@cd $(ROOT_DIR) && $(GO) build -o /dev/null ./modules/crawl/...
	@echo "✅ Syntax check passed"

# Run go vet
vet:
	@echo "Running go vet on crawl module..."
	@cd $(ROOT_DIR) && $(GO) vet ./modules/crawl/...
	@echo "✅ Vet check passed"

# Format code
fmt:
	@echo "Formatting crawl module code..."
	@find $(MODULE_DIR) -name "*.go" -exec $(GOFMT) -w {} \;
	@echo "✅ Code formatted"

# Fix imports
imports:
	@echo "Fixing imports for crawl module..."
	@if command -v $(GOIMPORTS) >/dev/null 2>&1; then \
		find $(MODULE_DIR) -name "*.go" -exec $(GOIMPORTS) -w {} \; ; \
		echo "✅ Imports fixed"; \
	else \
		echo "⚠️  goimports not found. Install with: go install golang.org/x/tools/cmd/goimports@latest"; \
	fi

# Run linter
lint:
	@echo "Running linter on crawl module..."
	@if command -v $(GOLINT) >/dev/null 2>&1; then \
		cd $(ROOT_DIR) && $(GOLINT) run ./modules/crawl/... ; \
		echo "✅ Lint check completed"; \
	else \
		echo "⚠️  golangci-lint not found. Install from: https://golangci-lint.run/usage/install/"; \
	fi

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	@cd $(ROOT_DIR) && $(GO) mod download
	@cd $(ROOT_DIR) && $(GO) mod tidy
	@echo "✅ Dependencies updated"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@cd $(ROOT_DIR) && $(GO) clean -cache
	@rm -f $(ROOT_DIR)/coverage.out $(ROOT_DIR)/coverage.html
	@echo "✅ Clean completed"

# Install development tools
install-tools:
	@echo "Installing development tools..."
	@$(GO) install golang.org/x/tools/cmd/goimports@latest
	@$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@echo "✅ Development tools installed"

# Run all checks
all: fmt imports vet check-syntax build test
	@echo "✅ All checks completed successfully"

# Quick check (without tests)
quick: fmt imports vet check-syntax
	@echo "✅ Quick checks completed"

# Development workflow
dev: deps fmt imports vet check-syntax
	@echo "✅ Development setup completed"

# CI workflow
ci: deps fmt imports vet lint build test
	@echo "✅ CI checks completed"

# Show module structure
structure:
	@echo "Crawl module structure:"
	@find $(MODULE_DIR) -type f -name "*.go" | sort

# Count lines of code
loc:
	@echo "Lines of code in crawl module:"
	@find $(MODULE_DIR) -name "*.go" -exec wc -l {} + | tail -1

# Show dependencies
show-deps:
	@echo "Dependencies for crawl module:"
	@cd $(ROOT_DIR) && $(GO) list -m all | grep -E "(colly|cron|zap|gin|gorm)"

# Benchmark tests
bench:
	@echo "Running benchmarks for crawl module..."
	@cd $(ROOT_DIR) && $(GO) test -bench=. -benchmem ./modules/crawl/...

# Profile CPU usage
profile-cpu:
	@echo "Running CPU profile for crawl module..."
	@cd $(ROOT_DIR) && $(GO) test -cpuprofile=cpu.prof -bench=. ./modules/crawl/...
	@echo "CPU profile saved to cpu.prof"

# Profile memory usage
profile-mem:
	@echo "Running memory profile for crawl module..."
	@cd $(ROOT_DIR) && $(GO) test -memprofile=mem.prof -bench=. ./modules/crawl/...
	@echo "Memory profile saved to mem.prof"

# Generate documentation
docs:
	@echo "Generating documentation for crawl module..."
	@cd $(ROOT_DIR) && $(GO) doc -all ./modules/crawl/...

# Check for security vulnerabilities
security:
	@echo "Checking for security vulnerabilities..."
	@if command -v gosec >/dev/null 2>&1; then \
		cd $(ROOT_DIR) && gosec ./modules/crawl/... ; \
		echo "✅ Security check completed"; \
	else \
		echo "⚠️  gosec not found. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Check for unused dependencies
unused-deps:
	@echo "Checking for unused dependencies..."
	@if command -v govulncheck >/dev/null 2>&1; then \
		cd $(ROOT_DIR) && govulncheck ./modules/crawl/... ; \
		echo "✅ Vulnerability check completed"; \
	else \
		echo "⚠️  govulncheck not found. Install with: go install golang.org/x/vuln/cmd/govulncheck@latest"; \
	fi

# Watch for changes and run tests
watch:
	@echo "Watching for changes in crawl module..."
	@if command -v fswatch >/dev/null 2>&1; then \
		fswatch -o $(MODULE_DIR) | xargs -n1 -I{} make quick ; \
	else \
		echo "⚠️  fswatch not found. Install with: brew install fswatch (macOS) or apt-get install inotify-tools (Linux)"; \
	fi

# Generate mock files
mocks:
	@echo "Generating mock files for crawl module..."
	@if command -v mockgen >/dev/null 2>&1; then \
		cd $(ROOT_DIR) && mockgen -source=modules/crawl/repository/interfaces.go -destination=modules/crawl/mocks/repository_mocks.go ; \
		echo "✅ Mock files generated"; \
	else \
		echo "⚠️  mockgen not found. Install with: go install github.com/golang/mock/mockgen@latest"; \
	fi

# Run specific test
test-func:
	@read -p "Enter test function name: " func; \
	cd $(ROOT_DIR) && $(GO) test -v -run $$func ./modules/crawl/...

# Build for different platforms
build-all:
	@echo "Building crawl module for multiple platforms..."
	@cd $(ROOT_DIR) && GOOS=linux GOARCH=amd64 $(GO) build -o build/crawl-linux-amd64 ./modules/crawl/...
	@cd $(ROOT_DIR) && GOOS=darwin GOARCH=amd64 $(GO) build -o build/crawl-darwin-amd64 ./modules/crawl/...
	@cd $(ROOT_DIR) && GOOS=windows GOARCH=amd64 $(GO) build -o build/crawl-windows-amd64.exe ./modules/crawl/...
	@echo "✅ Multi-platform build completed"