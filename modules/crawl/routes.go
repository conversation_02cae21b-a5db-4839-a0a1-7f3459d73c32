package crawl

import (
	"github.com/gin-gonic/gin"

	"wnapi/modules/crawl/handlers"
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
)

// RegisterCrawlRoutes registers crawl routes
func RegisterCrawlRoutes(r *gin.Engine, handler *handlers.CrawlHandler, jwtService *auth.JWTService, tenantService middleware.TenantService) {
	// API v1 group
	v1 := r.Group("/api/v1")
	v1.Use(jwtService.JWTAuthMiddleware())
	v1.Use(middleware.TenantMiddleware(tenantService))

	// Crawl jobs routes
	crawlJobs := v1.Group("/crawl/jobs")
	{
		// CRUD operations
		crawlJobs.POST("", handler.CreateCrawlJob)                    // Create crawl job
		crawlJobs.GET("", handler.ListCrawlJobs)                      // List crawl jobs
		crawlJobs.GET("/:id", handler.GetCrawlJob)                    // Get crawl job by ID
		crawlJobs.PUT("/:id", handler.UpdateCrawlJob)                 // Update crawl job
		crawlJobs.DELETE("/:id", handler.DeleteCrawlJob)              // Delete crawl job

		// Job control operations
		crawlJobs.POST("/:id/start", handler.StartCrawlJob)           // Start crawl job
		crawlJobs.POST("/:id/stop", handler.StopCrawlJob)             // Stop crawl job
		crawlJobs.GET("/:id/progress", handler.GetCrawlJobProgress)   // Get crawl job progress

		// Test crawl configuration
		crawlJobs.POST("/test", handler.TestCrawl)                    // Test crawl configuration
	}

	// Crawl articles routes
	crawlArticles := v1.Group("/crawl/articles")
	{
		// CRUD operations
		crawlArticles.GET("", handler.ListCrawlArticles)              // List crawl articles
		crawlArticles.GET("/search", handler.SearchCrawlArticles)     // Search crawl articles
		crawlArticles.GET("/:id", handler.GetCrawlArticle)            // Get crawl article by ID
		crawlArticles.PUT("/:id", handler.UpdateCrawlArticle)         // Update crawl article
		crawlArticles.DELETE("/:id", handler.DeleteCrawlArticle)      // Delete crawl article
	}

	// Crawl statistics routes
	crawlStats := v1.Group("/crawl/stats")
	{
		crawlStats.GET("", handler.GetCrawlStats)                     // Get crawl statistics
	}
}