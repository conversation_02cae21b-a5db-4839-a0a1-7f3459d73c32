package services

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
)

// CrawlJobService handles crawl job business logic
type CrawlJobService struct {
	crawlJobRepo repository.CrawlJobRepository
}

// NewCrawlJobService creates a new crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
) *CrawlJobService {
	return &CrawlJobService{
		crawlJobRepo: crawlJobRepo,
	}
}

// Create creates a new crawl job
func (s *CrawlJobService) Create(ctx context.Context, tenantID uint, req request.CreateCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Creating crawl job for tenant %d: %s\n", tenantID, req.Name)

	// Create crawl job model
	crawlJob := &models.CrawlJob{
		TenantID:    tenantID,
		WebsiteID:   req.WebsiteID,
		Name:        req.Name,
		Description: req.Description,
		StartURL:    req.StartURL,
		Type:        req.Type,
		Status:      "pending",
		Rules:       models.CrawlRuleJSON(req.Rules),
		Schedule:    req.Schedule,
		Active:      req.Active,
		MaxRetries:  req.MaxRetries,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, tenantID, crawlJob); err != nil {
		fmt.Printf("Failed to create crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to create crawl job: %w", err)
	}

	fmt.Printf("Crawl job created successfully with ID: %d\n", crawlJob.ID)
	return crawlJob, nil
}

// GetByID gets a crawl job by ID
func (s *CrawlJobService) GetByID(ctx context.Context, tenantID, id uint) (*response.CrawlJobResponse, error) {
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		fmt.Printf("Failed to get crawl job %d: %v\n", id, err)
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, nil
	}

	return &response.CrawlJobResponse{
		CrawlJob: crawlJob,
		CanStart: crawlJob.CanStart(),
		CanStop:  crawlJob.CanStop(),
	}, nil
}

// Update updates a crawl job
func (s *CrawlJobService) Update(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Updating crawl job %d for tenant %d\n", id, tenantID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.Status == "running" {
		return nil, fmt.Errorf("cannot update running crawl job")
	}

	// Update fields
	if req.Name != nil {
		crawlJob.Name = *req.Name
	}
	if req.Description != nil {
		crawlJob.Description = *req.Description
	}
	if req.StartURL != nil {
		crawlJob.StartURL = *req.StartURL
	}
	if req.Type != nil {
		crawlJob.Type = *req.Type
	}
	if req.Rules != nil {
		crawlJob.Rules = models.CrawlRuleJSON(*req.Rules)
	}
	if req.Schedule != nil {
		crawlJob.Schedule = *req.Schedule
	}
	if req.Active != nil {
		crawlJob.Active = *req.Active
	}
	if req.MaxRetries != nil {
		crawlJob.MaxRetries = *req.MaxRetries
	}

	crawlJob.UpdatedAt = time.Now()

	// Update in database
	if err := s.crawlJobRepo.Update(ctx, tenantID, crawlJob); err != nil {
		fmt.Printf("Failed to update crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to update crawl job: %w", err)
	}

	fmt.Printf("Crawl job updated successfully: %d\n", crawlJob.ID)
	return crawlJob, nil
}

// Delete deletes a crawl job
func (s *CrawlJobService) Delete(ctx context.Context, tenantID, id uint) error {
	fmt.Printf("Deleting crawl job %d for tenant %d\n", id, tenantID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.Status == "running" {
		return fmt.Errorf("cannot delete running crawl job")
	}

	// Delete from database
	if err := s.crawlJobRepo.Delete(ctx, tenantID, id); err != nil {
		fmt.Printf("Failed to delete crawl job: %v\n", err)
		return fmt.Errorf("failed to delete crawl job: %w", err)
	}

	fmt.Printf("Crawl job deleted successfully: %d\n", id)
	return nil
}

// List lists crawl jobs with pagination
func (s *CrawlJobService) List(ctx context.Context, tenantID uint, req request.ListCrawlJobRequest) (*response.CrawlJobListResponse, error) {
	fmt.Printf("Listing crawl jobs for tenant %d\n", tenantID)

	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// Get crawl jobs from repository
	crawlJobs, err := s.crawlJobRepo.List(ctx, tenantID, req)
	if err != nil {
		fmt.Printf("Failed to list crawl jobs: %v\n", err)
		return nil, fmt.Errorf("failed to list crawl jobs: %w", err)
	}

	// Get total count for pagination
	total := int64(len(crawlJobs)) // This is a simplified approach, ideally should be a separate count query

	// Calculate pagination metadata
	totalPages := int((total + int64(req.Limit) - 1) / int64(req.Limit))
	hasMore := req.Page < totalPages

	return &response.CrawlJobListResponse{
		Data: crawlJobs,
		Meta: response.Meta{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
			HasMore:    hasMore,
		},
	}, nil
}

// Start starts a crawl job
func (s *CrawlJobService) Start(ctx context.Context, tenantID, id uint, req request.StartCrawlJobRequest) error {
	fmt.Printf("Starting crawl job %d for tenant %d\n", id, tenantID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be started
	if !req.Force && crawlJob.Status == "running" {
		return fmt.Errorf("crawl job is already running")
	}

	// Update status to running
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, "running"); err != nil {
		fmt.Printf("Failed to update crawl job status: %v\n", err)
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Queue the crawl job for processing
	// This would typically involve sending a message to a queue system
	// For now, we'll just log it
	fmt.Printf("Crawl job queued for processing: %d\n", id)

	return nil
}

// Stop stops a crawl job
func (s *CrawlJobService) Stop(ctx context.Context, tenantID, id uint, req request.StopCrawlJobRequest) error {
	fmt.Printf("Stopping crawl job %d for tenant %d\n", id, tenantID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be stopped
	if crawlJob.Status != "running" {
		return fmt.Errorf("crawl job is not running")
	}

	// Update status to cancelled
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, "cancelled"); err != nil {
		fmt.Printf("Failed to update crawl job status: %v\n", err)
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Send stop signal to the running crawler
	// This would typically involve sending a message to stop the crawler
	fmt.Printf("Stop signal sent to crawl job %d, reason: %s\n", id, req.Reason)

	return nil
}

// UpdateStatus updates the status of a crawl job
func (s *CrawlJobService) UpdateStatus(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobStatusRequest) error {
	fmt.Printf("Updating crawl job status %d for tenant %d to %s\n", id, tenantID, req.Status)

	return s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, string(req.Status))
}

// UpdateProgress updates the progress of a crawl job
func (s *CrawlJobService) UpdateProgress(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobProgressRequest) error {
	fmt.Printf("Updating crawl job progress %d for tenant %d\n", id, tenantID)
	return s.crawlJobRepo.UpdateProgress(ctx, tenantID, id, &req.Progress)
}

// GetStats gets statistics for crawl jobs
func (s *CrawlJobService) GetStats(ctx context.Context, tenantID uint) (*response.CrawlJobStatsResponse, error) {
	fmt.Printf("Getting crawl job stats for tenant %d\n", tenantID)
	return s.crawlJobRepo.GetStats(ctx, tenantID)
}

// GetActiveJobs gets active crawl jobs
func (s *CrawlJobService) GetActiveJobs(ctx context.Context, tenantID uint) ([]*models.CrawlJob, error) {
	fmt.Printf("Getting active crawl jobs for tenant %d\n", tenantID)
	return s.crawlJobRepo.GetActiveJobs(ctx, tenantID)
}

// GetScheduledJobs gets scheduled crawl jobs that are due to run
func (s *CrawlJobService) GetScheduledJobs(ctx context.Context) ([]*models.CrawlJob, error) {
	fmt.Printf("Getting scheduled crawl jobs\n")
	return s.crawlJobRepo.GetScheduledJobs(ctx)
}

// Clone clones a crawl job
func (s *CrawlJobService) Clone(ctx context.Context, tenantID, id uint, req request.CloneCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Cloning crawl job %d for tenant %d\n", id, tenantID)

	// Get existing crawl job
	originalJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if originalJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Create new crawl job based on original
	newJob := &models.CrawlJob{
		TenantID:    tenantID,
		WebsiteID:   originalJob.WebsiteID,
		Name:        req.Name,
		Description: req.Description,
		StartURL:    originalJob.StartURL,
		Type:        originalJob.Type,
		Status:      "pending",
		Rules:       originalJob.Rules,
		Schedule:    originalJob.Schedule,
		Active:      req.Active,
		MaxRetries:  originalJob.MaxRetries,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, tenantID, newJob); err != nil {
		fmt.Printf("Failed to clone crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to clone crawl job: %w", err)
	}

	fmt.Printf("Crawl job cloned successfully: original %d, new %d\n", id, newJob.ID)
	return newJob, nil
}

// BulkUpdate performs bulk operations on crawl jobs
func (s *CrawlJobService) BulkUpdate(ctx context.Context, tenantID uint, req request.BulkUpdateCrawlJobRequest) (*response.BulkOperationResponse, error) {
	fmt.Printf("Performing bulk operation %s on crawl jobs for tenant %d, count: %d\n", req.Action, tenantID, len(req.IDs))

	response := &response.BulkOperationResponse{
		Success:   true,
		Processed: 0,
		Failed:    0,
		Errors:    []string{},
	}

	for _, id := range req.IDs {
		var err error
		switch req.Action {
		case "start":
			err = s.Start(ctx, tenantID, id, request.StartCrawlJobRequest{})
		case "stop":
			err = s.Stop(ctx, tenantID, id, request.StopCrawlJobRequest{Reason: req.Reason})
		case "activate":
			_, err = s.Update(ctx, tenantID, id, request.UpdateCrawlJobRequest{Active: &[]bool{true}[0]})
		case "deactivate":
			_, err = s.Update(ctx, tenantID, id, request.UpdateCrawlJobRequest{Active: &[]bool{false}[0]})
		case "delete":
			err = s.Delete(ctx, tenantID, id)
		default:
			err = fmt.Errorf("unknown action: %s", req.Action)
		}

		if err != nil {
			response.Failed++
			response.Errors = append(response.Errors, fmt.Sprintf("ID %d: %s", id, err.Error()))
		} else {
			response.Processed++
		}
	}

	if response.Failed > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("Bulk operation completed with %d failures", response.Failed)
	} else {
		response.Message = fmt.Sprintf("Bulk operation completed successfully for %d items", response.Processed)
	}

	return response, nil
}