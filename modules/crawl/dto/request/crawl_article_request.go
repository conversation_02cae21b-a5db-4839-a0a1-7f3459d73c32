package request

import (
	"time"

	"wnapi/modules/crawl/internal"
)

// CreateCrawlArticleRequest represents the request to create a crawl article
type CreateCrawlArticleRequest struct {
	WebsiteID    uint                           `json:"website_id" binding:"required"`
	CrawlJobID   uint                           `json:"crawl_job_id" binding:"required"`
	Title        string                         `json:"title" binding:"required,min=1,max=500"`
	Slug         string                         `json:"slug" binding:"max=255"`
	Content      string                         `json:"content" binding:"required"`
	Summary      string                         `json:"summary" binding:"max=1000"`
	URL          string                         `json:"url" binding:"required,url"`
	ImageURL     string                         `json:"image_url" binding:"omitempty,url"`
	Author       string                         `json:"author" binding:"max=255"`
	PublishedAt  *time.Time                     `json:"published_at"`
	Metadata     internal.ArticleMetadata       `json:"metadata"`
	Tags         []string                       `json:"tags"`
	Categories   []string                       `json:"categories"`
	Language     string                         `json:"language" binding:"max=10"`
}

// UpdateCrawlArticleRequest represents the request to update a crawl article
type UpdateCrawlArticleRequest struct {
	Title       *string                    `json:"title" binding:"omitempty,min=1,max=500"`
	Slug        *string                    `json:"slug" binding:"omitempty,max=255"`
	Content     *string                    `json:"content"`
	Summary     *string                    `json:"summary" binding:"omitempty,max=1000"`
	ImageURL    *string                    `json:"image_url" binding:"omitempty,url"`
	Author      *string                    `json:"author" binding:"omitempty,max=255"`
	PublishedAt *time.Time                 `json:"published_at"`
	Metadata    *internal.ArticleMetadata  `json:"metadata"`
	Tags        []string                   `json:"tags"`
	Categories  []string                   `json:"categories"`
	Language    *string                    `json:"language" binding:"omitempty,max=10"`
}

// ListCrawlArticleRequest represents the request to list crawl articles
type ListCrawlArticleRequest struct {
	Page        int        `form:"page" binding:"min=1" default:"1"`
	Limit       int        `form:"limit" binding:"min=1,max=100" default:"20"`
	CrawlJobID  uint       `form:"crawl_job_id"`
	Status      string     `form:"status" binding:"omitempty,oneof=pending processed failed duplicate"`
	Language    string     `form:"language" binding:"omitempty,max=10"`
	Search      string     `form:"search" binding:"max=255"`
	StartDate   time.Time  `form:"start_date"`
	EndDate     time.Time  `form:"end_date"`
	SortBy      string     `form:"sort_by" binding:"omitempty,oneof=id title created_at updated_at published_at word_count"`
	SortOrder   string     `form:"sort_order" binding:"omitempty,oneof=asc desc" default:"desc"`
}

// SearchCrawlArticleRequest represents the request to search crawl articles
type SearchCrawlArticleRequest struct {
	Page         int        `form:"page" binding:"min=1" default:"1"`
	Limit        int        `form:"limit" binding:"min=1,max=100" default:"20"`
	WebsiteID    uint       `form:"website_id"`
	CrawlJobID   uint       `form:"crawl_job_id"`
	Status       string     `form:"status" binding:"omitempty,oneof=pending processed failed duplicate"`
	Language     string     `form:"language" binding:"omitempty,max=10"`
	Author       string     `form:"author" binding:"max=255"`
	Query        string     `form:"q" binding:"max=255"`
	Tags         []string   `form:"tags"`
	Categories   []string   `form:"categories"`
	StartDate    time.Time  `form:"start_date"`
	EndDate      time.Time  `form:"end_date"`
	MinWordCount int        `form:"min_word_count" binding:"min=0"`
	MaxWordCount int        `form:"max_word_count" binding:"min=0"`
	SortBy       string     `form:"sort_by" binding:"omitempty,oneof=id title created_at updated_at published_at word_count relevance"`
	SortOrder    string     `form:"sort_order" binding:"omitempty,oneof=asc desc" default:"desc"`
}

// UpdateCrawlArticleStatusRequest represents the request to update crawl article status
type UpdateCrawlArticleStatusRequest struct {
	Status internal.CrawlArticleStatus `json:"status" binding:"required,oneof=pending processed failed duplicate"`
	Reason string                       `json:"reason" binding:"max=255"`
}

// BulkUpdateCrawlArticleRequest represents the request to bulk update crawl articles
type BulkUpdateCrawlArticleRequest struct {
	IDs    []uint `json:"ids" binding:"required,min=1"`
	Action string `json:"action" binding:"required,oneof=process retry delete mark_duplicate"`
	Reason string `json:"reason" binding:"max=255"`
}

// CrawlArticleStatsRequest represents the request to get crawl article statistics
type CrawlArticleStatsRequest struct {
	WebsiteID   *uint      `form:"website_id"`
	CrawlJobID  *uint      `form:"crawl_job_id"`
	StartDate   *time.Time `form:"start_date"`
	EndDate     *time.Time `form:"end_date"`
	GroupBy     string     `form:"group_by" binding:"omitempty,oneof=day week month year status language"`
}

// ExportCrawlArticleRequest represents the request to export crawl articles
type ExportCrawlArticleRequest struct {
	WebsiteID   *uint      `form:"website_id"`
	CrawlJobID  *uint      `form:"crawl_job_id"`
	Status      string     `form:"status" binding:"omitempty,oneof=pending processed failed duplicate"`
	Language    string     `form:"language" binding:"omitempty,max=10"`
	StartDate   *time.Time `form:"start_date"`
	EndDate     *time.Time `form:"end_date"`
	Format      string     `form:"format" binding:"required,oneof=json csv xml"`
	Fields      []string   `form:"fields"` // Specific fields to export
}

// DuplicateCheckRequest represents the request to check for duplicate articles
type DuplicateCheckRequest struct {
	URL         string `json:"url" binding:"omitempty,url"`
	Title       string `json:"title" binding:"omitempty,min=1"`
	Content     string `json:"content" binding:"omitempty,min=1"`
	HashContent string `json:"hash_content" binding:"omitempty"`
	Threshold   float64 `json:"threshold" binding:"min=0,max=1" default:"0.8"` // Similarity threshold
}

// ProcessArticleRequest represents the request to process a crawl article
type ProcessArticleRequest struct {
	ExtractSummary   bool `json:"extract_summary" default:"true"`
	ExtractTags      bool `json:"extract_tags" default:"true"`
	ExtractCategories bool `json:"extract_categories" default:"true"`
	Translate        bool `json:"translate" default:"false"`
	TargetLanguage   string `json:"target_language" binding:"omitempty,max=10"`
}

// ReprocessArticleRequest represents the request to reprocess failed articles
type ReprocessArticleRequest struct {
	IDs           []uint `json:"ids" binding:"required,min=1"`
	ResetRetryCount bool `json:"reset_retry_count" default:"false"`
	Force         bool   `json:"force" default:"false"` // Force reprocess even if not failed
}