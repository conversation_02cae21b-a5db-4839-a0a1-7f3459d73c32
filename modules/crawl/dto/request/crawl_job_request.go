package request

import (
	"time"

	"wnapi/modules/crawl/internal"
)

// CreateCrawlJobRequest represents the request to create a crawl job
type CreateCrawlJobRequest struct {
	WebsiteID   uint                     `json:"website_id" binding:"required"`
	Name        string                   `json:"name" binding:"required,min=1,max=255"`
	Description string                   `json:"description" binding:"max=1000"`
	StartURL    string                   `json:"start_url" binding:"required,url"`
	Type        internal.CrawlJobType    `json:"type" binding:"required,oneof=news blog ecommerce general"`
	Rules       internal.CrawlRule       `json:"rules"`
	Schedule    string                   `json:"schedule" binding:"max=100"` // Cron expression
	Active      bool                     `json:"active"`
	MaxRetries  int                      `json:"max_retries" binding:"min=0,max=10"`
}

// UpdateCrawlJobRequest represents the request to update a crawl job
type UpdateCrawlJobRequest struct {
	Name        *string                  `json:"name" binding:"omitempty,min=1,max=255"`
	Description *string                  `json:"description" binding:"omitempty,max=1000"`
	StartURL    *string                  `json:"start_url" binding:"omitempty,url"`
	Type        *internal.CrawlJobType   `json:"type" binding:"omitempty,oneof=news blog ecommerce general"`
	Rules       *internal.CrawlRule      `json:"rules"`
	Schedule    *string                  `json:"schedule" binding:"omitempty,max=100"`
	Active      *bool                    `json:"active"`
	MaxRetries  *int                     `json:"max_retries" binding:"omitempty,min=0,max=10"`
}

// ListCrawlJobRequest represents the request to list crawl jobs
type ListCrawlJobRequest struct {
	Page      int    `form:"page" binding:"min=1" default:"1"`
	Limit     int    `form:"limit" binding:"min=1,max=100" default:"20"`
	Status    string `form:"status" binding:"omitempty,oneof=pending running completed failed cancelled paused"`
	Type      string `form:"type" binding:"omitempty,oneof=news blog ecommerce general"`
	Active    *bool  `form:"active"`
	Search    string `form:"search" binding:"max=255"`
	SortBy    string `form:"sort_by" binding:"omitempty,oneof=id name created_at updated_at last_run_at"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc" default:"desc"`
}

// StartCrawlJobRequest represents the request to start a crawl job
type StartCrawlJobRequest struct {
	Force bool `json:"force"` // Force start even if already running
}

// StopCrawlJobRequest represents the request to stop a crawl job
type StopCrawlJobRequest struct {
	Reason string `json:"reason" binding:"max=255"`
}

// UpdateCrawlJobStatusRequest represents the request to update crawl job status
type UpdateCrawlJobStatusRequest struct {
	Status internal.CrawlJobStatus `json:"status" binding:"required,oneof=pending running completed failed cancelled paused"`
	Reason string                  `json:"reason" binding:"max=255"`
}

// UpdateCrawlJobProgressRequest represents the request to update crawl job progress
type UpdateCrawlJobProgressRequest struct {
	Progress internal.CrawlProgress `json:"progress" binding:"required"`
}

// CrawlJobStatsRequest represents the request to get crawl job statistics
type CrawlJobStatsRequest struct {
	WebsiteID *uint      `form:"website_id"`
	StartDate *time.Time `form:"start_date"`
	EndDate   *time.Time `form:"end_date"`
}

// BulkUpdateCrawlJobRequest represents the request to bulk update crawl jobs
type BulkUpdateCrawlJobRequest struct {
	IDs    []uint                   `json:"ids" binding:"required,min=1"`
	Action string                   `json:"action" binding:"required,oneof=start stop activate deactivate delete"`
	Reason string                   `json:"reason" binding:"max=255"`
}

// CloneCrawlJobRequest represents the request to clone a crawl job
type CloneCrawlJobRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=255"`
	Description string `json:"description" binding:"max=1000"`
	Active      bool   `json:"active"`
}

// TestCrawlJobRequest represents the request to test a crawl job configuration
type TestCrawlJobRequest struct {
	StartURL string             `json:"start_url" binding:"required,url"`
	Rules    internal.CrawlRule `json:"rules"`
	MaxPages int                `json:"max_pages" binding:"min=1,max=10" default:"5"`
}