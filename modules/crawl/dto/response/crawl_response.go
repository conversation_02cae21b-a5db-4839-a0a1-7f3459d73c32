package response

import (
	"time"

	"wnapi/modules/crawl/models"
)

// Meta represents pagination metadata
type Meta struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	Has<PERSON><PERSON>    bool  `json:"has_more"`
}

// CrawlJobResponse represents a crawl job response
type CrawlJobResponse struct {
	*models.CrawlJob
	CanStart bool `json:"can_start"`
	CanStop  bool `json:"can_stop"`
}

// CrawlJobListResponse represents the response for listing crawl jobs
type CrawlJobListResponse struct {
	Data []*models.CrawlJob `json:"data"`
	Meta Meta               `json:"meta"`
}

// CrawlJobStatsResponse represents crawl job statistics
type CrawlJobStatsResponse struct {
	TotalJobs     int64 `json:"total_jobs"`
	RunningJobs   int64 `json:"running_jobs"`
	CompletedJobs int64 `json:"completed_jobs"`
	FailedJobs    int64 `json:"failed_jobs"`
	PendingJobs   int64 `json:"pending_jobs"`
	PausedJobs    int64 `json:"paused_jobs"`
	CancelledJobs int64 `json:"cancelled_jobs"`
}

// CrawlArticleResponse represents a crawl article response
type CrawlArticleResponse struct {
	*models.CrawlArticle
	ReadingTime int `json:"reading_time"` // in minutes
}

// CrawlArticleListResponse represents the response for listing crawl articles
type CrawlArticleListResponse struct {
	Data []*models.CrawlArticle `json:"data"`
	Meta Meta                   `json:"meta"`
}

// CrawlArticleStatsResponse represents crawl article statistics
type CrawlArticleStatsResponse struct {
	TotalArticles     int64 `json:"total_articles"`
	ProcessedArticles int64 `json:"processed_articles"`
	FailedArticles    int64 `json:"failed_articles"`
	PendingArticles   int64 `json:"pending_articles"`
	DuplicateArticles int64 `json:"duplicate_articles"`
	AverageWordCount  int   `json:"average_word_count"`
}

// CrawlSystemStatsResponse represents overall crawl system statistics
type CrawlSystemStatsResponse struct {
	Jobs     CrawlJobStatsResponse     `json:"jobs"`
	Articles CrawlArticleStatsResponse `json:"articles"`
	Uptime   time.Duration             `json:"uptime"`
	Version  string                    `json:"version"`
}

// CrawlTestResponse represents the response for testing a crawl configuration
type CrawlTestResponse struct {
	Success      bool                    `json:"success"`
	Message      string                  `json:"message"`
	PagesFound   int                     `json:"pages_found"`
	ArticlesFound int                    `json:"articles_found"`
	Errors       []string                `json:"errors,omitempty"`
	SampleArticles []*CrawlTestArticle   `json:"sample_articles,omitempty"`
	Duration     time.Duration           `json:"duration"`
}

// CrawlTestArticle represents a sample article from crawl test
type CrawlTestArticle struct {
	Title    string `json:"title"`
	URL      string `json:"url"`
	Content  string `json:"content,omitempty"`
	ImageURL string `json:"image_url,omitempty"`
	Author   string `json:"author,omitempty"`
}

// DuplicateCheckResponse represents the response for duplicate check
type DuplicateCheckResponse struct {
	IsDuplicate    bool                  `json:"is_duplicate"`
	Similarity     float64               `json:"similarity"`
	MatchedArticle *models.CrawlArticle  `json:"matched_article,omitempty"`
	Message        string                `json:"message"`
}

// BulkOperationResponse represents the response for bulk operations
type BulkOperationResponse struct {
	Success     bool     `json:"success"`
	Processed   int      `json:"processed"`
	Failed      int      `json:"failed"`
	Errors      []string `json:"errors,omitempty"`
	Message     string   `json:"message"`
}

// ExportResponse represents the response for export operations
type ExportResponse struct {
	Success   bool   `json:"success"`
	FileURL   string `json:"file_url,omitempty"`
	FileName  string `json:"file_name"`
	FileSize  int64  `json:"file_size"`
	Format    string `json:"format"`
	ExpiresAt time.Time `json:"expires_at"`
	Message   string `json:"message"`
}

// CrawlProgressResponse represents real-time crawl progress
type CrawlProgressResponse struct {
	JobID            uint      `json:"job_id"`
	Status           string    `json:"status"`
	Progress         float64   `json:"progress"` // 0-100
	PagesProcessed   int       `json:"pages_processed"`
	ArticlesFound    int       `json:"articles_found"`
	ArticlesProcessed int      `json:"articles_processed"`
	ErrorsCount      int       `json:"errors_count"`
	StartedAt        time.Time `json:"started_at"`
	EstimatedFinish  *time.Time `json:"estimated_finish,omitempty"`
	CurrentURL       string    `json:"current_url,omitempty"`
	Message          string    `json:"message,omitempty"`
}

// CrawlHealthResponse represents crawl system health status
type CrawlHealthResponse struct {
	Status       string            `json:"status"` // healthy, degraded, unhealthy
	Version      string            `json:"version"`
	Uptime       time.Duration     `json:"uptime"`
	ActiveJobs   int               `json:"active_jobs"`
	QueueSize    int               `json:"queue_size"`
	WorkerCount  int               `json:"worker_count"`
	MemoryUsage  int64             `json:"memory_usage"` // in bytes
	CPUUsage     float64           `json:"cpu_usage"`    // percentage
	DiskUsage    int64             `json:"disk_usage"`   // in bytes
	Checks       map[string]string `json:"checks"`       // component health checks
	LastUpdated  time.Time         `json:"last_updated"`
}

// CrawlConfigResponse represents crawl configuration response
type CrawlConfigResponse struct {
	UserAgent      string `json:"user_agent"`
	Delay          int    `json:"delay"`
	Timeout        int    `json:"timeout"`
	MaxRetries     int    `json:"max_retries"`
	Concurrency    int    `json:"concurrency"`
	RespectRobots  bool   `json:"respect_robots"`
	FollowRedirect bool   `json:"follow_redirect"`
	MaxDepth       int    `json:"max_depth"`
	QueueName      string `json:"queue_name"`
	WorkerCount    int    `json:"worker_count"`
	StorageEnabled bool   `json:"storage_enabled"`
	StoragePath    string `json:"storage_path"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string                 `json:"error"`
	Message string                 `json:"message"`
	Code    string                 `json:"code,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}