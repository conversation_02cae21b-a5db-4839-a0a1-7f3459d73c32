package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
)

// CrawlJobRepository implements the CrawlJobRepository interface
type CrawlJobRepository struct {
	db *gorm.DB
}

// NewCrawlJobRepository creates a new crawl job repository
func NewCrawlJobRepository(db *gorm.DB) repository.CrawlJobRepository {
	return &CrawlJobRepository{
		db: db,
	}
}

// Create creates a new crawl job
func (r *CrawlJobRepository) Create(ctx context.Context, tenantID uint, job *models.CrawlJob) error {
	job.TenantID = tenantID
	return r.db.WithContext(ctx).Create(job).Error
}

// GetByID gets a crawl job by ID
func (r *CrawlJobRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.CrawlJob, error) {
	var job models.CrawlJob
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, id).First(&job).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &job, nil
}

// Update updates a crawl job
func (r *CrawlJobRepository) Update(ctx context.Context, tenantID uint, job *models.CrawlJob) error {
	job.TenantID = tenantID
	return r.db.WithContext(ctx).Save(job).Error
}

// Delete deletes a crawl job
func (r *CrawlJobRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, id).Delete(&models.CrawlJob{}).Error
}

// List lists crawl jobs with pagination
func (r *CrawlJobRepository) List(ctx context.Context, tenantID uint, req request.ListCrawlJobRequest) ([]*models.CrawlJob, error) {
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Filter by status if specified
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Filter by type if specified
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// Filter by active status if specified
	if req.Active != nil {
		query = query.Where("active = ?", *req.Active)
	}

	// Search by name if specified
	if req.Search != "" {
		query = query.Where("name LIKE ?", "%"+req.Search+"%")
	}

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// Apply sorting
	if req.SortBy != "" {
		order := "ASC"
		if req.SortOrder == "desc" {
			order = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", req.SortBy, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// Execute query
	var crawlJobs []*models.CrawlJob
	if err := query.Find(&crawlJobs).Error; err != nil {
		return nil, fmt.Errorf("failed to list crawl jobs: %w", err)
	}

	return crawlJobs, nil
}

// GetByStatus gets crawl jobs by status
func (r *CrawlJobRepository) GetByStatus(ctx context.Context, tenantID uint, status string) ([]*models.CrawlJob, error) {
	var crawlJobs []*models.CrawlJob
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Find(&crawlJobs).Error; err != nil {
		return nil, fmt.Errorf("failed to get crawl jobs by status: %w", err)
	}

	return crawlJobs, nil
}

// GetActiveJobs gets active crawl jobs
func (r *CrawlJobRepository) GetActiveJobs(ctx context.Context, tenantID uint) ([]*models.CrawlJob, error) {
	var crawlJobs []*models.CrawlJob
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND active = ?", tenantID, true).
		Find(&crawlJobs).Error; err != nil {
		return nil, fmt.Errorf("failed to get active crawl jobs: %w", err)
	}

	return crawlJobs, nil
}

// GetScheduledJobs gets scheduled crawl jobs that are due to run
func (r *CrawlJobRepository) GetScheduledJobs(ctx context.Context) ([]*models.CrawlJob, error) {
	now := time.Now()
	var crawlJobs []*models.CrawlJob
	if err := r.db.WithContext(ctx).
		Where("active = ? AND schedule != '' AND (next_run_at IS NULL OR next_run_at <= ?)", true, now).
		Find(&crawlJobs).Error; err != nil {
		return nil, fmt.Errorf("failed to get scheduled crawl jobs: %w", err)
	}

	return crawlJobs, nil
}

// UpdateStatus updates the status of a crawl job
func (r *CrawlJobRepository) UpdateStatus(ctx context.Context, tenantID, id uint, status string) error {
	updates := map[string]interface{}{
		"status": status,
	}

	// Set last_run_at if status is running
	if status == string(internal.CrawlJobStatusRunning) {
		updates["last_run_at"] = time.Now()
	}

	if err := r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	return nil
}

// UpdateProgress updates the progress of a crawl job
func (r *CrawlJobRepository) UpdateProgress(ctx context.Context, tenantID, id uint, progress *internal.CrawlProgress) error {
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("progress", progress).Error; err != nil {
		return fmt.Errorf("failed to update crawl job progress: %w", err)
	}

	return nil
}

// IncrementRetryCount increments the retry count of a crawl job
func (r *CrawlJobRepository) IncrementRetryCount(ctx context.Context, tenantID, id uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("retry_count", gorm.Expr("retry_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment retry count: %w", err)
	}

	return nil
}

// GetStats gets statistics for crawl jobs
func (r *CrawlJobRepository) GetStats(ctx context.Context, tenantID uint) (*response.CrawlJobStatsResponse, error) {
	query := r.db.WithContext(ctx).Model(&models.CrawlJob{}).Where("tenant_id = ?", tenantID)

	stats := &response.CrawlJobStatsResponse{}

	// Count total jobs
	if err := query.Count(&stats.TotalJobs).Error; err != nil {
		return nil, fmt.Errorf("failed to count total jobs: %w", err)
	}

	// Count by status
	var statusCounts []struct {
		Status string
		Count  int64
	}

	if err := query.Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count jobs by status: %w", err)
	}

	for _, sc := range statusCounts {
		switch sc.Status {
		case string(internal.CrawlJobStatusRunning):
			stats.RunningJobs = sc.Count
		case string(internal.CrawlJobStatusCompleted):
			stats.CompletedJobs = sc.Count
		case string(internal.CrawlJobStatusFailed):
			stats.FailedJobs = sc.Count
		case string(internal.CrawlJobStatusPending):
			stats.PendingJobs = sc.Count
		}
	}

	return stats, nil
}

// CountByStatus counts crawl jobs by status
func (r *CrawlJobRepository) CountByStatus(ctx context.Context, tenantID uint, status string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count crawl jobs by status: %w", err)
	}
	return count, nil
}

// UpdateLastRunAt updates the last run time of a crawl job
func (r *CrawlJobRepository) UpdateLastRunAt(ctx context.Context, tenantID, id uint, lastRunAt time.Time) error {
	return r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("last_run_at", lastRunAt).Error
}

// UpdateNextRunAt updates the next run time of a crawl job
func (r *CrawlJobRepository) UpdateNextRunAt(ctx context.Context, tenantID, id uint, nextRunAt time.Time) error {
	return r.db.WithContext(ctx).
		Model(&models.CrawlJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("next_run_at", nextRunAt).Error
}

// BulkUpdate updates multiple crawl jobs
func (r *CrawlJobRepository) BulkUpdate(ctx context.Context, tenantID uint, req request.BulkUpdateCrawlJobRequest) error {
	if len(req.IDs) == 0 {
		return nil
	}

	for _, id := range req.IDs {
		switch req.Action {
		case "start":
			if err := r.UpdateStatus(ctx, tenantID, id, "running"); err != nil {
				return fmt.Errorf("failed to start job %d: %w", id, err)
			}
		case "stop":
			if err := r.UpdateStatus(ctx, tenantID, id, "stopped"); err != nil {
				return fmt.Errorf("failed to stop job %d: %w", id, err)
			}
		case "activate":
			if err := r.db.WithContext(ctx).Model(&models.CrawlJob{}).Where("tenant_id = ? AND id = ?", tenantID, id).Update("active", true).Error; err != nil {
				return fmt.Errorf("failed to activate job %d: %w", id, err)
			}
		case "deactivate":
			if err := r.db.WithContext(ctx).Model(&models.CrawlJob{}).Where("tenant_id = ? AND id = ?", tenantID, id).Update("active", false).Error; err != nil {
				return fmt.Errorf("failed to deactivate job %d: %w", id, err)
			}
		case "delete":
			if err := r.Delete(ctx, tenantID, id); err != nil {
				return fmt.Errorf("failed to delete job %d: %w", id, err)
			}
		default:
			return fmt.Errorf("unsupported bulk action: %s", req.Action)
		}
	}

	return nil
}