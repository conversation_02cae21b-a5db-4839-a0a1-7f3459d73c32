package internal

import "time"

// CrawlJobStatus represents the status of a crawl job
type CrawlJobStatus string

const (
	CrawlJobStatusPending   CrawlJobStatus = "pending"
	CrawlJobStatusRunning   CrawlJobStatus = "running"
	CrawlJobStatusCompleted CrawlJobStatus = "completed"
	CrawlJobStatusFailed    CrawlJobStatus = "failed"
	CrawlJobStatusCancelled CrawlJobStatus = "cancelled"
	CrawlJobStatusPaused    CrawlJobStatus = "paused"
)

// CrawlJobType represents the type of crawl job
type CrawlJobType string

const (
	CrawlJobTypeNews     CrawlJobType = "news"
	CrawlJobTypeBlog     CrawlJobType = "blog"
	CrawlJobTypeEcommerce CrawlJobType = "ecommerce"
	CrawlJobTypeGeneral  CrawlJobType = "general"
)

// CrawlArticleStatus represents the status of a crawled article
type CrawlArticleStatus string

const (
	CrawlArticleStatusPending   CrawlArticleStatus = "pending"
	CrawlArticleStatusProcessed CrawlArticleStatus = "processed"
	CrawlArticleStatusFailed    CrawlArticleStatus = "failed"
	CrawlArticleStatusDuplicate CrawlArticleStatus = "duplicate"
)

// CrawlRule represents crawling rules for a specific website
type CrawlRule struct {
	Domain       string            `json:"domain"`
	Selectors    CrawlSelectors    `json:"selectors"`
	MaxDepth     int               `json:"max_depth"`
	Delay        time.Duration     `json:"delay"`
	UserAgent    string            `json:"user_agent"`
	Headers      map[string]string `json:"headers"`
	AllowedURLs  []string          `json:"allowed_urls"`
	BlockedURLs  []string          `json:"blocked_urls"`
	RespectRobots bool             `json:"respect_robots"`
}

// CrawlSelectors represents CSS selectors for extracting content
type CrawlSelectors struct {
	Title   string `json:"title"`
	Content string `json:"content"`
	Author  string `json:"author"`
	Image   string `json:"image"`
	Summary string `json:"summary"`
}

// CrawlStats represents crawling statistics
type CrawlStats struct {
	TotalJobs       int64 `json:"total_jobs"`
	RunningJobs     int64 `json:"running_jobs"`
	CompletedJobs   int64 `json:"completed_jobs"`
	FailedJobs      int64 `json:"failed_jobs"`
	TotalArticles   int64 `json:"total_articles"`
	ProcessedArticles int64 `json:"processed_articles"`
	FailedArticles  int64 `json:"failed_articles"`
	DuplicateArticles int64 `json:"duplicate_articles"`
}

// ArticleMetadata represents metadata extracted from an article
type ArticleMetadata struct {
	Keywords    []string  `json:"keywords"`
	Description string    `json:"description"`
	PublishedAt time.Time `json:"published_at"`
	ModifiedAt  time.Time `json:"modified_at"`
	Language    string    `json:"language"`
	WordCount   int       `json:"word_count"`
	ReadTime    int       `json:"read_time"`
}

// CrawlProgress represents the progress of a crawl operation
type CrawlProgress struct {
	JobID            uint       `json:"job_id"`
	Status           string     `json:"status"`
	StartedAt        time.Time  `json:"started_at"`
	CompletedAt      *time.Time `json:"completed_at,omitempty"`
	Message          string     `json:"message,omitempty"`
	PagesProcessed   int        `json:"pages_processed"`
	ArticlesFound    int        `json:"articles_found"`
	ArticlesProcessed int       `json:"articles_processed"`
	ErrorsCount      int        `json:"errors_count"`
}