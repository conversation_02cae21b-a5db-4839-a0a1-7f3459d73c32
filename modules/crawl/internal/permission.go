package internal

// Crawl module permissions
const (
	// Crawl Job permissions
	ListCrawlJobPermission   = "crawl.job.list"
	ReadCrawlJobPermission   = "crawl.job.read"
	CreateCrawlJobPermission = "crawl.job.create"
	UpdateCrawlJobPermission = "crawl.job.update"
	DeleteCrawlJobPermission = "crawl.job.delete"
	StartCrawlJobPermission  = "crawl.job.start"
	StopCrawlJobPermission   = "crawl.job.stop"

	// Crawl Article permissions
	ListCrawlArticlePermission   = "crawl.article.list"
	ReadCrawlArticlePermission   = "crawl.article.read"
	CreateCrawlArticlePermission = "crawl.article.create"
	UpdateCrawlArticlePermission = "crawl.article.update"
	DeleteCrawlArticlePermission = "crawl.article.delete"

	// Crawl System permissions
	ManageCrawlSystemPermission = "crawl.system.manage"
	ViewCrawlStatsPermission    = "crawl.stats.view"
)

// GetAllCrawlPermissions returns all crawl module permissions
func GetAllCrawlPermissions() []string {
	return []string{
		// Crawl Job permissions
		ListCrawlJobPermission,
		ReadCrawlJobPermission,
		CreateCrawlJobPermission,
		UpdateCrawlJobPermission,
		DeleteCrawlJobPermission,
		StartCrawlJobPermission,
		StopCrawlJobPermission,

		// Crawl Article permissions
		ListCrawlArticlePermission,
		ReadCrawlArticlePermission,
		CreateCrawlArticlePermission,
		UpdateCrawlArticlePermission,
		DeleteCrawlArticlePermission,

		// Crawl System permissions
		ManageCrawlSystemPermission,
		ViewCrawlStatsPermission,
	}
}