package internal

import (
	"time"

	"wnapi/internal/pkg/config"
)

// CrawlConfig holds configuration for crawl module
type CrawlConfig struct {
	// Crawl settings
	UserAgent       string        `mapstructure:"user_agent"`
	Delay           time.Duration `mapstructure:"delay"`
	Timeout         time.Duration `mapstructure:"timeout"`
	MaxRetries      int           `mapstructure:"max_retries"`
	Concurrency     int           `mapstructure:"concurrency"`
	RespectRobots   bool          `mapstructure:"respect_robots"`
	FollowRedirect  bool          `mapstructure:"follow_redirect"`
	MaxDepth        int           `mapstructure:"max_depth"`

	// Queue settings
	QueueName       string `mapstructure:"queue_name"`
	WorkerCount     int    `mapstructure:"worker_count"`
	QueueWorkerCount int   `mapstructure:"queue_worker_count"`

	// Storage settings
	StorageEnabled  bool   `mapstructure:"storage_enabled"`
	StoragePath     string `mapstructure:"storage_path"`
}

// NewCrawlConfigFromAppConfig creates crawl config from app config
func NewCrawlConfigFromAppConfig(cfg config.Config) (*CrawlConfig, error) {
	crawlConfig := &CrawlConfig{
		// Default values
		UserAgent:       "WebNewV2-Crawler/1.0",
		Delay:           1 * time.Second,
		Timeout:         30 * time.Second,
		MaxRetries:      3,
		Concurrency:     2,
		RespectRobots:   true,
		FollowRedirect:  true,
		MaxDepth:        3,
		QueueName:       "crawl_jobs",
		WorkerCount:     2,
		QueueWorkerCount: 3,
		StorageEnabled:  true,
		StoragePath:     "./storage/crawl",
	}

	// Override with config values if available
	if userAgent := cfg.GetString("crawl.user_agent"); userAgent != "" {
		crawlConfig.UserAgent = userAgent
	}
	if delay := cfg.GetDuration("crawl.delay"); delay > 0 {
		crawlConfig.Delay = delay
	}
	if timeout := cfg.GetDuration("crawl.timeout"); timeout > 0 {
		crawlConfig.Timeout = timeout
	}
	if maxRetries := cfg.GetInt("crawl.max_retries"); maxRetries > 0 {
		crawlConfig.MaxRetries = maxRetries
	}
	if concurrency := cfg.GetInt("crawl.concurrency"); concurrency > 0 {
		crawlConfig.Concurrency = concurrency
	}
	if queueName := cfg.GetString("crawl.queue_name"); queueName != "" {
		crawlConfig.QueueName = queueName
	}
	if workerCount := cfg.GetInt("crawl.worker_count"); workerCount > 0 {
		crawlConfig.WorkerCount = workerCount
	}
	if queueWorkerCount := cfg.GetInt("crawl.queue_worker_count"); queueWorkerCount > 0 {
		crawlConfig.QueueWorkerCount = queueWorkerCount
	}
	if storagePath := cfg.GetString("crawl.storage_path"); storagePath != "" {
		crawlConfig.StoragePath = storagePath
	}
	crawlConfig.RespectRobots = cfg.GetBool("crawl.respect_robots")
	crawlConfig.FollowRedirect = cfg.GetBool("crawl.follow_redirect")
	crawlConfig.StorageEnabled = cfg.GetBool("crawl.storage_enabled")

	return crawlConfig, nil
}