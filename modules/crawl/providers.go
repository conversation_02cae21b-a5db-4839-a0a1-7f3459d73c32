package crawl

import (
	"wnapi/modules/crawl/handlers"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/services"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/middleware"
)

// NewCrawlConfig creates crawl configuration
func NewCrawlConfig(cfg *config.Config) (*internal.CrawlConfig, error) {
	return internal.NewCrawlConfigFromAppConfig(*cfg)
}

// NewCrawlJobService creates crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
) *services.CrawlJobService {
	return services.NewCrawlJobService(crawlJobRepo)
}

// NewCrawlArticleService creates crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
) *services.CrawlArticleService {
	return services.NewCrawlArticleService(crawlArticleRepo)
}

// NewCrawlService creates crawl service with Colly
func NewCrawlService(
	crawlJobRepo repository.CrawlJobRepository,
	crawlArticleRepo repository.CrawlArticleRepository,
	crawlConfig *internal.CrawlConfig,
) *services.CrawlService {
	return services.NewCrawlService(crawlJobRepo, crawlArticleRepo, crawlConfig)
}

// NewCrawlJWTService creates JWT service for crawl module
func NewCrawlJWTService(cfg *config.Config) *auth.JWTService {
	jwtConfig := auth.JWTConfig{
		AccessSigningKey:       (*cfg).GetString("JWT_ACCESS_SIGNING_KEY"),
		RefreshSigningKey:      (*cfg).GetString("JWT_REFRESH_SIGNING_KEY"),
		AccessTokenExpiration:  (*cfg).GetDuration("JWT_ACCESS_TOKEN_EXPIRATION"),
		RefreshTokenExpiration: (*cfg).GetDuration("JWT_REFRESH_TOKEN_EXPIRATION"),
		Issuer:                (*cfg).GetString("JWT_ISSUER"),
	}
	return auth.NewJWTService(jwtConfig)
}

// NewCrawlTenantService creates tenant service for crawl module
func NewCrawlTenantService() middleware.TenantService {
	// Return a no-op tenant service for now
	return &middleware.NoOpTenantService{}
}

// NewCrawlHandler creates crawl API handler
func NewCrawlHandler(
	crawlJobService *services.CrawlJobService,
	crawlArticleService *services.CrawlArticleService,
	crawlService *services.CrawlService,
	jwtService *auth.JWTService,
	tenantService middleware.TenantService,
) *handlers.CrawlHandler {
	return handlers.NewCrawlHandler(crawlJobService, crawlArticleService, crawlService)
}