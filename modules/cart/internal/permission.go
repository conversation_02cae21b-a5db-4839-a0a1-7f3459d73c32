package internal

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "cart"

// Carts permissions
const (
	// Standard CRUD permissions for carts
	CreateCartPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + permission.ActionCreate
	ReadCartPermission   = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + permission.ActionRead
	UpdateCartPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteCartPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + permission.ActionDelete
	ListCartPermission   = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + permission.ActionList

	// Cart lifecycle management permissions
	ExpireCartPermission         = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "expire"
	ConvertCartToOrderPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "convert_to_order"
	MergeAnonymousCartPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "merge_anonymous"
	RecalculateCartPermission    = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "recalculate"

	// Cart status management permissions
	ActivateCartPermission = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "activate"
	AbandonCartPermission  = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "abandon"
	RestoreCartPermission  = ModuleName + permission.PermissionSeparator + "carts" + permission.PermissionSeparator + "restore"
)

// Cart Items permissions
const (
	// Standard CRUD permissions for cart items
	CreateCartItemPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + permission.ActionCreate
	ReadCartItemPermission   = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + permission.ActionRead
	UpdateCartItemPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteCartItemPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + permission.ActionDelete
	ListCartItemPermission   = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + permission.ActionList

	// Cart item operations
	AddItemToCartPermission      = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + "add_to_cart"
	RemoveItemFromCartPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + "remove_from_cart"
	UpdateItemQuantityPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + "update_quantity"
	MoveItemToWishlistPermission = ModuleName + permission.PermissionSeparator + "cart_items" + permission.PermissionSeparator + "move_to_wishlist"
)

// Cart Item Addons permissions
const (
	// Standard CRUD permissions for cart item addons
	CreateCartItemAddonPermission = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + permission.ActionCreate
	ReadCartItemAddonPermission   = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + permission.ActionRead
	UpdateCartItemAddonPermission = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteCartItemAddonPermission = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + permission.ActionDelete
	ListCartItemAddonPermission   = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + permission.ActionList

	// Cart item addon operations
	AddAddonToCartItemPermission      = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + "add_addon"
	RemoveAddonFromCartItemPermission = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + "remove_addon"
	UpdateAddonQuantityPermission     = ModuleName + permission.PermissionSeparator + "cart_item_addons" + permission.PermissionSeparator + "update_quantity"
)

// Cart Summary and Pricing permissions
const (
	// Cart summary operations
	ReadCartSummaryPermission    = ModuleName + permission.PermissionSeparator + "cart_summary" + permission.PermissionSeparator + permission.ActionRead
	UpdateCartSummaryPermission  = ModuleName + permission.PermissionSeparator + "cart_summary" + permission.PermissionSeparator + permission.ActionUpdate
	RecalculateSummaryPermission = ModuleName + permission.PermissionSeparator + "cart_summary" + permission.PermissionSeparator + "recalculate"

	// Coupon and discount operations
	ApplyCouponPermission    = ModuleName + permission.PermissionSeparator + "coupons" + permission.PermissionSeparator + "apply"
	RemoveCouponPermission   = ModuleName + permission.PermissionSeparator + "coupons" + permission.PermissionSeparator + "remove"
	ValidateCouponPermission = ModuleName + permission.PermissionSeparator + "coupons" + permission.PermissionSeparator + "validate"
	ApplyDiscountPermission  = ModuleName + permission.PermissionSeparator + "discounts" + permission.PermissionSeparator + "apply"
	RemoveDiscountPermission = ModuleName + permission.PermissionSeparator + "discounts" + permission.PermissionSeparator + "remove"

	// Tax and shipping operations
	CalculateTaxPermission         = ModuleName + permission.PermissionSeparator + "tax" + permission.PermissionSeparator + "calculate"
	CalculateShippingPermission    = ModuleName + permission.PermissionSeparator + "shipping" + permission.PermissionSeparator + "calculate"
	UpdateShippingMethodPermission = ModuleName + permission.PermissionSeparator + "shipping" + permission.PermissionSeparator + "update_method"
)

// Cart Management permissions
const (
	// Bulk operations
	BulkDeleteCartsPermission  = ModuleName + permission.PermissionSeparator + "bulk_operations" + permission.PermissionSeparator + "bulk_delete"
	BulkExpireCartsPermission  = ModuleName + permission.PermissionSeparator + "bulk_operations" + permission.PermissionSeparator + "bulk_expire"
	BulkConvertCartsPermission = ModuleName + permission.PermissionSeparator + "bulk_operations" + permission.PermissionSeparator + "bulk_convert"
	BulkMergeCartsPermission   = ModuleName + permission.PermissionSeparator + "bulk_operations" + permission.PermissionSeparator + "bulk_merge"

	// Cart management operations
	ManageCartPermission            = ModuleName + permission.PermissionSeparator + "management" + permission.PermissionSeparator + permission.ActionManage
	TransferCartOwnershipPermission = ModuleName + permission.PermissionSeparator + "management" + permission.PermissionSeparator + "transfer_ownership"
	CloneCartPermission             = ModuleName + permission.PermissionSeparator + "management" + permission.PermissionSeparator + "clone"
	ArchiveCartPermission           = ModuleName + permission.PermissionSeparator + "management" + permission.PermissionSeparator + "archive"
)

// Cart Analytics and Reporting permissions
const (
	// Analytics permissions
	ReadCartAnalyticsPermission     = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + permission.ActionRead
	ReadCartStatisticsPermission    = ModuleName + permission.PermissionSeparator + "statistics" + permission.PermissionSeparator + permission.ActionRead
	ReadAbandonedCartsPermission    = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "abandoned_carts"
	ReadConversionRatePermission    = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "conversion_rate"
	ReadCartValueAnalysisPermission = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "cart_value_analysis"

	// Reporting permissions
	ExportCartDataPermission     = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "export_data"
	GenerateCartReportPermission = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "generate_report"
	ScheduleCartReportPermission = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "schedule_report"
)

// Cart Configuration permissions
const (
	// Configuration management
	ReadCartConfigPermission   = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + permission.ActionRead
	UpdateCartConfigPermission = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + permission.ActionUpdate
	ManageCartRulesPermission  = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_rules"
	ManageCartLimitsPermission = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_limits"
	ManageCartExpiryPermission = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_expiry"
)

// Full management permission
const (
	ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// GetCartCRUDPermissions trả về tất cả CRUD permissions cho cart management
func GetCartCRUDPermissions() []string {
	return []string{
		CreateCartPermission,
		ReadCartPermission,
		UpdateCartPermission,
		DeleteCartPermission,
		ListCartPermission,
	}
}

// GetCartLifecyclePermissions trả về tất cả lifecycle permissions cho carts
func GetCartLifecyclePermissions() []string {
	return []string{
		ExpireCartPermission,
		ConvertCartToOrderPermission,
		MergeAnonymousCartPermission,
		RecalculateCartPermission,
		ActivateCartPermission,
		AbandonCartPermission,
		RestoreCartPermission,
	}
}

// GetCartItemCRUDPermissions trả về tất cả CRUD permissions cho cart items
func GetCartItemCRUDPermissions() []string {
	return []string{
		CreateCartItemPermission,
		ReadCartItemPermission,
		UpdateCartItemPermission,
		DeleteCartItemPermission,
		ListCartItemPermission,
	}
}

// GetCartItemOperationPermissions trả về tất cả operation permissions cho cart items
func GetCartItemOperationPermissions() []string {
	return []string{
		AddItemToCartPermission,
		RemoveItemFromCartPermission,
		UpdateItemQuantityPermission,
		MoveItemToWishlistPermission,
	}
}

// GetCartItemAddonCRUDPermissions trả về tất cả CRUD permissions cho cart item addons
func GetCartItemAddonCRUDPermissions() []string {
	return []string{
		CreateCartItemAddonPermission,
		ReadCartItemAddonPermission,
		UpdateCartItemAddonPermission,
		DeleteCartItemAddonPermission,
		ListCartItemAddonPermission,
	}
}

// GetCartItemAddonOperationPermissions trả về tất cả operation permissions cho cart item addons
func GetCartItemAddonOperationPermissions() []string {
	return []string{
		AddAddonToCartItemPermission,
		RemoveAddonFromCartItemPermission,
		UpdateAddonQuantityPermission,
	}
}

// GetCartSummaryPermissions trả về tất cả permissions cho cart summary
func GetCartSummaryPermissions() []string {
	return []string{
		ReadCartSummaryPermission,
		UpdateCartSummaryPermission,
		RecalculateSummaryPermission,
	}
}

// GetCouponDiscountPermissions trả về tất cả permissions cho coupons và discounts
func GetCouponDiscountPermissions() []string {
	return []string{
		ApplyCouponPermission,
		RemoveCouponPermission,
		ValidateCouponPermission,
		ApplyDiscountPermission,
		RemoveDiscountPermission,
	}
}

// GetTaxShippingPermissions trả về tất cả permissions cho tax và shipping
func GetTaxShippingPermissions() []string {
	return []string{
		CalculateTaxPermission,
		CalculateShippingPermission,
		UpdateShippingMethodPermission,
	}
}

// GetBulkOperationPermissions trả về tất cả permissions cho bulk operations
func GetBulkOperationPermissions() []string {
	return []string{
		BulkDeleteCartsPermission,
		BulkExpireCartsPermission,
		BulkConvertCartsPermission,
		BulkMergeCartsPermission,
	}
}

// GetCartManagementPermissions trả về tất cả permissions cho cart management
func GetCartManagementPermissions() []string {
	return []string{
		ManageCartPermission,
		TransferCartOwnershipPermission,
		CloneCartPermission,
		ArchiveCartPermission,
	}
}

// GetCartAnalyticsPermissions trả về tất cả permissions cho cart analytics
func GetCartAnalyticsPermissions() []string {
	return []string{
		ReadCartAnalyticsPermission,
		ReadCartStatisticsPermission,
		ReadAbandonedCartsPermission,
		ReadConversionRatePermission,
		ReadCartValueAnalysisPermission,
	}
}

// GetCartReportingPermissions trả về tất cả permissions cho cart reporting
func GetCartReportingPermissions() []string {
	return []string{
		ExportCartDataPermission,
		GenerateCartReportPermission,
		ScheduleCartReportPermission,
	}
}

// GetCartConfigurationPermissions trả về tất cả permissions cho cart configuration
func GetCartConfigurationPermissions() []string {
	return []string{
		ReadCartConfigPermission,
		UpdateCartConfigPermission,
		ManageCartRulesPermission,
		ManageCartLimitsPermission,
		ManageCartExpiryPermission,
	}
}

// GetAllPermissions trả về tất cả permissions của module
func GetAllPermissions() []string {
	permissions := []string{}
	permissions = append(permissions, GetCartCRUDPermissions()...)
	permissions = append(permissions, GetCartLifecyclePermissions()...)
	permissions = append(permissions, GetCartItemCRUDPermissions()...)
	permissions = append(permissions, GetCartItemOperationPermissions()...)
	permissions = append(permissions, GetCartItemAddonCRUDPermissions()...)
	permissions = append(permissions, GetCartItemAddonOperationPermissions()...)
	permissions = append(permissions, GetCartSummaryPermissions()...)
	permissions = append(permissions, GetCouponDiscountPermissions()...)
	permissions = append(permissions, GetTaxShippingPermissions()...)
	permissions = append(permissions, GetBulkOperationPermissions()...)
	permissions = append(permissions, GetCartManagementPermissions()...)
	permissions = append(permissions, GetCartAnalyticsPermissions()...)
	permissions = append(permissions, GetCartReportingPermissions()...)
	permissions = append(permissions, GetCartConfigurationPermissions()...)
	permissions = append(permissions, ManagePermission)
	return permissions
}
