package models

import (
	"time"
)

// AdsBanner status constants
const (
	ADS_BANNER_STATUS_PENDING  = "pending"
	ADS_BANNER_STATUS_ACTIVE   = "active"
	ADS_BANNER_STATUS_INACTIVE = "inactive"
	ADS_BANNER_STATUS_EXPIRED  = "expired"
)

// AdsBanner platform constants
const (
	ADS_BANNER_PLATFORM_WEB    = "web"
	ADS_BANNER_PLATFORM_MOBILE = "mobile"
	ADS_BANNER_PLATFORM_ALL    = "all"
)

type AdsBanner struct {
	ID           uint       `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID     uint       `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id;not null;index"`
	WebsiteID    uint       `db:"website_id" json:"website_id" gorm:"column:website_id;not null;index"`
	Name         string     `db:"name" json:"name" gorm:"column:name;not null"`
	Type         string     `db:"type" json:"type" gorm:"column:type;not null"`
	Position     string     `db:"position" json:"position" gorm:"column:position;not null"`
	Image        *string    `db:"image" json:"image,omitempty" gorm:"column:image"`
	Link         *string    `db:"link" json:"link,omitempty" gorm:"column:link"`
	Content      *string    `db:"content" json:"content,omitempty" gorm:"column:content"`
	OpenInNewTab bool       `db:"open_in_new_tab" json:"open_in_new_tab" gorm:"column:open_in_new_tab;default:false"`
	Platform     string     `db:"platform" json:"platform" gorm:"column:platform;not null"`
	FromDate     *time.Time `db:"from_date" json:"from_date,omitempty" gorm:"column:from_date"`
	ToDate       *time.Time `db:"to_date" json:"to_date,omitempty" gorm:"column:to_date"`
	SortOrder    int        `db:"sort_order" json:"sort_order" gorm:"column:sort_order;default:0"`
	Status       string     `db:"status" json:"status" gorm:"column:status;default:pending"`
	CreatedAt    time.Time  `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt    time.Time  `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`
	CreatedBy    *uint      `db:"created_by" json:"created_by,omitempty" gorm:"column:created_by"`
	UpdatedBy    *uint      `db:"updated_by" json:"updated_by,omitempty" gorm:"column:updated_by"`
}

// TableName returns the table name for GORM
func (AdsBanner) TableName() string {
	return "ads_banners"
}

// IsActive checks if the ads banner is active
func (ab *AdsBanner) IsActive() bool {
	return ab.Status == ADS_BANNER_STATUS_ACTIVE
}

// IsExpired checks if the ads banner is expired based on current time
func (ab *AdsBanner) IsExpired() bool {
	now := time.Now()
	if ab.ToDate != nil && ab.ToDate.Before(now) {
		return true
	}
	return false
}

// IsScheduled checks if the ads banner is scheduled for future
func (ab *AdsBanner) IsScheduled() bool {
	now := time.Now()
	if ab.FromDate != nil && ab.FromDate.After(now) {
		return true
	}
	return false
}
