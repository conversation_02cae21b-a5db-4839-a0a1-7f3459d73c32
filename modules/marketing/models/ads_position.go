package models

import (
	"time"
)

// AdsPosition status constants
const (
	ADS_POSITION_STATUS_ACTIVE   = "active"
	ADS_POSITION_STATUS_INACTIVE = "inactive"
)

type AdsPosition struct {
	ID          uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID    uint      `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id;not null;index"`
	WebsiteID   uint      `db:"website_id" json:"website_id" gorm:"column:website_id;not null;index"`
	Name        string    `db:"name" json:"name" gorm:"column:name;not null"`
	Code        string    `db:"code" json:"code" gorm:"column:code;not null"`
	Description *string   `db:"description" json:"description,omitempty" gorm:"column:description"`
	Status      string    `db:"status" json:"status" gorm:"column:status;default:active"`
	CreatedAt   time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`
	CreatedBy   *uint     `db:"created_by" json:"created_by,omitempty" gorm:"column:created_by"`
	UpdatedBy   *uint     `db:"updated_by" json:"updated_by,omitempty" gorm:"column:updated_by"`
}

// TableName returns the table name for GORM
func (AdsPosition) TableName() string {
	return "ads_positions"
}

// IsActive checks if the ads position is active
func (ap *AdsPosition) IsActive() bool {
	return ap.Status == ADS_POSITION_STATUS_ACTIVE
}
