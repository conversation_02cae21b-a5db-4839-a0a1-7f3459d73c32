package service

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/marketing/dto/request"
	"wnapi/modules/marketing/dto/response"
	"wnapi/modules/marketing/models"
	"wnapi/modules/marketing/repository"
)

// AdsBannerService defines the interface for ads banner service
type AdsBannerService interface {
	// CreateAdsBanner creates a new ads banner
	CreateAdsBanner(ctx context.Context, tenantID, websiteID, userID uint, req *request.CreateAdsBannerRequest) (*response.AdsBannerResponse, error)

	// GetAdsBanner retrieves an ads banner by ID
	GetAdsBanner(ctx context.Context, tenantID, websiteID, id uint) (*response.AdsBannerResponse, error)

	// UpdateAdsBanner updates an ads banner
	UpdateAdsBanner(ctx context.Context, tenantID, websiteID, id, userID uint, req *request.UpdateAdsBannerRequest) (*response.AdsBannerResponse, error)

	// DeleteAdsBanner deletes an ads banner
	DeleteAdsBanner(ctx context.Context, tenantID, websiteID, id uint) error

	// ListAdsBanners retrieves ads banners with pagination and filters
	ListAdsBanners(ctx context.Context, tenantID, websiteID uint, req *request.ListAdsBannersRequest) (*response.AdsBannerListResponse, error)

	// GetActiveBanners retrieves active ads banners for a specific position and platform
	GetActiveBanners(ctx context.Context, tenantID, websiteID uint, position, platform string) ([]response.AdsBannerPublicResponse, error)

	// GetBannersByPosition retrieves banners by position
	GetBannersByPosition(ctx context.Context, tenantID, websiteID uint, position string) ([]response.AdsBannerResponse, error)

	// UpdateBannerStatus updates banner status
	UpdateBannerStatus(ctx context.Context, tenantID, websiteID, id uint, status string) error

	// ProcessExpiredBanners processes expired banners and updates their status
	ProcessExpiredBanners(ctx context.Context, tenantID, websiteID uint) error
}

// adsBannerService implements the AdsBannerService interface
type adsBannerService struct {
	adsBannerRepo repository.AdsBannerRepository
	logger        logger.Logger
}

// NewAdsBannerService creates a new ads banner service
func NewAdsBannerService(
	adsBannerRepo repository.AdsBannerRepository,
	logger logger.Logger,
) AdsBannerService {
	return &adsBannerService{
		adsBannerRepo: adsBannerRepo,
		logger:        logger,
	}
}

// CreateAdsBanner creates a new ads banner
func (s *adsBannerService) CreateAdsBanner(ctx context.Context, tenantID, websiteID, userID uint, req *request.CreateAdsBannerRequest) (*response.AdsBannerResponse, error) {
	// Convert request to model
	banner := req.ToModel(tenantID, websiteID, userID)

	// Validate dates
	if banner.FromDate != nil && banner.ToDate != nil && banner.FromDate.After(*banner.ToDate) {
		return nil, fmt.Errorf("from_date cannot be after to_date")
	}

	// Create ads banner
	err := s.adsBannerRepo.Create(ctx, banner)
	if err != nil {
		s.logger.Error("Failed to create ads banner", "error", err)
		return nil, fmt.Errorf("failed to create ads banner: %w", err)
	}

	// Convert to response
	var resp response.AdsBannerResponse
	resp.FromModel(banner)

	s.logger.Info("Ads banner created successfully", "id", banner.ID, "name", banner.Name)
	return &resp, nil
}

// GetAdsBanner retrieves an ads banner by ID
func (s *adsBannerService) GetAdsBanner(ctx context.Context, tenantID, websiteID, id uint) (*response.AdsBannerResponse, error) {
	banner, err := s.adsBannerRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("ads banner not found")
		}
		s.logger.Error("Failed to get ads banner", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get ads banner: %w", err)
	}

	var resp response.AdsBannerResponse
	resp.FromModel(banner)
	return &resp, nil
}

// UpdateAdsBanner updates an ads banner
func (s *adsBannerService) UpdateAdsBanner(ctx context.Context, tenantID, websiteID, id, userID uint, req *request.UpdateAdsBannerRequest) (*response.AdsBannerResponse, error) {
	// Get existing banner
	banner, err := s.adsBannerRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("ads banner not found")
		}
		s.logger.Error("Failed to get ads banner for update", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get ads banner: %w", err)
	}

	// Update fields
	if req.Name != nil {
		banner.Name = *req.Name
	}
	if req.Type != nil {
		banner.Type = *req.Type
	}
	if req.Position != nil {
		banner.Position = *req.Position
	}
	if req.Image != nil {
		banner.Image = req.Image
	}
	if req.Link != nil {
		banner.Link = req.Link
	}
	if req.Content != nil {
		banner.Content = req.Content
	}
	if req.OpenInNewTab != nil {
		banner.OpenInNewTab = *req.OpenInNewTab
	}
	if req.Platform != nil {
		banner.Platform = *req.Platform
	}
	if req.FromDate != nil {
		banner.FromDate = req.FromDate
	}
	if req.ToDate != nil {
		banner.ToDate = req.ToDate
	}
	if req.SortOrder != nil {
		banner.SortOrder = *req.SortOrder
	}
	if req.Status != nil {
		banner.Status = *req.Status
	}
	banner.UpdatedBy = &userID

	// Validate dates
	if banner.FromDate != nil && banner.ToDate != nil && banner.FromDate.After(*banner.ToDate) {
		return nil, fmt.Errorf("from_date cannot be after to_date")
	}

	// Update ads banner
	err = s.adsBannerRepo.Update(ctx, banner)
	if err != nil {
		s.logger.Error("Failed to update ads banner", "error", err, "id", id)
		return nil, fmt.Errorf("failed to update ads banner: %w", err)
	}

	// Convert to response
	var resp response.AdsBannerResponse
	resp.FromModel(banner)

	s.logger.Info("Ads banner updated successfully", "id", banner.ID)
	return &resp, nil
}

// DeleteAdsBanner deletes an ads banner
func (s *adsBannerService) DeleteAdsBanner(ctx context.Context, tenantID, websiteID, id uint) error {
	// Check if banner exists
	_, err := s.adsBannerRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("ads banner not found")
		}
		s.logger.Error("Failed to get ads banner for deletion", "error", err, "id", id)
		return fmt.Errorf("failed to get ads banner: %w", err)
	}

	// Delete ads banner
	err = s.adsBannerRepo.Delete(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to delete ads banner", "error", err, "id", id)
		return fmt.Errorf("failed to delete ads banner: %w", err)
	}

	s.logger.Info("Ads banner deleted successfully", "id", id)
	return nil
}

// ListAdsBanners retrieves ads banners with cursor pagination and filters
func (s *adsBannerService) ListAdsBanners(ctx context.Context, tenantID, websiteID uint, req *request.ListAdsBannersRequest) (*response.AdsBannerListResponse, error) {
	// Set default values
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// Build filters
	filters := repository.ListAdsBannerFilters{
		Cursor:   req.Cursor,
		Limit:    req.Limit,
		Status:   req.Status,
		Platform: req.Platform,
		Position: req.Position,
		Type:     req.Type,
		Search:   req.Search,
		SortBy:   "sort_order",
		SortDesc: false,
	}

	if req.SortBy != nil {
		filters.SortBy = *req.SortBy
	}
	if req.SortDesc != nil {
		filters.SortDesc = *req.SortDesc
	}

	// Get banners
	banners, nextCursor, hasMore, err := s.adsBannerRepo.List(ctx, tenantID, filters)
	if err != nil {
		s.logger.Error("Failed to list ads banners", "error", err)
		return nil, fmt.Errorf("failed to list ads banners: %w", err)
	}

	// Convert to response
	data := response.FromAdsBannerModels(banners)

	return &response.AdsBannerListResponse{
		Data: data,
		Meta: response.CursorMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// GetActiveBanners retrieves active ads banners for a specific position and platform
func (s *adsBannerService) GetActiveBanners(ctx context.Context, tenantID, websiteID uint, position, platform string) ([]response.AdsBannerPublicResponse, error) {
	banners, err := s.adsBannerRepo.GetActiveBanners(ctx, tenantID, position, platform)
	if err != nil {
		s.logger.Error("Failed to get active ads banners", "error", err, "position", position, "platform", platform)
		return nil, fmt.Errorf("failed to get active ads banners: %w", err)
	}

	return response.FromAdsBannerModelsPublic(banners), nil
}

// GetBannersByPosition retrieves banners by position
func (s *adsBannerService) GetBannersByPosition(ctx context.Context, tenantID, websiteID uint, position string) ([]response.AdsBannerResponse, error) {
	banners, err := s.adsBannerRepo.GetBannersByPosition(ctx, tenantID, position)
	if err != nil {
		s.logger.Error("Failed to get banners by position", "error", err, "position", position)
		return nil, fmt.Errorf("failed to get banners by position: %w", err)
	}

	return response.FromAdsBannerModels(banners), nil
}

// UpdateBannerStatus updates banner status
func (s *adsBannerService) UpdateBannerStatus(ctx context.Context, tenantID, websiteID, id uint, status string) error {
	// Check if banner exists
	_, err := s.adsBannerRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("ads banner not found")
		}
		s.logger.Error("Failed to get ads banner for status update", "error", err, "id", id)
		return fmt.Errorf("failed to get ads banner: %w", err)
	}

	// Update status
	err = s.adsBannerRepo.UpdateStatus(ctx, tenantID, id, status)
	if err != nil {
		s.logger.Error("Failed to update banner status", "error", err, "id", id, "status", status)
		return fmt.Errorf("failed to update banner status: %w", err)
	}

	s.logger.Info("Banner status updated successfully", "id", id, "status", status)
	return nil
}

// ProcessExpiredBanners processes expired banners and updates their status
func (s *adsBannerService) ProcessExpiredBanners(ctx context.Context, tenantID, websiteID uint) error {
	expiredBanners, err := s.adsBannerRepo.GetExpiredBanners(ctx, tenantID)
	if err != nil {
		s.logger.Error("Failed to get expired banners", "error", err)
		return fmt.Errorf("failed to get expired banners: %w", err)
	}

	for _, banner := range expiredBanners {
		err = s.adsBannerRepo.UpdateStatus(ctx, tenantID, banner.ID, models.ADS_BANNER_STATUS_EXPIRED)
		if err != nil {
			s.logger.Error("Failed to update expired banner status", "error", err, "id", banner.ID)
			continue
		}
		s.logger.Info("Banner marked as expired", "id", banner.ID, "name", banner.Name)
	}

	s.logger.Info("Processed expired banners", "count", len(expiredBanners))
	return nil
}
