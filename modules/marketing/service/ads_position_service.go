package service

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/marketing/dto/request"
	"wnapi/modules/marketing/dto/response"
	"wnapi/modules/marketing/repository"
)

// AdsPositionService defines the interface for ads position service
type AdsPositionService interface {
	// CreateAdsPosition creates a new ads position
	CreateAdsPosition(ctx context.Context, tenantID, websiteID, userID uint, req *request.CreateAdsPositionRequest) (*response.AdsPositionResponse, error)

	// GetAdsPosition retrieves an ads position by ID
	GetAdsPosition(ctx context.Context, tenantID, websiteID, id uint) (*response.AdsPositionResponse, error)

	// UpdateAdsPosition updates an ads position
	UpdateAdsPosition(ctx context.Context, tenantID, websiteID, id, userID uint, req *request.UpdateAdsPositionRequest) (*response.AdsPositionResponse, error)

	// DeleteAdsPosition deletes an ads position
	DeleteAdsPosition(ctx context.Context, tenantID, websiteID, id uint) error

	// ListAdsPositions retrieves ads positions with pagination and filters
	ListAdsPositions(ctx context.Context, tenantID, websiteID uint, req *request.ListAdsPositionsRequest) (*response.AdsPositionListResponse, error)

	// GetActivePositions retrieves all active ads positions
	GetActivePositions(ctx context.Context, tenantID, websiteID uint) ([]response.AdsPositionResponse, error)
}

// adsPositionService implements the AdsPositionService interface
type adsPositionService struct {
	adsPositionRepo repository.AdsPositionRepository
	logger          logger.Logger
}

// NewAdsPositionService creates a new ads position service
func NewAdsPositionService(
	adsPositionRepo repository.AdsPositionRepository,
	logger logger.Logger,
) AdsPositionService {
	return &adsPositionService{
		adsPositionRepo: adsPositionRepo,
		logger:          logger,
	}
}

// CreateAdsPosition creates a new ads position
func (s *adsPositionService) CreateAdsPosition(ctx context.Context, tenantID, websiteID, userID uint, req *request.CreateAdsPositionRequest) (*response.AdsPositionResponse, error) {
	// Check if code already exists
	existingPosition, err := s.adsPositionRepo.GetByCode(ctx, tenantID, req.Code)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		s.logger.Error("Failed to check existing ads position", "error", err)
		return nil, fmt.Errorf("failed to check existing ads position: %w", err)
	}
	if existingPosition != nil {
		return nil, fmt.Errorf("ads position with code '%s' already exists", req.Code)
	}

	// Convert request to model
	position := req.ToModel(tenantID, websiteID, userID)

	// Create ads position
	err = s.adsPositionRepo.Create(ctx, position)
	if err != nil {
		s.logger.Error("Failed to create ads position", "error", err)
		return nil, fmt.Errorf("failed to create ads position: %w", err)
	}

	// Convert to response
	var resp response.AdsPositionResponse
	resp.FromModel(position)

	s.logger.Info("Ads position created successfully", "id", position.ID, "code", position.Code)
	return &resp, nil
}

// GetAdsPosition retrieves an ads position by ID
func (s *adsPositionService) GetAdsPosition(ctx context.Context, tenantID, websiteID, id uint) (*response.AdsPositionResponse, error) {
	position, err := s.adsPositionRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("ads position not found")
		}
		s.logger.Error("Failed to get ads position", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get ads position: %w", err)
	}

	var resp response.AdsPositionResponse
	resp.FromModel(position)
	return &resp, nil
}

// UpdateAdsPosition updates an ads position
func (s *adsPositionService) UpdateAdsPosition(ctx context.Context, tenantID, websiteID, id, userID uint, req *request.UpdateAdsPositionRequest) (*response.AdsPositionResponse, error) {
	// Get existing position
	position, err := s.adsPositionRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("ads position not found")
		}
		s.logger.Error("Failed to get ads position for update", "error", err, "id", id)
		return nil, fmt.Errorf("failed to get ads position: %w", err)
	}

	// Check if code is being updated and already exists
	if req.Code != nil && *req.Code != position.Code {
		existingPosition, err := s.adsPositionRepo.GetByCode(ctx, tenantID, *req.Code)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			s.logger.Error("Failed to check existing ads position code", "error", err)
			return nil, fmt.Errorf("failed to check existing ads position code: %w", err)
		}
		if existingPosition != nil {
			return nil, fmt.Errorf("ads position with code '%s' already exists", *req.Code)
		}
	}

	// Update fields
	if req.Name != nil {
		position.Name = *req.Name
	}
	if req.Code != nil {
		position.Code = *req.Code
	}
	if req.Description != nil {
		position.Description = req.Description
	}
	if req.Status != nil {
		position.Status = *req.Status
	}
	position.UpdatedBy = &userID

	// Update ads position
	err = s.adsPositionRepo.Update(ctx, position)
	if err != nil {
		s.logger.Error("Failed to update ads position", "error", err, "id", id)
		return nil, fmt.Errorf("failed to update ads position: %w", err)
	}

	// Convert to response
	var resp response.AdsPositionResponse
	resp.FromModel(position)

	s.logger.Info("Ads position updated successfully", "id", position.ID)
	return &resp, nil
}

// DeleteAdsPosition deletes an ads position
func (s *adsPositionService) DeleteAdsPosition(ctx context.Context, tenantID, websiteID, id uint) error {
	// Check if position exists
	_, err := s.adsPositionRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("ads position not found")
		}
		s.logger.Error("Failed to get ads position for deletion", "error", err, "id", id)
		return fmt.Errorf("failed to get ads position: %w", err)
	}

	// Delete ads position
	err = s.adsPositionRepo.Delete(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to delete ads position", "error", err, "id", id)
		return fmt.Errorf("failed to delete ads position: %w", err)
	}

	s.logger.Info("Ads position deleted successfully", "id", id)
	return nil
}

// ListAdsPositions retrieves ads positions with cursor pagination and filters
func (s *adsPositionService) ListAdsPositions(ctx context.Context, tenantID, websiteID uint, req *request.ListAdsPositionsRequest) (*response.AdsPositionListResponse, error) {
	// Set default values
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// Build filters
	filters := repository.ListAdsPositionFilters{
		Cursor:   req.Cursor,
		Limit:    req.Limit,
		Status:   req.Status,
		Search:   req.Search,
		SortBy:   "created_at",
		SortDesc: true,
	}

	if req.SortBy != nil {
		filters.SortBy = *req.SortBy
	}
	if req.SortDesc != nil {
		filters.SortDesc = *req.SortDesc
	}

	// Get positions
	positions, nextCursor, hasMore, err := s.adsPositionRepo.List(ctx, tenantID, filters)
	if err != nil {
		s.logger.Error("Failed to list ads positions", "error", err)
		return nil, fmt.Errorf("failed to list ads positions: %w", err)
	}

	// Convert to response
	data := response.FromAdsPositionModels(positions)

	return &response.AdsPositionListResponse{
		Data: data,
		Meta: response.CursorMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// GetActivePositions retrieves all active ads positions
func (s *adsPositionService) GetActivePositions(ctx context.Context, tenantID, websiteID uint) ([]response.AdsPositionResponse, error) {
	positions, err := s.adsPositionRepo.GetActivePositions(ctx, tenantID)
	if err != nil {
		s.logger.Error("Failed to get active ads positions", "error", err)
		return nil, fmt.Errorf("failed to get active ads positions: %w", err)
	}

	return response.FromAdsPositionModels(positions), nil
}
