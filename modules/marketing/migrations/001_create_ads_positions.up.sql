CREATE TABLE ads_positions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    code VA<PERSON>HAR(255) NOT NULL,
    description VARCHAR(500),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED,
    updated_by INT UNSIGNED,
    UNIQUE KEY unique_ads_position_code_tenant_website (tenant_id, website_id, code),
    INDEX idx_ads_position_status (status),
    INDEX idx_ads_position_tenant (tenant_id),
    INDEX idx_ads_position_website (website_id),
    INDEX idx_ads_position_tenant_website (tenant_id, website_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;