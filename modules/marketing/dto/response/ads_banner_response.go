package response

import (
	"time"
	"wnapi/modules/marketing/models"
)

// AdsBannerResponse represents the response for ads banner
type AdsBannerResponse struct {
	ID           uint       `json:"id" example:"1"`
	TenantID     uint       `json:"tenant_id" example:"1"`
	Name         string     `json:"name" example:"Summer Sale Banner"`
	Type         string     `json:"type" example:"image"`
	Position     string     `json:"position" example:"header"`
	Image        *string    `json:"image,omitempty" example:"/uploads/banner.jpg"`
	Link         *string    `json:"link,omitempty" example:"https://example.com/sale"`
	Content      *string    `json:"content,omitempty" example:"<h1>Summer Sale 50% Off</h1>"`
	OpenInNewTab bool       `json:"open_in_new_tab" example:"true"`
	Platform     string     `json:"platform" example:"web"`
	FromDate     *time.Time `json:"from_date,omitempty" example:"2024-01-01T00:00:00Z"`
	ToDate       *time.Time `json:"to_date,omitempty" example:"2024-12-31T23:59:59Z"`
	SortOrder    int        `json:"sort_order" example:"1"`
	Status       string     `json:"status" example:"active"`
	CreatedAt    time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt    time.Time  `json:"updated_at" example:"2024-01-01T00:00:00Z"`
	CreatedBy    *uint      `json:"created_by,omitempty" example:"1"`
	UpdatedBy    *uint      `json:"updated_by,omitempty" example:"1"`
}

// AdsBannerListResponse represents the response for listing ads banners
type AdsBannerListResponse struct {
	Data []AdsBannerResponse `json:"data"`
	Meta CursorMeta          `json:"meta"`
}

// AdsBannerPublicResponse represents the public response for ads banner (for frontend display)
type AdsBannerPublicResponse struct {
	ID           uint       `json:"id" example:"1"`
	Name         string     `json:"name" example:"Summer Sale Banner"`
	Type         string     `json:"type" example:"image"`
	Position     string     `json:"position" example:"header"`
	Image        *string    `json:"image,omitempty" example:"/uploads/banner.jpg"`
	Link         *string    `json:"link,omitempty" example:"https://example.com/sale"`
	Content      *string    `json:"content,omitempty" example:"<h1>Summer Sale 50% Off</h1>"`
	OpenInNewTab bool       `json:"open_in_new_tab" example:"true"`
	Platform     string     `json:"platform" example:"web"`
	SortOrder    int        `json:"sort_order" example:"1"`
}

// FromModel converts AdsBanner model to AdsBannerResponse
func (r *AdsBannerResponse) FromModel(banner *models.AdsBanner) {
	r.ID = banner.ID
	r.TenantID = banner.TenantID
	r.Name = banner.Name
	r.Type = banner.Type
	r.Position = banner.Position
	r.Image = banner.Image
	r.Link = banner.Link
	r.Content = banner.Content
	r.OpenInNewTab = banner.OpenInNewTab
	r.Platform = banner.Platform
	r.FromDate = banner.FromDate
	r.ToDate = banner.ToDate
	r.SortOrder = banner.SortOrder
	r.Status = banner.Status
	r.CreatedAt = banner.CreatedAt
	r.UpdatedAt = banner.UpdatedAt
	r.CreatedBy = banner.CreatedBy
	r.UpdatedBy = banner.UpdatedBy
}

// FromModelPublic converts AdsBanner model to AdsBannerPublicResponse
func (r *AdsBannerPublicResponse) FromModel(banner *models.AdsBanner) {
	r.ID = banner.ID
	r.Name = banner.Name
	r.Type = banner.Type
	r.Position = banner.Position
	r.Image = banner.Image
	r.Link = banner.Link
	r.Content = banner.Content
	r.OpenInNewTab = banner.OpenInNewTab
	r.Platform = banner.Platform
	r.SortOrder = banner.SortOrder
}

// FromAdsBannerModels converts slice of AdsBanner models to slice of AdsBannerResponse
func FromAdsBannerModels(banners []models.AdsBanner) []AdsBannerResponse {
	responses := make([]AdsBannerResponse, len(banners))
	for i, banner := range banners {
		responses[i].FromModel(&banner)
	}
	return responses
}

// FromAdsBannerModelsPublic converts slice of AdsBanner models to slice of AdsBannerPublicResponse
func FromAdsBannerModelsPublic(banners []models.AdsBanner) []AdsBannerPublicResponse {
	responses := make([]AdsBannerPublicResponse, len(banners))
	for i, banner := range banners {
		responses[i].FromModel(&banner)
	}
	return responses
}