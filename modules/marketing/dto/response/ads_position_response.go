package response

import (
	"time"
	"wnapi/modules/marketing/models"
)

// AdsPositionResponse represents the response for ads position
type AdsPositionResponse struct {
	ID          uint      `json:"id" example:"1"`
	TenantID    uint      `json:"tenant_id" example:"1"`
	Name        string    `json:"name" example:"Header Banner"`
	Code        string    `json:"code" example:"header_banner"`
	Description *string   `json:"description,omitempty" example:"Banner position at the top of the page"`
	Status      string    `json:"status" example:"active"`
	CreatedAt   time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2024-01-01T00:00:00Z"`
	CreatedBy   *uint     `json:"created_by,omitempty" example:"1"`
	UpdatedBy   *uint     `json:"updated_by,omitempty" example:"1"`
}

// AdsPositionListResponse represents the response for listing ads positions
type AdsPositionListResponse struct {
	Data []AdsPositionResponse `json:"data"`
	Meta CursorMeta            `json:"meta"`
}

// CursorMeta represents cursor pagination metadata
type CursorMeta struct {
	NextCursor string `json:"next_cursor,omitempty" example:"eyJpZCI6MTAsImRpcmVjdGlvbiI6Im5leHQifQ=="`
	HasMore    bool   `json:"has_more" example:"true"`
}

// PaginationResponse represents pagination information (deprecated, use CursorMeta instead)
type PaginationResponse struct {
	Page       int   `json:"page" example:"1"`
	Limit      int   `json:"limit" example:"10"`
	Total      int64 `json:"total" example:"100"`
	TotalPages int   `json:"total_pages" example:"10"`
}

// FromModel converts AdsPosition model to AdsPositionResponse
func (r *AdsPositionResponse) FromModel(position *models.AdsPosition) {
	r.ID = position.ID
	r.TenantID = position.TenantID
	r.Name = position.Name
	r.Code = position.Code
	r.Description = position.Description
	r.Status = position.Status
	r.CreatedAt = position.CreatedAt
	r.UpdatedAt = position.UpdatedAt
	r.CreatedBy = position.CreatedBy
	r.UpdatedBy = position.UpdatedBy
}

// FromModels converts slice of AdsPosition models to slice of AdsPositionResponse
func FromAdsPositionModels(positions []models.AdsPosition) []AdsPositionResponse {
	responses := make([]AdsPositionResponse, len(positions))
	for i, position := range positions {
		responses[i].FromModel(&position)
	}
	return responses
}