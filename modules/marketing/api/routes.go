package api

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/tracing"
	"wnapi/modules/marketing/api/handlers"
)

// RegisterMarketingRoutes registers all marketing module routes
func RegisterMarketingRoutes(
	engine *gin.Engine,
	adsPositionHandler *handlers.AdsPositionHandler,
	adsBannerHandler *handlers.AdsBannerHandler,
	authMiddleware gin.HandlerFunc,
	permissionMiddleware gin.HandlerFunc,
) {
	// ===== ADMIN API GROUP =====
	adminApiGroup := engine.Group("/api/admin/v1/marketing")
	adminApiGroup.Use(tracing.GinMiddleware("marketing"))

	// Health check for admin
	adminApiGroup.GET("/healthy", healthCheck)

	// Protected routes - require authentication
	protected := adminApiGroup.Group("/")
	if authMiddleware != nil {
		protected.Use(authMiddleware)
	}
	{
		// ===== ADS POSITIONS MANAGEMENT =====
		adsPositionsGroup := protected.Group("/ads-positions")
		{
			// List ads positions
			adsPositionsGroup.GET("",
				// permissionMiddleware.RequirePermission("marketing.ads_positions.read"),
				adsPositionHandler.ListAdsPositions,
			)

			// Get single ads position
			adsPositionsGroup.GET("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_positions.read"),
				adsPositionHandler.GetAdsPosition,
			)

			// Create ads position
			adsPositionsGroup.POST("",
				// permissionMiddleware.RequirePermission("marketing.ads_positions.create"),
				adsPositionHandler.CreateAdsPosition,
			)

			// Update ads position
			adsPositionsGroup.PUT("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_positions.update"),
				adsPositionHandler.UpdateAdsPosition,
			)

			// Delete ads position
			adsPositionsGroup.DELETE("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_positions.delete"),
				adsPositionHandler.DeleteAdsPosition,
			)
		}

		// ===== ADS BANNERS MANAGEMENT =====
		adsBannersGroup := protected.Group("/ads-banners")
		{
			// List ads banners
			adsBannersGroup.GET("",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.read"),
				adsBannerHandler.ListAdsBanners,
			)

			// Get single ads banner
			adsBannersGroup.GET("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.read"),
				adsBannerHandler.GetAdsBanner,
			)

			// Create ads banner
			adsBannersGroup.POST("",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.create"),
				adsBannerHandler.CreateAdsBanner,
			)

			// Update ads banner
			adsBannersGroup.PUT("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.update"),
				adsBannerHandler.UpdateAdsBanner,
			)

			// Delete ads banner
			adsBannersGroup.DELETE("/:id",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.delete"),
				adsBannerHandler.DeleteAdsBanner,
			)

			// Update banner status
			adsBannersGroup.PATCH("/:id/status",
				// permissionMiddleware.RequirePermission("marketing.ads_banners.update"),
				adsBannerHandler.UpdateBannerStatus,
			)
		}
	}

	// ===== PUBLIC API GROUP =====
	// Public API group for frontend consumption
	publicApiGroup := engine.Group("/api/v1/public/marketing")
	publicApiGroup.Use(tracing.GinMiddleware("marketing-public"))

	// Health check for public
	publicApiGroup.GET("/healthy", healthCheck)

	// Public routes
	publicApiGroup.GET("/ads-positions/active", adsPositionHandler.GetActivePositions)
	publicApiGroup.GET("/ads-banners/active", adsBannerHandler.GetActiveBanners)
	publicApiGroup.GET("/ads-banners/position/:position", adsBannerHandler.GetBannersByPosition)
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "marketing",
		"message": "Marketing module is running",
	})
}