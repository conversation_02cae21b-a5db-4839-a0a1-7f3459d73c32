package mysql

import (
	"context"
	"wnapi/internal/pkg/tracing"
)

// IsUserMemberOfTenant kiểm tra user có thuộc tenant không
func (r *tenantRepository) IsUserMemberOfTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	return tracing.WithSpanResult(ctx, "tenant-repository", "is-user-member-of-tenant", func(ctx context.Context) (bool, error) {
		var count int64
		err := r.db.WithContext(ctx).
			Table("user_tenants"). // <PERSON><PERSON><PERSON> đ<PERSON>nh tên bảng là user_tenants
			Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, "active").
			Count(&count).Error

		if err != nil {
			r.logger.Error("Failed to check user-tenant membership",
				"error", err.<PERSON>rror(),
				"user_id", userID,
				"tenant_id", tenantID)
			return false, err
		}

		return count > 0, nil
	})
}