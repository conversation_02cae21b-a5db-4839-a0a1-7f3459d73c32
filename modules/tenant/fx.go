package tenant

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/middleware"
	"wnapi/modules/tenant/repository"
	"wnapi/modules/tenant/repository/mysql"
	"wnapi/modules/tenant/service"
)

// TenantModule implements FXModule interface
type TenantModule struct{}

// Name returns module name
func (m *TenantModule) Name() string {
	return "tenant"
}

// Dependencies returns module dependencies
func (m *TenantModule) Dependencies() []string {
	return []string{} // No dependencies - loads first
}

// Priority returns loading priority
func (m *TenantModule) Priority() int {
	return 1 // Highest priority - load first
}

// Enabled checks if module should be loaded
func (m *TenantModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// GetMigrationPath returns path to module migrations
func (m *TenantModule) GetMigrationPath() string {
	return "modules/tenant/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *TenantModule) GetMigrationOrder() int {
	return 1 // Tenant module runs first
}

// Module returns fx.Module for tenant
func (m *TenantModule) Module() fx.Option {
	return fx.Module("tenant",
		// Providers
		fx.Provide(
			// Configuration
			NewTenantConfig,

			// Repositories
			fx.Annotate(
				mysql.NewTenantRepository,
				fx.As(new(repository.TenantRepository)),
			),

			// Services
			fx.Annotate(
				NewTenantService,
				fx.As(new(service.TenantService)),
			),
			fx.Annotate(
				NewMiddlewareTenantService,
				fx.As(new(middleware.TenantService)),
			),

			// Handlers
			NewTenantHandler,
		),

		// Route registration
		fx.Invoke(RegisterTenantRoutes),
	)
}

// Register tenant module with global registry
func init() {
	modules.RegisterModule(&TenantModule{})
}
