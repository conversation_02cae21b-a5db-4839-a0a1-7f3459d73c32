package tenant

import (
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/tenant/api"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/repository"
	"wnapi/modules/tenant/service"

	"github.com/gin-gonic/gin"
)

// NewTenantConfig creates tenant configuration
func NewTenantConfig(cfg config.Config) *internal.TenantConfig {
	return &internal.TenantConfig{
		MaxTenantsPerUser: cfg.GetIntWithDefault("TENANT_MAX_PER_USER", 5),
		DefaultPlan:       cfg.GetStringWithDefault("TENANT_DEFAULT_PLAN", "standard"),
		Message:           cfg.GetStringWithDefault("MESSAGE", "Hello from Tenant module!"),
	}
}

// NewTenantService creates tenant service with dependencies
func NewTenantService(
	repo repository.TenantRepository,
	config *internal.TenantConfig,
	log logger.Logger,
) service.TenantService {
	return service.NewTenantService(repo, config, log)
}

// NewTenantHandler creates tenant API handler with dependencies
func NewTenantHandler(
	tenantService service.TenantService,
	log logger.Logger,
) *api.Handler {
	return api.NewHandler(tenantService, log)
}

// TenantServiceParams defines parameters for tenant service creation
type TenantServiceParams struct {
	Repository repository.TenantRepository
	Config     *internal.TenantConfig
	Logger     logger.Logger
}

// NewTenantServiceWithParams creates tenant service with structured params
func NewTenantServiceWithParams(params TenantServiceParams) service.TenantService {
	return service.NewTenantService(params.Repository, params.Config, params.Logger)
}

// NewMiddlewareTenantService creates middleware.TenantService from tenant service
func NewMiddlewareTenantService(tenantService service.TenantService) middleware.TenantService {
	return tenantService
}

// RegisterTenantRoutes registers tenant routes with Gin engine
func RegisterTenantRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering tenant routes")

	// Use existing route registration logic
	if err := handler.RegisterRoutes(engine); err != nil {
		log.Error("Failed to register tenant routes", "error", err)
		return
	}

	log.Info("Tenant routes registered successfully")
}
