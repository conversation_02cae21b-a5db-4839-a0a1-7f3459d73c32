package tenant

import (
	"testing"
)

func TestTenantModule(t *testing.T) {
	tenantModule := &TenantModule{}
	
	// Test module interface
	if tenantModule.Name() != "tenant" {
		t.<PERSON><PERSON>("Expected module name 'tenant', got '%s'", tenantModule.Name())
	}
	
	if tenantModule.Priority() != 1 {
		t.<PERSON><PERSON>("Expected priority 1, got %d", tenantModule.Priority())
	}
	
	deps := tenantModule.Dependencies()
	if len(deps) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 dependencies, got %d", len(deps))
	}
}

func TestTenantModuleEnabled(t *testing.T) {
	tenantModule := &TenantModule{}
	
	// Test default enabled
	if !tenantModule.Enabled(nil) {
		t.<PERSON>rror("Expected module to be enabled by default")
	}
	
	// Test explicitly enabled
	config := map[string]interface{}{"enabled": true}
	if !tenantModule.Enabled(config) {
		t.<PERSON>r("Expected module to be enabled when config says true")
	}
	
	// Test explicitly disabled
	config = map[string]interface{}{"enabled": false}
	if tenantModule.Enabled(config) {
		t.<PERSON><PERSON>("Expected module to be disabled when config says false")
	}
}

func TestTenantModulePriority(t *testing.T) {
	tenantModule := &TenantModule{}
	
	// Tenant module should have highest priority (1) to load first
	if tenantModule.Priority() != 1 {
		t.Errorf("Expected tenant module to have priority 1 (highest), got %d", tenantModule.Priority())
	}
}
