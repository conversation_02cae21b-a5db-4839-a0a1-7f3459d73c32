package service

import (
	"context"
	"fmt"
	"wnapi/modules/tenant/models"
)

// VerifyUserTenantAccess kiểm tra quyền truy cập của user vào tenant
func (s *tenantService) VerifyUserTenantAccess(ctx context.Context, userID, tenantID uint) (*models.Tenant, error) {
	// 1. Kiểm tra tenant có tồn tại và active không
	tenant, err := s.repo.GetByID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("tenant not found: %w", err)
	}

	if tenant.Status != models.TenantStatusActive {
		return nil, fmt.Errorf("tenant is not active (status: %s)", tenant.Status)
	}

	// 2. Kiểm tra user có thuộc tenant không
	isMember, err := s.repo.IsUserMemberOfTenant(ctx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to check user-tenant membership: %w", err)
	}

	if !isMember {
		return nil, fmt.Errorf("user %d does not belong to tenant %d", userID, tenantID)
	}

	return tenant, nil
}