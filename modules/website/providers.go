package website

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/website/internal"
)

// WebsiteHandler simple handler for website module
type WebsiteHandler struct {
	config *internal.WebsiteConfig
	logger logger.Logger
}

// NewWebsiteConfig creates website configuration
func NewWebsiteConfig(cfg config.Config) (*internal.WebsiteConfig, error) {
	return internal.LoadWebsiteConfig()
}

// NewWebsiteHandler creates website handler
func NewWebsiteHandler(config *internal.WebsiteConfig, log logger.Logger) *WebsiteHandler {
	return &WebsiteHandler{
		config: config,
		logger: log,
	}
}

// RegisterWebsiteRoutes registers website routes with gin.Engine
func RegisterWebsiteRoutes(handler *WebsiteHandler, engine *gin.Engine) error {
	// For now, just register a simple health check route
	// TODO: Implement full route registration when website module is fully migrated to FX
	
	websiteGroup := engine.Group("/api/v1/website")
	websiteGroup.GET("/healthy", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"module":  "website",
			"message": "Website module is running (FX mode)",
		})
	})
	
	return nil
}
