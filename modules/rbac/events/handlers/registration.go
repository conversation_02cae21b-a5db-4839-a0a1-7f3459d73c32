package handlers

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/service"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-redisstream/pkg/redisstream"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/redis/go-redis/v9"
)

// EventManager quản lý việc đăng ký và xử lý events cho RBAC module
type EventManager struct {
	subscriber      *redisstream.Subscriber
	router          *message.Router
	userRoleService service.UserRoleService
	roleService     service.RoleService
	logger          logger.Logger
}

// NewEventManager tạo một event manager mới cho RBAC module
func NewEventManager(config events.Config, userRoleService service.UserRoleService, roleService service.RoleService, logger logger.Logger) (*EventManager, error) {
	if !config.Enabled {
		logger.Info("Event system is disabled for RBAC module")
		return &EventManager{
			userRoleService: userRoleService,
			roleService:     roleService,
			logger:          logger,
		}, nil
	}

	// Tạo logger cho Watermill
	wmLogger := watermill.NewStdLogger(config.Debug, config.Debug)

	// Tạo Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.RedisHost, config.RedisPort),
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	})

	// Kiểm tra kết nối Redis
	if err := redisClient.Ping(context.Background()).Err(); err != nil {
		return nil, fmt.Errorf("không thể kết nối đến Redis cho RBAC events: %w", err)
	}

	// Tạo Watermill Redis subscriber
	subscriber, err := redisstream.NewSubscriber(
		redisstream.SubscriberConfig{
			Client:        redisClient,
			Unmarshaller:  redisstream.DefaultMarshallerUnmarshaller{},
			ConsumerGroup: "rbac_module",
		},
		wmLogger,
	)
	if err != nil {
		return nil, fmt.Errorf("không thể tạo Redis subscriber cho RBAC: %w", err)
	}

	// Tạo message router
	router, err := message.NewRouter(message.RouterConfig{}, wmLogger)
	if err != nil {
		return nil, fmt.Errorf("không thể tạo message router cho RBAC: %w", err)
	}

	return &EventManager{
		subscriber:      subscriber,
		router:          router,
		userRoleService: userRoleService,
		roleService:     roleService,
		logger:          logger,
	}, nil
}

// RegisterHandlers đăng ký tất cả event handlers cho RBAC module
func (em *EventManager) RegisterHandlers() error {
	if em.subscriber == nil || em.router == nil {
		em.logger.Info("Event system is disabled, skipping handler registration")
		return nil
	}

	// Đăng ký user event handlers
	userHandlers := RegisterUserEventHandlers(em.userRoleService, em.roleService, em.logger)

	for _, handler := range userHandlers {
		em.logger.Info("Đăng ký RBAC event handler",
			logger.String("handler_name", handler.Name),
			logger.String("event_type", handler.EventType),
		)

		em.router.AddNoPublisherHandler(
			handler.Name,
			handler.EventType,
			em.subscriber,
			handler.HandlerFunc,
		)
	}

	em.logger.Info("Đã đăng ký tất cả RBAC event handlers")
	return nil
}

// Start bắt đầu xử lý events
func (em *EventManager) Start(ctx context.Context) error {
	if em.router == nil {
		em.logger.Info("Event system is disabled, skipping event processing")
		return nil
	}

	em.logger.Info("Bắt đầu xử lý events cho RBAC module")
	return em.router.Run(ctx)
}

// Close đóng event manager
func (em *EventManager) Close() error {
	if em.router != nil {
		if err := em.router.Close(); err != nil {
			em.logger.Error("Lỗi khi đóng message router", logger.String("error", err.Error()))
		}
	}

	if em.subscriber != nil {
		if err := em.subscriber.Close(); err != nil {
			em.logger.Error("Lỗi khi đóng subscriber", logger.String("error", err.Error()))
		}
	}

	em.logger.Info("Đã đóng RBAC event manager")
	return nil
}
