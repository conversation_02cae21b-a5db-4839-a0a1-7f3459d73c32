package handlers

import (
	"context"
	"fmt"
	"strings"

	"wnapi/internal/pkg/events"
	"wnapi/internal/pkg/events/types"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/ThreeDotsLabs/watermill/message"
)

// UserEventHandler xử lý các sự kiện liên quan đến người dùng từ auth module
type UserEventHandler struct {
	userRoleService service.UserRoleService
	roleService     service.RoleService
	logger          logger.Logger
}

// NewUserEventHandler tạo một handler mới cho user events
func NewUserEventHandler(userRoleService service.UserRoleService, roleService service.RoleService, logger logger.Logger) *UserEventHandler {
	if userRoleService == nil {
		logger.Error("userRoleService is nil in NewUserEventHandler")
	}
	if roleService == nil {
		logger.Error("roleService is nil in NewUserEventHandler")
	}
	if logger == nil {
		// Can't log if logger is nil, but we should handle this
		panic("logger is nil in NewUserEventHandler")
	}

	return &UserEventHandler{
		userRoleService: userRoleService,
		roleService:     roleService,
		logger:          logger,
	}
}

// HandleUserCreated xử lý sự kiện khi người dùng được tạo
func (h *UserEventHandler) HandleUserCreated(msg *message.Message) error {
	// Add safety check for nil message
	if msg == nil {
		h.logger.Error("Received nil message in HandleUserCreated")
		return fmt.Errorf("received nil message")
	}

	h.logger.Info("Nhận sự kiện user.created từ auth module")

	// Extract event từ message
	event, err := events.ExtractEvent(msg)
	if err != nil {
		h.logger.Error("Không thể extract event từ message", "error", err.Error())
		return fmt.Errorf("failed to extract event: %w", err)
	}

	// Safety check for nil event
	if event == nil {
		h.logger.Error("Extracted event is nil")
		return fmt.Errorf("extracted event is nil")
	}

	// Extract payload
	var payload types.UserCreatedPayload
	if err := events.ExtractPayload(event, &payload); err != nil {
		h.logger.Error("Không thể extract payload từ event", "error", err.Error())
		return fmt.Errorf("failed to extract payload: %w", err)
	}

	// Validate payload data
	if payload.UserID == 0 {
		h.logger.Error("Invalid payload: UserID is 0")
		return fmt.Errorf("invalid payload: UserID is 0")
	}

	tenantID := event.TenantID()
	if tenantID == 0 {
		h.logger.Error("Invalid event: TenantID is 0")
		return fmt.Errorf("invalid event: TenantID is 0")
	}

	h.logger.Info("Xử lý user created event",
		"user_id", payload.UserID,
		"username", payload.Username,
		"email", payload.Email,
		"tenant_id", tenantID,
	)

	ctx := context.Background()

	h.logger.Info("Implementing automatic role assignment for new user",
		"user_id", payload.UserID,
		"username", payload.Username,
		"tenant_id", tenantID,
	)

	// Implement actual role assignment logic
	if err := h.assignDefaultRolesToUser(ctx, tenantID, payload); err != nil {
		// Log error but don't fail the entire event processing
		h.logger.Error("Failed to assign default roles to new user",
			"user_id", payload.UserID,
			"tenant_id", tenantID,
			"error", err.Error(),
		)
		// Continue processing - role assignment is best-effort
	}

	h.logger.Info("User created event processed successfully",
		"user_id", payload.UserID,
		"tenant_id", tenantID,
	)

	return nil
}

// HandleUserUpdated xử lý sự kiện khi người dùng được cập nhật
func (h *UserEventHandler) HandleUserUpdated(msg *message.Message) error {
	h.logger.Info("Nhận sự kiện user.updated từ auth module")

	// Extract event từ message
	event, err := events.ExtractEvent(msg)
	if err != nil {
		h.logger.Error("Không thể extract event từ message", logger.String("error", err.Error()))
		return fmt.Errorf("failed to extract event: %w", err)
	}

	// Extract payload
	var payload types.UserUpdatedPayload
	if err := events.ExtractPayload(event, &payload); err != nil {
		h.logger.Error("Không thể extract payload từ event", logger.String("error", err.Error()))
		return fmt.Errorf("failed to extract payload: %w", err)
	}

	h.logger.Info("Xử lý user updated event",
		"user_id", payload.UserID,
		"username", payload.Username,
		"email", payload.Email,
		"status", payload.Status,
		"tenant_id", event.TenantID(),
	)

	ctx := context.Background()

	// TODO: Implement role update logic based on user changes
	// For now, we'll just log the event
	// In a full implementation, you might:
	// 1. Check if user status changed (active/inactive) and update role assignments accordingly
	// 2. Check if user type changed and reassign roles
	// 3. Handle tenant changes if applicable

	// Suppress unused variable warning
	_ = ctx

	h.logger.Info("User updated event processed successfully",
		"user_id", payload.UserID,
		"tenant_id", event.TenantID(),
	)

	return nil
}

// assignDefaultRolesToUser gán các vai trò mặc định cho user mới
func (h *UserEventHandler) assignDefaultRolesToUser(ctx context.Context, tenantID uint, payload types.UserCreatedPayload) error {
	// Kiểm tra nil pointer trước khi sử dụng
	if h.userRoleService == nil {
		h.logger.Error("userRoleService is nil, cannot assign roles",
			"user_id", payload.UserID,
			"tenant_id", tenantID,
		)
		return fmt.Errorf("userRoleService is nil")
	}

	h.logger.Info("Looking up default roles for tenant",
		"tenant_id", tenantID,
		"user_id", payload.UserID,
	)

	// Determine default roles for the user
	defaultRoleIDs := h.determineDefaultRoles(tenantID, payload)

	if len(defaultRoleIDs) == 0 {
		h.logger.Info("No default roles found for user",
			"tenant_id", tenantID,
			"user_id", payload.UserID,
		)
		return nil
	}

	// Gán từng role cho user
	assignedCount := 0
	for _, roleID := range defaultRoleIDs {
		req := request.AssignUserRoleRequest{
			UserID: payload.UserID,
			RoleID: roleID,
		}

		if err := h.userRoleService.AssignRoleToUser(ctx, tenantID, req); err != nil {
			h.logger.Error("Failed to assign role to user",
				"user_id", payload.UserID,
				"role_id", roleID,
				"tenant_id", tenantID,
				"error", err.Error(),
			)
			// Continue với role khác thay vì fail toàn bộ
			continue
		}

		assignedCount++
		h.logger.Info("Successfully assigned role to user",
			"user_id", payload.UserID,
			"role_id", roleID,
			"tenant_id", tenantID,
		)
	}

	h.logger.Info("Role assignment completed",
		"user_id", payload.UserID,
		"tenant_id", tenantID,
		"assigned_count", assignedCount,
		"total_attempted", len(defaultRoleIDs),
	)

	return nil
}

// determineDefaultRoles xác định các role mặc định dựa trên user data
func (h *UserEventHandler) determineDefaultRoles(tenantID uint, payload types.UserCreatedPayload) []uint {
	ctx := context.Background()
	var roleIDs []uint

	// Kiểm tra nil pointer trước khi sử dụng
	if h.roleService == nil {
		h.logger.Error("roleService is nil, cannot determine default roles",
			"user_id", payload.UserID,
			"tenant_id", tenantID,
		)
		return roleIDs
	}

	h.logger.Info("Determining default roles for new user",
		"user_id", payload.UserID,
		"tenant_id", tenantID,
		"email", payload.Email,
	)

	// Logic 1: Dựa trên email domain
	if strings.Contains(payload.Email, "@admin.") {
		h.logger.Info("Admin email domain detected, looking for admin roles",
			"email", payload.Email,
			"user_id", payload.UserID,
		)

		// Tìm admin role cho tenant
		if adminRole, err := h.roleService.GetRoleByCode(ctx, tenantID, "admin"); err == nil && adminRole != nil {
			roleIDs = append(roleIDs, adminRole.RoleID)
			h.logger.Info("Found admin role for admin email domain",
				"role_id", adminRole.RoleID,
				"role_code", adminRole.RoleCode,
			)
		} else {
			h.logger.Info("No admin role found for admin email domain",
				"tenant_id", tenantID,
				"error", err,
			)
		}
	}

	// Logic 2: Default user role cho tất cả user mới
	// Tìm role mặc định theo thứ tự ưu tiên
	defaultRoleCodes := []string{
		fmt.Sprintf("user_%d", tenantID),   // Tenant-specific user role
		fmt.Sprintf("viewer_%d", tenantID), // Tenant-specific viewer role
		"user",                             // Global user role
		"viewer",                           // Global viewer role
	}

	for _, roleCode := range defaultRoleCodes {
		if role, err := h.roleService.GetRoleByCode(ctx, tenantID, roleCode); err == nil && role != nil {
			// Kiểm tra xem role này đã được thêm chưa (tránh duplicate)
			alreadyAdded := false
			for _, existingRoleID := range roleIDs {
				if existingRoleID == role.RoleID {
					alreadyAdded = true
					break
				}
			}

			if !alreadyAdded {
				roleIDs = append(roleIDs, role.RoleID)
				h.logger.Info("Found default role for new user",
					"role_id", role.RoleID,
					"role_code", role.RoleCode,
					"role_name", role.RoleName,
				)
				break // Chỉ cần một default role là đủ
			}
		} else {
			h.logger.Debug("Role not found",
				"role_code", roleCode,
				"tenant_id", tenantID,
				"error", err,
			)
		}
	}

	// Nếu không tìm thấy role nào, log thông tin
	if len(roleIDs) == 0 {
		h.logger.Info("No default roles found for user",
			"tenant_id", tenantID,
			"user_id", payload.UserID,
			"note", "Consider creating default roles with codes like 'user' or 'viewer' for this tenant",
		)
	}

	h.logger.Info("Default role determination completed",
		"user_id", payload.UserID,
		"tenant_id", tenantID,
		"found_roles", len(roleIDs),
		"role_ids", roleIDs,
	)

	return roleIDs
}

// RegisterUserEventHandlers đăng ký các handlers cho user events
func RegisterUserEventHandlers(userRoleService service.UserRoleService, roleService service.RoleService, logger logger.Logger) []HandlerRegistration {
	handler := NewUserEventHandler(userRoleService, roleService, logger)

	return []HandlerRegistration{
		{
			Name:        "rbac_handle_user_created",
			EventType:   types.AuthUserCreated,
			HandlerFunc: handler.HandleUserCreated,
		},
		{
			Name:        "rbac_handle_user_updated",
			EventType:   types.AuthUserUpdated,
			HandlerFunc: handler.HandleUserUpdated,
		},
	}
}

// HandlerRegistration đại diện cho việc đăng ký một event handler
type HandlerRegistration struct {
	Name        string
	EventType   string
	HandlerFunc func(*message.Message) error
}
