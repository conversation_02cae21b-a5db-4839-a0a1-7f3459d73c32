package rbac

import (
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/api"
	"wnapi/modules/rbac/internal"
	"wnapi/modules/rbac/repository"
	"wnapi/modules/rbac/service"
	tenantService "wnapi/modules/tenant/service"

	"github.com/gin-gonic/gin"
)

// NewRBACConfig creates RBAC configuration
func NewRBACConfig(cfg config.Config) *internal.RBACConfig {
	return &internal.RBACConfig{
		JWTAccessSecret:    cfg.GetString("JWT_ACCESS_SIGNING_KEY"),
		JWTRefreshSecret:   cfg.GetString("JWT_REFRESH_SIGNING_KEY"),
		AccessTokenExpiry:  cfg.GetDuration("JWT_ACCESS_TOKEN_EXPIRATION"),
		RefreshTokenExpiry: cfg.GetDuration("JWT_REFRESH_TOKEN_EXPIRATION"),
		JWTIssuer:          cfg.GetString("JWT_ISSUER"),
		DBHost:             cfg.GetStringWithDefault("DB_HOST", "localhost"),
		DBPort:             cfg.GetIntWithDefault("DB_PORT", 3306),
		DBUsername:         cfg.GetStringWithDefault("DB_USERNAME", "root"),
		DBPassword:         cfg.GetString("DB_PASSWORD"),
		DBName:             cfg.GetStringWithDefault("DB_NAME", "wnapi"),
		HTTPHost:           cfg.GetStringWithDefault("HTTP_HOST", "localhost"),
		HTTPPort:           cfg.GetIntWithDefault("HTTP_PORT", 8081),
		GRPCHost:           cfg.GetStringWithDefault("GRPC_HOST", "localhost"),
		GRPCPort:           cfg.GetIntWithDefault("GRPC_PORT", 50051),
		TracingEnabled:     cfg.GetBoolWithDefault("TRACING_ENABLED", false),
		TracingServiceName: cfg.GetStringWithDefault("TRACING_SERVICE_NAME", "rbac-service"),
		TracingEndpoint:    cfg.GetStringWithDefault("TRACING_ENDPOINT", "http://localhost:14268/api/traces"),
	}
}

// NewRoleService creates role service with dependencies
func NewRoleService(
	roleRepo repository.RoleRepository,
	permissionRepo repository.PermissionRepository,
	log logger.Logger,
) service.RoleService {
	return service.NewRoleService(roleRepo, permissionRepo, log)
}

// NewPermissionService creates permission service with dependencies
func NewPermissionService(
	permissionRepo repository.PermissionRepository,
	roleRepo repository.RoleRepository,
	userRoleRepo repository.UserRoleRepository,
	permissionGroupRepo repository.PermissionGroupRepository,
	log logger.Logger,
) service.PermissionService {
	return service.NewPermissionService(permissionRepo, roleRepo, userRoleRepo, permissionGroupRepo, log)
}

// NewUserRoleService creates user role service with dependencies
func NewUserRoleService(
	userRoleRepo repository.UserRoleRepository,
	roleRepo repository.RoleRepository,
) service.UserRoleService {
	return service.NewUserRoleService(userRoleRepo, roleRepo)
}

// NewPermissionGroupService creates permission group service with dependencies
func NewPermissionGroupService(
	permissionGroupRepo repository.PermissionGroupRepository,
) service.PermissionGroupAPIService {
	return service.NewPermissionGroupAPIService(permissionGroupRepo)
}

// NewRBACHandler creates RBAC API handler with dependencies
func NewRBACHandler(
	roleService service.RoleService,
	permissionService service.PermissionService,
	userRoleService service.UserRoleService,
	permissionGroupService service.PermissionGroupAPIService,
	jwtService *auth.JWTService,
	tenantService tenantService.TenantService,
	log logger.Logger,
) *api.Handler {
	return api.NewHandler(
		permissionService,
		roleService,
		userRoleService,
		permissionGroupService,
		jwtService,
		tenantService,
	)
}

// RBACServiceParams defines parameters for RBAC service creation
type RBACServiceParams struct {
	RoleRepository            repository.RoleRepository
	PermissionRepository      repository.PermissionRepository
	UserRoleRepository        repository.UserRoleRepository
	PermissionGroupRepository repository.PermissionGroupRepository
	Logger                    logger.Logger
}

// NewRBACServicesWithParams creates all RBAC services with structured params
func NewRBACServicesWithParams(params RBACServiceParams) (
	service.RoleService,
	service.PermissionService,
	service.UserRoleService,
) {
	roleService := service.NewRoleService(params.RoleRepository, params.PermissionRepository, params.Logger)
	permissionService := service.NewPermissionService(
		params.PermissionRepository,
		params.RoleRepository,
		params.UserRoleRepository,
		params.PermissionGroupRepository,
		params.Logger,
	)
	userRoleService := service.NewUserRoleService(params.UserRoleRepository, params.RoleRepository)

	return roleService, permissionService, userRoleService
}

// RegisterRBACRoutes registers RBAC routes with Gin engine
func RegisterRBACRoutes(engine *gin.Engine, handler *api.Handler, log logger.Logger) {
	log.Info("Registering RBAC routes")

	// Use the new gin-compatible route registration method
	if err := handler.RegisterRoutesWithGin(engine); err != nil {
		log.Error("Failed to register RBAC routes", "error", err)
		return
	}

	log.Info("RBAC routes registered successfully")
}
