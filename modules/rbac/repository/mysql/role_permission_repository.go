package mysql

import (
	"context"

	"gorm.io/gorm"

	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type rolePermissionRepository struct {
	db *gorm.DB
}

// NewRolePermissionRepository tạo một instance mới của RolePermissionRepository
func NewRolePermissionRepository(db *gorm.DB) repository.RolePermissionRepository {
	return &rolePermissionRepository{
		db: db,
	}
}

// AssignPermissions gán danh sách quyền cho một vai trò
func (r *rolePermissionRepository) AssignPermissions(ctx context.Context, roleID uint, permissionIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Xóa tất cả quyền hiện tại của vai trò
		if err := tx.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
			return err
		}

		// <PERSON><PERSON>u không có quyền nào để gán, kết thúc transaction
		if len(permissionIDs) == 0 {
			return nil
		}

		// Thêm các quyền mới
		var rolePermissions []models.RolePermission
		for _, permID := range permissionIDs {
			rolePermissions = append(rolePermissions, models.RolePermission{
				RoleID:       roleID,
				PermissionID: permID,
			})
		}
		if err := tx.Create(&rolePermissions).Error; err != nil {
			return err
		}
		return nil
	})
}

// GetRolePermissions lấy danh sách quyền của một vai trò
func (r *rolePermissionRepository) GetRolePermissions(ctx context.Context, roleID uint) ([]uint, error) {
	var permissionIDs []uint
	result := r.db.WithContext(ctx).Table("rbac_role_permissions").
		Where("role_id = ?", roleID).
		Pluck("permission_id", &permissionIDs)
	return permissionIDs, result.Error
}
