package rbac

import (
	"testing"
)

func TestRBACModule(t *testing.T) {
	rbacModule := &RBACModule{}
	
	// Test module interface
	if rbacModule.Name() != "rbac" {
		t.<PERSON><PERSON>("Expected module name 'rbac', got '%s'", rbacModule.Name())
	}
	
	if rbacModule.Priority() != 20 {
		t.<PERSON>("Expected priority 20, got %d", rbacModule.Priority())
	}
	
	deps := rbacModule.Dependencies()
	expectedDeps := []string{"tenant", "auth"}
	if len(deps) != len(expectedDeps) {
		t.<PERSON>("Expected %d dependencies, got %d", len(expectedDeps), len(deps))
	}
	
	for i, dep := range deps {
		if dep != expectedDeps[i] {
			t.<PERSON><PERSON>("Expected dependency '%s', got '%s'", expectedDeps[i], dep)
		}
	}
}

func TestRBACModuleEnabled(t *testing.T) {
	rbacModule := &RBACModule{}
	
	// Test default enabled
	if !rbacModule.Enabled(nil) {
		t.Error("Expected module to be enabled by default")
	}
	
	// Test explicitly enabled
	config := map[string]interface{}{"enabled": true}
	if !rbacModule.Enabled(config) {
		t.Error("Expected module to be enabled when config says true")
	}
	
	// Test explicitly disabled
	config = map[string]interface{}{"enabled": false}
	if rbacModule.Enabled(config) {
		t.Error("Expected module to be disabled when config says false")
	}
}

func TestRBACModulePriority(t *testing.T) {
	rbacModule := &RBACModule{}
	
	// RBAC module should have priority 20 to load after tenant (1) and auth (10)
	if rbacModule.Priority() != 20 {
		t.Errorf("Expected RBAC module to have priority 20, got %d", rbacModule.Priority())
	}
}

func TestRBACModuleDependencies(t *testing.T) {
	rbacModule := &RBACModule{}
	
	deps := rbacModule.Dependencies()
	
	// Should depend on both tenant and auth
	if len(deps) != 2 {
		t.Errorf("Expected 2 dependencies, got %d", len(deps))
	}
	
	// Check specific dependencies
	expectedDeps := map[string]bool{"tenant": false, "auth": false}
	for _, dep := range deps {
		if _, exists := expectedDeps[dep]; exists {
			expectedDeps[dep] = true
		} else {
			t.Errorf("Unexpected dependency: %s", dep)
		}
	}
	
	for dep, found := range expectedDeps {
		if !found {
			t.Errorf("Missing expected dependency: %s", dep)
		}
	}
}
