package rbac

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/console"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/api"
	"wnapi/modules/rbac/commands"
	"wnapi/modules/rbac/repository"
	"wnapi/modules/rbac/repository/mysql"
	"wnapi/modules/rbac/service"
	tenantService "wnapi/modules/tenant/service"
)

// RBACModule implements FXModule interface
type RBACModule struct {
	permissionService service.PermissionService
}

// Name returns module name
func (m *RBACModule) Name() string {
	return "rbac"
}

// Dependencies returns module dependencies
func (m *RBACModule) Dependencies() []string {
	return []string{"tenant", "auth"} // Depends on tenant and auth
}

// Priority returns loading priority
func (m *RBACModule) Priority() int {
	return 20 // Load after tenant (1) and auth (10)
}

// Enabled checks if module should be loaded
func (m *RBACModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default enabled
}

// GetMigrationPath returns path to module migrations
func (m *RBACModule) GetMigrationPath() string {
	return "modules/rbac/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *RBACModule) GetMigrationOrder() int {
	return 20 // RBAC module runs after tenant and auth
}

// Module returns fx.Module for rbac
func (m *RBACModule) Module() fx.Option {
	return fx.Module("rbac",
		// Providers
		fx.Provide(
			// Configuration
			NewRBACConfig,

			// Repositories
			fx.Annotate(mysql.NewRoleRepository, fx.As(new(repository.RoleRepository))),
			fx.Annotate(mysql.NewPermissionRepository, fx.As(new(repository.PermissionRepository))),
			fx.Annotate(mysql.NewUserRoleRepository, fx.As(new(repository.UserRoleRepository))),
			fx.Annotate(mysql.NewPermissionGroupRepository, fx.As(new(repository.PermissionGroupRepository))),

			// Services
			fx.Annotate(
				NewRoleService,
				fx.As(new(service.RoleService)),
			),
			fx.Annotate(
				NewPermissionService,
				fx.As(new(service.PermissionService)),
			),
			fx.Annotate(
				NewUserRoleService,
				fx.As(new(service.UserRoleService)),
			),
			fx.Annotate(
				NewPermissionGroupService,
				fx.As(new(service.PermissionGroupAPIService)),
			),

			// Handlers
			NewRBACHandler,
		),

		// Route registration
		fx.Invoke(RegisterRBACRoutes),
	)
}

// RegisterConsoleCommands implements ConsoleModule interface
func (m *RBACModule) RegisterConsoleCommands(registry *console.Registry) error {
	// We need to get the permission service from the DI container
	// For now, we'll register a placeholder that will be resolved later
	// This is a limitation of the current architecture where modules are stateless

	// Create commands without service dependency for discovery command
	discoverPermissionsCmd := commands.NewDiscoverPermissionsCommand()
	registry.RegisterCommand(discoverPermissionsCmd)

	// Note: seed-permissions and list-permissions commands require PermissionService
	// These will need to be registered differently or the architecture needs to be updated
	// to allow service injection into modules

	return nil
}

// Register rbac module with global registry
func init() {
	modules.RegisterModule(&RBACModule{})
}

// Module exports all RBAC related components
var Module = fx.Options(
	fx.Provide(
		NewRBACHandler,
		service.NewRoleService,
		service.NewPermissionService,
		service.NewUserRoleService,
		service.NewPermissionGroupAPIService,
		mysql.NewRoleRepository,
		mysql.NewPermissionRepository,
		mysql.NewUserRoleRepository,
		mysql.NewPermissionGroupRepository,
	),
	fx.Invoke(func(
		handler *api.Handler,
		engine *gin.Engine,
		jwtService *auth.JWTService,
		tenantService tenantService.TenantService,
		log logger.Logger,
	) {
		handler.RegisterRoutesWithGin(engine)
	}),
)
