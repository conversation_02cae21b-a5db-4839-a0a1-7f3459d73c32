package models

import (
	"time"
)

// NotificationUser đại diện cho một thông báo của người dùng trong hệ thống
type NotificationUser struct {
	NotificationID uint      `json:"notification_id" gorm:"primaryKey" db:"notification_id"`
	TenantID       uint      `json:"tenant_id" gorm:"not null;index" db:"tenant_id"`
	WebsiteID      uint      `json:"website_id" gorm:"not null;index" db:"website_id"`
	UserID         uint      `json:"user_id" gorm:"not null;index" db:"user_id"`
	Title          string    `json:"title" gorm:"not null" db:"title"`
	Content        string    `json:"content" gorm:"not null" db:"content"`
	IsRead         bool      `json:"is_read" gorm:"default:false" db:"is_read"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
}

// TableName trả về tên bảng trong cơ sở dữ liệu
func (NotificationUser) TableName() string {
	return "notification_users"
}
