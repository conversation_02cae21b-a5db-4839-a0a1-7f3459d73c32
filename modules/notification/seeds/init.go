package seeds

import (
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/notification/internal"

	"gorm.io/gorm"
)

// RegisterNotificationSeed đăng ký notification seed với global registry
func RegisterNotificationSeed(repo internal.Repository, db *gorm.DB, logger logger.Logger, config *seed.Config) error {
	deps := Dependencies{
		Repository: repo,
		DB:         db,
		Logger:     logger,
		Config:     config,
	}

	notificationSeed := NewNotificationSeed(deps)
	return seed.RegisterModuleSeed("notification", notificationSeed)
}

// init function để tự động đăng ký (sẽ được gọi khi import package)
// T<PERSON><PERSON> thời comment out vì cần dependencies
/*
func init() {
	// Tự động đăng ký notification seed khi import module
	// Cần có cách để inject dependencies
	seed.RegisterModuleSeed("notification", NewNotificationSeed)
}
*/