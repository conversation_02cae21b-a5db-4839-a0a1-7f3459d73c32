package seeds

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/seeds/seeders"

	"gorm.io/gorm"
)

// NotificationSeed implements seed.ModuleSeed interface for notification module
type NotificationSeed struct {
	repo    internal.Repository
	db      *gorm.DB
	logger  logger.Logger
	config  *seed.Config
	seeders []seed.Seeder
}

// Dependencies chứa các dependencies cần thiết cho notification seeders
type Dependencies struct {
	Repository internal.Repository
	DB         *gorm.DB
	Logger     logger.Logger
	Config     *seed.Config
}

// NewNotificationSeed tạo một NotificationSeed mới
func NewNotificationSeed(deps Dependencies) *NotificationSeed {
	if deps.Config == nil {
		deps.Config = seed.DefaultConfig()
	}

	notificationSeed := &NotificationSeed{
		repo:   deps.Repository,
		db:     deps.DB,
		logger: deps.<PERSON>gger,
		config: deps.Config,
	}

	// Khởi tạo seeders theo thứ tự dependencies
	notificationSeed.seeders = []seed.Seeder{
		seeders.NewNotificationChannelSeeder(deps.Repository, deps.DB, deps.Logger, deps.Config),
		seeders.NewNotificationTemplateSeeder(deps.Repository, deps.DB, deps.Logger, deps.Config),
	}

	return notificationSeed
}

// ModuleName trả về tên module
func (s *NotificationSeed) ModuleName() string {
	return "notification"
}

// GetSeeders trả về danh sách seeders của module
func (s *NotificationSeed) GetSeeders() []seed.Seeder {
	return s.seeders
}

// SeedAll chạy tất cả seeders trong module
func (s *NotificationSeed) SeedAll(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed notification module", "environment", s.config.Environment)

	for _, seeder := range s.seeders {
		s.logger.Info("Chạy seeder", "seeder", seeder.Name())

		if err := seeder.Run(ctx); err != nil {
			s.logger.Error("Lỗi chạy seeder",
				"seeder", seeder.Name(),
				"error", err.Error())
			return fmt.Errorf("lỗi chạy seeder %s: %w", seeder.Name(), err)
		}

		s.logger.Info("Hoàn thành seeder", "seeder", seeder.Name())
	}

	s.logger.Info("Hoàn thành seed notification module")
	return nil
}

// SeedSpecific chạy seeder cụ thể
func (s *NotificationSeed) SeedSpecific(ctx context.Context, seederName string) error {
	s.logger.Info("Chạy seeder cụ thể", "seeder", seederName)

	// Tìm seeder theo tên
	var targetSeeder seed.Seeder
	for _, seeder := range s.seeders {
		if seeder.Name() == seederName {
			targetSeeder = seeder
			break
		}
	}

	if targetSeeder == nil {
		return fmt.Errorf("không tìm thấy seeder '%s' trong notification module", seederName)
	}

	// Kiểm tra dependencies
	if err := s.checkDependencies(ctx, targetSeeder); err != nil {
		return fmt.Errorf("lỗi kiểm tra dependencies cho seeder %s: %w", seederName, err)
	}

	// Chạy seeder
	if err := targetSeeder.Run(ctx); err != nil {
		s.logger.Error("Lỗi chạy seeder", "seeder", seederName, "error", err.Error())
		return fmt.Errorf("lỗi chạy seeder %s: %w", seederName, err)
	}

	s.logger.Info("Hoàn thành seeder", "seeder", seederName)
	return nil
}

// checkDependencies kiểm tra và chạy dependencies nếu cần
func (s *NotificationSeed) checkDependencies(ctx context.Context, targetSeeder seed.Seeder) error {
	dependencies := targetSeeder.Dependencies()
	if len(dependencies) == 0 {
		return nil
	}

	s.logger.Info("Kiểm tra dependencies",
		"seeder", targetSeeder.Name(),
		"dependencies", fmt.Sprintf("%v", dependencies))

	// Tạo map để track seeders đã chạy
	seederMap := make(map[string]seed.Seeder)
	for _, seeder := range s.seeders {
		seederMap[seeder.Name()] = seeder
	}

	// Chạy dependencies theo thứ tự
	for _, depName := range dependencies {
		depSeeder, exists := seederMap[depName]
		if !exists {
			return fmt.Errorf("dependency '%s' không tồn tại", depName)
		}

		s.logger.Info("Chạy dependency", "dependency", depName)
		if err := depSeeder.Run(ctx); err != nil {
			return fmt.Errorf("lỗi chạy dependency %s: %w", depName, err)
		}
	}

	return nil
}

// RollbackAll rollback tất cả seeders trong module
func (s *NotificationSeed) RollbackAll(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback notification module")

	// Rollback theo thứ tự ngược lại
	for i := len(s.seeders) - 1; i >= 0; i-- {
		seeder := s.seeders[i]
		s.logger.Info("Rollback seeder", "seeder", seeder.Name())

		if err := seeder.Rollback(ctx); err != nil {
			s.logger.Error("Lỗi rollback seeder",
				"seeder", seeder.Name(),
				"error", err.Error())
			return fmt.Errorf("lỗi rollback seeder %s: %w", seeder.Name(), err)
		}

		s.logger.Info("Hoàn thành rollback seeder", "seeder", seeder.Name())
	}

	s.logger.Info("Hoàn thành rollback notification module")
	return nil
}

// RollbackSpecific rollback seeder cụ thể
func (s *NotificationSeed) RollbackSpecific(ctx context.Context, seederName string) error {
	s.logger.Info("Rollback seeder cụ thể", "seeder", seederName)

	// Tìm seeder theo tên
	var targetSeeder seed.Seeder
	for _, seeder := range s.seeders {
		if seeder.Name() == seederName {
			targetSeeder = seeder
			break
		}
	}

	if targetSeeder == nil {
		return fmt.Errorf("không tìm thấy seeder '%s' trong notification module", seederName)
	}

	// Rollback seeder
	if err := targetSeeder.Rollback(ctx); err != nil {
		s.logger.Error("Lỗi rollback seeder", "seeder", seederName, "error", err.Error())
		return fmt.Errorf("lỗi rollback seeder %s: %w", seederName, err)
	}

	s.logger.Info("Hoàn thành rollback seeder", "seeder", seederName)
	return nil
}