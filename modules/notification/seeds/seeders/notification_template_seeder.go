package seeders

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/notification/internal"

	"gorm.io/gorm"
)

// NotificationTemplateSeeder implements seed.Seeder interface for notification template data
type NotificationTemplateSeeder struct {
	*BaseSeeder
}

// NotificationTemplateData represents the structure of notification template data
type NotificationTemplateData struct {
	TenantID         uint   `json:"tenant_id"`
	WebsiteID        uint   `json:"website_id"`
	TemplateCode     string `json:"template_code"`
	TitleTemplate    string `json:"title_template"`
	ContentTemplate  string `json:"content_template"`
	NotificationType string `json:"notification_type"`
	IsActive         bool   `json:"is_active"`
}

// NewNotificationTemplateSeeder tạo một NotificationTemplateSeeder mới
func NewNotificationTemplateSeeder(repo internal.Repository, db *gorm.DB, logger logger.Logger, config *seed.Config) *NotificationTemplateSeeder {
	return &NotificationTemplateSeeder{
		BaseSeeder: NewBaseSeeder(repo, db, logger, config),
	}
}

// Name trả về tên của seeder
func (s *NotificationTemplateSeeder) Name() string {
	return "notification_templates"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *NotificationTemplateSeeder) Dependencies() []string {
	return []string{"notification_channels"} // Phụ thuộc vào channels
}

// Description mô tả chức năng của seeder
func (s *NotificationTemplateSeeder) Description() string {
	return "Seed notification template data for the system"
}

// Run thực thi seed notification template data
func (s *NotificationTemplateSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed notification template data", "environment", s.config.Environment)

	// Load seed data từ JSON file
	seedData, err := s.LoadSeedData("notification_templates.json")
	if err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi load seed data: %w", err)
	}

	// Parse data thành slice of structs
	var templates []NotificationTemplateData
	if err := s.ParseDataToSlice(seedData, &templates); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi parse seed data: %w", err)
	}

	// Validate data
	if err := s.ValidateData(templates); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi validate data: %w", err)
	}

	s.logger.Info("Đã load notification template data", "count", len(templates))

	// Process từng template
	for i, template := range templates {
		if err := s.processTemplate(ctx, template); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi process template %s: %w", template.TemplateCode, err)
		}

		s.LogProgress(s.Name(), i+1, len(templates))
	}

	s.LogSuccess(s.Name(), len(templates))
	return nil
}

// processTemplate xử lý từng notification template
func (s *NotificationTemplateSeeder) processTemplate(ctx context.Context, template NotificationTemplateData) error {
	// Validate required fields
	requiredFields := []string{"template_code", "title_template", "content_template", "notification_type"}
	templateMap := map[string]interface{}{
		"template_code":     template.TemplateCode,
		"title_template":    template.TitleTemplate,
		"content_template":  template.ContentTemplate,
		"notification_type": template.NotificationType,
	}

	if err := s.ValidateRequiredFields(templateMap, requiredFields); err != nil {
		return err
	}

	// Sử dụng tenant_id từ config nếu không được cung cấp
	tenantID := template.TenantID
	if tenantID == 0 {
		tenantID = s.GetTenantID(ctx)
	}

	// Kiểm tra xem template đã tồn tại chưa
	conditions := map[string]interface{}{
		"tenant_id":     tenantID,
		"website_id":    template.WebsiteID,
		"template_code": template.TemplateCode,
	}

	exists, err := s.CheckDataExists(ctx, "notification_templates", conditions)
	if err != nil {
		return fmt.Errorf("lỗi kiểm tra template tồn tại: %w", err)
	}

	if exists && s.ShouldSkipExisting() {
		s.LogSkipped(s.Name(), fmt.Sprintf("Template %s đã tồn tại", template.TemplateCode))
		return nil
	}

	// Chuẩn bị data để insert/update
	now := time.Now()
	templateData := map[string]interface{}{
		"tenant_id":         tenantID,
		"website_id":        template.WebsiteID,
		"template_code":     template.TemplateCode,
		"title_template":    template.TitleTemplate,
		"content_template":  template.ContentTemplate,
		"notification_type": template.NotificationType,
		"is_active":         template.IsActive,
		"created_at":        now,
		"updated_at":        now,
	}

	if s.IsDryRun() {
		s.LogDryRun(s.Name(), fmt.Sprintf("Would insert/update template: %s", template.TemplateCode))
		return nil
	}

	// Insert hoặc update template
	if exists {
		// Update existing template
		delete(templateData, "created_at") // Không update created_at
		if err := s.db.WithContext(ctx).Table("notification_templates").Where(conditions).Updates(templateData).Error; err != nil {
			return fmt.Errorf("lỗi update template %s: %w", template.TemplateCode, err)
		}
		s.logger.Info("Đã update notification template", "template_code", template.TemplateCode)
	} else {
		// Insert new template
		if err := s.db.WithContext(ctx).Table("notification_templates").Create(templateData).Error; err != nil {
			return fmt.Errorf("lỗi insert template %s: %w", template.TemplateCode, err)
		}
		s.logger.Info("Đã insert notification template", "template_code", template.TemplateCode)
	}

	return nil
}

// Rollback xóa notification template data
func (s *NotificationTemplateSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback notification template data")

	// Load seed data để biết cần xóa gì
	seedData, err := s.LoadSeedData("notification_templates.json")
	if err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi load seed data cho rollback: %w", err)
	}

	// Parse data
	var templates []NotificationTemplateData
	if err := s.ParseDataToSlice(seedData, &templates); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi parse seed data cho rollback: %w", err)
	}

	// Xóa từng template
	for _, template := range templates {
		tenantID := template.TenantID
		if tenantID == 0 {
			tenantID = s.GetTenantID(ctx)
		}

		conditions := map[string]interface{}{
			"tenant_id":     tenantID,
			"website_id":    template.WebsiteID,
			"template_code": template.TemplateCode,
		}

		if err := s.DeleteByConditions(ctx, "notification_templates", conditions); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi xóa template %s: %w", template.TemplateCode, err)
		}

		s.logger.Info("Đã xóa notification template", "template_code", template.TemplateCode)
	}

	s.logger.Info("Hoàn thành rollback notification template data")
	return nil
}