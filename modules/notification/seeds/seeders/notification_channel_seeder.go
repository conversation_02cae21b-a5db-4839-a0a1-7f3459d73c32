package seeders

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/notification/internal"

	"gorm.io/gorm"
)

// NotificationChannelSeeder implements seed.Seeder interface for notification channel data
type NotificationChannelSeeder struct {
	*BaseSeeder
}

// NotificationChannelData represents the structure of notification channel data
type NotificationChannelData struct {
	TenantID    uint   `json:"tenant_id"`
	WebsiteID   uint   `json:"website_id"`
	ChannelCode string `json:"channel_code"`
	ChannelName string `json:"channel_name"`
	IsActive    bool   `json:"is_active"`
}

// NewNotificationChannelSeeder tạo một NotificationChannelSeeder mới
func NewNotificationChannelSeeder(repo internal.Repository, db *gorm.DB, logger logger.Logger, config *seed.Config) *NotificationChannelSeeder {
	return &NotificationChannelSeeder{
		BaseSeeder: NewBaseSeeder(repo, db, logger, config),
	}
}

// Name trả về tên của seeder
func (s *NotificationChannelSeeder) Name() string {
	return "notification_channels"
}

// Dependencies trả về danh sách seeder phụ thuộc
func (s *NotificationChannelSeeder) Dependencies() []string {
	return []string{} // Không có dependencies
}

// Description mô tả chức năng của seeder
func (s *NotificationChannelSeeder) Description() string {
	return "Seed notification channel data for the system"
}

// Run thực thi seed notification channel data
func (s *NotificationChannelSeeder) Run(ctx context.Context) error {
	s.logger.Info("Bắt đầu seed notification channel data", "environment", s.config.Environment)

	// Load seed data từ JSON file
	seedData, err := s.LoadSeedData("notification_channels.json")
	if err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi load seed data: %w", err)
	}

	// Parse data thành slice of structs
	var channels []NotificationChannelData
	if err := s.ParseDataToSlice(seedData, &channels); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi parse seed data: %w", err)
	}

	// Validate data
	if err := s.ValidateData(channels); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi validate data: %w", err)
	}

	s.logger.Info("Đã load notification channel data", "count", len(channels))

	// Process từng channel
	for i, channel := range channels {
		if err := s.processChannel(ctx, channel); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi process channel %s: %w", channel.ChannelCode, err)
		}

		s.LogProgress(s.Name(), i+1, len(channels))
	}

	s.LogSuccess(s.Name(), len(channels))
	return nil
}

// processChannel xử lý từng notification channel
func (s *NotificationChannelSeeder) processChannel(ctx context.Context, channel NotificationChannelData) error {
	// Validate required fields
	requiredFields := []string{"channel_code", "channel_name"}
	channelMap := map[string]interface{}{
		"channel_code": channel.ChannelCode,
		"channel_name": channel.ChannelName,
	}

	if err := s.ValidateRequiredFields(channelMap, requiredFields); err != nil {
		return err
	}

	// Sử dụng tenant_id từ config nếu không được cung cấp
	tenantID := channel.TenantID
	if tenantID == 0 {
		tenantID = s.GetTenantID(ctx)
	}

	// Kiểm tra xem channel đã tồn tại chưa
	conditions := map[string]interface{}{
		"tenant_id":    tenantID,
		"website_id":   channel.WebsiteID,
		"channel_code": channel.ChannelCode,
	}

	exists, err := s.CheckDataExists(ctx, "notification_channels", conditions)
	if err != nil {
		return fmt.Errorf("lỗi kiểm tra channel tồn tại: %w", err)
	}

	if exists && s.ShouldSkipExisting() {
		s.LogSkipped(s.Name(), fmt.Sprintf("Channel %s đã tồn tại", channel.ChannelCode))
		return nil
	}

	// Chuẩn bị data để insert/update
	now := time.Now()
	channelData := map[string]interface{}{
		"tenant_id":    tenantID,
		"website_id":   channel.WebsiteID,
		"channel_code": channel.ChannelCode,
		"channel_name": channel.ChannelName,
		"is_active":    channel.IsActive,
		"created_at":   now,
		"updated_at":   now,
	}

	if s.IsDryRun() {
		s.LogDryRun(s.Name(), fmt.Sprintf("Would insert/update channel: %s", channel.ChannelCode))
		return nil
	}

	// Insert hoặc update channel
	if exists {
		// Update existing channel
		delete(channelData, "created_at") // Không update created_at
		if err := s.db.WithContext(ctx).Table("notification_channels").Where(conditions).Updates(channelData).Error; err != nil {
			return fmt.Errorf("lỗi update channel %s: %w", channel.ChannelCode, err)
		}
		s.logger.Info("Đã update notification channel", "channel_code", channel.ChannelCode)
	} else {
		// Insert new channel
		if err := s.db.WithContext(ctx).Table("notification_channels").Create(channelData).Error; err != nil {
			return fmt.Errorf("lỗi insert channel %s: %w", channel.ChannelCode, err)
		}
		s.logger.Info("Đã insert notification channel", "channel_code", channel.ChannelCode)
	}

	return nil
}

// Rollback xóa notification channel data
func (s *NotificationChannelSeeder) Rollback(ctx context.Context) error {
	s.logger.Info("Bắt đầu rollback notification channel data")

	// Load seed data để biết cần xóa gì
	seedData, err := s.LoadSeedData("notification_channels.json")
	if err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi load seed data cho rollback: %w", err)
	}

	// Parse data
	var channels []NotificationChannelData
	if err := s.ParseDataToSlice(seedData, &channels); err != nil {
		s.LogError(s.Name(), err)
		return fmt.Errorf("lỗi parse seed data cho rollback: %w", err)
	}

	// Xóa từng channel
	for _, channel := range channels {
		tenantID := channel.TenantID
		if tenantID == 0 {
			tenantID = s.GetTenantID(ctx)
		}

		conditions := map[string]interface{}{
			"tenant_id":    tenantID,
			"website_id":   channel.WebsiteID,
			"channel_code": channel.ChannelCode,
		}

		if err := s.DeleteByConditions(ctx, "notification_channels", conditions); err != nil {
			s.LogError(s.Name(), err)
			return fmt.Errorf("lỗi xóa channel %s: %w", channel.ChannelCode, err)
		}

		s.logger.Info("Đã xóa notification channel", "channel_code", channel.ChannelCode)
	}

	s.logger.Info("Hoàn thành rollback notification channel data")
	return nil
}