package seeders

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/seed"
	"wnapi/modules/notification/internal"

	"gorm.io/gorm"
)

// BaseSeeder cung cấp functionality chung cho tất cả notification seeders
type BaseSeeder struct {
	repo       internal.Repository
	db         *gorm.DB
	logger     logger.Logger
	config     *seed.Config
	dataLoader seed.DataLoader
}

// NewBaseSeeder tạo một BaseSeeder mới
func NewBaseSeeder(repo internal.Repository, db *gorm.DB, logger logger.Logger, config *seed.Config) *BaseSeeder {
	return &BaseSeeder{
		repo:       repo,
		db:         db,
		logger:     logger,
		config:     config,
		dataLoader: seed.NewFileDataLoader(config),
	}
}

// LoadSeedData load data từ JSON file
func (b *BaseSeeder) LoadSeedData(fileName string) (*seed.SeedData, error) {
	dataPath := b.getDataPath()
	return b.dataLoader.LoadDataWithFallback(dataPath, b.config.Environment, fileName)
}

// ParseDataToSlice parse seed data thành slice of structs
func (b *BaseSeeder) ParseDataToSlice(seedData *seed.SeedData, target interface{}) error {
	dataBytes, err := json.Marshal(seedData.Data)
	if err != nil {
		return fmt.Errorf("lỗi marshal seed data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, target); err != nil {
		return fmt.Errorf("lỗi unmarshal seed data: %w", err)
	}

	return nil
}

// ValidateData validate cấu trúc data
func (b *BaseSeeder) ValidateData(data interface{}) error {
	if data == nil {
		return fmt.Errorf("data không được để trống")
	}
	return nil
}

// GetTenantID trả về tenant ID từ config hoặc context
func (b *BaseSeeder) GetTenantID(ctx context.Context) uint {
	// Có thể lấy từ context nếu cần
	return b.config.TenantID
}

// GetUserID trả về user ID cho audit purposes
func (b *BaseSeeder) GetUserID() *uint {
	return b.config.UserID
}

// ShouldSkipExisting kiểm tra xem có skip existing data không
func (b *BaseSeeder) ShouldSkipExisting() bool {
	return b.config.SkipExists
}

// IsDryRun kiểm tra xem có phải dry run mode không
func (b *BaseSeeder) IsDryRun() bool {
	return b.config.DryRun
}

// GetBatchSize trả về batch size cho bulk operations
func (b *BaseSeeder) GetBatchSize() int {
	return b.config.BatchSize
}

// LogProgress log progress của seeding operation
func (b *BaseSeeder) LogProgress(seederName string, current, total int) {
	b.logger.Info("Seeding progress",
		"seeder", seederName,
		"current", current,
		"total", total,
		"percentage", fmt.Sprintf("%.1f%%", float64(current)/float64(total)*100))
}

// LogSuccess log thành công của seeding operation
func (b *BaseSeeder) LogSuccess(seederName string, count int) {
	b.logger.Info("Seeding completed successfully",
		"seeder", seederName,
		"records_processed", count)
}

// LogError log lỗi của seeding operation
func (b *BaseSeeder) LogError(seederName string, err error) {
	b.logger.Error("Seeding failed",
		"seeder", seederName,
		"error", err.Error())
}

// LogSkipped log khi skip existing data
func (b *BaseSeeder) LogSkipped(seederName string, reason string) {
	b.logger.Info("Seeding skipped",
		"seeder", seederName,
		"reason", reason)
}

// LogDryRun log khi chạy dry run mode
func (b *BaseSeeder) LogDryRun(seederName string, action string) {
	b.logger.Info("Dry run mode",
		"seeder", seederName,
		"action", action,
		"note", "No actual changes made to database")
}

// getDataPath trả về đường dẫn đến thư mục data
func (b *BaseSeeder) getDataPath() string {
	return filepath.Join(b.config.DataPath, "modules", "notification", "seeds", "data")
}

// CheckDataExists kiểm tra xem data đã tồn tại chưa
func (b *BaseSeeder) CheckDataExists(ctx context.Context, tableName string, conditions map[string]interface{}) (bool, error) {
	if b.db == nil {
		return false, fmt.Errorf("database connection is nil")
	}
	dbOps := seed.NewGormDatabaseOperations(b.db, b.logger)
	return dbOps.CheckDataExists(ctx, tableName, conditions)
}

// BatchInsert thực hiện batch insert
func (b *BaseSeeder) BatchInsert(ctx context.Context, tableName string, data []interface{}) error {
	if b.IsDryRun() {
		b.LogDryRun("batch_insert", fmt.Sprintf("Would insert %d records into %s", len(data), tableName))
		return nil
	}

	if b.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	dbOps := seed.NewGormDatabaseOperations(b.db, b.logger)
	return dbOps.BatchInsert(ctx, tableName, data, b.GetBatchSize())
}

// BulkUpsert thực hiện bulk upsert
func (b *BaseSeeder) BulkUpsert(ctx context.Context, tableName string, data []interface{}, conflictColumns []string) error {
	if b.IsDryRun() {
		b.LogDryRun("bulk_upsert", fmt.Sprintf("Would upsert %d records into %s", len(data), tableName))
		return nil
	}

	if b.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	dbOps := seed.NewGormDatabaseOperations(b.db, b.logger)
	return dbOps.BulkUpsert(ctx, tableName, data, conflictColumns, b.GetBatchSize())
}

// DeleteByConditions xóa records theo conditions
func (b *BaseSeeder) DeleteByConditions(ctx context.Context, tableName string, conditions map[string]interface{}) error {
	if b.IsDryRun() {
		b.LogDryRun("delete", fmt.Sprintf("Would delete records from %s with conditions %v", tableName, conditions))
		return nil
	}

	if b.db == nil {
		return fmt.Errorf("database connection is nil")
	}
	dbOps := seed.NewGormDatabaseOperations(b.db, b.logger)
	return dbOps.DeleteByConditions(ctx, tableName, conditions)
}

// ValidateRequiredFields kiểm tra required fields
func (b *BaseSeeder) ValidateRequiredFields(data map[string]interface{}, requiredFields []string) error {
	for _, field := range requiredFields {
		if value, exists := data[field]; !exists || value == nil || value == "" {
			return fmt.Errorf("required field '%s' is missing or empty", field)
		}
	}
	return nil
}