package dto

import (
	"time"
)

// Request DTOs

// CreateNotificationUserRequest represents a request to create a notification for a user
type CreateNotificationUserRequest struct {
	TenantID  uint   `json:"tenant_id" binding:"required"`
	WebsiteID uint   `json:"website_id" binding:"required"`
	UserID    uint   `json:"user_id" binding:"required"`
	Title     string `json:"title" binding:"required"`
	Content   string `json:"content" binding:"required"`
}

// UpdateNotificationUserRequest represents a request to update a notification for a user
type UpdateNotificationUserRequest struct {
	Title   string `json:"title,omitempty"`
	Content string `json:"content,omitempty"`
	IsRead  *bool  `json:"is_read,omitempty"`
}

// ListNotificationUsersRequest represents a request to list notifications for a user
type ListNotificationUsersRequest struct {
	UserID    uint   `form:"user_id" binding:"required"`
	TenantID  uint   `form:"tenant_id" binding:"required"`
	WebsiteID uint   `form:"website_id" binding:"required"`
	IsRead    *bool  `form:"is_read,omitempty"`
	Cursor    string `form:"cursor"`
	Limit     int    `form:"limit,default=20"`
}

// Response DTOs

// NotificationUserResponse represents a notification user response
type NotificationUserResponse struct {
	NotificationID uint      `json:"notification_id"`
	TenantID       uint      `json:"tenant_id"`
	WebsiteID      uint      `json:"website_id"`
	UserID         uint      `json:"user_id"`
	Title          string    `json:"title"`
	Content        string    `json:"content"`
	IsRead         bool      `json:"is_read"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// NotificationUserListResponse represents a list of user notifications with pagination metadata
type NotificationUserListResponse struct {
	Notifications []NotificationUserResponse `json:"notifications"`
	Meta          PaginationMeta             `json:"meta"`
}
