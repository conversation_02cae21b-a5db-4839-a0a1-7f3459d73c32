CREATE TABLE notification_users (
    notification_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_read B<PERSON><PERSON><PERSON>N DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_tenant_website_user (tenant_id, website_id, user_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_website_id (website_id),
    INDEX idx_user_id (user_id)
);