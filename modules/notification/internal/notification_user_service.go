package internal

import (
	"context"
	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/models"
)

// NotificationUserService defines the interface for notification user operations
type NotificationUserService interface {
	CreateNotificationUser(ctx context.Context, req *dto.CreateNotificationUserRequest) (*models.NotificationUser, error)
	GetNotificationUserByID(ctx context.Context, notificationID uint) (*models.NotificationUser, error)
	GetUserNotifications(ctx context.Context, tenantID uint, websiteID uint, isRead *bool, cursor string, limit int, userID *uint) ([]*models.NotificationUser, string, error)
	MarkAsRead(ctx context.Context, notificationID uint) error
	MarkAllAsRead(ctx context.Context, userID uint, tenantID uint, websiteID uint) error
	GetUnreadCount(ctx context.Context, userID uint, tenantID uint, websiteID uint) (int, error)
	UpdateNotificationUser(ctx context.Context, notificationID uint, req *dto.UpdateNotificationUserRequest) (*models.NotificationUser, error)
	DeleteNotificationUser(ctx context.Context, notificationID uint) error
}
