package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
	"wnapi/modules/integration/socket/repository"
)

// NotificationService interface cho notification business logic
type NotificationService interface {
	// Subscription management
	Subscribe(ctx context.Context, req dto.SubscribeRequest) (*dto.SubscriptionDTO, error)
	Unsubscribe(ctx context.Context, req dto.UnsubscribeRequest) error
	GetUserSubscriptions(ctx context.Context, userID uint, tenantID uint) ([]*dto.SubscriptionDTO, error)
	UpdateSubscriptionStatus(ctx context.Context, subscriptionID uint, isActive bool) error

	// Notification sending
	SendNotification(ctx context.Context, req dto.SendNotificationRequest) error
	BroadcastNotification(ctx context.Context, req dto.BroadcastNotificationRequest) error
	
	// Notification management
	GetUserNotifications(ctx context.Context, userID uint, tenantID uint, page, limit int) ([]*dto.NotificationDTO, int64, error)
	MarkNotificationsAsRead(ctx context.Context, req dto.MarkNotificationReadRequest) error
	
	// Statistics
	GetNotificationStats(ctx context.Context, tenantID uint) (*dto.NotificationStatsDTO, error)
	
	// Cleanup
	CleanupExpiredNotifications(ctx context.Context) (int64, error)
}

// notificationService implementation của NotificationService
type notificationService struct {
	notificationRepo repository.NotificationRepository
	hub              socket.Hub
	eventRouter      socket.EventRouter
	logger           logger.Logger
}

// NewNotificationService tạo NotificationService mới
func NewNotificationService(
	notificationRepo repository.NotificationRepository,
	hub socket.Hub,
	eventRouter socket.EventRouter,
	logger logger.Logger,
) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
		hub:              hub,
		eventRouter:      eventRouter,
		logger:           logger.Named("notification-service"),
	}
}

// Subscribe tạo subscription mới cho user
func (s *notificationService) Subscribe(ctx context.Context, req dto.SubscribeRequest) (*dto.SubscriptionDTO, error) {
	// Create subscription model
	subscription := &models.Subscription{
		UserID:       req.UserID,
		TenantID:     req.TenantID,
		WebsiteID:    req.WebsiteID,
		EventType:    req.EventType,
		Channel:      req.Channel,
		ResourceID:   req.ResourceID,
		ResourceType: req.ResourceType,
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Save to database
	if err := s.notificationRepo.CreateSubscription(ctx, subscription); err != nil {
		s.logger.Error("Failed to create subscription in repository",
			"user_id", req.UserID,
			"event_type", req.EventType,
			"channel", req.Channel,
			"error", err)
		return nil, err
	}

	s.logger.Info("Subscription created successfully",
		"subscription_id", subscription.ID,
		"user_id", req.UserID,
		"event_type", req.EventType,
		"channel", req.Channel)

	// Convert to DTO
	return s.subscriptionModelToDTO(subscription), nil
}

// Unsubscribe xóa subscription
func (s *notificationService) Unsubscribe(ctx context.Context, req dto.UnsubscribeRequest) error {
	// Get user subscriptions
	subscriptions, err := s.notificationRepo.GetUserSubscriptions(ctx, req.UserID, req.TenantID)
	if err != nil {
		return err
	}

	// Find matching subscriptions và delete
	deletedCount := 0
	for _, subscription := range subscriptions {
		shouldDelete := true
		
		// Apply filters
		if req.EventType != "" && subscription.EventType != req.EventType {
			shouldDelete = false
		}
		if req.Channel != "" && subscription.Channel != req.Channel {
			shouldDelete = false
		}
		if req.ResourceID != "" && subscription.ResourceID != req.ResourceID {
			shouldDelete = false
		}

		if shouldDelete {
			if err := s.notificationRepo.DeleteSubscription(ctx, subscription.ID); err != nil {
				s.logger.Error("Failed to delete subscription",
					"subscription_id", subscription.ID,
					"error", err)
			} else {
				deletedCount++
			}
		}
	}

	s.logger.Info("Subscriptions deleted successfully",
		"user_id", req.UserID,
		"tenant_id", req.TenantID,
		"deleted_count", deletedCount)

	return nil
}

// GetUserSubscriptions lấy tất cả subscriptions của user
func (s *notificationService) GetUserSubscriptions(ctx context.Context, userID uint, tenantID uint) ([]*dto.SubscriptionDTO, error) {
	subscriptions, err := s.notificationRepo.GetUserSubscriptions(ctx, userID, tenantID)
	if err != nil {
		s.logger.Error("Failed to get user subscriptions from repository",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	// Convert to DTOs
	subscriptionDTOs := make([]*dto.SubscriptionDTO, len(subscriptions))
	for i, subscription := range subscriptions {
		subscriptionDTOs[i] = s.subscriptionModelToDTO(subscription)
	}

	s.logger.Debug("User subscriptions retrieved successfully",
		"user_id", userID,
		"tenant_id", tenantID,
		"count", len(subscriptionDTOs))

	return subscriptionDTOs, nil
}

// UpdateSubscriptionStatus cập nhật status của subscription
func (s *notificationService) UpdateSubscriptionStatus(ctx context.Context, subscriptionID uint, isActive bool) error {
	if err := s.notificationRepo.UpdateSubscriptionStatus(ctx, subscriptionID, isActive); err != nil {
		s.logger.Error("Failed to update subscription status in repository",
			"subscription_id", subscriptionID,
			"is_active", isActive,
			"error", err)
		return err
	}

	s.logger.Info("Subscription status updated successfully",
		"subscription_id", subscriptionID,
		"is_active", isActive)

	return nil
}

// SendNotification gửi notification đến một user
func (s *notificationService) SendNotification(ctx context.Context, req dto.SendNotificationRequest) error {
	// Validate priority
	if req.Priority == "" {
		req.Priority = "normal"
	}

	// Set default channels if not provided
	if len(req.Channels) == 0 {
		req.Channels = []string{models.ChannelUser, models.ChannelNotification}
	}

	// Send via each channel
	for _, channel := range req.Channels {
		if err := s.sendToChannel(ctx, channel, req); err != nil {
			s.logger.Error("Failed to send notification via channel",
				"user_id", req.UserID,
				"channel", channel,
				"type", req.Type,
				"error", err)
			// Continue with other channels
		}
	}

	s.logger.Info("Notification sent successfully",
		"user_id", req.UserID,
		"tenant_id", req.TenantID,
		"type", req.Type,
		"title", req.Title,
		"channels", req.Channels)

	return nil
}

// BroadcastNotification gửi notification đến nhiều users
func (s *notificationService) BroadcastNotification(ctx context.Context, req dto.BroadcastNotificationRequest) error {
	// Validate priority
	if req.Priority == "" {
		req.Priority = "normal"
	}

	// Set default channels if not provided
	if len(req.Channels) == 0 {
		req.Channels = []string{models.ChannelBroadcast, models.ChannelNotification}
	}

	// Determine target users
	var targetUserIDs []uint
	if len(req.UserIDs) > 0 {
		targetUserIDs = req.UserIDs
	} else {
		// If no specific users, broadcast to all active users in tenant
		// This would need implementation to get active users
		// For now, just use provided user IDs
		targetUserIDs = req.UserIDs
	}

	// Send to each target user
	successCount := 0
	for _, userID := range targetUserIDs {
		userReq := dto.SendNotificationRequest{
			UserID:    userID,
			TenantID:  req.TenantID,
			WebsiteID: req.WebsiteID,
			Type:      req.Type,
			Title:     req.Title,
			Message:   req.Message,
			Data:      req.Data,
			Priority:  req.Priority,
			Category:  req.Category,
			ExpiresAt: req.ExpiresAt,
			Channels:  req.Channels,
		}

		if err := s.SendNotification(ctx, userReq); err != nil {
			s.logger.Warn("Failed to send broadcast notification to user",
				"user_id", userID,
				"type", req.Type,
				"error", err)
		} else {
			successCount++
		}
	}

	// Also send to rooms if specified
	if len(req.RoomIDs) > 0 {
		for _, roomID := range req.RoomIDs {
			if err := s.sendToRoom(ctx, roomID, req); err != nil {
				s.logger.Warn("Failed to send broadcast notification to room",
					"room_id", roomID,
					"type", req.Type,
					"error", err)
			}
		}
	}

	s.logger.Info("Broadcast notification sent successfully",
		"tenant_id", req.TenantID,
		"type", req.Type,
		"title", req.Title,
		"target_users", len(targetUserIDs),
		"success_count", successCount,
		"target_rooms", len(req.RoomIDs))

	return nil
}

// GetUserNotifications lấy notifications của user với pagination
func (s *notificationService) GetUserNotifications(ctx context.Context, userID uint, tenantID uint, page, limit int) ([]*dto.NotificationDTO, int64, error) {
	deliveries, total, err := s.notificationRepo.GetUserDeliveries(ctx, userID, tenantID, page, limit)
	if err != nil {
		s.logger.Error("Failed to get user notifications from repository",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, 0, err
	}

	// Convert to notification DTOs
	notificationDTOs := make([]*dto.NotificationDTO, len(deliveries))
	for i, delivery := range deliveries {
		notificationDTOs[i] = s.deliveryToNotificationDTO(delivery)
	}

	s.logger.Debug("User notifications retrieved successfully",
		"user_id", userID,
		"tenant_id", tenantID,
		"count", len(notificationDTOs),
		"total", total)

	return notificationDTOs, total, nil
}

// MarkNotificationsAsRead đánh dấu notifications đã được đọc
func (s *notificationService) MarkNotificationsAsRead(ctx context.Context, req dto.MarkNotificationReadRequest) error {
	readAt := time.Now()
	
	successCount := 0
	for _, notificationID := range req.NotificationIDs {
		if err := s.notificationRepo.MarkDeliveryAsRead(ctx, notificationID, readAt); err != nil {
			s.logger.Warn("Failed to mark notification as read",
				"notification_id", notificationID,
				"error", err)
		} else {
			successCount++
		}
	}

	s.logger.Info("Notifications marked as read",
		"total_notifications", len(req.NotificationIDs),
		"success_count", successCount)

	return nil
}

// GetNotificationStats lấy thống kê notifications
func (s *notificationService) GetNotificationStats(ctx context.Context, tenantID uint) (*dto.NotificationStatsDTO, error) {
	stats, err := s.notificationRepo.GetNotificationStats(ctx, tenantID)
	if err != nil {
		s.logger.Error("Failed to get notification stats from repository",
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	s.logger.Debug("Notification stats retrieved successfully",
		"tenant_id", tenantID,
		"total_deliveries", stats.DeliveryStats.TotalDeliveries,
		"delivery_rate", stats.DeliveryStats.DeliveryRate)

	return stats, nil
}

// CleanupExpiredNotifications dọn dẹp notifications cũ
func (s *notificationService) CleanupExpiredNotifications(ctx context.Context) (int64, error) {
	// Define cleanup threshold (e.g., 30 days)
	cleanupThreshold := time.Now().Add(-30 * 24 * time.Hour)

	count, err := s.notificationRepo.CleanupExpiredDeliveries(ctx, cleanupThreshold)
	if err != nil {
		s.logger.Error("Failed to cleanup expired notifications",
			"threshold", cleanupThreshold,
			"error", err)
		return 0, err
	}

	s.logger.Info("Expired notifications cleaned up",
		"count", count,
		"threshold", cleanupThreshold)

	return count, nil
}

// sendToChannel gửi notification qua channel cụ thể
func (s *notificationService) sendToChannel(ctx context.Context, channel string, req dto.SendNotificationRequest) error {
	switch channel {
	case models.ChannelUser:
		return s.sendToUser(ctx, req)
	case models.ChannelNotification:
		return s.sendAsNotification(ctx, req)
	case models.ChannelSystem:
		return s.sendAsSystemMessage(ctx, req)
	default:
		return fmt.Errorf("unsupported channel: %s", channel)
	}
}

// sendToUser gửi trực tiếp đến user qua WebSocket
func (s *notificationService) sendToUser(ctx context.Context, req dto.SendNotificationRequest) error {
	// Get user connections
	userConns := s.hub.GetUserConnections(fmt.Sprintf("%d", req.UserID))
	if len(userConns) == 0 {
		s.logger.Debug("No active connections for user",
			"user_id", req.UserID,
			"tenant_id", req.TenantID)
		return nil // Not an error, user just not online
	}

	// Create notification message
	message := socket.NewMessage("notification", "received", map[string]interface{}{
		"type":     req.Type,
		"title":    req.Title,
		"message":  req.Message,
		"data":     req.Data,
		"priority": req.Priority,
		"category": req.Category,
		"timestamp": time.Now(),
	})

	// Send to all user connections
	successCount := 0
	for _, conn := range userConns {
		if req.WebsiteID > 0 && conn.WebsiteID() != fmt.Sprintf("%d", req.WebsiteID) {
			continue // Skip if website doesn't match
		}

		if err := conn.Send(message); err != nil {
			s.logger.Warn("Failed to send notification to user connection",
				"conn_id", conn.ID(),
				"user_id", req.UserID,
				"error", err)
		} else {
			successCount++
		}
	}

	// Create delivery record
	delivery := &models.NotificationDelivery{
		UserID:       req.UserID,
		TenantID:     req.TenantID,
		WebsiteID:    req.WebsiteID,
		Channel:      models.ChannelUser,
		Status:       models.DeliveryStatusDelivered,
		DeliveredAt:  &[]time.Time{time.Now()}[0],
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if successCount == 0 {
		delivery.Status = models.DeliveryStatusFailed
		delivery.ErrorMessage = "No active connections available"
		delivery.DeliveredAt = nil
	}

	if err := s.notificationRepo.CreateDelivery(ctx, delivery); err != nil {
		s.logger.Warn("Failed to create delivery record",
			"user_id", req.UserID,
			"channel", models.ChannelUser,
			"error", err)
	}

	s.logger.Debug("Notification sent to user via WebSocket",
		"user_id", req.UserID,
		"success_count", successCount,
		"total_connections", len(userConns))

	return nil
}

// sendAsNotification lưu notification để user có thể đọc sau
func (s *notificationService) sendAsNotification(ctx context.Context, req dto.SendNotificationRequest) error {
	// Create delivery record
	delivery := &models.NotificationDelivery{
		UserID:      req.UserID,
		TenantID:    req.TenantID,
		WebsiteID:   req.WebsiteID,
		Channel:     models.ChannelNotification,
		Status:      models.DeliveryStatusPending,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.notificationRepo.CreateDelivery(ctx, delivery); err != nil {
		s.logger.Error("Failed to create notification delivery record",
			"user_id", req.UserID,
			"type", req.Type,
			"error", err)
		return err
	}

	s.logger.Debug("Notification saved for later retrieval",
		"user_id", req.UserID,
		"delivery_id", delivery.ID,
		"type", req.Type)

	return nil
}

// sendAsSystemMessage gửi system message
func (s *notificationService) sendAsSystemMessage(ctx context.Context, req dto.SendNotificationRequest) error {
	// Publish system event
	event := &socket.Event{
		Type:         req.Type,
		UserID:       fmt.Sprintf("%d", req.UserID),
		ConnectionID: "", // System event
		Data: map[string]interface{}{
			"title":     req.Title,
			"message":   req.Message,
			"priority":  req.Priority,
			"category":  req.Category,
			"timestamp": time.Now(),
		},
	}

	if err := s.eventRouter.PublishEvent(event); err != nil {
		s.logger.Error("Failed to publish system notification event",
			"user_id", req.UserID,
			"type", req.Type,
			"error", err)
		return err
	}

	s.logger.Debug("System notification event published",
		"user_id", req.UserID,
		"type", req.Type)

	return nil
}

// sendToRoom gửi notification đến room
func (s *notificationService) sendToRoom(ctx context.Context, roomID string, req dto.BroadcastNotificationRequest) error {
	// Note: Room broadcasting not implemented yet
	// TODO: Implement room broadcasting functionality
	s.logger.Debug("Room broadcasting not implemented yet", "room_id", roomID)

	s.logger.Debug("Notification broadcasted to room successfully",
		"room_id", roomID,
		"type", req.Type)

	return nil
}

// subscriptionModelToDTO chuyển đổi subscription model sang DTO
func (s *notificationService) subscriptionModelToDTO(subscription *models.Subscription) *dto.SubscriptionDTO {
	return &dto.SubscriptionDTO{
		ID:           subscription.ID,
		UserID:       subscription.UserID,
		TenantID:     subscription.TenantID,
		WebsiteID:    subscription.WebsiteID,
		EventType:    subscription.EventType,
		Channel:      subscription.Channel,
		ResourceID:   subscription.ResourceID,
		ResourceType: subscription.ResourceType,
		IsActive:     subscription.IsActive,
		CreatedAt:    subscription.CreatedAt,
		UpdatedAt:    subscription.UpdatedAt,
	}
}

// deliveryToNotificationDTO chuyển đổi delivery model sang notification DTO
func (s *notificationService) deliveryToNotificationDTO(delivery *models.NotificationDelivery) *dto.NotificationDTO {
	return &dto.NotificationDTO{
		ID:          delivery.ID,
		TenantID:    delivery.TenantID,
		WebsiteID:   delivery.WebsiteID,
		UserID:      delivery.UserID,
		Type:        delivery.Channel, // Use channel as type for now
		Title:       "Notification",  // Would need to store title in delivery
		Message:     "You have a new notification",
		IsRead:      delivery.Status == models.DeliveryStatusRead,
		ReadAt:      delivery.ReadAt,
		DeliveredAt: delivery.DeliveredAt,
		CreatedAt:   delivery.CreatedAt,
		UpdatedAt:   delivery.UpdatedAt,
	}
}
