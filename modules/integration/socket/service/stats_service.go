package service

import (
	"context"
	"runtime"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/dto"
)

// StatsService interface cho statistics business logic
type StatsService interface {
	// Overall statistics
	GetOverallStats(ctx context.Context, tenantID uint) (*dto.StatsDTO, error)
	GetSystemStats(ctx context.Context) (*dto.SystemStatsDTO, error)
	GetPerformanceStats(ctx context.Context) (*dto.PerformanceStatsDTO, error)
	
	// Health checks
	GetHealthCheck(ctx context.Context) (*dto.HealthCheckDTO, error)
	
	// Event statistics
	GetEventStats(ctx context.Context) (*dto.EventStatsDTO, error)
	
	// Real-time metrics
	GetRealTimeMetrics(ctx context.Context, req dto.MetricsRequest) (interface{}, error)
}

// statsService implementation của StatsService
type statsService struct {
	hub         socket.Hub
	eventRouter socket.EventRouter
	logger      logger.Logger
	startTime   time.Time
}

// NewStatsService tạo StatsService mới
func NewStatsService(
	hub socket.Hub,
	eventRouter socket.EventRouter,
	logger logger.Logger,
) StatsService {
	return &statsService{
		hub:         hub,
		eventRouter: eventRouter,
		logger:      logger.Named("stats-service"),
		startTime:   time.Now(),
	}
}

// GetOverallStats lấy tổng hợp tất cả statistics
func (s *statsService) GetOverallStats(ctx context.Context, tenantID uint) (*dto.StatsDTO, error) {
	stats := &dto.StatsDTO{
		LastUpdated: time.Now(),
	}

	// Get system stats
	systemStats, err := s.GetSystemStats(ctx)
	if err != nil {
		s.logger.Warn("Failed to get system stats", "error", err)
		systemStats = &dto.SystemStatsDTO{} // Use empty stats
	}
	stats.System = *systemStats

	// Get performance stats
	perfStats, err := s.GetPerformanceStats(ctx)
	if err != nil {
		s.logger.Warn("Failed to get performance stats", "error", err)
		perfStats = &dto.PerformanceStatsDTO{} // Use empty stats
	}
	stats.Performance = *perfStats

	// Get event stats
	eventStats, err := s.GetEventStats(ctx)
	if err != nil {
		s.logger.Warn("Failed to get event stats", "error", err)
		eventStats = &dto.EventStatsDTO{} // Use empty stats
	}
	stats.Events = *eventStats

	// Note: Hub statistics not implemented yet
	// TODO: Implement hub statistics integration
	stats.Connections = dto.ConnectionStatsDTO{
		TotalConnections:    0,
		ActiveConnections:   0,
		ConnectionsByStatus: make(map[string]int64),
		ConnectionsByTenant: make(map[uint]int64),
		LastUpdated:        time.Now(),
		Uptime:             time.Since(s.startTime).String(),
	}

	stats.Rooms = dto.RoomStatsDTO{
		TotalRooms:    0,
		ActiveRooms:   0,
		RoomsByType:   make(map[string]int64),
		RoomsByStatus: make(map[string]int64),
		LastUpdated:   time.Now(),
	}

	s.logger.Debug("Overall stats retrieved successfully",
		"tenant_id", tenantID,
		"total_connections", stats.Connections.TotalConnections,
		"total_rooms", stats.Rooms.TotalRooms)

	return stats, nil
}

// GetSystemStats lấy system-level statistics
func (s *statsService) GetSystemStats(ctx context.Context) (*dto.SystemStatsDTO, error) {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	uptime := time.Since(s.startTime)

	stats := &dto.SystemStatsDTO{
		ServerName:    "WNAPI-v2-Socket",
		Version:       "1.0.0",
		Uptime:        uptime,
		StartTime:     s.startTime,
		CurrentTime:   time.Now(),
		GoVersion:     runtime.Version(),
		NumGoroutines: runtime.NumGoroutine(),
		MemoryUsage: dto.MemoryStats{
			Allocated:      memStats.Alloc,
			TotalAllocated: memStats.TotalAlloc,
			SystemMemory:   memStats.Sys,
			NumGC:          memStats.NumGC,
			HeapInUse:      memStats.HeapInuse,
			StackInUse:     memStats.StackInuse,
		},
		CPUUsage:    s.getCPUUsage(),
		LoadAverage: s.getLoadAverage(),
	}

	s.logger.Debug("System stats retrieved successfully",
		"uptime", uptime,
		"goroutines", stats.NumGoroutines,
		"memory_allocated", stats.MemoryUsage.Allocated)

	return stats, nil
}

// GetPerformanceStats lấy performance metrics
func (s *statsService) GetPerformanceStats(ctx context.Context) (*dto.PerformanceStatsDTO, error) {
	// Note: Hub statistics not implemented yet
	// TODO: Implement hub statistics integration
	stats := &dto.PerformanceStatsDTO{
		ConnectionPoolStats: dto.ConnectionPoolStatsDTO{
			MaxConnections:    10000, // From constants
			ActiveConnections: 0,
			PoolUtilization:   0.0,
		},
		ResourceUtilization: dto.ResourceUtilizationDTO{
			CPUUsage:        s.getCPUUsage(),
			MemoryUsage:     s.getMemoryUsage(),
			FileDescriptors: s.getFileDescriptorCount(),
			ThreadCount:     runtime.NumGoroutine(),
		},
		BottleneckAnalysis: []dto.BottleneckDTO{},
	}

	// Calculate pool utilization
	if stats.ConnectionPoolStats.MaxConnections > 0 {
		stats.ConnectionPoolStats.PoolUtilization = float64(stats.ConnectionPoolStats.ActiveConnections) / float64(stats.ConnectionPoolStats.MaxConnections) * 100
	}

	// Note: Event metrics not implemented yet
	// TODO: Implement event metrics collection
	stats.AvgResponseTime = 0

	s.logger.Debug("Performance stats retrieved successfully",
		"pool_utilization", stats.ConnectionPoolStats.PoolUtilization,
		"cpu_usage", stats.ResourceUtilization.CPUUsage,
		"memory_usage", stats.ResourceUtilization.MemoryUsage)

	return stats, nil
}

// GetHealthCheck thực hiện health check
func (s *statsService) GetHealthCheck(ctx context.Context) (*dto.HealthCheckDTO, error) {
	health := &dto.HealthCheckDTO{
		Status:    dto.HealthStatusHealthy,
		Timestamp: time.Now(),
		Uptime:    time.Since(s.startTime),
		Checks:    make(map[string]dto.HealthCheckItem),
		Overview: dto.HealthOverviewDTO{
			OverallStatus: dto.HealthStatusHealthy,
		},
	}

	// Check hub health
	health.Checks["hub"] = s.checkHubHealth()
	
	// Check event router health
	health.Checks["event_router"] = s.checkEventRouterHealth()
	
	// Check memory usage
	health.Checks["memory"] = s.checkMemoryHealth()
	
	// Check goroutine count
	health.Checks["goroutines"] = s.checkGoroutineHealth()

	// Calculate overview
	healthyCount := 0
	unhealthyCount := 0
	warningCount := 0
	totalChecks := len(health.Checks)

	for _, check := range health.Checks {
		switch check.Status {
		case dto.HealthStatusHealthy:
			healthyCount++
		case dto.HealthStatusUnhealthy:
			unhealthyCount++
		case dto.HealthStatusWarning:
			warningCount++
		}
	}

	health.Overview.HealthyChecks = healthyCount
	health.Overview.UnhealthyChecks = unhealthyCount
	health.Overview.WarningChecks = warningCount
	health.Overview.TotalChecks = totalChecks

	// Determine overall status
	if unhealthyCount > 0 {
		health.Status = dto.HealthStatusUnhealthy
		health.Overview.OverallStatus = dto.HealthStatusUnhealthy
	} else if warningCount > 0 {
		health.Status = dto.HealthStatusWarning
		health.Overview.OverallStatus = dto.HealthStatusWarning
	}

	s.logger.Debug("Health check completed",
		"overall_status", health.Status,
		"healthy_checks", healthyCount,
		"warning_checks", warningCount,
		"unhealthy_checks", unhealthyCount)

	return health, nil
}

// GetEventStats lấy event processing statistics
func (s *statsService) GetEventStats(ctx context.Context) (*dto.EventStatsDTO, error) {
	// Note: Event metrics not implemented yet
	// TODO: Implement event metrics collection
	stats := &dto.EventStatsDTO{
		TotalEvents:        0,
		TotalErrors:        0,
		EventCounts:        make(map[string]int64),
		ErrorCounts:        make(map[string]int64),
		EventDurations:     make(map[string]time.Duration),
		RecentEvents:       make([]dto.EventLogDTO, 0),
		ErrorRate:          0.0,
		AvgProcessingTime:  0,
		EventsPerSecond:    0.0,
	}

	s.logger.Debug("Event stats retrieved successfully (placeholder)",
		"total_events", stats.TotalEvents,
		"total_errors", stats.TotalErrors,
		"error_rate", stats.ErrorRate,
		"events_per_second", stats.EventsPerSecond)

	return stats, nil
}

// GetRealTimeMetrics lấy real-time metrics theo yêu cầu
func (s *statsService) GetRealTimeMetrics(ctx context.Context, req dto.MetricsRequest) (interface{}, error) {
	switch req.Format {
	case "json":
		return s.getJSONMetrics(ctx, req)
	case "prometheus":
		return s.getPrometheusMetrics(ctx, req)
	case "csv":
		return s.getCSVMetrics(ctx, req)
	default:
		return s.getJSONMetrics(ctx, req)
	}
}

// Helper methods

func (s *statsService) getInt64FromMap(m map[string]interface{}, key string) int64 {
	if val, ok := m[key].(int64); ok {
		return val
	}
	if val, ok := m[key].(int); ok {
		return int64(val)
	}
	return 0
}

func (s *statsService) getCPUUsage() float64 {
	// This is a simplified CPU usage calculation
	// In production, you might want to use a more sophisticated approach
	return 0.0 // Placeholder
}

func (s *statsService) getMemoryUsage() float64 {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	// Calculate memory usage percentage (simplified)
	return float64(memStats.Alloc) / float64(memStats.Sys) * 100
}

func (s *statsService) getLoadAverage() []float64 {
	// This would need platform-specific implementation
	// Returning placeholder values
	return []float64{0.1, 0.2, 0.3}
}

func (s *statsService) getFileDescriptorCount() int {
	// This would need platform-specific implementation
	return 0
}

func (s *statsService) analyzeBottlenecks(hubStats map[string]interface{}) []dto.BottleneckDTO {
	var bottlenecks []dto.BottleneckDTO
	
	// Check connection pool utilization
	totalConns := s.getInt64FromMap(hubStats, "total_connections")
	if totalConns > 8000 { // 80% of max 10,000
		bottlenecks = append(bottlenecks, dto.BottleneckDTO{
			Type:        "connection_pool",
			Severity:    dto.BottleneckSeverityHigh,
			Description: "Connection pool utilization is high",
			Metric:      "total_connections",
			Value:       float64(totalConns),
			Threshold:   8000,
			Timestamp:   time.Now(),
		})
	}
	
	// Check memory usage
	memUsage := s.getMemoryUsage()
	if memUsage > 80 {
		severity := dto.BottleneckSeverityMedium
		if memUsage > 90 {
			severity = dto.BottleneckSeverityHigh
		}
		
		bottlenecks = append(bottlenecks, dto.BottleneckDTO{
			Type:        "memory",
			Severity:    severity,
			Description: "Memory usage is high",
			Metric:      "memory_usage_percent",
			Value:       memUsage,
			Threshold:   80,
			Timestamp:   time.Now(),
		})
	}
	
	// Check goroutine count
	goroutineCount := runtime.NumGoroutine()
	if goroutineCount > 1000 {
		bottlenecks = append(bottlenecks, dto.BottleneckDTO{
			Type:        "goroutines",
			Severity:    dto.BottleneckSeverityMedium,
			Description: "High number of goroutines",
			Metric:      "goroutine_count",
			Value:       float64(goroutineCount),
			Threshold:   1000,
			Timestamp:   time.Now(),
		})
	}
	
	return bottlenecks
}

func (s *statsService) checkHubHealth() dto.HealthCheckItem {
	start := time.Now()
	
	// Note: Hub health check not implemented yet
	// TODO: Implement hub health check
	status := dto.HealthStatusHealthy
	message := "Hub health check not implemented"
	
	return dto.HealthCheckItem{
		Status:    status,
		Message:   message,
		Duration:  time.Since(start),
		LastCheck: time.Now(),
	}
}

func (s *statsService) checkEventRouterHealth() dto.HealthCheckItem {
	start := time.Now()
	
	status := dto.HealthStatusHealthy
	message := "Event router is operating normally"
	
	// Note: Event router health check not implemented yet
	// TODO: Implement event router health check
	if s.eventRouter == nil {
		status = dto.HealthStatusWarning
		message = "Event router is not configured"
	}
	
	return dto.HealthCheckItem{
		Status:    status,
		Message:   message,
		Duration:  time.Since(start),
		LastCheck: time.Now(),
	}
}

func (s *statsService) checkMemoryHealth() dto.HealthCheckItem {
	start := time.Now()
	
	memUsage := s.getMemoryUsage()
	
	status := dto.HealthStatusHealthy
	message := "Memory usage is normal"
	
	if memUsage > 90 {
		status = dto.HealthStatusUnhealthy
		message = "Memory usage is critically high"
	} else if memUsage > 80 {
		status = dto.HealthStatusWarning
		message = "Memory usage is high"
	}
	
	return dto.HealthCheckItem{
		Status:    status,
		Message:   message,
		Duration:  time.Since(start),
		LastCheck: time.Now(),
		Metadata: map[string]interface{}{
			"memory_usage_percent": memUsage,
		},
	}
}

func (s *statsService) checkGoroutineHealth() dto.HealthCheckItem {
	start := time.Now()
	
	goroutineCount := runtime.NumGoroutine()
	
	status := dto.HealthStatusHealthy
	message := "Goroutine count is normal"
	
	if goroutineCount > 2000 {
		status = dto.HealthStatusWarning
		message = "High number of goroutines"
	}
	
	return dto.HealthCheckItem{
		Status:    status,
		Message:   message,
		Duration:  time.Since(start),
		LastCheck: time.Now(),
		Metadata: map[string]interface{}{
			"goroutine_count": goroutineCount,
		},
	}
}

func (s *statsService) getJSONMetrics(ctx context.Context, req dto.MetricsRequest) (interface{}, error) {
	// Return overall stats as JSON
	return s.GetOverallStats(ctx, req.TenantID)
}

func (s *statsService) getPrometheusMetrics(ctx context.Context, req dto.MetricsRequest) (interface{}, error) {
	// This would format metrics in Prometheus format
	// For now, return a simple string representation
	stats, err := s.GetOverallStats(ctx, req.TenantID)
	if err != nil {
		return nil, err
	}
	
	prometheusData := map[string]interface{}{
		"format": "prometheus",
		"metrics": map[string]interface{}{
			"socket_connections_total":    stats.Connections.TotalConnections,
			"socket_connections_active":   stats.Connections.ActiveConnections,
			"socket_rooms_total":         stats.Rooms.TotalRooms,
			"socket_rooms_active":        stats.Rooms.ActiveRooms,
			"socket_events_total":        stats.Events.TotalEvents,
			"socket_events_errors_total": stats.Events.TotalErrors,
		},
	}
	
	return prometheusData, nil
}

func (s *statsService) getCSVMetrics(ctx context.Context, req dto.MetricsRequest) (interface{}, error) {
	// This would format metrics as CSV
	// For now, return a simple structure
	stats, err := s.GetOverallStats(ctx, req.TenantID)
	if err != nil {
		return nil, err
	}
	
	csvData := map[string]interface{}{
		"format": "csv",
		"headers": []string{
			"timestamp", "total_connections", "active_connections", 
			"total_rooms", "active_rooms", "total_events", "total_errors",
		},
		"data": [][]interface{}{
			{
				time.Now().Format(time.RFC3339),
				stats.Connections.TotalConnections,
				stats.Connections.ActiveConnections,
				stats.Rooms.TotalRooms,
				stats.Rooms.ActiveRooms,
				stats.Events.TotalEvents,
				stats.Events.TotalErrors,
			},
		},
	}
	
	return csvData, nil
}
