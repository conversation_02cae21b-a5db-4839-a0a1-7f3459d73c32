package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/dto"
)

// SocketService interface cho main socket orchestrator
type SocketService interface {
	// Orchestration methods
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error
	
	// Service delegation
	ConnectionService() ConnectionService
	RoomService() RoomService
	NotificationService() NotificationService
	StatsService() StatsService
	
	// High-level operations
	HandleUserConnect(ctx context.Context, userID uint, tenantID uint, websiteID uint) error
	HandleUserDisconnect(ctx context.Context, userID uint, tenantID uint) error
	BroadcastSystemMessage(ctx context.Context, message string, tenantID uint) error
	
	// Maintenance operations
	PerformMaintenance(ctx context.Context) error
	GetSystemStatus(ctx context.Context) (*dto.SystemStatusDTO, error)
}



// socketService implementation của SocketService
type socketService struct {
	connectionService   ConnectionService
	roomService         RoomService
	notificationService NotificationService
	statsService        StatsService
	hub                 socket.Hub
	eventRouter         socket.EventRouter
	logger              logger.Logger
	startTime           time.Time
	lastMaintenance     time.Time
}

// NewSocketService tạo SocketService mới
func NewSocketService(
	connectionService ConnectionService,
	roomService RoomService,
	notificationService NotificationService,
	statsService StatsService,
	hub socket.Hub,
	eventRouter socket.EventRouter,
	logger logger.Logger,
) SocketService {
	return &socketService{
		connectionService:   connectionService,
		roomService:         roomService,
		notificationService: notificationService,
		statsService:        statsService,
		hub:                 hub,
		eventRouter:         eventRouter,
		logger:              logger.Named("socket-service"),
		startTime:           time.Now(),
		lastMaintenance:     time.Now(),
	}
}

// Initialize khởi tạo socket service
func (s *socketService) Initialize(ctx context.Context) error {
	s.logger.Info("Initializing Socket Service...")

	// Register default event handlers
	if err := s.registerEventHandlers(); err != nil {
		s.logger.Error("Failed to register event handlers", "error", err)
		return err
	}

	// Setup maintenance scheduler
	go s.maintenanceScheduler(ctx)

	// Start health monitor
	go s.healthMonitor(ctx)

	s.logger.Info("Socket Service initialized successfully",
		"start_time", s.startTime)

	return nil
}

// Shutdown tắt socket service
func (s *socketService) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down Socket Service...")

	// Note: Shutdown notification not implemented yet
	// TODO: Implement graceful shutdown notification
	s.logger.Info("Preparing for graceful shutdown")
	
	// Give connections time to close gracefully
	time.Sleep(5 * time.Second)

	// Perform final cleanup
	if err := s.performCleanup(ctx); err != nil {
		s.logger.Warn("Cleanup failed during shutdown", "error", err)
	}

	s.logger.Info("Socket Service shutdown completed")
	return nil
}

// Service getters
func (s *socketService) ConnectionService() ConnectionService {
	return s.connectionService
}

func (s *socketService) RoomService() RoomService {
	return s.roomService
}

func (s *socketService) NotificationService() NotificationService {
	return s.notificationService
}

func (s *socketService) StatsService() StatsService {
	return s.statsService
}

// HandleUserConnect xử lý khi user connect
func (s *socketService) HandleUserConnect(ctx context.Context, userID uint, tenantID uint, websiteID uint) error {
	s.logger.Debug("Handling user connect",
		"user_id", userID,
		"tenant_id", tenantID,
		"website_id", websiteID)

	// Send welcome notification
	welcomeReq := dto.SendNotificationRequest{
		UserID:    userID,
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Type:      "system.welcome",
		Title:     "Welcome",
		Message:   "You are now connected to the system",
		Priority:  "normal",
		Category:  "system",
		Channels:  []string{"user"},
	}

	if err := s.notificationService.SendNotification(ctx, welcomeReq); err != nil {
		s.logger.Warn("Failed to send welcome notification",
			"user_id", userID,
			"error", err)
		// Don't return error, this is not critical
	}

	// Publish user online event
	event := &socket.Event{
		Type:   "user.online",
		UserID: fmt.Sprintf("%d", userID),
		Data: map[string]interface{}{
			"user_id":    userID,
			"tenant_id":  tenantID,
			"website_id": websiteID,
			"timestamp":  time.Now(),
		},
	}

	if err := s.eventRouter.PublishEvent(event); err != nil {
		s.logger.Warn("Failed to publish user online event",
			"user_id", userID,
			"error", err)
	}

	return nil
}

// HandleUserDisconnect xử lý khi user disconnect
func (s *socketService) HandleUserDisconnect(ctx context.Context, userID uint, tenantID uint) error {
	s.logger.Debug("Handling user disconnect",
		"user_id", userID,
		"tenant_id", tenantID)

	// Check if user has any remaining connections
	userConns := s.hub.GetUserConnections(fmt.Sprintf("%d", userID))
	if len(userConns) > 0 {
		s.logger.Debug("User still has active connections",
			"user_id", userID,
			"remaining_connections", len(userConns))
		return nil // User still connected via other connections
	}

	// Publish user offline event
	event := &socket.Event{
		Type:   "user.offline",
		UserID: fmt.Sprintf("%d", userID),
		Data: map[string]interface{}{
			"user_id":   userID,
			"tenant_id": tenantID,
			"timestamp": time.Now(),
		},
	}

	if err := s.eventRouter.PublishEvent(event); err != nil {
		s.logger.Warn("Failed to publish user offline event",
			"user_id", userID,
			"error", err)
	}

	// Update user's last seen in all rooms
	userRooms, err := s.roomService.GetUserRooms(ctx, userID, tenantID)
	if err != nil {
		s.logger.Warn("Failed to get user rooms for last seen update",
			"user_id", userID,
			"error", err)
	} else {
		for _, room := range userRooms {
			// Update last seen would need to be implemented
			s.logger.Debug("Updated last seen for user in room",
				"user_id", userID,
				"room_id", room.ID)
		}
	}

	return nil
}

// BroadcastSystemMessage gửi system message tới tất cả users trong tenant
func (s *socketService) BroadcastSystemMessage(ctx context.Context, message string, tenantID uint) error {
	s.logger.Info("Broadcasting system message",
		"tenant_id", tenantID,
		"message", message)

	// Broadcast via notification service
	broadcastReq := dto.BroadcastNotificationRequest{
		TenantID: tenantID,
		Type:     "system.broadcast",
		Title:    "System Message",
		Message:  message,
		Priority: "high",
		Category: "system",
		Channels: []string{"broadcast", "system"},
	}

	if err := s.notificationService.BroadcastNotification(ctx, broadcastReq); err != nil {
		s.logger.Error("Failed to broadcast system message",
			"tenant_id", tenantID,
			"error", err)
		return err
	}

	// Note: Global broadcasting not implemented yet
	// TODO: Implement global broadcasting functionality

	return nil
}

// PerformMaintenance thực hiện maintenance tasks
func (s *socketService) PerformMaintenance(ctx context.Context) error {
	s.logger.Info("Performing maintenance tasks...")
	
	startTime := time.Now()
	var errors []error

	// Cleanup inactive connections
	if connCleaned, err := s.connectionService.CleanupInactiveConnections(ctx); err != nil {
		s.logger.Error("Failed to cleanup inactive connections", "error", err)
		errors = append(errors, err)
	} else {
		s.logger.Debug("Cleaned up inactive connections", "count", connCleaned)
	}

	// Cleanup expired notifications
	if notifCleaned, err := s.notificationService.CleanupExpiredNotifications(ctx); err != nil {
		s.logger.Error("Failed to cleanup expired notifications", "error", err)
		errors = append(errors, err)
	} else {
		s.logger.Debug("Cleaned up expired notifications", "count", notifCleaned)
	}

	// Update maintenance timestamp
	s.lastMaintenance = time.Now()
	
	duration := time.Since(startTime)
	s.logger.Info("Maintenance completed",
		"duration", duration,
		"errors", len(errors))

	if len(errors) > 0 {
		return errors[0] // Return first error
	}

	return nil
}

// GetSystemStatus lấy overall system status
func (s *socketService) GetSystemStatus(ctx context.Context) (*dto.SystemStatusDTO, error) {
	// Get overall stats
	stats, err := s.statsService.GetOverallStats(ctx, 0)
	if err != nil {
		s.logger.Error("Failed to get overall stats", "error", err)
		return nil, err
	}

	// Get health check
	health, err := s.statsService.GetHealthCheck(ctx)
	if err != nil {
		s.logger.Error("Failed to get health check", "error", err)
		return nil, err
	}

	// Build health checks map
	healthChecks := make(map[string]string)
	for name, check := range health.Checks {
		healthChecks[name] = check.Status
	}

	status := &dto.SystemStatusDTO{
		Status:           health.Status,
		Uptime:           time.Since(s.startTime),
		TotalConnections: stats.Connections.TotalConnections,
		TotalRooms:       stats.Rooms.TotalRooms,
		TotalEvents:      stats.Events.TotalEvents,
		ErrorRate:        stats.Events.ErrorRate,
		MemoryUsage:      float64(stats.System.MemoryUsage.Allocated),
		LastMaintenance:  s.lastMaintenance,
		HealthChecks:     healthChecks,
		ActiveServices: []string{
			"connection_service",
			"room_service", 
			"notification_service",
			"stats_service",
		},
		Timestamp: time.Now(),
	}

	s.logger.Debug("System status retrieved",
		"status", status.Status,
		"uptime", status.Uptime,
		"total_connections", status.TotalConnections)

	return status, nil
}

// Private helper methods

func (s *socketService) registerEventHandlers() error {
	s.logger.Debug("Registering default event handlers...")

	// Register connection event handlers
	s.eventRouter.RegisterHandler("connection.ping", s.handlePingEvent)
	s.eventRouter.RegisterHandler("connection.heartbeat", s.handleHeartbeatEvent)
	
	// Register room event handlers
	s.eventRouter.RegisterHandler("room.join", s.handleRoomJoinEvent)
	s.eventRouter.RegisterHandler("room.leave", s.handleRoomLeaveEvent)
	s.eventRouter.RegisterHandler("room.message", s.handleRoomMessageEvent)
	
	// Register notification event handlers
	s.eventRouter.RegisterHandler("notification.subscribe", s.handleNotificationSubscribeEvent)
	s.eventRouter.RegisterHandler("notification.unsubscribe", s.handleNotificationUnsubscribeEvent)
	
	// Add authentication middleware
	s.eventRouter.Use(socket.AuthenticationMiddleware())
	
	// Add logging middleware
	s.eventRouter.Use(socket.LoggingMiddleware(s.logger))
	
	// Add rate limiting middleware
	s.eventRouter.Use(socket.RateLimitMiddleware(100, time.Minute))

	s.logger.Debug("Event handlers registered successfully")
	return nil
}

func (s *socketService) maintenanceScheduler(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour) // Run maintenance every hour
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Debug("Maintenance scheduler stopped")
			return
		case <-ticker.C:
			if err := s.PerformMaintenance(ctx); err != nil {
				s.logger.Error("Scheduled maintenance failed", "error", err)
			}
		}
	}
}

func (s *socketService) healthMonitor(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // Check health every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Debug("Health monitor stopped")
			return
		case <-ticker.C:
			health, err := s.statsService.GetHealthCheck(ctx)
			if err != nil {
				s.logger.Warn("Health check failed", "error", err)
				continue
			}

			if health.Status == dto.HealthStatusUnhealthy {
				s.logger.Warn("System health is unhealthy",
					"unhealthy_checks", health.Overview.UnhealthyChecks,
					"warning_checks", health.Overview.WarningChecks)
			}
		}
	}
}

func (s *socketService) performCleanup(ctx context.Context) error {
	s.logger.Debug("Performing final cleanup...")

	// Note: Hub statistics not implemented yet
	// TODO: Implement connection cleanup using hub statistics

	return nil
}

func (s *socketService) getInt64FromMap(m map[string]interface{}, key string) int64 {
	if val, ok := m[key].(int64); ok {
		return val
	}
	if val, ok := m[key].(int); ok {
		return int64(val)
	}
	if val, ok := m[key].(float64); ok {
		return int64(val)
	}
	return 0
}

// parseUint helper function to convert string to uint
func parseUint(s string) uint {
	if val, err := strconv.ParseUint(s, 10, 32); err == nil {
		return uint(val)
	}
	return 0
}

// Event handler implementations

func (s *socketService) handlePingEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	// Send pong response
	pongMsg := socket.NewMessage("connection", "pong", map[string]interface{}{
		"timestamp": time.Now(),
	})
	return conn.Send(pongMsg)
}

func (s *socketService) handleHeartbeatEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	// Update connection activity
	connID := conn.ID()
	return s.connectionService.UpdateConnectionActivity(ctx, connID)
}

func (s *socketService) handleRoomJoinEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	roomID, ok := data["room_id"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	req := dto.JoinRoomRequest{
		UserID: parseUint(conn.UserID()),
		Role:   "member",
	}

	return s.roomService.JoinRoom(ctx, roomID, req)
}

func (s *socketService) handleRoomLeaveEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	roomID, ok := data["room_id"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	req := dto.LeaveRoomRequest{
		UserID: parseUint(conn.UserID()),
		Reason: "user_request",
	}

	return s.roomService.LeaveRoom(ctx, roomID, req)
}

func (s *socketService) handleRoomMessageEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	roomID, ok := data["room_id"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	message, ok := data["message"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	req := dto.BroadcastToRoomRequest{
		Type:  "message",
		Event: "room.message_received",
		Data: map[string]interface{}{
			"message":   message,
			"user_id":   conn.UserID(),
			"timestamp": time.Now(),
		},
		ExcludeUserIDs: []uint{parseUint(conn.UserID())}, // Don't send back to sender
	}

	return s.roomService.BroadcastToRoom(ctx, roomID, req)
}

func (s *socketService) handleNotificationSubscribeEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	eventType, ok := data["event_type"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	channel, ok := data["channel"].(string)
	if !ok {
		channel = "notification"
	}

	req := dto.SubscribeRequest{
		UserID:    parseUint(conn.UserID()),
		TenantID:  parseUint(conn.TenantID()),
		WebsiteID: parseUint(conn.WebsiteID()),
		EventType: eventType,
		Channel:   channel,
	}

	_, err := s.notificationService.Subscribe(ctx, req)
	return err
}

func (s *socketService) handleNotificationUnsubscribeEvent(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
	eventType, ok := data["event_type"].(string)
	if !ok {
		return socket.ErrInvalidEvent
	}

	channel, ok := data["channel"].(string)
	if !ok {
		channel = "notification"
	}

	req := dto.UnsubscribeRequest{
		UserID:    parseUint(conn.UserID()),
		TenantID:  parseUint(conn.TenantID()),
		WebsiteID: parseUint(conn.WebsiteID()),
		EventType: eventType,
		Channel:   channel,
	}

	return s.notificationService.Unsubscribe(ctx, req)
}
