package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/pagination"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
	"wnapi/modules/integration/socket/repository"
)

// ConnectionService interface cho connection business logic
type ConnectionService interface {
	// Connection management
	GetConnections(ctx context.Context, filter dto.ConnectionFilter, page, limit int) ([]*dto.ConnectionDTO, int64, error)
	GetConnectionsWithCursor(ctx context.Context, filter *dto.ConnectionFilter, params *pagination.Params) ([]*dto.ConnectionDTO, string, bool, error)
	GetConnection(ctx context.Context, id string) (*dto.ConnectionDTO, error)
	GetUserConnections(ctx context.Context, userID uint, tenantID uint) ([]*dto.ConnectionDTO, error)
	CloseConnection(ctx context.Context, id string, reason string, force bool) error
	BroadcastToUser(ctx context.Context, req dto.BroadcastToUserRequest) error
	
	// Statistics and monitoring
	GetConnectionStats(ctx context.Context, tenantID uint) (*dto.ConnectionStatsDTO, error)
	GetUserConnectionCount(ctx context.Context, userID uint, tenantID uint) (int64, error)
	
	// Health checks
	UpdateConnectionActivity(ctx context.Context, id string) error
	CleanupInactiveConnections(ctx context.Context) (int64, error)
}

// connectionService implementation của ConnectionService
type connectionService struct {
	connectionRepo repository.ConnectionRepository
	hub           socket.Hub
	logger        logger.Logger
}

// NewConnectionService tạo ConnectionService mới
func NewConnectionService(
	connectionRepo repository.ConnectionRepository,
	hub socket.Hub,
	logger logger.Logger,
) ConnectionService {
	return &connectionService{
		connectionRepo: connectionRepo,
		hub:           hub,
		logger:        logger.Named("connection-service"),
	}
}

// GetConnections lấy danh sách connections với filter và pagination
func (s *connectionService) GetConnections(ctx context.Context, filter dto.ConnectionFilter, page, limit int) ([]*dto.ConnectionDTO, int64, error) {
	connections, total, err := s.connectionRepo.List(ctx, filter, page, limit)
	if err != nil {
		s.logger.Error("Failed to get connections from repository", "error", err)
		return nil, 0, err
	}

	// Convert to DTOs
	connectionDTOs := make([]*dto.ConnectionDTO, len(connections))
	for i, conn := range connections {
		connectionDTOs[i] = s.modelToDTO(conn)
	}

	s.logger.Debug("Connections retrieved successfully",
		"count", len(connectionDTOs),
		"total", total,
		"page", page,
		"limit", limit)

	return connectionDTOs, total, nil
}

// GetConnectionsWithCursor lấy danh sách connections với cursor-based pagination
func (s *connectionService) GetConnectionsWithCursor(ctx context.Context, filter *dto.ConnectionFilter, params *pagination.Params) ([]*dto.ConnectionDTO, string, bool, error) {
	connections, nextCursor, hasMore, err := s.connectionRepo.ListWithCursor(ctx, filter, params)
	if err != nil {
		s.logger.Error("Failed to get connections with cursor from repository", "error", err)
		return nil, "", false, err
	}

	// Convert to DTOs
	connectionDTOs := make([]*dto.ConnectionDTO, len(connections))
	for i, conn := range connections {
		connectionDTOs[i] = s.modelToDTO(conn)
	}

	s.logger.Debug("Connections retrieved successfully with cursor",
		"count", len(connectionDTOs),
		"has_more", hasMore,
		"limit", params.Limit)

	return connectionDTOs, nextCursor, hasMore, nil
}

// GetConnection lấy thông tin một connection cụ thể
func (s *connectionService) GetConnection(ctx context.Context, id string) (*dto.ConnectionDTO, error) {
	connection, err := s.connectionRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get connection from repository",
			"connection_id", id,
			"error", err)
		return nil, err
	}

	if connection == nil {
		s.logger.Warn("Connection not found",
			"connection_id", id)
		return nil, nil
	}

	// Check if connection is still active in hub
	hubConn, exists := s.hub.GetConnection(id)
	if exists {
		// Update connection info from hub
		connection.LastActivity = hubConn.LastActivity()
		connection.Status = models.ConnectionStatusActive
	}

	connectionDTO := s.modelToDTO(connection)

	s.logger.Debug("Connection retrieved successfully",
		"connection_id", id)

	return connectionDTO, nil
}

// GetUserConnections lấy tất cả connections của user
func (s *connectionService) GetUserConnections(ctx context.Context, userID uint, tenantID uint) ([]*dto.ConnectionDTO, error) {
	connections, err := s.connectionRepo.GetByUserID(ctx, userID, tenantID)
	if err != nil {
		s.logger.Error("Failed to get user connections from repository",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	// Convert to DTOs and update status from hub
	connectionDTOs := make([]*dto.ConnectionDTO, len(connections))
	for i, conn := range connections {
		// Check if connection is still active in hub
	hubConn, exists := s.hub.GetConnection(conn.ID)
	if exists {
		conn.LastActivity = hubConn.LastActivity()
		conn.Status = models.ConnectionStatusActive
	}
		connectionDTOs[i] = s.modelToDTO(conn)
	}

	s.logger.Debug("User connections retrieved successfully",
		"user_id", userID,
		"tenant_id", tenantID,
		"count", len(connectionDTOs))

	return connectionDTOs, nil
}

// CloseConnection đóng một connection
func (s *connectionService) CloseConnection(ctx context.Context, id string, reason string, force bool) error {
	// Get connection from hub
	hubConn, exists := s.hub.GetConnection(id)
	if !exists && !force {
		s.logger.Warn("Connection not found in hub",
			"connection_id", id)
		return socket.ErrConnectionNotFound
	}

	// Close connection in hub if exists
	if exists {
		// Send close message to client
		closeMsg := socket.NewMessage("system", "connection_closing", map[string]interface{}{
			"reason": reason,
			"timestamp": time.Now(),
		})
		
		if err := hubConn.Send(closeMsg); err != nil {
			s.logger.Warn("Failed to send close message to client",
				"connection_id", id,
				"error", err)
		}

		// Close the connection
		if err := hubConn.Close(); err != nil {
			s.logger.Error("Failed to close connection in hub",
				"connection_id", id,
				"error", err)
			return err
		}
	}

	// Update connection status in database
	if err := s.connectionRepo.MarkAsDisconnected(ctx, id); err != nil {
		s.logger.Error("Failed to mark connection as disconnected in database",
			"connection_id", id,
			"error", err)
		return err
	}

	s.logger.Info("Connection closed successfully",
		"connection_id", id,
		"reason", reason,
		"force", force)

	return nil
}

// BroadcastToUser gửi message tới tất cả connections của user
func (s *connectionService) BroadcastToUser(ctx context.Context, req dto.BroadcastToUserRequest) error {
	// Get user connections from hub
	userConns := s.hub.GetUserConnections(fmt.Sprintf("%d", req.UserID))
	if len(userConns) == 0 {
		s.logger.Warn("No active connections found for user",
			"user_id", req.UserID,
			"tenant_id", req.TenantID)
		return socket.ErrConnectionNotFound
	}

	// Create message
	message := socket.NewMessage(req.Type, req.Event, req.Data)
	if req.RoomID != "" {
		message.RoomID = req.RoomID
	}

	// Send to all user connections
	var successCount int
	var lastError error

	for _, conn := range userConns {
		// Filter by website if specified
		if req.WebsiteID > 0 && conn.WebsiteID() != fmt.Sprintf("%d", req.WebsiteID) {
			continue
		}

		if err := conn.Send(message); err != nil {
			s.logger.Warn("Failed to send message to user connection",
				"connection_id", conn.ID(),
				"user_id", req.UserID,
				"error", err)
			lastError = err
		} else {
			successCount++
		}
	}

	if successCount == 0 && lastError != nil {
		s.logger.Error("Failed to send message to any user connection",
			"user_id", req.UserID,
			"tenant_id", req.TenantID,
			"error", lastError)
		return lastError
	}

	s.logger.Debug("Message broadcasted to user successfully",
		"user_id", req.UserID,
		"tenant_id", req.TenantID,
		"event", req.Event,
		"success_count", successCount,
		"total_connections", len(userConns))

	return nil
}

// GetConnectionStats lấy thống kê connections
func (s *connectionService) GetConnectionStats(ctx context.Context, tenantID uint) (*dto.ConnectionStatsDTO, error) {
	// Get stats from repository
	stats, err := s.connectionRepo.GetStats(ctx, tenantID)
	if err != nil {
		s.logger.Error("Failed to get connection stats from repository",
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	// Enhance with real-time data from hub
	hubStats := s.hub.GetStatistics()
	if hubStats != nil {
		stats.ActiveConnections = hubStats.TotalConnections
		stats.Uptime = hubStats.StartTime.String()
	}

	// Note: Top users would need to be implemented separately
	// as GetUserConnectionCounts is not available in hub

	s.logger.Debug("Connection stats retrieved successfully",
		"tenant_id", tenantID,
		"total_connections", stats.TotalConnections,
		"active_connections", stats.ActiveConnections)

	return stats, nil
}

// GetUserConnectionCount đếm số connections của user
func (s *connectionService) GetUserConnectionCount(ctx context.Context, userID uint, tenantID uint) (int64, error) {
	count, err := s.connectionRepo.GetUserConnectionCount(ctx, userID, tenantID)
	if err != nil {
		s.logger.Error("Failed to get user connection count from repository",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return 0, err
	}

	return count, nil
}

// UpdateConnectionActivity cập nhật last activity của connection
func (s *connectionService) UpdateConnectionActivity(ctx context.Context, id string) error {
	now := time.Now()
	if err := s.connectionRepo.UpdateLastActivity(ctx, id, now); err != nil {
		s.logger.Error("Failed to update connection activity",
			"connection_id", id,
			"error", err)
		return err
	}

	return nil
}

// CleanupInactiveConnections dọn dẹp các connections không hoạt động
func (s *connectionService) CleanupInactiveConnections(ctx context.Context) (int64, error) {
	// Define inactive threshold (e.g., 24 hours)
	inactiveThreshold := time.Now().Add(-24 * time.Hour)

	count, err := s.connectionRepo.CleanupOldConnections(ctx, inactiveThreshold)
	if err != nil {
		s.logger.Error("Failed to cleanup inactive connections",
			"threshold", inactiveThreshold,
			"error", err)
		return 0, err
	}

	s.logger.Info("Inactive connections cleaned up",
		"count", count,
		"threshold", inactiveThreshold)

	return count, nil
}

// modelToDTO chuyển đổi model sang DTO
func (s *connectionService) modelToDTO(conn *models.Connection) *dto.ConnectionDTO {
	isActive := conn.Status == models.ConnectionStatusActive
	duration := time.Since(conn.ConnectedAt).String()

	return &dto.ConnectionDTO{
		ID:           conn.ID,
		UserID:       conn.UserID,
		TenantID:     conn.TenantID,
		WebsiteID:    conn.WebsiteID,
		Status:       conn.Status,
		ConnectedAt:  conn.ConnectedAt,
		LastActivity: conn.LastActivity,
		IPAddress:    conn.IPAddress,
		UserAgent:    conn.UserAgent,
		IsActive:     isActive,
		Duration:     duration,
	}
}
