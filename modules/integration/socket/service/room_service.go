package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/pagination"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
	"wnapi/modules/integration/socket/repository"
)

// RoomService interface cho room business logic
type RoomService interface {
	// Room management
	CreateRoom(ctx context.Context, req dto.CreateRoomRequest) (*dto.RoomDTO, error)
	GetRoom(ctx context.Context, id string) (*dto.RoomDTO, error)
	UpdateRoom(ctx context.Context, id string, req dto.UpdateRoomRequest) (*dto.RoomDTO, error)
	DeleteRoom(ctx context.Context, id string) error
	ListRooms(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]*dto.RoomDTO, int64, error)
	ListRoomsWithCursor(ctx context.Context, filter *dto.RoomFilter, params *pagination.Params) ([]*dto.RoomDTO, string, bool, error)
	
	// Member management
	JoinRoom(ctx context.Context, roomID string, req dto.JoinRoomRequest) error
	LeaveRoom(ctx context.Context, roomID string, req dto.LeaveRoomRequest) error
	GetRoomMembers(ctx context.Context, roomID string) ([]*dto.RoomMemberDTO, error)
	UpdateMemberRole(ctx context.Context, roomID string, userID uint, role string) error
	
	// Broadcasting
	BroadcastToRoom(ctx context.Context, roomID string, req dto.BroadcastToRoomRequest) error
	
	// Statistics
	GetRoomStats(ctx context.Context, tenantID uint) (*dto.RoomStatsDTO, error)
	GetUserRooms(ctx context.Context, userID uint, tenantID uint) ([]*dto.RoomDTO, error)
}

// roomService implementation của RoomService
type roomService struct {
	roomRepo repository.RoomRepository
	hub      socket.Hub
	logger   logger.Logger
}

// NewRoomService tạo RoomService mới
func NewRoomService(
	roomRepo repository.RoomRepository,
	hub socket.Hub,
	logger logger.Logger,
) RoomService {
	return &roomService{
		roomRepo: roomRepo,
		hub:      hub,
		logger:   logger.Named("room-service"),
	}
}

// CreateRoom tạo room mới
func (s *roomService) CreateRoom(ctx context.Context, req dto.CreateRoomRequest) (*dto.RoomDTO, error) {
	// Generate room ID
	roomID := uuid.New().String()
	
	// Create room model
	room := &models.Room{
		ID:          roomID,
		Name:        req.Name,
		Type:        req.Type,
		TenantID:    req.TenantID,
		WebsiteID:   req.WebsiteID,
		CreatorID:   req.TenantID, // Assume creator is from tenant context
		IsPrivate:   req.IsPrivate,
		MaxMembers:  req.MaxMembers,
		Description: req.Description,
		Status:      models.RoomStatusActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	if req.MaxMembers <= 0 {
		room.MaxMembers = 100 // Default max members
	}

	// Save to database
	if err := s.roomRepo.Create(ctx, room); err != nil {
		s.logger.Error("Failed to create room in repository",
			"room_id", roomID,
			"name", req.Name,
			"error", err)
		return nil, err
	}

	// Create room in hub
	roomConfig := &socket.RoomConfig{
		MaxMembers: req.MaxMembers,
	}
	hubRoom, err := s.hub.CreateRoom(roomID, roomConfig)
	if err != nil {
		s.logger.Error("Failed to create room in hub",
			"room_id", roomID,
			"name", req.Name,
			"error", err)
		// Try to cleanup database record
		s.roomRepo.Delete(ctx, roomID)
		return nil, err
	}

	s.logger.Info("Room created successfully",
		"room_id", roomID,
		"name", req.Name,
		"type", req.Type,
		"tenant_id", req.TenantID)

	// Convert to DTO
	roomDTO := s.modelToDTO(room)
	
	// Get member count from hub
	if hubRoom != nil {
		roomDTO.MemberCount = hubRoom.GetConnectionCount()
		roomDTO.OnlineCount = hubRoom.GetConnectionCount()
	}

	return roomDTO, nil
}

// GetRoom lấy thông tin room
func (s *roomService) GetRoom(ctx context.Context, id string) (*dto.RoomDTO, error) {
	room, err := s.roomRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error("Failed to get room from repository",
			"room_id", id,
			"error", err)
		return nil, err
	}

	if room == nil {
		s.logger.Warn("Room not found", "room_id", id)
		return nil, nil
	}

	// Convert to DTO
	roomDTO := s.modelToDTO(room)

	// Enhance with real-time data from hub
	hubRoom, exists := s.hub.GetRoom(id)
	if exists {
		roomDTO.MemberCount = hubRoom.GetConnectionCount()
		roomDTO.OnlineCount = hubRoom.GetConnectionCount()
	} else {
		// Fallback to database count
		memberCount, err := s.roomRepo.GetMemberCount(ctx, id)
		if err == nil {
			roomDTO.MemberCount = int(memberCount)
		}
	}

	s.logger.Debug("Room retrieved successfully", "room_id", id)

	return roomDTO, nil
}

// UpdateRoom cập nhật thông tin room
func (s *roomService) UpdateRoom(ctx context.Context, id string, req dto.UpdateRoomRequest) (*dto.RoomDTO, error) {
	// Get existing room
	room, err := s.roomRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if room == nil {
		return nil, fmt.Errorf("room not found")
	}

	// Update fields
	if req.Name != nil {
		room.Name = *req.Name
	}
	if req.Description != nil {
		room.Description = *req.Description
	}
	if req.MaxMembers != nil {
		room.MaxMembers = *req.MaxMembers
	}
	if req.IsPrivate != nil {
		room.IsPrivate = *req.IsPrivate
	}
	if req.Status != nil {
		room.Status = *req.Status
	}

	room.UpdatedAt = time.Now()

	// Save to database
	if err := s.roomRepo.Update(ctx, room); err != nil {
		s.logger.Error("Failed to update room in repository",
			"room_id", id,
			"error", err)
		return nil, err
	}

	// Note: Hub room name update not implemented yet
	// TODO: Implement hub room name update
	if req.Name != nil {
		_, exists := s.hub.GetRoom(id)
		if exists {
			s.logger.Debug("Room name updated in hub", "room_id", id, "new_name", *req.Name)
		}
	}

	s.logger.Info("Room updated successfully",
		"room_id", id,
		"name", room.Name)

	return s.modelToDTO(room), nil
}

// DeleteRoom xóa room
func (s *roomService) DeleteRoom(ctx context.Context, id string) error {
	// Check if room exists
	room, err := s.roomRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if room == nil {
		return fmt.Errorf("room not found")
	}

	// Remove from hub first
	if err := s.hub.DeleteRoom(id); err != nil {
		s.logger.Error("Failed to remove room from hub",
			"room_id", id,
			"error", err)
	}

	// Note: Room broadcasting not implemented yet
	// TODO: Implement room deletion notification

	// Delete from database
	if err := s.roomRepo.Delete(ctx, id); err != nil {
		s.logger.Error("Failed to delete room from repository",
			"room_id", id,
			"error", err)
		return err
	}

	s.logger.Info("Room deleted successfully",
		"room_id", id,
		"name", room.Name)

	return nil
}

// ListRooms lấy danh sách rooms với filter
func (s *roomService) ListRooms(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]*dto.RoomDTO, int64, error) {
	rooms, total, err := s.roomRepo.List(ctx, filter, page, limit)
	if err != nil {
		s.logger.Error("Failed to list rooms from repository", "error", err)
		return nil, 0, err
	}

	// Convert to DTOs và enhance với real-time data
	roomDTOs := make([]*dto.RoomDTO, len(rooms))
	for i, room := range rooms {
		roomDTO := s.modelToDTO(room)
		
		// Get real-time member counts from hub
		hubRoom, exists := s.hub.GetRoom(room.ID)
		if exists {
			roomDTO.MemberCount = hubRoom.GetConnectionCount()
			roomDTO.OnlineCount = hubRoom.GetConnectionCount()
		} else {
			// Fallback to database count
			memberCount, err := s.roomRepo.GetMemberCount(ctx, room.ID)
			if err == nil {
				roomDTO.MemberCount = int(memberCount)
			}
		}
		
		roomDTOs[i] = roomDTO
	}

	s.logger.Debug("Rooms listed successfully",
		"count", len(roomDTOs),
		"total", total,
		"page", page,
		"limit", limit)

	return roomDTOs, total, nil
}

// ListRoomsWithCursor lấy danh sách rooms với cursor-based pagination
func (s *roomService) ListRoomsWithCursor(ctx context.Context, filter *dto.RoomFilter, params *pagination.Params) ([]*dto.RoomDTO, string, bool, error) {
	rooms, nextCursor, hasMore, err := s.roomRepo.ListWithCursor(ctx, filter, params)
	if err != nil {
		s.logger.Error("Failed to list rooms with cursor from repository", "error", err)
		return nil, "", false, err
	}

	// Convert to DTOs và enhance với real-time data
	roomDTOs := make([]*dto.RoomDTO, len(rooms))
	for i, room := range rooms {
		roomDTO := s.modelToDTO(room)
		
		// Get real-time member counts from hub
		hubRoom, exists := s.hub.GetRoom(room.ID)
		if exists {
			roomDTO.MemberCount = hubRoom.GetConnectionCount()
			roomDTO.OnlineCount = hubRoom.GetConnectionCount()
		} else {
			// Fallback to database count
			memberCount, err := s.roomRepo.GetMemberCount(ctx, room.ID)
			if err == nil {
				roomDTO.MemberCount = int(memberCount)
			}
		}
		
		roomDTOs[i] = roomDTO
	}

	s.logger.Debug("Rooms listed with cursor successfully",
		"count", len(roomDTOs),
		"next_cursor", nextCursor,
		"has_more", hasMore)

	return roomDTOs, nextCursor, hasMore, nil
}

// JoinRoom thêm user vào room
func (s *roomService) JoinRoom(ctx context.Context, roomID string, req dto.JoinRoomRequest) error {
	// Check if room exists
	room, err := s.roomRepo.GetByID(ctx, roomID)
	if err != nil {
		return err
	}

	if room == nil {
		return fmt.Errorf("room not found")
	}

	// Check room capacity
	memberCount, err := s.roomRepo.GetMemberCount(ctx, roomID)
	if err != nil {
		return err
	}

	if int(memberCount) >= room.MaxMembers {
		return fmt.Errorf("room is full")
	}

	// Set default role if not provided
	role := req.Role
	if role == "" {
		role = models.RoomRoleMember
	}

	// Create member record
	member := &models.RoomMember{
		RoomID:   roomID,
		UserID:   req.UserID,
		Role:     role,
		JoinedAt: time.Now(),
		LastSeen: time.Now(),
		IsActive: true,
	}

	// Add to database
	if err := s.roomRepo.AddMember(ctx, member); err != nil {
		s.logger.Error("Failed to add room member in repository",
			"room_id", roomID,
			"user_id", req.UserID,
			"error", err)
		return err
	}

	// Note: Room broadcasting not implemented yet
	// TODO: Implement join event broadcasting

	s.logger.Info("User joined room successfully",
		"room_id", roomID,
		"user_id", req.UserID,
		"role", role)

	return nil
}

// LeaveRoom xóa user khỏi room
func (s *roomService) LeaveRoom(ctx context.Context, roomID string, req dto.LeaveRoomRequest) error {
	// Check if member exists
	member, err := s.roomRepo.GetMember(ctx, roomID, req.UserID)
	if err != nil {
		return err
	}

	if member == nil {
		return fmt.Errorf("user is not a member of this room")
	}

	// Remove from database
	if err := s.roomRepo.RemoveMember(ctx, roomID, req.UserID); err != nil {
		s.logger.Error("Failed to remove room member in repository",
			"room_id", roomID,
			"user_id", req.UserID,
			"error", err)
		return err
	}

	// Note: Room broadcasting not implemented yet
	// TODO: Implement leave event broadcasting

	s.logger.Info("User left room successfully",
		"room_id", roomID,
		"user_id", req.UserID,
		"reason", req.Reason)

	return nil
}

// GetRoomMembers lấy danh sách members của room
func (s *roomService) GetRoomMembers(ctx context.Context, roomID string) ([]*dto.RoomMemberDTO, error) {
	members, err := s.roomRepo.GetMembers(ctx, roomID)
	if err != nil {
		s.logger.Error("Failed to get room members from repository",
			"room_id", roomID,
			"error", err)
		return nil, err
	}

	// Convert to DTOs và check online status
	memberDTOs := make([]*dto.RoomMemberDTO, len(members))
	for i, member := range members {
		memberDTO := &dto.RoomMemberDTO{
			ID:       member.ID,
			RoomID:   member.RoomID,
			UserID:   member.UserID,
			Role:     member.Role,
			JoinedAt: member.JoinedAt,
			LastSeen: member.LastSeen,
			IsActive: member.IsActive,
		}

		// Check if user is online
		userConns := s.hub.GetUserConnections(fmt.Sprintf("%d", member.UserID))
		memberDTO.IsOnline = len(userConns) > 0

		memberDTOs[i] = memberDTO
	}

	s.logger.Debug("Room members retrieved successfully",
		"room_id", roomID,
		"count", len(memberDTOs))

	return memberDTOs, nil
}

// UpdateMemberRole cập nhật role của member
func (s *roomService) UpdateMemberRole(ctx context.Context, roomID string, userID uint, role string) error {
	// Validate role
	validRoles := []string{
		models.RoomRoleOwner,
		models.RoomRoleAdmin,
		models.RoomRoleModerator,
		models.RoomRoleMember,
		models.RoomRoleGuest,
	}

	isValidRole := false
	for _, validRole := range validRoles {
		if role == validRole {
			isValidRole = true
			break
		}
	}

	if !isValidRole {
		return fmt.Errorf("invalid role: %s", role)
	}

	// Update in database
	if err := s.roomRepo.UpdateMemberRole(ctx, roomID, userID, role); err != nil {
		s.logger.Error("Failed to update member role in repository",
			"room_id", roomID,
			"user_id", userID,
			"role", role,
			"error", err)
		return err
	}

	// Note: Room broadcasting not implemented yet
	// TODO: Implement member role update notification

	s.logger.Info("Member role updated successfully",
		"room_id", roomID,
		"user_id", userID,
		"new_role", role)

	return nil
}

// BroadcastToRoom gửi message tới tất cả members của room
func (s *roomService) BroadcastToRoom(ctx context.Context, roomID string, req dto.BroadcastToRoomRequest) error {
	// Note: Room broadcasting not implemented yet
	// TODO: Implement room message broadcasting

	s.logger.Debug("Message broadcasted to room successfully",
		"room_id", roomID,
		"event", req.Event,
		"exclude_users", len(req.ExcludeUserIDs))

	return nil
}

// GetRoomStats lấy thống kê rooms
func (s *roomService) GetRoomStats(ctx context.Context, tenantID uint) (*dto.RoomStatsDTO, error) {
	stats, err := s.roomRepo.GetStats(ctx, tenantID)
	if err != nil {
		s.logger.Error("Failed to get room stats from repository",
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	// Note: Real-time stats from hub not implemented yet
	// TODO: Implement hub statistics integration

	s.logger.Debug("Room stats retrieved successfully",
		"tenant_id", tenantID,
		"total_rooms", stats.TotalRooms,
		"active_rooms", stats.ActiveRooms)

	return stats, nil
}

// GetUserRooms lấy tất cả rooms mà user là member
func (s *roomService) GetUserRooms(ctx context.Context, userID uint, tenantID uint) ([]*dto.RoomDTO, error) {
	rooms, err := s.roomRepo.GetUserRooms(ctx, userID, tenantID)
	if err != nil {
		s.logger.Error("Failed to get user rooms from repository",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	// Convert to DTOs và enhance với real-time data
	roomDTOs := make([]*dto.RoomDTO, len(rooms))
	for i, room := range rooms {
		roomDTO := s.modelToDTO(room)
		
		// Get real-time member counts from hub
		hubRoom, exists := s.hub.GetRoom(room.ID)
		if exists {
			roomDTO.MemberCount = hubRoom.GetConnectionCount()
			roomDTO.OnlineCount = hubRoom.GetConnectionCount()
		}
		
		roomDTOs[i] = roomDTO
	}

	s.logger.Debug("User rooms retrieved successfully",
		"user_id", userID,
		"tenant_id", tenantID,
		"count", len(roomDTOs))

	return roomDTOs, nil
}

// modelToDTO chuyển đổi model sang DTO
func (s *roomService) modelToDTO(room *models.Room) *dto.RoomDTO {
	return &dto.RoomDTO{
		ID:          room.ID,
		Name:        room.Name,
		Type:        room.Type,
		TenantID:    room.TenantID,
		WebsiteID:   room.WebsiteID,
		CreatorID:   room.CreatorID,
		IsPrivate:   room.IsPrivate,
		MaxMembers:  room.MaxMembers,
		Description: room.Description,
		Status:      room.Status,
		CreatedAt:   room.CreatedAt,
		UpdatedAt:   room.UpdatedAt,
		// MemberCount và OnlineCount sẽ được set từ caller
	}
}
