package socket

import (
	"time"

	"go.uber.org/fx"
	"gorm.io/gorm"

	"wnapi/internal/fx/modules"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/api"
	"wnapi/modules/integration/socket/repository"
	"wnapi/modules/integration/socket/service"
)

func init() {
	modules.GlobalRegistry.Register(&SocketModule{})
}

// SocketModule implements the FX module interface
type SocketModule struct{}

// Name returns the module name
func (m *SocketModule) Name() string {
	return "socket"
}

// Dependencies returns module dependencies
func (m *SocketModule) Dependencies() []string {
	return []string{"tenant", "auth"}
}

// Priority returns module loading priority
func (m *SocketModule) Priority() int {
	return 30 // Load before blog module
}

// Enabled returns whether the module is enabled
func (m *SocketModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *SocketModule) GetMigrationPath() string {
	return "modules/integration/socket/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *SocketModule) GetMigrationOrder() int {
	return 30 // Socket module runs before blog
}

// Module returns FX options for the module
func (m *SocketModule) Module() fx.Option {
	return fx.Module("socket",
		// Providers
		fx.Provide(
			// Module instance
			NewModule,

			// Database provider
			ProvideDatabaseInterface,

			// Socket infrastructure
			ProvideHubConfig,
			ProvideEventRouterConfig,
			ProvideEventPublisher,
			ProvideHub,
			ProvideEventRouter,

			// Repositories
			repository.NewConnectionRepository,
			repository.NewRoomRepository,
			repository.NewNotificationRepository,

			// Services
			service.NewConnectionService,
			service.NewRoomService,
			service.NewNotificationService,
			service.NewStatsService,
			service.NewSocketService,

			// API Handlers
			api.NewConnectionHandler,
			api.NewRoomHandler,
			api.NewNotificationHandler,
			api.NewStatsHandler,
			api.NewWebSocketHandler,
			api.NewRoutes,
		),

		// Route registration
		fx.Invoke(api.RegisterSocketRoutes),
	)
}

// ProvideHubConfig provides default hub configuration
func ProvideHubConfig() *socket.HubConfig {
	return &socket.HubConfig{
		MaxConnections:        1000,
		MaxConnectionsPerUser: 10,
		MaxRoomsPerUser:       50,
		PingInterval:          30 * time.Second,
		WriteTimeout:          10 * time.Second,
		ReadTimeout:           60 * time.Second,
		CleanupInterval:       5 * time.Minute,
		EnableStatistics:      true,
	}
}

// ProvideEventRouterConfig provides default event router configuration
func ProvideEventRouterConfig() *socket.EventRouterConfig {
	return &socket.EventRouterConfig{
		EnableMetrics:       true,
		EnableErrorHandler:  true,
		DefaultTimeout:      30 * time.Second,
		MaxConcurrentEvents: 1000,
	}
}

// ProvideEventPublisher provides a default event publisher implementation
func ProvideEventPublisher() socket.EventPublisher {
	return &defaultEventPublisher{}
}

// defaultEventPublisher is a simple implementation of EventPublisher
type defaultEventPublisher struct{}

func (p *defaultEventPublisher) PublishConnectionEvent(connID, userID, eventType string, metadata map[string]interface{}) error {
	// TODO: Implement actual event publishing logic
	return nil
}

func (p *defaultEventPublisher) PublishRoomEvent(roomID, eventType string, metadata map[string]interface{}) error {
	// TODO: Implement actual event publishing logic
	return nil
}

func (p *defaultEventPublisher) PublishMessageEvent(connID, roomID string, message *socket.Message) error {
	// TODO: Implement actual event publishing logic
	return nil
}

func (p *defaultEventPublisher) PublishNotificationEvent(userID, eventType string, metadata map[string]interface{}) error {
	// TODO: Implement actual event publishing logic
	return nil
}

// ProvideHub provides Hub implementation
func ProvideHub(config *socket.HubConfig, eventRouter socket.EventRouter, logger logger.Logger) socket.Hub {
	return socket.NewHub(config, eventRouter, logger)
}

// ProvideEventRouter provides EventRouter interface implementation
func ProvideEventRouter(config *socket.EventRouterConfig, publisher socket.EventPublisher, logger logger.Logger) socket.EventRouter {
	router := socket.NewEventRouter(config, publisher, logger)
	return router
}

// ProvideDatabaseInterface provides database.Database interface from gorm.DB
func ProvideDatabaseInterface(gormDB *gorm.DB, logger logger.Logger) database.Database {
	return database.NewDBManager(gormDB, gormDB, logger)
}