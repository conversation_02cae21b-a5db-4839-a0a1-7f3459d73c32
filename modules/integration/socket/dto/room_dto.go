package dto

import (
	"time"
)

// RoomDTO represents room data for API responses
type RoomDTO struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	TenantID    uint                   `json:"tenant_id"`
	WebsiteID   uint                   `json:"website_id"`
	CreatorID   uint                   `json:"creator_id"`
	IsPrivate   bool                   `json:"is_private"`
	MaxMembers  int                    `json:"max_members"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Status      string                 `json:"status"`
	MemberCount int                    `json:"member_count"`
	OnlineCount int                    `json:"online_count"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// RoomMemberDTO represents room member data
type RoomMemberDTO struct {
	ID       uint      `json:"id"`
	RoomID   string    `json:"room_id"`
	UserID   uint      `json:"user_id"`
	Role     string    `json:"role"`
	JoinedAt time.Time `json:"joined_at"`
	LastSeen time.Time `json:"last_seen"`
	IsActive bool      `json:"is_active"`
	IsOnline bool      `json:"is_online"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// CreateRoomRequest request để tạo room mới
type CreateRoomRequest struct {
	Name        string                 `json:"name" binding:"required,min=1,max=255"`
	Type        string                 `json:"type" binding:"required"`
	TenantID    uint                   `json:"tenant_id" binding:"required"`
	WebsiteID   uint                   `json:"website_id" binding:"required"`
	IsPrivate   bool                   `json:"is_private"`
	MaxMembers  int                    `json:"max_members" binding:"min=1,max=1000"`
	Description string                 `json:"description" binding:"max=1000"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// UpdateRoomRequest request để update room
type UpdateRoomRequest struct {
	Name        *string                `json:"name,omitempty" binding:"omitempty,min=1,max=255"`
	Description *string                `json:"description,omitempty" binding:"omitempty,max=1000"`
	MaxMembers  *int                   `json:"max_members,omitempty" binding:"omitempty,min=1,max=1000"`
	IsPrivate   *bool                  `json:"is_private,omitempty"`
	Status      *string                `json:"status,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// JoinRoomRequest request để join room
type JoinRoomRequest struct {
	UserID   uint                   `json:"user_id" binding:"required"`
	Role     string                 `json:"role"`
	Metadata map[string]interface{} `json:"metadata"`
}

// LeaveRoomRequest request để leave room
type LeaveRoomRequest struct {
	UserID uint   `json:"user_id" binding:"required"`
	Reason string `json:"reason"`
}

// RoomListRequest request for listing rooms
type RoomListRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	Limit     int    `form:"limit" binding:"min=1,max=100"`
	TenantID  uint   `form:"tenant_id"`
	WebsiteID uint   `form:"website_id"`
	Type      string `form:"type"`
	Status    string `form:"status"`
	CreatorID uint   `form:"creator_id"`
	IsPrivate *bool  `form:"is_private"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// RoomFilter filter cho room queries
type RoomFilter struct {
	TenantID  uint   `json:"tenant_id,omitempty"`
	WebsiteID uint   `json:"website_id,omitempty"`
	Type      string `json:"type,omitempty"`
	Status    string `json:"status,omitempty"`
	CreatorID uint   `json:"creator_id,omitempty"`
	IsPrivate *bool  `json:"is_private,omitempty"`
	UserID    uint   `json:"user_id,omitempty"` // Rooms that user is member of
}

// RoomStatsDTO statistics about rooms
type RoomStatsDTO struct {
	TotalRooms      int64            `json:"total_rooms"`
	ActiveRooms     int64            `json:"active_rooms"`
	RoomsByType     map[string]int64 `json:"rooms_by_type"`
	RoomsByStatus   map[string]int64 `json:"rooms_by_status"`
	TotalMembers    int64            `json:"total_members"`
	OnlineMembers   int64            `json:"online_members"`
	TopRooms        []RoomStatistic  `json:"top_rooms"`
	RecentRooms     []RoomDTO        `json:"recent_rooms"`
	LastUpdated     time.Time        `json:"last_updated"`
}

// RoomStatistic thống kê về một room cụ thể
type RoomStatistic struct {
	RoomID      string `json:"room_id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	MemberCount int    `json:"member_count"`
	OnlineCount int    `json:"online_count"`
	MessageCount int64 `json:"message_count"`
}

// BroadcastToRoomRequest request để broadcast message tới room
type BroadcastToRoomRequest struct {
	Type       string                 `json:"type" binding:"required"`
	Event      string                 `json:"event" binding:"required"`
	Data       map[string]interface{} `json:"data"`
	ExcludeUserIDs []uint             `json:"exclude_user_ids,omitempty"`
}

// RoomEventDTO represents room events
type RoomEventDTO struct {
	ID        string                 `json:"id"`
	RoomID    string                 `json:"room_id"`
	UserID    uint                   `json:"user_id"`
	EventType string                 `json:"event_type"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}
