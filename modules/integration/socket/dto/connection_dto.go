package dto

import (
	"time"
)

// ConnectionDTO represents connection data for API responses
type ConnectionDTO struct {
	ID           string                 `json:"id"`
	UserID       uint                   `json:"user_id"`
	TenantID     uint                   `json:"tenant_id"`
	WebsiteID    uint                   `json:"website_id"`
	Status       string                 `json:"status"`
	ConnectedAt  time.Time              `json:"connected_at"`
	LastActivity time.Time              `json:"last_activity"`
	IPAddress    string                 `json:"ip_address"`
	UserAgent    string                 `json:"user_agent"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	IsActive     bool                   `json:"is_active"`
	Duration     string                 `json:"duration"`
}

// ConnectionListRequest request for listing connections
type ConnectionListRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	Limit     int    `form:"limit" binding:"min=1,max=100"`
	TenantID  uint   `form:"tenant_id"`
	WebsiteID uint   `form:"website_id"`
	UserID    uint   `form:"user_id"`
	Status    string `form:"status"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// ConnectionFilter filter cho connection queries
type ConnectionFilter struct {
	TenantID     uint   `json:"tenant_id,omitempty"`
	WebsiteID    uint   `json:"website_id,omitempty"`
	UserID       uint   `json:"user_id,omitempty"`
	Status       string `json:"status,omitempty"`
	IPAddress    string `json:"ip_address,omitempty"`
	ConnectedAfter  *time.Time `json:"connected_after,omitempty"`
	ConnectedBefore *time.Time `json:"connected_before,omitempty"`
}

// ConnectionStatsDTO statistics about connections
type ConnectionStatsDTO struct {
	TotalConnections    int64                    `json:"total_connections"`
	ActiveConnections   int64                    `json:"active_connections"`
	ConnectionsByStatus map[string]int64         `json:"connections_by_status"`
	ConnectionsByTenant map[uint]int64           `json:"connections_by_tenant"`
	RecentConnections   []ConnectionDTO          `json:"recent_connections"`
	TopUsers            []UserConnectionStat     `json:"top_users"`
	Uptime              string                   `json:"uptime"`
	LastUpdated         time.Time                `json:"last_updated"`
}

// UserConnectionStat thống kê connection theo user
type UserConnectionStat struct {
	UserID      uint  `json:"user_id"`
	Connections int64 `json:"connections"`
	IsOnline    bool  `json:"is_online"`
}

// CloseConnectionRequest request để đóng connection
type CloseConnectionRequest struct {
	Reason string `json:"reason" binding:"required"`
	Force  bool   `json:"force"`
}

// BroadcastToUserRequest request để broadcast message tới user
type BroadcastToUserRequest struct {
	UserID    uint                   `json:"user_id" binding:"required"`
	TenantID  uint                   `json:"tenant_id" binding:"required"`
	WebsiteID uint                   `json:"website_id"`
	Type      string                 `json:"type" binding:"required"`
	Event     string                 `json:"event" binding:"required"`
	Data      map[string]interface{} `json:"data"`
	RoomID    string                 `json:"room_id,omitempty"`
}

// ConnectionEventDTO represents connection events
type ConnectionEventDTO struct {
	ID           string                 `json:"id"`
	ConnectionID string                 `json:"connection_id"`
	UserID       uint                   `json:"user_id"`
	EventType    string                 `json:"event_type"`
	Data         map[string]interface{} `json:"data"`
	Timestamp    time.Time              `json:"timestamp"`
}
