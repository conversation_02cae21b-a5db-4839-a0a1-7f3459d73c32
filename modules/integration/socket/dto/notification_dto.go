package dto

import (
	"time"
)

// NotificationDTO represents notification data for API responses
type NotificationDTO struct {
	ID          uint                   `json:"id"`
	TenantID    uint                   `json:"tenant_id"`
	WebsiteID   uint                   `json:"website_id"`
	UserID      uint                   `json:"user_id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Data        map[string]interface{} `json:"data,omitempty"`
	Priority    string                 `json:"priority"`
	Category    string                 `json:"category"`
	IsRead      bool                   `json:"is_read"`
	ReadAt      *time.Time             `json:"read_at,omitempty"`
	DeliveredAt *time.Time             `json:"delivered_at,omitempty"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// SendNotificationRequest request để gửi notification
type SendNotificationRequest struct {
	UserID    uint                   `json:"user_id" binding:"required"`
	TenantID  uint                   `json:"tenant_id" binding:"required"`
	WebsiteID uint                   `json:"website_id" binding:"required"`
	Type      string                 `json:"type" binding:"required"`
	Title     string                 `json:"title" binding:"required,max=255"`
	Message   string                 `json:"message" binding:"required,max=1000"`
	Data      map[string]interface{} `json:"data"`
	Priority  string                 `json:"priority"`
	Category  string                 `json:"category"`
	ExpiresAt *time.Time             `json:"expires_at"`
	Channels  []string               `json:"channels"`
}

// BroadcastNotificationRequest request để broadcast notification
type BroadcastNotificationRequest struct {
	TenantID     uint                   `json:"tenant_id" binding:"required"`
	WebsiteID    uint                   `json:"website_id" binding:"required"`
	Type         string                 `json:"type" binding:"required"`
	Title        string                 `json:"title" binding:"required,max=255"`
	Message      string                 `json:"message" binding:"required,max=1000"`
	Data         map[string]interface{} `json:"data"`
	Priority     string                 `json:"priority"`
	Category     string                 `json:"category"`
	ExpiresAt    *time.Time             `json:"expires_at"`
	Channels     []string               `json:"channels"`
	UserIDs      []uint                 `json:"user_ids,omitempty"`
	UserFilters  map[string]interface{} `json:"user_filters,omitempty"`
	RoomIDs      []string               `json:"room_ids,omitempty"`
}

// NotificationListRequest request for listing notifications
type NotificationListRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	Limit     int    `form:"limit" binding:"min=1,max=100"`
	TenantID  uint   `form:"tenant_id"`
	WebsiteID uint   `form:"website_id"`
	UserID    uint   `form:"user_id"`
	Type      string `form:"type"`
	Priority  string `form:"priority"`
	Category  string `form:"category"`
	IsRead    *bool  `form:"is_read"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// NotificationFilter filter cho notification queries
type NotificationFilter struct {
	TenantID    uint       `json:"tenant_id,omitempty"`
	WebsiteID   uint       `json:"website_id,omitempty"`
	UserID      uint       `json:"user_id,omitempty"`
	Type        string     `json:"type,omitempty"`
	Priority    string     `json:"priority,omitempty"`
	Category    string     `json:"category,omitempty"`
	IsRead      *bool      `json:"is_read,omitempty"`
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
}

// MarkNotificationReadRequest request để mark notification as read
type MarkNotificationReadRequest struct {
	NotificationIDs []uint `json:"notification_ids" binding:"required,dive,required"`
}

// NotificationStatsDTO statistics about notifications
type NotificationStatsDTO struct {
	TotalNotifications      int64                     `json:"total_notifications"`
	UnreadNotifications     int64                     `json:"unread_notifications"`
	NotificationsByType     map[string]int64          `json:"notifications_by_type"`
	NotificationsByPriority map[string]int64          `json:"notifications_by_priority"`
	NotificationsByCategory map[string]int64          `json:"notifications_by_category"`
	DeliveryStats           NotificationDeliveryStats `json:"delivery_stats"`
	RecentNotifications     []NotificationDTO         `json:"recent_notifications"`
	LastUpdated             time.Time                 `json:"last_updated"`
}

// NotificationDeliveryStats statistics about notification delivery
type NotificationDeliveryStats struct {
	TotalDeliveries    int64            `json:"total_deliveries"`
	SuccessfulDeliveries int64          `json:"successful_deliveries"`
	FailedDeliveries   int64            `json:"failed_deliveries"`
	DeliveryRate       float64          `json:"delivery_rate"`
	ChannelStats       map[string]int64 `json:"channel_stats"`
	AvgDeliveryTime    time.Duration    `json:"avg_delivery_time"`
}

// SubscribeRequest request để subscribe to events
type SubscribeRequest struct {
	UserID       uint                   `json:"user_id" binding:"required"`
	TenantID     uint                   `json:"tenant_id" binding:"required"`
	WebsiteID    uint                   `json:"website_id" binding:"required"`
	EventType    string                 `json:"event_type" binding:"required"`
	Channel      string                 `json:"channel" binding:"required"`
	ResourceID   string                 `json:"resource_id"`
	ResourceType string                 `json:"resource_type"`
	Filters      map[string]interface{} `json:"filters"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// UnsubscribeRequest request để unsubscribe from events
type UnsubscribeRequest struct {
	UserID    uint   `json:"user_id" binding:"required"`
	TenantID  uint   `json:"tenant_id" binding:"required"`
	WebsiteID uint   `json:"website_id" binding:"required"`
	EventType string `json:"event_type"`
	Channel   string `json:"channel"`
	ResourceID string `json:"resource_id"`
}

// SubscriptionDTO represents subscription data
type SubscriptionDTO struct {
	ID           uint                   `json:"id"`
	UserID       uint                   `json:"user_id"`
	TenantID     uint                   `json:"tenant_id"`
	WebsiteID    uint                   `json:"website_id"`
	EventType    string                 `json:"event_type"`
	Channel      string                 `json:"channel"`
	ResourceID   string                 `json:"resource_id"`
	ResourceType string                 `json:"resource_type"`
	IsActive     bool                   `json:"is_active"`
	Filters      map[string]interface{} `json:"filters,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}
