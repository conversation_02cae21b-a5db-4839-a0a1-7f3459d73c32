package dto

import (
	"time"
)

// StatsDTO represents overall socket system statistics
type StatsDTO struct {
	System      SystemStatsDTO      `json:"system"`
	Connections ConnectionStatsDTO  `json:"connections"`
	Rooms       RoomStatsDTO       `json:"rooms"`
	Events      EventStatsDTO      `json:"events"`
	Performance PerformanceStatsDTO `json:"performance"`
	LastUpdated time.Time          `json:"last_updated"`
}

// SystemStatsDTO represents system-level statistics
type SystemStatsDTO struct {
	ServerName       string        `json:"server_name"`
	Version          string        `json:"version"`
	Uptime           time.Duration `json:"uptime"`
	StartTime        time.Time     `json:"start_time"`
	CurrentTime      time.Time     `json:"current_time"`
	GoVersion        string        `json:"go_version"`
	NumGoroutines    int           `json:"num_goroutines"`
	MemoryUsage      MemoryStats   `json:"memory_usage"`
	CPUUsage         float64       `json:"cpu_usage"`
	LoadAverage      []float64     `json:"load_average"`
}

// MemoryStats represents memory usage statistics
type MemoryStats struct {
	Allocated      uint64 `json:"allocated"`       // bytes allocated and not yet freed
	TotalAllocated uint64 `json:"total_allocated"` // bytes allocated (even if freed)
	SystemMemory   uint64 `json:"system_memory"`   // bytes obtained from OS
	NumGC          uint32 `json:"num_gc"`          // number of garbage collections
	HeapInUse      uint64 `json:"heap_in_use"`     // bytes in in-use spans
	StackInUse     uint64 `json:"stack_in_use"`    // bytes in stack spans
}

// EventStatsDTO represents event processing statistics
type EventStatsDTO struct {
	TotalEvents      int64                    `json:"total_events"`
	TotalErrors      int64                    `json:"total_errors"`
	EventCounts      map[string]int64         `json:"event_counts"`
	ErrorCounts      map[string]int64         `json:"error_counts"`
	EventDurations   map[string]time.Duration `json:"event_durations"`
	EventsPerSecond  float64                  `json:"events_per_second"`
	ErrorRate        float64                  `json:"error_rate"`
	AvgProcessingTime time.Duration           `json:"avg_processing_time"`
	RecentEvents     []EventLogDTO            `json:"recent_events"`
}

// EventLogDTO represents an event log entry
type EventLogDTO struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`
	ConnectionID string                 `json:"connection_id"`
	UserID       uint                   `json:"user_id"`
	Data         map[string]interface{} `json:"data"`
	Duration     time.Duration          `json:"duration"`
	Success      bool                   `json:"success"`
	Error        string                 `json:"error,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
}

// PerformanceStatsDTO represents performance metrics
type PerformanceStatsDTO struct {
	RequestsPerSecond    float64                  `json:"requests_per_second"`
	AvgResponseTime      time.Duration            `json:"avg_response_time"`
	P50ResponseTime      time.Duration            `json:"p50_response_time"`
	P95ResponseTime      time.Duration            `json:"p95_response_time"`
	P99ResponseTime      time.Duration            `json:"p99_response_time"`
	ThroughputMBps       float64                  `json:"throughput_mbps"`
	ConnectionPoolStats  ConnectionPoolStatsDTO   `json:"connection_pool_stats"`
	ResourceUtilization  ResourceUtilizationDTO   `json:"resource_utilization"`
	BottleneckAnalysis   []BottleneckDTO          `json:"bottleneck_analysis"`
}

// ConnectionPoolStatsDTO represents connection pool statistics
type ConnectionPoolStatsDTO struct {
	MaxConnections     int     `json:"max_connections"`
	ActiveConnections  int     `json:"active_connections"`
	IdleConnections    int     `json:"idle_connections"`
	PoolUtilization    float64 `json:"pool_utilization"`
	ConnectionTimeouts int64   `json:"connection_timeouts"`
	ConnectionErrors   int64   `json:"connection_errors"`
}

// ResourceUtilizationDTO represents resource utilization metrics
type ResourceUtilizationDTO struct {
	CPUUsage      float64 `json:"cpu_usage"`
	MemoryUsage   float64 `json:"memory_usage"`
	DiskUsage     float64 `json:"disk_usage"`
	NetworkInMBps float64 `json:"network_in_mbps"`
	NetworkOutMBps float64 `json:"network_out_mbps"`
	FileDescriptors int   `json:"file_descriptors"`
	ThreadCount     int   `json:"thread_count"`
}

// BottleneckDTO represents a performance bottleneck
type BottleneckDTO struct {
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	Metric      string    `json:"metric"`
	Value       float64   `json:"value"`
	Threshold   float64   `json:"threshold"`
	Timestamp   time.Time `json:"timestamp"`
}

// SystemStatusDTO represents overall system status
type SystemStatusDTO struct {
	Status            string                 `json:"status"`
	Uptime            time.Duration          `json:"uptime"`
	TotalConnections  int64                  `json:"total_connections"`
	TotalRooms        int64                  `json:"total_rooms"`
	TotalEvents       int64                  `json:"total_events"`
	ErrorRate         float64                `json:"error_rate"`
	MemoryUsage       float64                `json:"memory_usage"`
	LastMaintenance   time.Time              `json:"last_maintenance"`
	HealthChecks      map[string]string      `json:"health_checks"`
	ActiveServices    []string               `json:"active_services"`
	Timestamp         time.Time              `json:"timestamp"`
}

// HealthCheckDTO represents system health status
type HealthCheckDTO struct {
	Status      string                    `json:"status"`
	Timestamp   time.Time                 `json:"timestamp"`
	Uptime      time.Duration             `json:"uptime"`
	Checks      map[string]HealthCheckItem `json:"checks"`
	Overview    HealthOverviewDTO         `json:"overview"`
}

// HealthCheckItem represents individual health check result
type HealthCheckItem struct {
	Status    string        `json:"status"`
	Message   string        `json:"message"`
	Duration  time.Duration `json:"duration"`
	LastCheck time.Time     `json:"last_check"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// HealthOverviewDTO represents overall health summary
type HealthOverviewDTO struct {
	OverallStatus    string `json:"overall_status"`
	HealthyChecks    int    `json:"healthy_checks"`
	UnhealthyChecks  int    `json:"unhealthy_checks"`
	WarningChecks    int    `json:"warning_checks"`
	TotalChecks      int    `json:"total_checks"`
}

// MetricsRequest request để lấy metrics với filters
type MetricsRequest struct {
	TimeRange   string   `form:"time_range"`   // last_hour, last_day, last_week, custom
	StartTime   *time.Time `form:"start_time"`
	EndTime     *time.Time `form:"end_time"`
	Granularity string   `form:"granularity"`  // minute, hour, day
	Metrics     []string `form:"metrics"`      // specific metrics to include
	TenantID    uint     `form:"tenant_id"`
	WebsiteID   uint     `form:"website_id"`
	Format      string   `form:"format"`       // json, csv, prometheus
}

// Health status constants
const (
	HealthStatusHealthy   = "healthy"
	HealthStatusUnhealthy = "unhealthy"
	HealthStatusWarning   = "warning"
	HealthStatusUnknown   = "unknown"
)

// Bottleneck severity constants
const (
	BottleneckSeverityLow      = "low"
	BottleneckSeverityMedium   = "medium"
	BottleneckSeverityHigh     = "high"
	BottleneckSeverityCritical = "critical"
)
