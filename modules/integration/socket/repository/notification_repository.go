package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
)

// NotificationRepository interface cho notification data operations
type NotificationRepository interface {
	// Subscription operations
	CreateSubscription(ctx context.Context, subscription *models.Subscription) error
	DeleteSubscription(ctx context.Context, id uint) error
	GetUserSubscriptions(ctx context.Context, userID uint, tenantID uint) ([]*models.Subscription, error)
	GetSubscriptionsByEvent(ctx context.Context, eventType string, tenantID uint) ([]*models.Subscription, error)
	UpdateSubscriptionStatus(ctx context.Context, id uint, isActive bool) error

	// Delivery operations
	CreateDelivery(ctx context.Context, delivery *models.NotificationDelivery) error
	UpdateDeliveryStatus(ctx context.Context, id uint, status string, deliveredAt *time.Time) error
	GetDeliveryByID(ctx context.Context, id uint) (*models.NotificationDelivery, error)
	GetUserDeliveries(ctx context.Context, userID uint, tenantID uint, page, limit int) ([]*models.NotificationDelivery, int64, error)
	MarkDeliveryAsRead(ctx context.Context, id uint, readAt time.Time) error

	// Query operations
	ListSubscriptions(ctx context.Context, filter dto.NotificationFilter, page, limit int) ([]*models.Subscription, int64, error)
	ListDeliveries(ctx context.Context, filter dto.NotificationFilter, page, limit int) ([]*models.NotificationDelivery, int64, error)

	// Statistics
	GetNotificationStats(ctx context.Context, tenantID uint) (*dto.NotificationStatsDTO, error)
	CleanupExpiredDeliveries(ctx context.Context, olderThan time.Time) (int64, error)
}

// notificationRepository implementation của NotificationRepository
type notificationRepository struct {
	db     database.Database
	logger logger.Logger
}

// NewNotificationRepository tạo NotificationRepository mới
func NewNotificationRepository(
	db database.Database,
	logger logger.Logger,
) NotificationRepository {
	return &notificationRepository{
		db:     db,
		logger: logger.Named("notification-repository"),
	}
}

// CreateSubscription tạo subscription mới
func (r *notificationRepository) CreateSubscription(ctx context.Context, subscription *models.Subscription) error {
	// Check if subscription already exists
	var existing models.Subscription
	err := r.db.WriteDB().WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND event_type = ? AND channel = ? AND resource_id = ?",
			subscription.UserID, subscription.TenantID, subscription.EventType, subscription.Channel, subscription.ResourceID).
		First(&existing).Error

	if err == nil {
		// Subscription exists, update it
		existing.IsActive = subscription.IsActive
		existing.Filters = subscription.Filters
		existing.Metadata = subscription.Metadata
		existing.UpdatedAt = time.Now()

		if err := r.db.WriteDB().WithContext(ctx).Save(&existing).Error; err != nil {
			r.logger.Error("Failed to update subscription",
				"user_id", subscription.UserID,
				"event_type", subscription.EventType,
				"error", err)
			return err
		}

		r.logger.Debug("Subscription updated successfully",
			"subscription_id", existing.ID,
			"user_id", subscription.UserID,
			"event_type", subscription.EventType)
		return nil
	} else if err != gorm.ErrRecordNotFound {
		r.logger.Error("Failed to check existing subscription", "error", err)
		return err
	}

	// Create new subscription
	if err := r.db.WriteDB().WithContext(ctx).Create(subscription).Error; err != nil {
		r.logger.Error("Failed to create subscription",
			"user_id", subscription.UserID,
			"event_type", subscription.EventType,
			"error", err)
		return err
	}

	r.logger.Debug("Subscription created successfully",
		"subscription_id", subscription.ID,
		"user_id", subscription.UserID,
		"event_type", subscription.EventType)
	return nil
}

// DeleteSubscription xóa subscription
func (r *notificationRepository) DeleteSubscription(ctx context.Context, id uint) error {
	if err := r.db.WriteDB().WithContext(ctx).Delete(&models.Subscription{}, id).Error; err != nil {
		r.logger.Error("Failed to delete subscription",
			"subscription_id", id,
			"error", err)
		return err
	}

	r.logger.Debug("Subscription deleted successfully",
		"subscription_id", id)
	return nil
}

// GetUserSubscriptions lấy tất cả subscriptions của user
func (r *notificationRepository) GetUserSubscriptions(ctx context.Context, userID uint, tenantID uint) ([]*models.Subscription, error) {
	var subscriptions []*models.Subscription
	if err := r.db.WriteDB().WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND is_active = ?", userID, tenantID, true).
		Find(&subscriptions).Error; err != nil {
		r.logger.Error("Failed to get user subscriptions",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return subscriptions, nil
}

// GetSubscriptionsByEvent lấy subscriptions theo event type
func (r *notificationRepository) GetSubscriptionsByEvent(ctx context.Context, eventType string, tenantID uint) ([]*models.Subscription, error) {
	var subscriptions []*models.Subscription
	if err := r.db.WriteDB().WithContext(ctx).
		Where("event_type = ? AND tenant_id = ? AND is_active = ?", eventType, tenantID, true).
		Find(&subscriptions).Error; err != nil {
		r.logger.Error("Failed to get subscriptions by event",
			"event_type", eventType,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return subscriptions, nil
}

// UpdateSubscriptionStatus cập nhật status của subscription
func (r *notificationRepository) UpdateSubscriptionStatus(ctx context.Context, id uint, isActive bool) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.Subscription{}).
		Where("id = ?", id).
		Update("is_active", isActive).Error; err != nil {
		r.logger.Error("Failed to update subscription status",
			"subscription_id", id,
			"is_active", isActive,
			"error", err)
		return err
	}

	r.logger.Debug("Subscription status updated",
		"subscription_id", id,
		"is_active", isActive)
	return nil
}

// CreateDelivery tạo notification delivery record
func (r *notificationRepository) CreateDelivery(ctx context.Context, delivery *models.NotificationDelivery) error {
	if err := r.db.WriteDB().WithContext(ctx).Create(delivery).Error; err != nil {
		r.logger.Error("Failed to create notification delivery",
			"notification_id", delivery.NotificationID,
			"user_id", delivery.UserID,
			"channel", delivery.Channel,
			"error", err)
		return err
	}

	r.logger.Debug("Notification delivery created successfully",
		"delivery_id", delivery.ID,
		"notification_id", delivery.NotificationID,
		"user_id", delivery.UserID,
		"channel", delivery.Channel)
	return nil
}

// UpdateDeliveryStatus cập nhật status của delivery
func (r *notificationRepository) UpdateDeliveryStatus(ctx context.Context, id uint, status string, deliveredAt *time.Time) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}
	
	if deliveredAt != nil {
		updates["delivered_at"] = deliveredAt
	}

	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.NotificationDelivery{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		r.logger.Error("Failed to update delivery status",
			"delivery_id", id,
			"status", status,
			"error", err)
		return err
	}

	r.logger.Debug("Delivery status updated",
		"delivery_id", id,
		"status", status)
	return nil
}

// GetDeliveryByID lấy delivery theo ID
func (r *notificationRepository) GetDeliveryByID(ctx context.Context, id uint) (*models.NotificationDelivery, error) {
	var delivery models.NotificationDelivery
	if err := r.db.WriteDB().WithContext(ctx).Where("id = ?", id).First(&delivery).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error("Failed to get delivery by ID",
			"delivery_id", id,
			"error", err)
		return nil, err
	}

	return &delivery, nil
}

// GetUserDeliveries lấy deliveries của user với pagination
func (r *notificationRepository) GetUserDeliveries(ctx context.Context, userID uint, tenantID uint, page, limit int) ([]*models.NotificationDelivery, int64, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.NotificationDelivery{}).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID)

	// Count total
	var total int64
	if err := db.Count(&total).Error; err != nil {
		r.logger.Error("Failed to count user deliveries", "error", err)
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit).Order("created_at DESC")

	// Get results
	var deliveries []*models.NotificationDelivery
	if err := db.Find(&deliveries).Error; err != nil {
		r.logger.Error("Failed to get user deliveries", "error", err)
		return nil, 0, err
	}

	return deliveries, total, nil
}

// MarkDeliveryAsRead đánh dấu delivery đã được đọc
func (r *notificationRepository) MarkDeliveryAsRead(ctx context.Context, id uint, readAt time.Time) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.NotificationDelivery{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     models.DeliveryStatusRead,
			"read_at":    readAt,
			"updated_at": time.Now(),
		}).Error; err != nil {
		r.logger.Error("Failed to mark delivery as read",
			"delivery_id", id,
			"error", err)
		return err
	}

	r.logger.Debug("Delivery marked as read",
		"delivery_id", id)
	return nil
}

// ListSubscriptions lấy danh sách subscriptions với filter
func (r *notificationRepository) ListSubscriptions(ctx context.Context, filter dto.NotificationFilter, page, limit int) ([]*models.Subscription, int64, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.Subscription{})

	// Apply filters
	if filter.TenantID > 0 {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		db = db.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.UserID > 0 {
		db = db.Where("user_id = ?", filter.UserID)
	}
	if filter.Type != "" {
		db = db.Where("event_type = ?", filter.Type)
	}

	// Count total
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit).Order("created_at DESC")

	// Get results
	var subscriptions []*models.Subscription
	if err := db.Find(&subscriptions).Error; err != nil {
		return nil, 0, err
	}

	return subscriptions, total, nil
}

// ListDeliveries lấy danh sách deliveries với filter
func (r *notificationRepository) ListDeliveries(ctx context.Context, filter dto.NotificationFilter, page, limit int) ([]*models.NotificationDelivery, int64, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.NotificationDelivery{})

	// Apply filters
	if filter.TenantID > 0 {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		db = db.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.UserID > 0 {
		db = db.Where("user_id = ?", filter.UserID)
	}
	if filter.CreatedAfter != nil {
		db = db.Where("created_at >= ?", filter.CreatedAfter)
	}
	if filter.CreatedBefore != nil {
		db = db.Where("created_at <= ?", filter.CreatedBefore)
	}

	// Count total
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit).Order("created_at DESC")

	// Get results
	var deliveries []*models.NotificationDelivery
	if err := db.Find(&deliveries).Error; err != nil {
		return nil, 0, err
	}

	return deliveries, total, nil
}

// GetNotificationStats lấy thống kê notifications
func (r *notificationRepository) GetNotificationStats(ctx context.Context, tenantID uint) (*dto.NotificationStatsDTO, error) {
	stats := &dto.NotificationStatsDTO{
		NotificationsByType:     make(map[string]int64),
		NotificationsByPriority: make(map[string]int64),
		NotificationsByCategory: make(map[string]int64),
		DeliveryStats: dto.NotificationDeliveryStats{
			ChannelStats: make(map[string]int64),
		},
		LastUpdated: time.Now(),
	}

	// Get delivery stats
	deliveryDB := r.db.WriteDB().WithContext(ctx).Model(&models.NotificationDelivery{})
	if tenantID > 0 {
		deliveryDB = deliveryDB.Where("tenant_id = ?", tenantID)
	}

	// Total deliveries
	if err := deliveryDB.Count(&stats.DeliveryStats.TotalDeliveries).Error; err != nil {
		return nil, err
	}

	// Successful deliveries
	if err := deliveryDB.Where("status = ?", models.DeliveryStatusDelivered).Count(&stats.DeliveryStats.SuccessfulDeliveries).Error; err != nil {
		return nil, err
	}

	// Failed deliveries
	if err := deliveryDB.Where("status = ?", models.DeliveryStatusFailed).Count(&stats.DeliveryStats.FailedDeliveries).Error; err != nil {
		return nil, err
	}

	// Calculate delivery rate
	if stats.DeliveryStats.TotalDeliveries > 0 {
		stats.DeliveryStats.DeliveryRate = float64(stats.DeliveryStats.SuccessfulDeliveries) / float64(stats.DeliveryStats.TotalDeliveries) * 100
	}

	// Channel stats
	var channelStats []struct {
		Channel string `json:"channel"`
		Count   int64  `json:"count"`
	}
	if err := deliveryDB.Select("channel, COUNT(*) as count").Group("channel").Scan(&channelStats).Error; err != nil {
		return nil, err
	}
	for _, cs := range channelStats {
		stats.DeliveryStats.ChannelStats[cs.Channel] = cs.Count
	}

	// Get subscription stats
	subscriptionDB := r.db.WriteDB().WithContext(ctx).Model(&models.Subscription{})
	if tenantID > 0 {
		subscriptionDB = subscriptionDB.Where("tenant_id = ?", tenantID)
	}

	// Total subscriptions
	var totalSubscriptions int64
	if err := subscriptionDB.Count(&totalSubscriptions).Error; err != nil {
		return nil, err
	}

	// Event type stats
	var typeStats []struct {
		EventType string `json:"event_type"`
		Count     int64  `json:"count"`
	}
	if err := subscriptionDB.Select("event_type, COUNT(*) as count").Group("event_type").Scan(&typeStats).Error; err != nil {
		return nil, err
	}
	for _, ts := range typeStats {
		stats.NotificationsByType[ts.EventType] = ts.Count
	}

	return stats, nil
}

// CleanupExpiredDeliveries xóa các deliveries cũ
func (r *notificationRepository) CleanupExpiredDeliveries(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.db.WriteDB().WithContext(ctx).
		Where("created_at < ? AND status IN ?", olderThan, []string{models.DeliveryStatusDelivered, models.DeliveryStatusRead}).
		Delete(&models.NotificationDelivery{})

	if result.Error != nil {
		r.logger.Error("Failed to cleanup expired deliveries",
			"older_than", olderThan,
			"error", result.Error)
		return 0, result.Error
	}

	r.logger.Info("Expired deliveries cleaned up",
		"count", result.RowsAffected,
		"older_than", olderThan)

	return result.RowsAffected, nil
}
