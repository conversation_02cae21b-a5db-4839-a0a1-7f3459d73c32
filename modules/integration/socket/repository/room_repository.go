package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/pagination"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
)

// RoomRepository interface cho room data operations
type RoomRepository interface {
	// CRUD operations
	Create(ctx context.Context, room *models.Room) error
	GetByID(ctx context.Context, id string) (*models.Room, error)
	Update(ctx context.Context, room *models.Room) error
	Delete(ctx context.Context, id string) error

	// Query operations
	List(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]*models.Room, int64, error)
	ListWithCursor(ctx context.Context, filter *dto.RoomFilter, params *pagination.Params) ([]*models.Room, string, bool, error)
	GetByTenantID(ctx context.Context, tenantID uint) ([]*models.Room, error)
	GetByCreatorID(ctx context.Context, creatorID uint) ([]*models.Room, error)
	GetUserRooms(ctx context.Context, userID uint, tenantID uint) ([]*models.Room, error)

	// Member operations
	AddMember(ctx context.Context, member *models.RoomMember) error
	RemoveMember(ctx context.Context, roomID string, userID uint) error
	GetMembers(ctx context.Context, roomID string) ([]*models.RoomMember, error)
	GetMember(ctx context.Context, roomID string, userID uint) (*models.RoomMember, error)
	UpdateMemberRole(ctx context.Context, roomID string, userID uint, role string) error
	UpdateMemberLastSeen(ctx context.Context, roomID string, userID uint, lastSeen time.Time) error
	GetMemberCount(ctx context.Context, roomID string) (int64, error)

	// Statistics
	GetStats(ctx context.Context, tenantID uint) (*dto.RoomStatsDTO, error)
	CleanupEmptyRooms(ctx context.Context) (int64, error)
}

// roomRepository implementation của RoomRepository
type roomRepository struct {
	db     database.Database
	logger logger.Logger
}

// NewRoomRepository tạo RoomRepository mới
func NewRoomRepository(
	db database.Database,
	logger logger.Logger,
) RoomRepository {
	return &roomRepository{
		db:     db,
		logger: logger.Named("room-repository"),
	}
}

// Create tạo room mới trong database
func (r *roomRepository) Create(ctx context.Context, room *models.Room) error {
	if err := r.db.WriteDB().WithContext(ctx).Create(room).Error; err != nil {
		r.logger.Error("Failed to create room",
			"room_id", room.ID,
			"name", room.Name,
			"error", err)
		return err
	}

	r.logger.Debug("Room created successfully",
		"room_id", room.ID,
		"name", room.Name)
	return nil
}

// GetByID lấy room theo ID
func (r *roomRepository) GetByID(ctx context.Context, id string) (*models.Room, error) {
	var room models.Room
	if err := r.db.WriteDB().WithContext(ctx).Where("id = ?", id).First(&room).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error("Failed to get room by ID",
			"room_id", id,
			"error", err)
		return nil, err
	}

	return &room, nil
}

// Update cập nhật room
func (r *roomRepository) Update(ctx context.Context, room *models.Room) error {
	if err := r.db.WriteDB().WithContext(ctx).Save(room).Error; err != nil {
		r.logger.Error("Failed to update room",
			"room_id", room.ID,
			"error", err)
		return err
	}

	r.logger.Debug("Room updated successfully",
		"room_id", room.ID)
	return nil
}

// Delete xóa room
func (r *roomRepository) Delete(ctx context.Context, id string) error {
	tx := r.db.WriteDB().WithContext(ctx).Begin()
	
	// Delete all members first
	if err := tx.Where("room_id = ?", id).Delete(&models.RoomMember{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Failed to delete room members",
			"room_id", id,
			"error", err)
		return err
	}
	
	// Delete room
	if err := tx.Where("id = ?", id).Delete(&models.Room{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Failed to delete room",
			"room_id", id,
			"error", err)
		return err
	}

	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Failed to commit room deletion",
			"room_id", id,
			"error", err)
		return err
	}

	r.logger.Debug("Room deleted successfully",
		"room_id", id)
	return nil
}

// List lấy danh sách rooms với filter và pagination
func (r *roomRepository) List(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]*models.Room, int64, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.Room{})

	// Apply filters
	if filter.TenantID > 0 {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		db = db.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.Type != "" {
		db = db.Where("type = ?", filter.Type)
	}
	if filter.Status != "" {
		db = db.Where("status = ?", filter.Status)
	}
	if filter.CreatorID > 0 {
		db = db.Where("creator_id = ?", filter.CreatorID)
	}
	if filter.IsPrivate != nil {
		db = db.Where("is_private = ?", *filter.IsPrivate)
	}
	if filter.UserID > 0 {
		// Join with room_members to get rooms where user is a member
		db = db.Joins("JOIN socket_room_members ON socket_room_members.room_id = socket_rooms.id").
			Where("socket_room_members.user_id = ? AND socket_room_members.is_active = ?", filter.UserID, true)
	}

	// Count total
	var total int64
	if err := db.Count(&total).Error; err != nil {
		r.logger.Error("Failed to count rooms", "error", err)
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit).Order("created_at DESC")

	// Get results
	var rooms []*models.Room
	if err := db.Find(&rooms).Error; err != nil {
		r.logger.Error("Failed to list rooms", "error", err)
		return nil, 0, err
	}

	r.logger.Debug("Rooms listed successfully",
		"count", len(rooms),
		"total", total,
		"page", page,
		"limit", limit)

	return rooms, total, nil
}

// ListWithCursor lấy danh sách rooms với cursor-based pagination
func (r *roomRepository) ListWithCursor(ctx context.Context, filter *dto.RoomFilter, params *pagination.Params) ([]*models.Room, string, bool, error) {
	query := r.db.WriteDB().WithContext(ctx).Model(&models.Room{})

	// Apply filters
	if filter != nil {
		if filter.TenantID > 0 {
			query = query.Where("tenant_id = ?", filter.TenantID)
		}
		if filter.WebsiteID > 0 {
			query = query.Where("website_id = ?", filter.WebsiteID)
		}
		if filter.CreatorID > 0 {
			query = query.Where("creator_id = ?", filter.CreatorID)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Type != "" {
			query = query.Where("type = ?", filter.Type)
		}
		if filter.IsPrivate != nil {
			query = query.Where("is_private = ?", *filter.IsPrivate)
		}
		if filter.UserID > 0 {
			// Filter rooms where user is a member
			query = query.Joins("JOIN socket_room_members ON socket_room_members.room_id = socket_rooms.id").
				Where("socket_room_members.user_id = ? AND socket_room_members.is_active = ?", filter.UserID, true)
		}
	}

	// Apply cursor
	if params.Cursor != "" {
		cursorTime, err := time.Parse(time.RFC3339, params.Cursor)
		if err != nil {
			r.logger.Error("Failed to parse cursor", "cursor", params.Cursor, "error", err)
			return nil, "", false, err
		}
		query = query.Where("created_at < ?", cursorTime)
	}

	// Order by created_at DESC (newest first)
	query = query.Order("created_at DESC")

	// Limit + 1 to check if there are more records
	limit := params.Limit + 1
	query = query.Limit(limit)

	var rooms []*models.Room
	if err := query.Find(&rooms).Error; err != nil {
		r.logger.Error("Failed to list rooms with cursor",
			"error", err,
			"cursor", params.Cursor,
			"limit", params.Limit)
		return nil, "", false, err
	}

	// Check if there are more records
	hasMore := len(rooms) > params.Limit
	if hasMore {
		rooms = rooms[:params.Limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(rooms) > 0 {
		nextCursor = rooms[len(rooms)-1].CreatedAt.Format(time.RFC3339)
	}

	r.logger.Debug("Listed rooms with cursor successfully",
		"count", len(rooms),
		"has_more", hasMore,
		"next_cursor", nextCursor)

	return rooms, nextCursor, hasMore, nil
}

// GetByTenantID lấy tất cả rooms của tenant
func (r *roomRepository) GetByTenantID(ctx context.Context, tenantID uint) ([]*models.Room, error) {
	var rooms []*models.Room
	if err := r.db.WriteDB().WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, models.RoomStatusActive).
		Find(&rooms).Error; err != nil {
		r.logger.Error("Failed to get rooms by tenant ID",
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return rooms, nil
}

// GetByCreatorID lấy tất cả rooms của creator
func (r *roomRepository) GetByCreatorID(ctx context.Context, creatorID uint) ([]*models.Room, error) {
	var rooms []*models.Room
	if err := r.db.WriteDB().WithContext(ctx).
		Where("creator_id = ? AND status = ?", creatorID, models.RoomStatusActive).
		Find(&rooms).Error; err != nil {
		r.logger.Error("Failed to get rooms by creator ID",
			"creator_id", creatorID,
			"error", err)
		return nil, err
	}

	return rooms, nil
}

// GetUserRooms lấy tất cả rooms mà user là member
func (r *roomRepository) GetUserRooms(ctx context.Context, userID uint, tenantID uint) ([]*models.Room, error) {
	var rooms []*models.Room
	if err := r.db.WriteDB().WithContext(ctx).
		Table("socket_rooms").
		Joins("JOIN socket_room_members ON socket_room_members.room_id = socket_rooms.id").
		Where("socket_room_members.user_id = ? AND socket_rooms.tenant_id = ? AND socket_room_members.is_active = ? AND socket_rooms.status = ?",
			userID, tenantID, true, models.RoomStatusActive).
		Find(&rooms).Error; err != nil {
		r.logger.Error("Failed to get user rooms",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return rooms, nil
}

// AddMember thêm member vào room
func (r *roomRepository) AddMember(ctx context.Context, member *models.RoomMember) error {
	// Check if member already exists
	var existingMember models.RoomMember
	err := r.db.WriteDB().WithContext(ctx).
		Where("room_id = ? AND user_id = ?", member.RoomID, member.UserID).
		First(&existingMember).Error
	
	if err == nil {
		// Member exists, update to active
		existingMember.IsActive = true
		existingMember.Role = member.Role
		existingMember.JoinedAt = time.Now()
		existingMember.Metadata = member.Metadata
		
		if err := r.db.WriteDB().WithContext(ctx).Save(&existingMember).Error; err != nil {
			r.logger.Error("Failed to reactivate room member",
				"room_id", member.RoomID,
				"user_id", member.UserID,
				"error", err)
			return err
		}
	} else if err == gorm.ErrRecordNotFound {
		// Create new member
		if err := r.db.WriteDB().WithContext(ctx).Create(member).Error; err != nil {
			r.logger.Error("Failed to add room member",
				"room_id", member.RoomID,
				"user_id", member.UserID,
				"error", err)
			return err
		}
	} else {
		r.logger.Error("Failed to check existing room member",
			"room_id", member.RoomID,
			"user_id", member.UserID,
			"error", err)
		return err
	}

	r.logger.Debug("Room member added successfully",
		"room_id", member.RoomID,
		"user_id", member.UserID,
		"role", member.Role)
	return nil
}

// RemoveMember xóa member khỏi room
func (r *roomRepository) RemoveMember(ctx context.Context, roomID string, userID uint) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.RoomMember{}).
		Where("room_id = ? AND user_id = ?", roomID, userID).
		Update("is_active", false).Error; err != nil {
		r.logger.Error("Failed to remove room member",
			"room_id", roomID,
			"user_id", userID,
			"error", err)
		return err
	}

	r.logger.Debug("Room member removed successfully",
		"room_id", roomID,
		"user_id", userID)
	return nil
}

// GetMembers lấy tất cả members của room
func (r *roomRepository) GetMembers(ctx context.Context, roomID string) ([]*models.RoomMember, error) {
	var members []*models.RoomMember
	if err := r.db.WriteDB().WithContext(ctx).
		Where("room_id = ? AND is_active = ?", roomID, true).
		Find(&members).Error; err != nil {
		r.logger.Error("Failed to get room members",
			"room_id", roomID,
			"error", err)
		return nil, err
	}

	return members, nil
}

// GetMember lấy thông tin member cụ thể
func (r *roomRepository) GetMember(ctx context.Context, roomID string, userID uint) (*models.RoomMember, error) {
	var member models.RoomMember
	if err := r.db.WriteDB().WithContext(ctx).
		Where("room_id = ? AND user_id = ? AND is_active = ?", roomID, userID, true).
		First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error("Failed to get room member",
			"room_id", roomID,
			"user_id", userID,
			"error", err)
		return nil, err
	}

	return &member, nil
}

// UpdateMemberRole cập nhật role của member
func (r *roomRepository) UpdateMemberRole(ctx context.Context, roomID string, userID uint, role string) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.RoomMember{}).
		Where("room_id = ? AND user_id = ? AND is_active = ?", roomID, userID, true).
		Update("role", role).Error; err != nil {
		r.logger.Error("Failed to update member role",
			"room_id", roomID,
			"user_id", userID,
			"role", role,
			"error", err)
		return err
	}

	r.logger.Debug("Member role updated successfully",
		"room_id", roomID,
		"user_id", userID,
		"role", role)
	return nil
}

// UpdateMemberLastSeen cập nhật last seen của member
func (r *roomRepository) UpdateMemberLastSeen(ctx context.Context, roomID string, userID uint, lastSeen time.Time) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.RoomMember{}).
		Where("room_id = ? AND user_id = ? AND is_active = ?", roomID, userID, true).
		Update("last_seen", lastSeen).Error; err != nil {
		return err
	}

	return nil
}

// GetMemberCount đếm số members của room
func (r *roomRepository) GetMemberCount(ctx context.Context, roomID string) (int64, error) {
	var count int64
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.RoomMember{}).
		Where("room_id = ? AND is_active = ?", roomID, true).
		Count(&count).Error; err != nil {
		r.logger.Error("Failed to count room members",
			"room_id", roomID,
			"error", err)
		return 0, err
	}

	return count, nil
}

// GetStats lấy thống kê rooms
func (r *roomRepository) GetStats(ctx context.Context, tenantID uint) (*dto.RoomStatsDTO, error) {
	stats := &dto.RoomStatsDTO{
		RoomsByType:   make(map[string]int64),
		RoomsByStatus: make(map[string]int64),
		LastUpdated:   time.Now(),
	}

	db := r.db.WriteDB().WithContext(ctx).Model(&models.Room{})
	if tenantID > 0 {
		db = db.Where("tenant_id = ?", tenantID)
	}

	// Total rooms
	if err := db.Count(&stats.TotalRooms).Error; err != nil {
		return nil, err
	}

	// Active rooms
	if err := db.Where("status = ?", models.RoomStatusActive).Count(&stats.ActiveRooms).Error; err != nil {
		return nil, err
	}

	// Rooms by type
	var typeCounts []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}
	if err := db.Select("type, COUNT(*) as count").Group("type").Scan(&typeCounts).Error; err != nil {
		return nil, err
	}
	for _, tc := range typeCounts {
		stats.RoomsByType[tc.Type] = tc.Count
	}

	// Rooms by status
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := db.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, err
	}
	for _, sc := range statusCounts {
		stats.RoomsByStatus[sc.Status] = sc.Count
	}

	// Total members count
	memberDB := r.db.WriteDB().WithContext(ctx).Model(&models.RoomMember{}).Where("is_active = ?", true)
	if tenantID > 0 {
		memberDB = memberDB.Joins("JOIN socket_rooms ON socket_rooms.id = socket_room_members.room_id").
			Where("socket_rooms.tenant_id = ?", tenantID)
	}
	if err := memberDB.Count(&stats.TotalMembers).Error; err != nil {
		return nil, err
	}

	return stats, nil
}

// CleanupEmptyRooms xóa các rooms trống
func (r *roomRepository) CleanupEmptyRooms(ctx context.Context) (int64, error) {
	// Get rooms with no active members
	var emptyRoomIDs []string
	if err := r.db.WriteDB().WithContext(ctx).
		Table("socket_rooms").
		Select("socket_rooms.id").
		Joins("LEFT JOIN socket_room_members ON socket_room_members.room_id = socket_rooms.id AND socket_room_members.is_active = ?", true).
		Where("socket_room_members.room_id IS NULL AND socket_rooms.status = ?", models.RoomStatusActive).
		Pluck("socket_rooms.id", &emptyRoomIDs).Error; err != nil {
		r.logger.Error("Failed to find empty rooms", "error", err)
		return 0, err
	}

	if len(emptyRoomIDs) == 0 {
		return 0, nil
	}

	// Archive empty rooms
	result := r.db.WriteDB().WithContext(ctx).
		Model(&models.Room{}).
		Where("id IN ?", emptyRoomIDs).
		Update("status", models.RoomStatusArchived)

	if result.Error != nil {
		r.logger.Error("Failed to cleanup empty rooms",
			"room_ids", emptyRoomIDs,
			"error", result.Error)
		return 0, result.Error
	}

	r.logger.Info("Empty rooms cleaned up",
		"count", result.RowsAffected,
		"room_ids", emptyRoomIDs)

	return result.RowsAffected, nil
}
