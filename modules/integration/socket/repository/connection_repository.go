package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/pagination"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/models"
)

// ConnectionRepository interface cho connection data operations
type ConnectionRepository interface {
	// CRUD operations
	Create(ctx context.Context, connection *models.Connection) error
	GetByID(ctx context.Context, id string) (*models.Connection, error)
	Update(ctx context.Context, connection *models.Connection) error
	Delete(ctx context.Context, id string) error

	// Query operations
	List(ctx context.Context, filter dto.ConnectionFilter, page, limit int) ([]*models.Connection, int64, error)
	ListWithCursor(ctx context.Context, filter *dto.ConnectionFilter, params *pagination.Params) ([]*models.Connection, string, bool, error)
	GetByUserID(ctx context.Context, userID uint, tenantID uint) ([]*models.Connection, error)
	GetActiveBytTenantID(ctx context.Context, tenantID uint) ([]*models.Connection, error)
	GetActiveByWebsiteID(ctx context.Context, websiteID uint) ([]*models.Connection, error)

	// Status operations
	UpdateStatus(ctx context.Context, id string, status string) error
	UpdateLastActivity(ctx context.Context, id string, lastActivity time.Time) error
	MarkAsDisconnected(ctx context.Context, id string) error

	// Statistics
	GetStats(ctx context.Context, tenantID uint) (*dto.ConnectionStatsDTO, error)
	GetUserConnectionCount(ctx context.Context, userID uint, tenantID uint) (int64, error)
	CleanupOldConnections(ctx context.Context, olderThan time.Time) (int64, error)
}

// connectionRepository implementation của ConnectionRepository
type connectionRepository struct {
	db     database.Database
	logger logger.Logger
}

// NewConnectionRepository tạo ConnectionRepository mới
func NewConnectionRepository(
	db database.Database,
	logger logger.Logger,
) ConnectionRepository {
	return &connectionRepository{
		db:     db,
		logger: logger.Named("connection-repository"),
	}
}

// Create tạo connection mới trong database
func (r *connectionRepository) Create(ctx context.Context, connection *models.Connection) error {
	if err := r.db.WriteDB().WithContext(ctx).Create(connection).Error; err != nil {
		r.logger.Error("Failed to create connection",
			"connection_id", connection.ID,
			"user_id", connection.UserID,
			"error", err)
		return err
	}

	r.logger.Debug("Connection created successfully",
		"connection_id", connection.ID,
		"user_id", connection.UserID)
	return nil
}

// GetByID lấy connection theo ID
func (r *connectionRepository) GetByID(ctx context.Context, id string) (*models.Connection, error) {
	var connection models.Connection
	if err := r.db.WriteDB().WithContext(ctx).Where("id = ?", id).First(&connection).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error("Failed to get connection by ID",
			"connection_id", id,
			"error", err)
		return nil, err
	}

	return &connection, nil
}

// Update cập nhật connection
func (r *connectionRepository) Update(ctx context.Context, connection *models.Connection) error {
	if err := r.db.WriteDB().WithContext(ctx).Save(connection).Error; err != nil {
		r.logger.Error("Failed to update connection",
			"connection_id", connection.ID,
			"error", err)
		return err
	}

	r.logger.Debug("Connection updated successfully",
		"connection_id", connection.ID)
	return nil
}

// Delete xóa connection
func (r *connectionRepository) Delete(ctx context.Context, id string) error {
	if err := r.db.WriteDB().WithContext(ctx).Where("id = ?", id).Delete(&models.Connection{}).Error; err != nil {
		r.logger.Error("Failed to delete connection",
			"connection_id", id,
			"error", err)
		return err
	}

	r.logger.Debug("Connection deleted successfully",
		"connection_id", id)
	return nil
}

// List lấy danh sách connections với filter và pagination
func (r *connectionRepository) List(ctx context.Context, filter dto.ConnectionFilter, page, limit int) ([]*models.Connection, int64, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.Connection{})

	// Apply filters
	if filter.TenantID > 0 {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		db = db.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.UserID > 0 {
		db = db.Where("user_id = ?", filter.UserID)
	}
	if filter.Status != "" {
		db = db.Where("status = ?", filter.Status)
	}
	if filter.IPAddress != "" {
		db = db.Where("ip_address = ?", filter.IPAddress)
	}
	if filter.ConnectedAfter != nil {
		db = db.Where("connected_at >= ?", filter.ConnectedAfter)
	}
	if filter.ConnectedBefore != nil {
		db = db.Where("connected_at <= ?", filter.ConnectedBefore)
	}

	// Count total
	var total int64
	if err := db.Count(&total).Error; err != nil {
		r.logger.Error("Failed to count connections", "error", err)
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit).Order("connected_at DESC")

	// Get results
	var connections []*models.Connection
	if err := db.Find(&connections).Error; err != nil {
		r.logger.Error("Failed to list connections", "error", err)
		return nil, 0, err
	}

	r.logger.Debug("Connections listed successfully",
		"count", len(connections),
		"total", total,
		"page", page,
		"limit", limit)

	return connections, total, nil
}

// ListWithCursor lấy danh sách connections với cursor-based pagination
func (r *connectionRepository) ListWithCursor(ctx context.Context, filter *dto.ConnectionFilter, params *pagination.Params) ([]*models.Connection, string, bool, error) {
	db := r.db.WriteDB().WithContext(ctx).Model(&models.Connection{})

	// Apply filters
	if filter.TenantID > 0 {
		db = db.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID > 0 {
		db = db.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.UserID > 0 {
		db = db.Where("user_id = ?", filter.UserID)
	}
	if filter.Status != "" {
		db = db.Where("status = ?", filter.Status)
	}
	if filter.IPAddress != "" {
		db = db.Where("ip_address = ?", filter.IPAddress)
	}
	if filter.ConnectedAfter != nil {
		db = db.Where("connected_at >= ?", filter.ConnectedAfter)
	}
	if filter.ConnectedBefore != nil {
		db = db.Where("connected_at <= ?", filter.ConnectedBefore)
	}

	// Apply cursor pagination
	if params.Cursor != "" {
		db = db.Where("connected_at < ?", params.Cursor)
	}

	// Apply limit + 1 to check if there are more records
	limit := params.Limit + 1
	db = db.Order("connected_at DESC").Limit(limit)

	// Get results
	var connections []*models.Connection
	if err := db.Find(&connections).Error; err != nil {
		r.logger.Error("Failed to list connections with cursor", "error", err)
		return nil, "", false, err
	}

	// Check if there are more records
	hasMore := len(connections) > params.Limit
	if hasMore {
		connections = connections[:params.Limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(connections) > 0 {
		nextCursor = connections[len(connections)-1].ConnectedAt.Format(time.RFC3339Nano)
	}

	r.logger.Debug("Connections listed with cursor successfully",
		"count", len(connections),
		"next_cursor", nextCursor,
		"has_more", hasMore)

	return connections, nextCursor, hasMore, nil
}

// GetByUserID lấy tất cả connections của user
func (r *connectionRepository) GetByUserID(ctx context.Context, userID uint, tenantID uint) ([]*models.Connection, error) {
	var connections []*models.Connection
	if err := r.db.WriteDB().WithContext(ctx).
		Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, models.ConnectionStatusActive).
		Find(&connections).Error; err != nil {
		r.logger.Error("Failed to get connections by user ID",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return connections, nil
}

// GetActiveBytTenantID lấy tất cả active connections của tenant
func (r *connectionRepository) GetActiveBytTenantID(ctx context.Context, tenantID uint) ([]*models.Connection, error) {
	var connections []*models.Connection
	if err := r.db.WriteDB().WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, models.ConnectionStatusActive).
		Find(&connections).Error; err != nil {
		r.logger.Error("Failed to get active connections by tenant ID",
			"tenant_id", tenantID,
			"error", err)
		return nil, err
	}

	return connections, nil
}

// GetActiveByWebsiteID lấy tất cả active connections của website
func (r *connectionRepository) GetActiveByWebsiteID(ctx context.Context, websiteID uint) ([]*models.Connection, error) {
	var connections []*models.Connection
	if err := r.db.WriteDB().WithContext(ctx).
		Where("website_id = ? AND status = ?", websiteID, models.ConnectionStatusActive).
		Find(&connections).Error; err != nil {
		r.logger.Error("Failed to get active connections by website ID",
			"website_id", websiteID,
			"error", err)
		return nil, err
	}

	return connections, nil
}

// UpdateStatus cập nhật status của connection
func (r *connectionRepository) UpdateStatus(ctx context.Context, id string, status string) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.Connection{}).
		Where("id = ?", id).
		Update("status", status).Error; err != nil {
		r.logger.Error("Failed to update connection status",
			"connection_id", id,
			"status", status,
			"error", err)
		return err
	}

	r.logger.Debug("Connection status updated",
		"connection_id", id,
		"status", status)
	return nil
}

// UpdateLastActivity cập nhật last activity của connection
func (r *connectionRepository) UpdateLastActivity(ctx context.Context, id string, lastActivity time.Time) error {
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.Connection{}).
		Where("id = ?", id).
		Update("last_activity", lastActivity).Error; err != nil {
		r.logger.Error("Failed to update connection last activity",
			"connection_id", id,
			"error", err)
		return err
	}

	return nil
}

// MarkAsDisconnected đánh dấu connection là disconnected
func (r *connectionRepository) MarkAsDisconnected(ctx context.Context, id string) error {
	now := time.Now()
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.Connection{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":        models.ConnectionStatusDisconnected,
			"last_activity": now,
			"updated_at":    now,
		}).Error; err != nil {
		r.logger.Error("Failed to mark connection as disconnected",
			"connection_id", id,
			"error", err)
		return err
	}

	r.logger.Debug("Connection marked as disconnected",
		"connection_id", id)
	return nil
}

// GetStats lấy thống kê connections
func (r *connectionRepository) GetStats(ctx context.Context, tenantID uint) (*dto.ConnectionStatsDTO, error) {
	stats := &dto.ConnectionStatsDTO{
		ConnectionsByStatus: make(map[string]int64),
		ConnectionsByTenant: make(map[uint]int64),
		LastUpdated:        time.Now(),
	}

	db := r.db.WriteDB().WithContext(ctx).Model(&models.Connection{})
	if tenantID > 0 {
		db = db.Where("tenant_id = ?", tenantID)
	}

	// Total connections
	if err := db.Count(&stats.TotalConnections).Error; err != nil {
		return nil, err
	}

	// Active connections
	if err := db.Where("status = ?", models.ConnectionStatusActive).Count(&stats.ActiveConnections).Error; err != nil {
		return nil, err
	}

	// Connections by status
	var statusCounts []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := db.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, err
	}
	for _, sc := range statusCounts {
		stats.ConnectionsByStatus[sc.Status] = sc.Count
	}

	// Recent connections (last 10)
	var recentConnections []*models.Connection
	if err := db.Order("connected_at DESC").Limit(10).Find(&recentConnections).Error; err != nil {
		return nil, err
	}

	for _, conn := range recentConnections {
		stats.RecentConnections = append(stats.RecentConnections, dto.ConnectionDTO{
			ID:           conn.ID,
			UserID:       conn.UserID,
			TenantID:     conn.TenantID,
			WebsiteID:    conn.WebsiteID,
			Status:       conn.Status,
			ConnectedAt:  conn.ConnectedAt,
			LastActivity: conn.LastActivity,
			IPAddress:    conn.IPAddress,
			IsActive:     conn.Status == models.ConnectionStatusActive,
			Duration:     time.Since(conn.ConnectedAt).String(),
		})
	}

	return stats, nil
}

// GetUserConnectionCount đếm số connections của user
func (r *connectionRepository) GetUserConnectionCount(ctx context.Context, userID uint, tenantID uint) (int64, error) {
	var count int64
	if err := r.db.WriteDB().WithContext(ctx).
		Model(&models.Connection{}).
		Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, models.ConnectionStatusActive).
		Count(&count).Error; err != nil {
		r.logger.Error("Failed to count user connections",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		return 0, err
	}

	return count, nil
}

// CleanupOldConnections xóa các connections cũ
func (r *connectionRepository) CleanupOldConnections(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.db.WriteDB().WithContext(ctx).
		Where("updated_at < ? AND status != ?", olderThan, models.ConnectionStatusActive).
		Delete(&models.Connection{})

	if result.Error != nil {
		r.logger.Error("Failed to cleanup old connections",
			"older_than", olderThan,
			"error", result.Error)
		return 0, result.Error
	}

	r.logger.Info("Old connections cleaned up",
		"count", result.RowsAffected,
		"older_than", olderThan)

	return result.RowsAffected, nil
}
