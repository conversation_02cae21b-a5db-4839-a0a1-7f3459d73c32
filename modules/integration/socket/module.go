package socket

import (
	"go.uber.org/fx"
	
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	socketapi "wnapi/modules/integration/socket/api"
	"wnapi/modules/integration/socket/repository"
	"wnapi/modules/integration/socket/service"
)

// Module định nghĩa Socket module
type Module struct {
	Name        string
	Version     string
	Description string
}

// NewModule tạo Socket module instance
func NewModule() *Module {
	return &Module{
		Name:        "socket",
		Version:     "1.0.0",
		Description: "WebSocket Integration Module",
	}
}

// ModuleOptions cấu hình cho module
type ModuleOptions struct {
	fx.In

	DB          database.Database
	Logger      logger.Logger
	Hub         socket.Hub
	EventRouter socket.EventRouter
}

// SocketModuleParams dependencies injection
type SocketModuleParams struct {
	fx.Out

	Module           *Module
	SocketService    service.SocketService
	ConnectionRepo   repository.ConnectionRepository
	RoomRepo         repository.RoomRepository
	NotificationRepo repository.NotificationRepository
	WebSocketHandler socketapi.WebSocketHandler
	Routes           socketapi.Routes
}

// ProvideSocketModule provide Socket module với dependencies
func ProvideSocketModule(opts ModuleOptions) (SocketModuleParams, error) {
	logger := opts.Logger.Named("socket-module")

	// Repositories
	connectionRepo := repository.NewConnectionRepository(opts.DB, logger)
	roomRepo := repository.NewRoomRepository(opts.DB, logger)
	notificationRepo := repository.NewNotificationRepository(opts.DB, logger)

	// Services
	connectionService := service.NewConnectionService(connectionRepo, opts.Hub, logger)
	roomService := service.NewRoomService(roomRepo, opts.Hub, logger)
	notificationService := service.NewNotificationService(notificationRepo, opts.Hub, opts.EventRouter, logger)
	statsService := service.NewStatsService(opts.Hub, opts.EventRouter, logger)

	socketService := service.NewSocketService(
		connectionService,
		roomService,
		notificationService,
		statsService,
		opts.Hub,
		opts.EventRouter,
		logger,
	)

	// API Handlers
	websocketHandler := socketapi.NewWebSocketHandler(socketService, opts.Hub, opts.EventRouter, logger)
	connectionHandler := socketapi.NewConnectionHandler(connectionService, logger)
	roomHandler := socketapi.NewRoomHandler(roomService, logger)
	notificationHandler := socketapi.NewNotificationHandler(notificationService, logger)
	statsHandler := socketapi.NewStatsHandler(statsService, logger)

	// Routes
	routes := socketapi.NewRoutes(
		websocketHandler,
		connectionHandler,
		roomHandler,
		notificationHandler,
		statsHandler,
	)

	module := NewModule()

	return SocketModuleParams{
		Module:           module,
		SocketService:    socketService,
		ConnectionRepo:   connectionRepo,
		RoomRepo:         roomRepo,
		NotificationRepo: notificationRepo,
		WebSocketHandler: websocketHandler,
		Routes:           routes,
	}, nil
}

// RegisterModule đăng ký module với FX
var RegisterModule = fx.Options(
	fx.Provide(ProvideSocketModule),
)
