package api

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/pagination"
	"wnapi/internal/pkg/response"
	"wnapi/modules/integration/socket/dto"
	"wnapi/modules/integration/socket/service"
)

// ConnectionHandler xử lý các API endpoints cho connection management
type ConnectionHandler interface {
	GetConnections(c *gin.Context)
	GetConnectionByID(c *gin.Context)
	DisconnectUser(c *gin.Context)
	BroadcastToUser(c *gin.Context)
	GetUserConnections(c *gin.Context)
}

type connectionHandler struct {
	connectionService service.ConnectionService
	logger            logger.Logger
}

// NewConnectionHandler tạo connection handler mới
func NewConnectionHandler(
	connectionService service.ConnectionService,
	logger logger.Logger,
) ConnectionHandler {
	return &connectionHandler{
		connectionService: connectionService,
		logger:           logger.Named("connection-handler"),
	}
}

// GetConnections lấy danh sách tất cả connections
func (h *connectionHandler) GetConnections(c *gin.Context) {
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		response.BadRequest(c, "Invalid tenant ID", "INVALID_TENANT_ID", err.Error())
		return
	}

	// Parse cursor pagination parameters
	params := pagination.ParseFromRequest(c)

	filter := &dto.ConnectionFilter{
		TenantID: tenantID,
	}
	connections, nextCursor, hasMore, err := h.connectionService.GetConnectionsWithCursor(c.Request.Context(), filter, &params)
	if err != nil {
		h.logger.Error("Failed to get connections",
			"tenant_id", tenantID,
			"error", err)
		response.InternalServerError(c, "Failed to retrieve connections")
		return
	}

	meta := &response.Meta{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	response.Success(c, connections, meta)
}

// GetConnectionByID lấy thông tin connection theo ID
func (h *connectionHandler) GetConnectionByID(c *gin.Context) {
	connectionID := c.Param("id")
	if connectionID == "" {
		response.BadRequest(c, "Connection ID is required", "MISSING_CONNECTION_ID", nil)
		return
	}

	connection, err := h.connectionService.GetConnection(c.Request.Context(), connectionID)
	if err != nil {
		h.logger.Error("Failed to get connection",
			"connection_id", connectionID,
			"error", err)
		response.NotFound(c, "Connection not found")
		return
	}

	response.Success(c, connection, nil)
}

// DisconnectUser disconnect tất cả connections của user
func (h *connectionHandler) DisconnectUser(c *gin.Context) {
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		response.BadRequest(c, "Invalid tenant ID", "INVALID_TENANT_ID", err.Error())
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid user ID", "INVALID_USER_ID", nil)
		return
	}

	var request struct {
		Reason string `json:"reason,omitempty"`
	}
	c.ShouldBindJSON(&request)

	// Get user connections first
	connections, err := h.connectionService.GetUserConnections(c.Request.Context(), uint(userID), tenantID)
	if err != nil {
		h.logger.Error("Failed to get user connections",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		response.InternalServerError(c, "Failed to get user connections")
		return
	}

	// Close each connection
	var disconnectedCount int
	for _, conn := range connections {
		if err := h.connectionService.CloseConnection(c.Request.Context(), conn.ID, request.Reason, false); err != nil {
			h.logger.Warn("Failed to close connection",
				"connection_id", conn.ID,
				"user_id", userID,
				"error", err)
		} else {
			disconnectedCount++
		}
	}

	result := gin.H{
		"message": "User connections disconnected",
		"disconnected_count": disconnectedCount,
		"total_connections": len(connections),
	}

	response.Success(c, result, nil)
}

// BroadcastToUser gửi message đến tất cả connections của user
func (h *connectionHandler) BroadcastToUser(c *gin.Context) {
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		response.BadRequest(c, "Invalid tenant ID", "INVALID_TENANT_ID", err.Error())
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid user ID", "INVALID_USER_ID", nil)
		return
	}

	var requestBody struct {
		Type  string                 `json:"type" binding:"required"`
		Event string                 `json:"event" binding:"required"`
		Data  map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&requestBody); err != nil {
		response.BadRequest(c, "Invalid request body", "INVALID_REQUEST_BODY", err.Error())
		return
	}

	// Create BroadcastToUserRequest
	request := dto.BroadcastToUserRequest{
		UserID:   uint(userID),
		TenantID: tenantID,
		Type:     requestBody.Type,
		Event:    requestBody.Event,
		Data:     requestBody.Data,
	}

	err = h.connectionService.BroadcastToUser(c.Request.Context(), request)
	if err != nil {
		h.logger.Error("Failed to broadcast to user",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		response.InternalServerError(c, "Failed to send broadcast")
		return
	}

	result := gin.H{
		"message": "Broadcast sent successfully",
	}

	response.Success(c, result, nil)
}

// GetUserConnections lấy danh sách connections của user
func (h *connectionHandler) GetUserConnections(c *gin.Context) {
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		response.BadRequest(c, "Invalid tenant ID", "INVALID_TENANT_ID", err.Error())
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "Invalid user ID", "INVALID_USER_ID", nil)
		return
	}

	connections, err := h.connectionService.GetUserConnections(c.Request.Context(), uint(userID), tenantID)
	if err != nil {
		h.logger.Error("Failed to get user connections",
			"user_id", userID,
			"tenant_id", tenantID,
			"error", err)
		response.InternalServerError(c, "Failed to retrieve user connections")
		return
	}

	result := gin.H{
		"connections": connections,
		"user_id":     userID,
		"tenant_id":   tenantID,
	}

	response.Success(c, result, nil)
}

// extractTenantID extract tenant ID từ context hoặc query
func (h *connectionHandler) extractTenantID(c *gin.Context) (uint, error) {
	// Try to get from JWT context first
	if tenantIDInterface, exists := c.Get("tenant_id"); exists {
		if tenantID, ok := tenantIDInterface.(uint); ok {
			return tenantID, nil
		}
	}

	// Fallback to query parameter
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		return 0, gin.Error{Err: gin.Error{Err: nil}, Type: gin.ErrorTypeBind}
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		return 0, err
	}

	return uint(tenantID), nil
}
