package api

import (
	"github.com/gin-gonic/gin"
	
	"wnapi/internal/pkg/logger"
)

// RegisterSocketRoutes đăng ký routes cho Socket module với FX
func RegisterSocketRoutes(router *gin.Engine, routes Routes) {
	// Tạo API group cho socket module
	socketGroup := router.Group("/api/admin/v1/socket")
	
	// Đăng ký tất cả routes
	routes.RegisterRoutes(socketGroup)
}

// Routes interface cho Socket module routes
type Routes interface {
	RegisterRoutes(router *gin.RouterGroup)
	GetRouteInfo() []RouteInfo
}

// RouteInfo thông tin về route
type RouteInfo struct {
	Method      string `json:"method"`
	Path        string `json:"path"`
	Handler     string `json:"handler"`
	Description string `json:"description"`
	Auth        bool   `json:"auth_required"`
}

type routes struct {
	websocketHandler    WebSocketHandler
	connectionHandler   ConnectionHandler
	roomHandler         RoomHandler
	notificationHandler NotificationHandler
	statsHandler        StatsHandler
	logger             logger.Logger
}

// NewRoutes tạo Routes instance mới
func NewRoutes(
	websocketHandler WebSocketHandler,
	connectionHandler ConnectionHandler,
	roomHandler RoomHandler,
	notificationHandler NotificationHandler,
	statsHandler StatsHandler,
) Routes {
	return &routes{
		websocketHandler:    websocketHandler,
		connectionHandler:   connectionHandler,
		roomHandler:         roomHandler,
		notificationHandler: notificationHandler,
		statsHandler:        statsHandler,
	}
}

// RegisterRoutes đăng ký tất cả routes cho Socket module
func (r *routes) RegisterRoutes(router *gin.RouterGroup) {
	// WebSocket endpoints
	ws := router.Group("/ws")
	{
		ws.GET("/connect", r.websocketHandler.HandleWebSocket)
		ws.GET("/health", r.websocketHandler.HandleHealthCheck)
	}

	// Connection management endpoints (Admin/Moderator only)
	connections := router.Group("/connections")
	// connections.Use(middleware.AuthRequired(), middleware.RoleRequired("admin", "moderator"))
	{
		connections.GET("", r.connectionHandler.GetConnections)
		connections.GET("/:id", r.connectionHandler.GetConnectionByID)
		connections.DELETE("/:id", r.connectionHandler.DisconnectUser)
		connections.POST("/users/:user_id/broadcast", r.connectionHandler.BroadcastToUser)
		connections.GET("/users/:user_id", r.connectionHandler.GetUserConnections)
	}

	// Room management endpoints
	rooms := router.Group("/rooms")
	// rooms.Use(middleware.AuthRequired())
	{
		rooms.POST("", r.roomHandler.CreateRoom)
		rooms.GET("", r.roomHandler.GetRooms)
		rooms.GET("/:id", r.roomHandler.GetRoom)
		rooms.PUT("/:id", r.roomHandler.UpdateRoom)
		rooms.DELETE("/:id", r.roomHandler.DeleteRoom)
		
		// Room membership endpoints
		rooms.POST("/:id/join", r.roomHandler.JoinRoom)
		rooms.POST("/:id/leave", r.roomHandler.LeaveRoom)
		rooms.GET("/:id/members", r.roomHandler.GetRoomMembers)
		rooms.POST("/:id/broadcast", r.roomHandler.BroadcastToRoom)
		
		// Room member management (Admin/Moderator only)
		rooms.PUT("/:id/members/:member_id/role", r.roomHandler.UpdateMemberRole)
		rooms.DELETE("/:id/members/:member_id", r.roomHandler.RemoveMember)
	}

	// Notification endpoints
	notifications := router.Group("/notifications")
	// notifications.Use(middleware.AuthRequired())
	{
		notifications.POST("/send", r.notificationHandler.SendNotification)
		notifications.POST("/send-bulk", r.notificationHandler.SendBulkNotification)
		notifications.GET("", r.notificationHandler.GetUserNotifications)
		notifications.PUT("/:id/read", r.notificationHandler.MarkAsRead)
		notifications.PUT("/read-all", r.notificationHandler.MarkAllAsRead)
		notifications.DELETE("/:id", r.notificationHandler.DeleteNotification)
		notifications.POST("/:id/resend", r.notificationHandler.ResendNotification)
		
		// Notification settings
		notifications.GET("/settings", r.notificationHandler.GetNotificationSettings)
		notifications.PUT("/settings", r.notificationHandler.UpdateNotificationSettings)
		
		// Delivery history
		notifications.GET("/delivery-history", r.notificationHandler.GetDeliveryHistory)
	}

	// Statistics và monitoring endpoints
	stats := router.Group("/stats")
	// stats.Use(middleware.AuthRequired())
	{
		stats.GET("/overall", r.statsHandler.GetOverallStats)
		stats.GET("/connections", r.statsHandler.GetConnectionStats)
		stats.GET("/rooms", r.statsHandler.GetRoomStats)
		stats.GET("/notifications", r.statsHandler.GetNotificationStats)
		stats.GET("/health", r.statsHandler.GetSystemHealth)
		stats.GET("/performance", r.statsHandler.GetPerformanceMetrics)
		stats.GET("/logs", r.statsHandler.GetActivityLogs)
		stats.GET("/export", r.statsHandler.ExportStats)
	}
}

// GetRouteInfo trả về thông tin về tất cả routes
func (r *routes) GetRouteInfo() []RouteInfo {
	return []RouteInfo{
		// WebSocket routes
		{
			Method:      "GET",
			Path:        "/ws/connect",
			Handler:     "websocketHandler.HandleWebSocket",
			Description: "Upgrade HTTP connection to WebSocket",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/ws/health",
			Handler:     "websocketHandler.HandleHealthCheck",
			Description: "WebSocket health check endpoint",
			Auth:        false,
		},

		// Connection routes
		{
			Method:      "GET",
			Path:        "/connections",
			Handler:     "connectionHandler.GetConnections",
			Description: "Get all active connections",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/connections/:id",
			Handler:     "connectionHandler.GetConnectionByID",
			Description: "Get connection details by ID",
			Auth:        true,
		},
		{
			Method:      "DELETE",
			Path:        "/connections/:id",
			Handler:     "connectionHandler.DisconnectUser",
			Description: "Disconnect a specific connection",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/connections/users/:user_id/broadcast",
			Handler:     "connectionHandler.BroadcastToUser",
			Description: "Broadcast message to all user connections",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/connections/users/:user_id",
			Handler:     "connectionHandler.GetUserConnections",
			Description: "Get all connections for a user",
			Auth:        true,
		},

		// Room routes
		{
			Method:      "POST",
			Path:        "/rooms",
			Handler:     "roomHandler.CreateRoom",
			Description: "Create a new room",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/rooms",
			Handler:     "roomHandler.GetRooms",
			Description: "Get user's rooms",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/rooms/:id",
			Handler:     "roomHandler.GetRoom",
			Description: "Get room details by ID",
			Auth:        true,
		},
		{
			Method:      "PUT",
			Path:        "/rooms/:id",
			Handler:     "roomHandler.UpdateRoom",
			Description: "Update room information",
			Auth:        true,
		},
		{
			Method:      "DELETE",
			Path:        "/rooms/:id",
			Handler:     "roomHandler.DeleteRoom",
			Description: "Delete a room",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/rooms/:id/join",
			Handler:     "roomHandler.JoinRoom",
			Description: "Join a room",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/rooms/:id/leave",
			Handler:     "roomHandler.LeaveRoom",
			Description: "Leave a room",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/rooms/:id/members",
			Handler:     "roomHandler.GetRoomMembers",
			Description: "Get room members",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/rooms/:id/broadcast",
			Handler:     "roomHandler.BroadcastToRoom",
			Description: "Broadcast message to room members",
			Auth:        true,
		},
		{
			Method:      "PUT",
			Path:        "/rooms/:id/members/:member_id/role",
			Handler:     "roomHandler.UpdateMemberRole",
			Description: "Update member role in room",
			Auth:        true,
		},
		{
			Method:      "DELETE",
			Path:        "/rooms/:id/members/:member_id",
			Handler:     "roomHandler.RemoveMember",
			Description: "Remove member from room",
			Auth:        true,
		},

		// Notification routes
		{
			Method:      "POST",
			Path:        "/notifications/send",
			Handler:     "notificationHandler.SendNotification",
			Description: "Send notification to user",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/notifications/send-bulk",
			Handler:     "notificationHandler.SendBulkNotification",
			Description: "Send bulk notifications",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/notifications",
			Handler:     "notificationHandler.GetUserNotifications",
			Description: "Get user notifications",
			Auth:        true,
		},
		{
			Method:      "PUT",
			Path:        "/notifications/:id/read",
			Handler:     "notificationHandler.MarkAsRead",
			Description: "Mark notification as read",
			Auth:        true,
		},
		{
			Method:      "PUT",
			Path:        "/notifications/read-all",
			Handler:     "notificationHandler.MarkAllAsRead",
			Description: "Mark all notifications as read",
			Auth:        true,
		},
		{
			Method:      "DELETE",
			Path:        "/notifications/:id",
			Handler:     "notificationHandler.DeleteNotification",
			Description: "Delete notification",
			Auth:        true,
		},
		{
			Method:      "POST",
			Path:        "/notifications/:id/resend",
			Handler:     "notificationHandler.ResendNotification",
			Description: "Resend notification",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/notifications/settings",
			Handler:     "notificationHandler.GetNotificationSettings",
			Description: "Get notification settings",
			Auth:        true,
		},
		{
			Method:      "PUT",
			Path:        "/notifications/settings",
			Handler:     "notificationHandler.UpdateNotificationSettings",
			Description: "Update notification settings",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/notifications/delivery-history",
			Handler:     "notificationHandler.GetDeliveryHistory",
			Description: "Get notification delivery history",
			Auth:        true,
		},

		// Statistics routes
		{
			Method:      "GET",
			Path:        "/stats/overall",
			Handler:     "statsHandler.GetOverallStats",
			Description: "Get overall system statistics",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/connections",
			Handler:     "statsHandler.GetConnectionStats",
			Description: "Get connection statistics",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/rooms",
			Handler:     "statsHandler.GetRoomStats",
			Description: "Get room statistics",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/notifications",
			Handler:     "statsHandler.GetNotificationStats",
			Description: "Get notification statistics",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/health",
			Handler:     "statsHandler.GetSystemHealth",
			Description: "Get system health status",
			Auth:        false,
		},
		{
			Method:      "GET",
			Path:        "/stats/performance",
			Handler:     "statsHandler.GetPerformanceMetrics",
			Description: "Get performance metrics",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/logs",
			Handler:     "statsHandler.GetActivityLogs",
			Description: "Get activity logs",
			Auth:        true,
		},
		{
			Method:      "GET",
			Path:        "/stats/export",
			Handler:     "statsHandler.ExportStats",
			Description: "Export statistics data",
			Auth:        true,
		},
	}
}
