package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	
	"wnapi/internal/pkg/logger"
	"wnapi/modules/integration/socket/service"
)

// StatsHandler xử lý các API endpoints cho statistics và monitoring
type StatsHandler interface {
	GetOverallStats(c *gin.Context)
	GetConnectionStats(c *gin.Context)
	GetRoomStats(c *gin.Context)
	GetNotificationStats(c *gin.Context)
	GetSystemHealth(c *gin.Context)
	GetPerformanceMetrics(c *gin.Context)
	GetActivityLogs(c *gin.Context)
	ExportStats(c *gin.Context)
}

type statsHandler struct {
	statsService service.StatsService
	logger      logger.Logger
}

// NewStatsHandler tạo StatsHandler mới
func NewStatsHandler(
	statsService service.StatsService,
	logger logger.Logger,
) StatsHandler {
	return &statsHandler{
		statsService: statsService,
		logger:      logger.Named("stats-handler"),
	}
}

// GetOverallStats lấy tổng quan thống kê hệ thống
func (h *statsHandler) GetOverallStats(c *gin.Context) {
	tenantID := h.getTenantIDFromContext(c)
	
	stats, err := h.statsService.GetOverallStats(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error("Failed to get overall stats", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
	})
}

// GetConnectionStats lấy thống kê connections
func (h *statsHandler) GetConnectionStats(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when connection stats are implemented
	
	// Parse time range parameters
	timeRange := c.DefaultQuery("range", "24h") // 1h, 24h, 7d, 30d
	
	// GetConnectionStats not implemented in service interface yet
	// TODO: Implement GetConnectionStats in StatsService
	stats := gin.H{
		"message": "Connection statistics not implemented yet",
		"time_range": timeRange,
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"time_range": timeRange,
	})
}

// GetRoomStats lấy thống kê rooms
func (h *statsHandler) GetRoomStats(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when room stats are implemented
	
	// Parse time range parameters
	timeRange := c.DefaultQuery("range", "24h")
	
	// GetRoomStats not implemented in service interface yet
	// TODO: Implement GetRoomStats in StatsService
	stats := gin.H{
		"message": "Room statistics not implemented yet",
		"time_range": timeRange,
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"time_range": timeRange,
	})
}

// GetNotificationStats lấy thống kê notifications
func (h *statsHandler) GetNotificationStats(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when notification stats are implemented
	
	// Parse time range parameters
	timeRange := c.DefaultQuery("range", "24h")
	
	// GetNotificationStats not implemented in service interface yet
	// TODO: Implement GetNotificationStats in StatsService
	stats := gin.H{
		"message": "Notification statistics not implemented yet",
		"time_range": timeRange,
	}

	c.JSON(http.StatusOK, gin.H{
		"stats": stats,
		"time_range": timeRange,
	})
}

// GetSystemHealth lấy thông tin system health
func (h *statsHandler) GetSystemHealth(c *gin.Context) {
	health, err := h.statsService.GetHealthCheck(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get system health", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve system health",
		})
		return
	}

	// Determine HTTP status based on health status
	var statusCode int
	switch health.Status {
	case "healthy":
		statusCode = http.StatusOK
	case "degraded":
		statusCode = http.StatusOK // Still return 200 but with degraded status
	case "unhealthy":
		statusCode = http.StatusServiceUnavailable
	default:
		statusCode = http.StatusInternalServerError
	}

	c.JSON(statusCode, gin.H{
		"health": health,
	})
}

// GetPerformanceMetrics lấy performance metrics
func (h *statsHandler) GetPerformanceMetrics(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when performance metrics are implemented
	
	// Parse time range parameters
	timeRange := c.DefaultQuery("range", "1h")
	interval := c.DefaultQuery("interval", "5m") // 1m, 5m, 15m, 1h
	
	metrics, err := h.statsService.GetPerformanceStats(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get performance metrics", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve performance metrics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"time_range": timeRange,
		"interval": interval,
	})
}

// GetActivityLogs lấy activity logs
func (h *statsHandler) GetActivityLogs(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when activity logs are implemented
	
	// Parse query parameters
	page := 1
	limit := 50
	
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 1000 {
			limit = l
		}
	}
	
	level := c.Query("level")     // info, warn, error
	source := c.Query("source")   // connection, room, notification, system
	userIDStr := c.Query("user_id")
	
	var userID uint
	if userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userID = uint(uid)
		}
	}
	
	// GetActivityLogs not implemented in service interface yet
	// TODO: Implement GetActivityLogs in StatsService
	logs := []gin.H{}
	total := int64(0)

	c.JSON(http.StatusOK, gin.H{
		"logs": logs,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
		"filters": gin.H{
			"level":   level,
			"source":  source,
			"user_id": userID,
		},
	})
}

// ExportStats export statistics về format khác nhau
func (h *statsHandler) ExportStats(c *gin.Context) {
	_ = h.getTenantIDFromContext(c)
	// Note: tenantID will be used when export stats are implemented
	
	// Parse export parameters
	format := c.DefaultQuery("format", "json") // json, csv, xlsx
	statsType := c.DefaultQuery("type", "overall") // overall, connections, rooms, notifications
	timeRange := c.DefaultQuery("range", "24h")
	
	// Validate format
	if format != "json" && format != "csv" && format != "xlsx" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid format. Supported formats: json, csv, xlsx",
		})
		return
	}
	
	// Validate stats type
	validTypes := map[string]bool{
		"overall": true,
		"connections": true,
		"rooms": true,
		"notifications": true,
		"performance": true,
	}
	
	if !validTypes[statsType] {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid stats type",
		})
		return
	}
	
	// ExportStats not implemented in service interface yet
	// TODO: Implement ExportStats in StatsService
	c.JSON(http.StatusNotImplemented, gin.H{
		"error": "Export functionality not implemented yet",
		"message": "This feature will be available in a future release",
		"requested_format": format,
		"requested_type": statsType,
		"requested_range": timeRange,
	})
}

// getTenantIDFromContext extract tenant ID từ context
func (h *statsHandler) getTenantIDFromContext(c *gin.Context) uint {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	// Fallback to query parameter
	if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
			return uint(tenantID)
		}
	}
	return 0
}
