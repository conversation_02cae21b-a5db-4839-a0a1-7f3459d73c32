package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/socket"
	"wnapi/modules/integration/socket/service"
)

// WebSocketHandler xử lý WebSocket connections
type WebSocketHandler interface {
	HandleWebSocket(c *gin.Context)
	HandleHealthCheck(c *gin.Context)
}

type websocketHandler struct {
	socketService service.SocketService
	hub           socket.Hub
	eventRouter   socket.EventRouter
	logger        logger.Logger
	upgrader      websocket.Upgrader
}

// NewWebSocketHandler tạo WebSocket handler mới
func NewWebSocketHandler(
	socketService service.SocketService,
	hub socket.Hub,
	eventRouter socket.EventRouter,
	logger logger.Logger,
) WebSocketHandler {
	upgrader := websocket.Upgrader{
		ReadBufferSize:  socket.DefaultReadBufferSize,
		WriteBufferSize: socket.DefaultWriteBufferSize,
		CheckOrigin: func(r *http.Request) bool {
			// TODO: Implement proper origin checking
			return true
		},
	}

	return &websocketHandler{
		socketService: socketService,
		hub:           hub,
		eventRouter:   eventRouter,
		logger:        logger.Named("websocket-handler"),
		upgrader:      upgrader,
	}
}

// HandleWebSocket xử lý WebSocket connection requests
func (h *websocketHandler) HandleWebSocket(c *gin.Context) {
	// Extract authentication info từ JWT token hoặc query params
	userID, tenantID, websiteID, err := h.extractAuthInfo(c)
	if err != nil {
		h.logger.Warn("WebSocket authentication failed",
			"remote_addr", c.ClientIP(),
			"error", err,
		)
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Upgrade connection đến WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Error("Failed to upgrade WebSocket connection",
			"remote_addr", c.ClientIP(),
			"error", err,
		)
		return
	}

	// Tạo WebSocket connection
	connID := fmt.Sprintf("%d_%d_%d", userID, tenantID, time.Now().UnixNano())
	clientIP := c.ClientIP()
	config := socket.DefaultConnectionConfig() // Sử dụng config mặc định
	
	socketConn := socket.NewWebSocketConnection(
		connID,
		fmt.Sprintf("%d", userID),
		fmt.Sprintf("%d", tenantID),
		fmt.Sprintf("%d", websiteID),
		clientIP,
		config,
		h.logger,
	)
	
	// Đăng ký connection với hub
	if err := h.hub.RegisterConnection(socketConn); err != nil {
		h.logger.Error("Failed to register connection",
			"conn_id", connID,
			"user_id", userID,
			"error", err,
		)
		conn.Close()
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to register connection",
		})
		return
	}

	h.logger.Info("WebSocket connection established",
		"conn_id", socketConn.ID(),
		"user_id", userID,
		"tenant_id", tenantID,
		"remote_addr", c.ClientIP(),
	)

	// Handle user connect event
	ctx := context.Background()
	if err := h.socketService.HandleUserConnect(ctx, userID, tenantID, websiteID); err != nil {
		h.logger.Warn("Failed to handle user connect",
			"user_id", userID,
			"error", err)
	}

	// Send welcome message
	welcomeMsg := socket.NewMessage("event", "system.connected", map[string]interface{}{
		"connection_id": socketConn.ID(),
		"user_id":      userID,
		"tenant_id":    tenantID,
		"website_id":   websiteID,
		"server_time":  time.Now(),
		"features": map[string]interface{}{
			"rooms":         true,
			"notifications": true,
			"file_sharing":  false,
			"video_calls":   false,
		},
	})

	if err := socketConn.Send(welcomeMsg); err != nil {
		h.logger.Warn("Failed to send welcome message",
			"conn_id", socketConn.ID(),
			"error", err,
		)
	}

	// Start connection monitoring
	go h.monitorConnection(socketConn, userID, tenantID)
}

// HandleHealthCheck xử lý health check cho WebSocket endpoint
func (h *websocketHandler) HandleHealthCheck(c *gin.Context) {
	stats := h.hub.GetStatistics()

	health := gin.H{
		"status":      "healthy",
		"timestamp":   time.Now(),
		"connections": stats.ActiveConnections,
		"rooms":       stats.ActiveRooms,
		"uptime":      time.Since(stats.StartTime).String(),
		"version":     "1.0.0",
	}

	c.JSON(http.StatusOK, health)
}

// extractAuthInfo extract authentication information từ request
func (h *websocketHandler) extractAuthInfo(c *gin.Context) (uint, uint, uint, error) {
	// Try to get from JWT token first (would need auth middleware)
	if userIDInterface, exists := c.Get("user_id"); exists {
		if userID, ok := userIDInterface.(uint); ok {
			tenantIDInterface, _ := c.Get("tenant_id")
			websiteIDInterface, _ := c.Get("website_id")
			
			var tenantID, websiteID uint
			if tid, ok := tenantIDInterface.(uint); ok {
				tenantID = tid
			}
			if wid, ok := websiteIDInterface.(uint); ok {
				websiteID = wid
			}
			
			return userID, tenantID, websiteID, nil
		}
	}

	// Fallback to query parameters (for development/testing)
	userIDStr := c.Query("user_id")
	tenantIDStr := c.Query("tenant_id") 
	websiteIDStr := c.Query("website_id")

	if userIDStr == "" || tenantIDStr == "" {
		return 0, 0, 0, socket.NewSocketError("AUTH_REQUIRED", "missing authentication parameters")
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		return 0, 0, 0, socket.NewSocketError("INVALID_USER_ID", "invalid user ID")
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		return 0, 0, 0, socket.NewSocketError("INVALID_TENANT_ID", "invalid tenant ID")
	}

	var websiteID uint64 = 0
	if websiteIDStr != "" {
		websiteID, err = strconv.ParseUint(websiteIDStr, 10, 32)
		if err != nil {
			return 0, 0, 0, socket.NewSocketError("INVALID_WEBSITE_ID", "invalid website ID")
		}
	}

	return uint(userID), uint(tenantID), uint(websiteID), nil
}

// monitorConnection monitor connection lifecycle
func (h *websocketHandler) monitorConnection(conn socket.Connection, userID uint, tenantID uint) {
	defer func() {
		// Handle user disconnect when connection is closed
		ctx := context.Background()
		if err := h.socketService.HandleUserDisconnect(ctx, userID, tenantID); err != nil {
			h.logger.Warn("Failed to handle user disconnect",
				"user_id", userID,
				"tenant_id", tenantID,
				"error", err)
		}

		h.logger.Info("Connection monitoring ended",
			"conn_id", conn.ID(),
			"user_id", userID,
			"tenant_id", tenantID)
	}()

	// Send periodic ping messages
	pingTicker := time.NewTicker(30 * time.Second)
	defer pingTicker.Stop()

	// Monitor connection health
	healthTicker := time.NewTicker(5 * time.Minute)
	defer healthTicker.Stop()

	for {
		select {
		case <-pingTicker.C:
			// Send ping message
			pingMsg := socket.NewMessage("ping", "system.ping", map[string]interface{}{
				"timestamp": time.Now(),
			})
			
			if err := conn.Send(pingMsg); err != nil {
				h.logger.Debug("Failed to send ping, connection may be closed",
					"conn_id", conn.ID(),
					"error", err)
				return
			}

		case <-healthTicker.C:
			// Check connection health
			if time.Since(conn.LastActivity()) > 10*time.Minute {
				h.logger.Warn("Connection inactive for too long, closing",
					"conn_id", conn.ID(),
					"last_activity", conn.LastActivity())
				
				// Send timeout notification
				timeoutMsg := socket.NewMessage("event", "system.timeout", map[string]interface{}{
					"reason":    "inactivity",
					"timestamp": time.Now(),
				})
				conn.Send(timeoutMsg)
				
				// Close connection
				conn.Close()
				return
			}

		default:
			// Check if connection is still alive
			if !conn.IsActive() {
				h.logger.Debug("Connection is no longer active",
					"conn_id", conn.ID())
				return
			}
			
			// Small delay to prevent tight loop
			time.Sleep(1 * time.Second)
		}
	}
}
