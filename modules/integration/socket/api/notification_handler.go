package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	
	"wnapi/internal/pkg/logger"
	"wnapi/modules/integration/socket/service"
	"wnapi/modules/integration/socket/dto"
)

// NotificationHandler xử lý các API endpoints cho notification management
type NotificationHandler interface {
	SendNotification(c *gin.Context)
	SendBulkNotification(c *gin.Context)
	GetUserNotifications(c *gin.Context)
	MarkAsRead(c *gin.Context)
	MarkAllAsRead(c *gin.Context)
	DeleteNotification(c *gin.Context)
	GetNotificationSettings(c *gin.Context)
	UpdateNotificationSettings(c *gin.Context)
	GetDeliveryHistory(c *gin.Context)
	ResendNotification(c *gin.Context)
}

type notificationHandler struct {
	notificationService service.NotificationService
	logger             logger.Logger
}

// NewNotificationHandler tạo NotificationHandler mới
func NewNotificationHandler(
	notificationService service.NotificationService,
	logger logger.Logger,
) NotificationHandler {
	return &notificationHandler{
		notificationService: notificationService,
		logger:             logger.Named("notification-handler"),
	}
}

// SendNotification gửi notification đến user
func (h *notificationHandler) SendNotification(c *gin.Context) {
	var request dto.SendNotificationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Extract sender info từ context
	senderID, tenantID, _ := h.extractUserInfo(c)
	if senderID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	request.TenantID = tenantID
	// WebsiteID will be set by service layer if needed

	err := h.notificationService.SendNotification(c.Request.Context(), request)
	if err != nil {
		h.logger.Error("Failed to send notification", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send notification",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Notification sent successfully",
	})
}

// SendBulkNotification gửi notification đến nhiều users
func (h *notificationHandler) SendBulkNotification(c *gin.Context) {
	var request dto.BroadcastNotificationRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Extract sender info từ context
	_, tenantID, _ := h.extractUserInfo(c)
	if tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	request.TenantID = tenantID
	// WebsiteID will be set by service layer if needed

	err := h.notificationService.BroadcastNotification(c.Request.Context(), request)
	if err != nil {
		h.logger.Error("Failed to send bulk notification", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send bulk notification",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Bulk notification sent successfully",
	})
}

// GetUserNotifications lấy danh sách notifications của user
func (h *notificationHandler) GetUserNotifications(c *gin.Context) {
	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Parse query parameters
	request := &dto.NotificationListRequest{
		UserID:   userID,
		TenantID: tenantID,
		Page:     1,
		Limit:    20,
	}

	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			request.Page = page
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			request.Limit = limit
		}
	}

	request.Type = c.Query("type")               // info, warning, error, success
	request.Category = c.Query("category")       // system, user, marketing, etc.
	if c.Query("unread") == "true" {
		isRead := false
		request.IsRead = &isRead
	}

	notifications, total, err := h.notificationService.GetUserNotifications(c.Request.Context(), userID, tenantID, request.Page, request.Limit)
	if err != nil {
		h.logger.Error("Failed to get user notifications", "user_id", userID, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to retrieve notifications",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"notifications": notifications,
		"pagination": gin.H{
			"page":  request.Page,
			"limit": request.Limit,
			"total": total,
		},
	})
}

// MarkAsRead đánh dấu notification đã đọc
func (h *notificationHandler) MarkAsRead(c *gin.Context) {
	notificationIDStr := c.Param("id")
	if notificationIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Notification ID is required",
		})
		return
	}

	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid notification ID",
		})
		return
	}

	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Create mark as read request
	markRequest := dto.MarkNotificationReadRequest{
		NotificationIDs: []uint{uint(notificationID)},
	}
	err = h.notificationService.MarkNotificationsAsRead(c.Request.Context(), markRequest)
	if err != nil {
		h.logger.Error("Failed to mark notification as read", 
			"notification_id", notificationID, 
			"user_id", userID, 
			"error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to mark notification as read",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Notification marked as read",
	})
}

// MarkAllAsRead đánh dấu tất cả notifications đã đọc
func (h *notificationHandler) MarkAllAsRead(c *gin.Context) {
	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// Get all user notifications first to mark them as read
	notifications, _, err := h.notificationService.GetUserNotifications(c.Request.Context(), userID, tenantID, 1, 1000)
	if err != nil {
		h.logger.Error("Failed to get user notifications for mark all as read", 
			"user_id", userID, 
			"error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to mark all notifications as read",
		})
		return
	}

	// Extract notification IDs
	var notificationIDs []uint
	for _, notification := range notifications {
		if !notification.IsRead {
			notificationIDs = append(notificationIDs, notification.ID)
		}
	}

	if len(notificationIDs) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"message": "No unread notifications to mark",
			"count":   0,
		})
		return
	}

	// Mark notifications as read
	markRequest := dto.MarkNotificationReadRequest{
		NotificationIDs: notificationIDs,
	}
	err = h.notificationService.MarkNotificationsAsRead(c.Request.Context(), markRequest)
	count := len(notificationIDs)
	if err != nil {
		h.logger.Error("Failed to mark all notifications as read", 
			"user_id", userID, 
			"error", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to mark all notifications as read",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "All notifications marked as read",
		"count":   count,
	})
}

// DeleteNotification xóa notification
func (h *notificationHandler) DeleteNotification(c *gin.Context) {
	notificationIDStr := c.Param("id")
	if notificationIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Notification ID is required",
		})
		return
	}

	_, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid notification ID",
		})
		return
	}

	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// TODO: Implement delete notification functionality
	// For now, return not implemented
	c.JSON(http.StatusNotImplemented, gin.H{
		"error": "Delete notification not implemented yet",
	})

}

// GetNotificationSettings lấy cài đặt notification của user
func (h *notificationHandler) GetNotificationSettings(c *gin.Context) {
	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// TODO: Implement get notification settings functionality
	// For now, return default settings
	settings := map[string]interface{}{
		"email_notifications":    true,
		"push_notifications":     true,
		"websocket_notifications": true,
	}

	c.JSON(http.StatusOK, gin.H{
		"settings": settings,
	})
}

// UpdateNotificationSettings cập nhật cài đặt notification
func (h *notificationHandler) UpdateNotificationSettings(c *gin.Context) {
	var request struct {
		EmailNotifications     bool `json:"email_notifications"`
		PushNotifications      bool `json:"push_notifications"`
		WebsocketNotifications bool `json:"websocket_notifications"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// TODO: Implement actual settings update
	// For now, just return the updated settings
	settings := gin.H{
		"email_notifications":     request.EmailNotifications,
		"push_notifications":      request.PushNotifications,
		"websocket_notifications": request.WebsocketNotifications,
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
		"settings": settings,
	})
}

// GetDeliveryHistory lấy lịch sử delivery của notifications
func (h *notificationHandler) GetDeliveryHistory(c *gin.Context) {
	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// TODO: Implement delivery history functionality
	c.JSON(http.StatusOK, gin.H{
		"message": "Delivery history not implemented yet",
		"history": []interface{}{},
		"pagination": gin.H{
			"page":  1,
			"limit": 20,
			"total": 0,
		},
	})
}

// ResendNotification gửi lại notification
func (h *notificationHandler) ResendNotification(c *gin.Context) {
	notificationIDStr := c.Param("id")
	if notificationIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Notification ID is required",
		})
		return
	}

	_, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid notification ID",
		})
		return
	}

	userID, tenantID, _ := h.extractUserInfo(c)
	if userID == 0 || tenantID == 0 {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Authentication required",
		})
		return
	}

	// TODO: Implement resend notification functionality
	c.JSON(http.StatusOK, gin.H{
		"message": "Resend notification not implemented yet",
	})
}

// extractUserInfo extract user information từ context
func (h *notificationHandler) extractUserInfo(c *gin.Context) (userID, tenantID, websiteID uint) {
	if uid, exists := c.Get("user_id"); exists {
		if id, ok := uid.(uint); ok {
			userID = id
		}
	}
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = id
		}
	}
	if wid, exists := c.Get("website_id"); exists {
		if id, ok := wid.(uint); ok {
			websiteID = id
		}
	}

	// Fallback to query parameters
	if userID == 0 {
		if userIDStr := c.Query("user_id"); userIDStr != "" {
			if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
				userID = uint(uid)
			}
		}
	}
	if tenantID == 0 {
		if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
			if tid, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
				tenantID = uint(tid)
			}
		}
	}
	if websiteID == 0 {
		if websiteIDStr := c.Query("website_id"); websiteIDStr != "" {
			if wid, err := strconv.ParseUint(websiteIDStr, 10, 32); err == nil {
				websiteID = uint(wid)
			}
		}
	}

	return userID, tenantID, websiteID
}
