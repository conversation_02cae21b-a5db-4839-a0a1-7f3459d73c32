package models

import (
	"time"
)

// Subscription represents a user's subscription to specific events or channels
type Subscription struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID       uint      `json:"user_id" gorm:"not null;index"`
	TenantID     uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID    uint      `json:"website_id" gorm:"not null;index"`
	EventType    string    `json:"event_type" gorm:"type:varchar(100);not null;index"`
	Channel      string    `json:"channel" gorm:"type:varchar(100);not null;index"`
	ResourceID   string    `json:"resource_id" gorm:"type:varchar(100);index"`
	ResourceType string    `json:"resource_type" gorm:"type:varchar(50);index"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	Filters      string    `json:"filters" gorm:"type:json"`
	Metadata     string    `json:"metadata" gorm:"type:json"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for Subscription model
func (Subscription) TableName() string {
	return "socket_subscriptions"
}

// NotificationDelivery represents a notification delivery record
type NotificationDelivery struct {
	ID             uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	NotificationID uint      `json:"notification_id" gorm:"not null;index"`
	UserID         uint      `json:"user_id" gorm:"not null;index"`
	TenantID       uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID      uint      `json:"website_id" gorm:"not null;index"`
	ConnectionID   string    `json:"connection_id" gorm:"type:varchar(36);index"`
	Channel        string    `json:"channel" gorm:"type:varchar(50);not null"`
	Status         string    `json:"status" gorm:"type:varchar(20);not null"`
	DeliveredAt    *time.Time `json:"delivered_at"`
	ReadAt         *time.Time `json:"read_at"`
	ErrorMessage   string    `json:"error_message" gorm:"type:text"`
	Metadata       string    `json:"metadata" gorm:"type:json"`
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for NotificationDelivery model
func (NotificationDelivery) TableName() string {
	return "socket_notification_deliveries"
}

// Event type constants
const (
	EventTypeUserOnline        = "user.online"
	EventTypeUserOffline       = "user.offline"
	EventTypeUserTyping        = "user.typing"
	EventTypeRoomJoined        = "room.joined"
	EventTypeRoomLeft          = "room.left"
	EventTypeMessageSent       = "message.sent"
	EventTypeMessageReceived   = "message.received"
	EventTypeNotificationSent  = "notification.sent"
	EventTypeNotificationRead  = "notification.read"
	EventTypeSystemMaintenance = "system.maintenance"
	EventTypeSystemAlert       = "system.alert"
)

// Channel constants
const (
	ChannelUser         = "user"
	ChannelRoom         = "room"
	ChannelNotification = "notification"
	ChannelSystem       = "system"
	ChannelBroadcast    = "broadcast"
)

// Delivery status constants
const (
	DeliveryStatusPending    = "pending"
	DeliveryStatusDelivered  = "delivered"
	DeliveryStatusFailed     = "failed"
	DeliveryStatusRead       = "read"
	DeliveryStatusExpired    = "expired"
)
