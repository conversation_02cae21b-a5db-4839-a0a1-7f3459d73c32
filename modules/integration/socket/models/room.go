package models

import (
	"time"
)

// Room represents a chat room or communication channel
type Room struct {
	ID          string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Type        string    `json:"type" gorm:"type:varchar(50);not null"`
	TenantID    uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID   uint      `json:"website_id" gorm:"not null;index"`
	CreatorID   uint      `json:"creator_id" gorm:"not null;index"`
	IsPrivate   bool      `json:"is_private" gorm:"default:false"`
	MaxMembers  int       `json:"max_members" gorm:"default:100"`
	Description string    `json:"description" gorm:"type:text"`
	Metadata    string    `json:"metadata" gorm:"type:json"`
	Status      string    `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for Room model
func (Room) TableName() string {
	return "socket_rooms"
}

// RoomMember represents membership in a room
type RoomMember struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"`
	RoomID     string    `json:"room_id" gorm:"type:varchar(36);not null;index"`
	UserID     uint      `json:"user_id" gorm:"not null;index"`
	Role       string    `json:"role" gorm:"type:varchar(20);not null;default:'member'"`
	JoinedAt   time.Time `json:"joined_at" gorm:"not null"`
	LastSeen   time.Time `json:"last_seen"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	Metadata   string    `json:"metadata" gorm:"type:json"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for RoomMember model
func (RoomMember) TableName() string {
	return "socket_room_members"
}

// Room type constants
const (
	RoomTypeGeneral     = "general"
	RoomTypePrivate     = "private"
	RoomTypeGroup       = "group"
	RoomTypeChannel     = "channel"
	RoomTypeNotification = "notification"
	RoomTypeSystem      = "system"
)

// Room status constants
const (
	RoomStatusActive   = "active"
	RoomStatusArchived = "archived"
	RoomStatusDeleted  = "deleted"
)

// Member role constants
const (
	RoomRoleOwner     = "owner"
	RoomRoleAdmin     = "admin"
	RoomRoleModerator = "moderator"
	RoomRoleMember    = "member"
	RoomRoleGuest     = "guest"
)
