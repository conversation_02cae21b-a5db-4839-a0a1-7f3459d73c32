package models

import (
	"time"
)

// Connection represents a WebSocket connection in the database
type Connection struct {
	ID           string    `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID       uint      `json:"user_id" gorm:"not null;index"`
	TenantID     uint      `json:"tenant_id" gorm:"not null;index"`
	WebsiteID    uint      `json:"website_id" gorm:"not null;index"`
	Status       string    `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	ConnectedAt  time.Time `json:"connected_at" gorm:"not null"`
	LastActivity time.Time `json:"last_activity" gorm:"not null"`
	IPAddress    string    `json:"ip_address" gorm:"type:varchar(45)"`
	UserAgent    string    `json:"user_agent" gorm:"type:text"`
	Metadata     string    `json:"metadata" gorm:"type:json"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for Connection model
func (Connection) TableName() string {
	return "socket_connections"
}

// ConnectionStatus constants
const (
	ConnectionStatusActive      = "active"
	ConnectionStatusDisconnected = "disconnected"
	ConnectionStatusTimedOut    = "timed_out"
	ConnectionStatusError       = "error"
)
