CREATE TABLE IF NOT EXISTS seo_meta (
    meta_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_id INT UNSIGNED NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    meta_title VARCHAR(255),
    meta_description TEXT,
    keywords VARCHAR(255),
    canonical_url VARCHAR(255),
    og_title VARCHAR(255),
    og_description TEXT,
    og_image VARCHAR(255),
    robots_index TINYINT(1) DEFAULT 1,
    robots_follow TINYINT(1) DEFAULT 1,
    robots_advanced VARCHAR(255),
    seo_score TINYINT DEFAULT 0 CHECK (seo_score >= 0 AND seo_score <= 100),
    readability_score TINYINT DEFAULT 0 CHECK (readability_score >= 0 AND readability_score <= 100),
    schema_data JSON, -- Consider JSON type for structured data
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Ensure unique SEO meta per table record
    UNIQUE KEY unique_table_record (table_id, table_name),
    INDEX idx_table (table_id, table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;