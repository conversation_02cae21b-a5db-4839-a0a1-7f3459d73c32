package internal

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "seo"

// SEO Meta permissions
const (
	// Standard CRUD permissions for SEO meta
	CreateSeoMetaPermission = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + permission.ActionCreate
	ReadSeoMetaPermission   = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + permission.ActionRead
	UpdateSeoMetaPermission = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteSeoMetaPermission = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + permission.ActionDelete
	ListSeoMetaPermission   = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + permission.ActionList

	// SEO meta operations
	UpsertSeoMetaPermission     = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + "upsert"
	BulkUpdateSeoMetaPermission = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + "bulk_update"
	ImportSeoMetaPermission     = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + "import"
	ExportSeoMetaPermission     = ModuleName + permission.PermissionSeparator + "meta" + permission.PermissionSeparator + "export"
)

// Keywords and Keyphrases permissions
const (
	// Keyword management
	CreateKeywordPermission = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + permission.ActionCreate
	ReadKeywordPermission   = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + permission.ActionRead
	UpdateKeywordPermission = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteKeywordPermission = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + permission.ActionDelete
	ListKeywordPermission   = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + permission.ActionList

	// Keyword operations
	AnalyzeKeywordPermission  = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + "analyze"
	ResearchKeywordPermission = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + "research"
	TrackKeywordPermission    = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + "track"
	OptimizeKeywordPermission = ModuleName + permission.PermissionSeparator + "keywords" + permission.PermissionSeparator + "optimize"
)

// Social Media Meta permissions
const (
	// Open Graph management
	CreateOpenGraphPermission = ModuleName + permission.PermissionSeparator + "open_graph" + permission.PermissionSeparator + permission.ActionCreate
	ReadOpenGraphPermission   = ModuleName + permission.PermissionSeparator + "open_graph" + permission.PermissionSeparator + permission.ActionRead
	UpdateOpenGraphPermission = ModuleName + permission.PermissionSeparator + "open_graph" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteOpenGraphPermission = ModuleName + permission.PermissionSeparator + "open_graph" + permission.PermissionSeparator + permission.ActionDelete

	// Twitter Cards management
	CreateTwitterCardPermission = ModuleName + permission.PermissionSeparator + "twitter_cards" + permission.PermissionSeparator + permission.ActionCreate
	ReadTwitterCardPermission   = ModuleName + permission.PermissionSeparator + "twitter_cards" + permission.PermissionSeparator + permission.ActionRead
	UpdateTwitterCardPermission = ModuleName + permission.PermissionSeparator + "twitter_cards" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteTwitterCardPermission = ModuleName + permission.PermissionSeparator + "twitter_cards" + permission.PermissionSeparator + permission.ActionDelete

	// Social media operations
	PreviewSocialMediaPermission = ModuleName + permission.PermissionSeparator + "social_media" + permission.PermissionSeparator + "preview"
	TestSocialMediaPermission    = ModuleName + permission.PermissionSeparator + "social_media" + permission.PermissionSeparator + "test"
)

// Schema Markup permissions
const (
	// Schema management
	CreateSchemaPermission = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + permission.ActionCreate
	ReadSchemaPermission   = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + permission.ActionRead
	UpdateSchemaPermission = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteSchemaPermission = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + permission.ActionDelete
	ListSchemaPermission   = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + permission.ActionList

	// Schema operations
	ValidateSchemaPermission = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + "validate"
	TestSchemaPermission     = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + "test"
	GenerateSchemaPermission = ModuleName + permission.PermissionSeparator + "schema" + permission.PermissionSeparator + "generate"
)

// Sitemaps permissions
const (
	// Sitemap management
	CreateSitemapPermission = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + permission.ActionCreate
	ReadSitemapPermission   = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + permission.ActionRead
	UpdateSitemapPermission = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteSitemapPermission = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + permission.ActionDelete
	ListSitemapPermission   = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + permission.ActionList

	// Sitemap operations
	GenerateSitemapPermission = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + "generate"
	SubmitSitemapPermission   = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + "submit"
	ValidateSitemapPermission = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + "validate"
	RefreshSitemapPermission  = ModuleName + permission.PermissionSeparator + "sitemaps" + permission.PermissionSeparator + "refresh"
)

// Redirects permissions
const (
	// Redirect management
	CreateRedirectPermission = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + permission.ActionCreate
	ReadRedirectPermission   = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + permission.ActionRead
	UpdateRedirectPermission = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteRedirectPermission = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + permission.ActionDelete
	ListRedirectPermission   = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + permission.ActionList

	// Redirect operations
	TestRedirectPermission       = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + "test"
	ImportRedirectPermission     = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + "import"
	ExportRedirectPermission     = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + "export"
	BulkCreateRedirectPermission = ModuleName + permission.PermissionSeparator + "redirects" + permission.PermissionSeparator + "bulk_create"
)

// Robots and Crawling permissions
const (
	// Robots.txt management
	ReadRobotsPermission   = ModuleName + permission.PermissionSeparator + "robots" + permission.PermissionSeparator + permission.ActionRead
	UpdateRobotsPermission = ModuleName + permission.PermissionSeparator + "robots" + permission.PermissionSeparator + permission.ActionUpdate
	TestRobotsPermission   = ModuleName + permission.PermissionSeparator + "robots" + permission.PermissionSeparator + "test"

	// Crawling directives
	ManageCrawlingPermission = ModuleName + permission.PermissionSeparator + "crawling" + permission.PermissionSeparator + permission.ActionManage
	BlockCrawlingPermission  = ModuleName + permission.PermissionSeparator + "crawling" + permission.PermissionSeparator + "block"
	AllowCrawlingPermission  = ModuleName + permission.PermissionSeparator + "crawling" + permission.PermissionSeparator + "allow"
)

// SEO Analytics permissions
const (
	// Analytics and scoring
	ReadSeoAnalyticsPermission     = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + permission.ActionRead
	ReadSeoScorePermission         = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "seo_score"
	ReadReadabilityScorePermission = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "readability_score"
	ReadPerformancePermission      = ModuleName + permission.PermissionSeparator + "analytics" + permission.PermissionSeparator + "performance"

	// SEO auditing
	RunSeoAuditPermission      = ModuleName + permission.PermissionSeparator + "audit" + permission.PermissionSeparator + "run"
	ReadSeoAuditPermission     = ModuleName + permission.PermissionSeparator + "audit" + permission.PermissionSeparator + permission.ActionRead
	ScheduleSeoAuditPermission = ModuleName + permission.PermissionSeparator + "audit" + permission.PermissionSeparator + "schedule"
	ExportSeoAuditPermission   = ModuleName + permission.PermissionSeparator + "audit" + permission.PermissionSeparator + "export"
)

// SEO Tools permissions
const (
	// SEO optimization tools
	OptimizeContentPermission      = ModuleName + permission.PermissionSeparator + "tools" + permission.PermissionSeparator + "optimize_content"
	AnalyzeCompetitorPermission    = ModuleName + permission.PermissionSeparator + "tools" + permission.PermissionSeparator + "analyze_competitor"
	CheckBrokenLinksPermission     = ModuleName + permission.PermissionSeparator + "tools" + permission.PermissionSeparator + "check_broken_links"
	AnalyzePageSpeedPermission     = ModuleName + permission.PermissionSeparator + "tools" + permission.PermissionSeparator + "analyze_page_speed"
	CheckMobileUsabilityPermission = ModuleName + permission.PermissionSeparator + "tools" + permission.PermissionSeparator + "check_mobile_usability"

	// SEO monitoring
	MonitorRankingsPermission    = ModuleName + permission.PermissionSeparator + "monitoring" + permission.PermissionSeparator + "rankings"
	MonitorBacklinksPermission   = ModuleName + permission.PermissionSeparator + "monitoring" + permission.PermissionSeparator + "backlinks"
	MonitorIndexingPermission    = ModuleName + permission.PermissionSeparator + "monitoring" + permission.PermissionSeparator + "indexing"
	MonitorCrawlErrorsPermission = ModuleName + permission.PermissionSeparator + "monitoring" + permission.PermissionSeparator + "crawl_errors"
)

// SEO Configuration permissions
const (
	// Configuration management
	ReadSeoConfigPermission      = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + permission.ActionRead
	UpdateSeoConfigPermission    = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + permission.ActionUpdate
	ManageSeoRulesPermission     = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_rules"
	ManageSeoTemplatesPermission = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_templates"
	ManageSeoSettingsPermission  = ModuleName + permission.PermissionSeparator + "configuration" + permission.PermissionSeparator + "manage_settings"
)

// SEO Reporting permissions
const (
	// Reporting
	GenerateSeoReportPermission = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "generate_report"
	ExportSeoReportPermission   = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "export_report"
	ScheduleSeoReportPermission = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "schedule_report"
	ShareSeoReportPermission    = ModuleName + permission.PermissionSeparator + "reporting" + permission.PermissionSeparator + "share_report"
)

// Full management permission
const (
	ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// GetSeoMetaCRUDPermissions trả về tất cả CRUD permissions cho SEO meta
func GetSeoMetaCRUDPermissions() []string {
	return []string{
		CreateSeoMetaPermission,
		ReadSeoMetaPermission,
		UpdateSeoMetaPermission,
		DeleteSeoMetaPermission,
		ListSeoMetaPermission,
	}
}

// GetSeoMetaOperationPermissions trả về tất cả operation permissions cho SEO meta
func GetSeoMetaOperationPermissions() []string {
	return []string{
		UpsertSeoMetaPermission,
		BulkUpdateSeoMetaPermission,
		ImportSeoMetaPermission,
		ExportSeoMetaPermission,
	}
}

// GetKeywordPermissions trả về tất cả permissions cho keywords
func GetKeywordPermissions() []string {
	return []string{
		CreateKeywordPermission,
		ReadKeywordPermission,
		UpdateKeywordPermission,
		DeleteKeywordPermission,
		ListKeywordPermission,
		AnalyzeKeywordPermission,
		ResearchKeywordPermission,
		TrackKeywordPermission,
		OptimizeKeywordPermission,
	}
}

// GetSocialMediaPermissions trả về tất cả permissions cho social media meta
func GetSocialMediaPermissions() []string {
	return []string{
		CreateOpenGraphPermission,
		ReadOpenGraphPermission,
		UpdateOpenGraphPermission,
		DeleteOpenGraphPermission,
		CreateTwitterCardPermission,
		ReadTwitterCardPermission,
		UpdateTwitterCardPermission,
		DeleteTwitterCardPermission,
		PreviewSocialMediaPermission,
		TestSocialMediaPermission,
	}
}

// GetSchemaPermissions trả về tất cả permissions cho schema markup
func GetSchemaPermissions() []string {
	return []string{
		CreateSchemaPermission,
		ReadSchemaPermission,
		UpdateSchemaPermission,
		DeleteSchemaPermission,
		ListSchemaPermission,
		ValidateSchemaPermission,
		TestSchemaPermission,
		GenerateSchemaPermission,
	}
}

// GetSitemapPermissions trả về tất cả permissions cho sitemaps
func GetSitemapPermissions() []string {
	return []string{
		CreateSitemapPermission,
		ReadSitemapPermission,
		UpdateSitemapPermission,
		DeleteSitemapPermission,
		ListSitemapPermission,
		GenerateSitemapPermission,
		SubmitSitemapPermission,
		ValidateSitemapPermission,
		RefreshSitemapPermission,
	}
}

// GetRedirectPermissions trả về tất cả permissions cho redirects
func GetRedirectPermissions() []string {
	return []string{
		CreateRedirectPermission,
		ReadRedirectPermission,
		UpdateRedirectPermission,
		DeleteRedirectPermission,
		ListRedirectPermission,
		TestRedirectPermission,
		ImportRedirectPermission,
		ExportRedirectPermission,
		BulkCreateRedirectPermission,
	}
}

// GetRobotsCrawlingPermissions trả về tất cả permissions cho robots và crawling
func GetRobotsCrawlingPermissions() []string {
	return []string{
		ReadRobotsPermission,
		UpdateRobotsPermission,
		TestRobotsPermission,
		ManageCrawlingPermission,
		BlockCrawlingPermission,
		AllowCrawlingPermission,
	}
}

// GetSeoAnalyticsPermissions trả về tất cả permissions cho SEO analytics
func GetSeoAnalyticsPermissions() []string {
	return []string{
		ReadSeoAnalyticsPermission,
		ReadSeoScorePermission,
		ReadReadabilityScorePermission,
		ReadPerformancePermission,
		RunSeoAuditPermission,
		ReadSeoAuditPermission,
		ScheduleSeoAuditPermission,
		ExportSeoAuditPermission,
	}
}

// GetSeoToolsPermissions trả về tất cả permissions cho SEO tools
func GetSeoToolsPermissions() []string {
	return []string{
		OptimizeContentPermission,
		AnalyzeCompetitorPermission,
		CheckBrokenLinksPermission,
		AnalyzePageSpeedPermission,
		CheckMobileUsabilityPermission,
		MonitorRankingsPermission,
		MonitorBacklinksPermission,
		MonitorIndexingPermission,
		MonitorCrawlErrorsPermission,
	}
}

// GetSeoConfigurationPermissions trả về tất cả permissions cho SEO configuration
func GetSeoConfigurationPermissions() []string {
	return []string{
		ReadSeoConfigPermission,
		UpdateSeoConfigPermission,
		ManageSeoRulesPermission,
		ManageSeoTemplatesPermission,
		ManageSeoSettingsPermission,
	}
}

// GetSeoReportingPermissions trả về tất cả permissions cho SEO reporting
func GetSeoReportingPermissions() []string {
	return []string{
		GenerateSeoReportPermission,
		ExportSeoReportPermission,
		ScheduleSeoReportPermission,
		ShareSeoReportPermission,
	}
}

// GetAllPermissions trả về tất cả permissions của module
func GetAllPermissions() []string {
	permissions := []string{}
	permissions = append(permissions, GetSeoMetaCRUDPermissions()...)
	permissions = append(permissions, GetSeoMetaOperationPermissions()...)
	permissions = append(permissions, GetKeywordPermissions()...)
	permissions = append(permissions, GetSocialMediaPermissions()...)
	permissions = append(permissions, GetSchemaPermissions()...)
	permissions = append(permissions, GetSitemapPermissions()...)
	permissions = append(permissions, GetRedirectPermissions()...)
	permissions = append(permissions, GetRobotsCrawlingPermissions()...)
	permissions = append(permissions, GetSeoAnalyticsPermissions()...)
	permissions = append(permissions, GetSeoToolsPermissions()...)
	permissions = append(permissions, GetSeoConfigurationPermissions()...)
	permissions = append(permissions, GetSeoReportingPermissions()...)
	permissions = append(permissions, ManagePermission)
	return permissions
}
