package seo

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/seo/api"
	"wnapi/modules/seo/api/handlers"
	"wnapi/modules/seo/internal"
	mysqlRepo "wnapi/modules/seo/repository/mysql"
	"wnapi/modules/seo/service"
)

// NewSEOConfig creates seo configuration
func NewSEOConfig(cfg config.Config) (*internal.Config, error) {
	return internal.NewSeoConfigFromAppConfig(cfg)
}

// NewSeoMetaRepository creates seo meta repository
func NewSeoMetaRepository(db *gorm.DB) internal.Repository {
	return mysqlRepo.NewSeoMetaRepository(db)
}

// NewSeoMetaService creates seo meta service
func NewSeoMetaService(repo internal.Repository, log logger.Logger) internal.Service {
	return service.NewSeoMetaService(repo, log)
}

// NewSeoMetaHandler creates seo meta handler
func NewSeoMetaHandler(seoService internal.Service) *handlers.SeoMetaHandler {
	return handlers.NewSeoMetaHandler(seoService)
}

// NewSEOAPIHandler creates full SEO API handler
func NewSEOAPIHandler(seoMetaHandler *handlers.SeoMetaHandler, middlewareFactory *permission.MiddlewareFactory, jwtService *auth.JWTService) *api.Handler {
	return api.NewHandler(seoMetaHandler, middlewareFactory, jwtService)
}

// RegisterSEORoutes registers seo routes with gin.Engine using full API handler
func RegisterSEORoutes(handler *api.Handler, engine *gin.Engine, log logger.Logger) error {
	log.Info("Registering SEO routes with full API handler")

	// Use the RegisterRoutesWithEngine method that we just added
	if err := handler.RegisterRoutesWithEngine(engine); err != nil {
		log.Error("Failed to register SEO routes", "error", err)
		return err
	}

	log.Info("SEO routes registered successfully")
	return nil
}
