package seo

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
)

// SEOModule implements the FX module interface
type SEOModule struct{}

// Name returns the module name
func (m *SEOModule) Name() string {
	return "seo"
}

// Dependencies returns module dependencies
func (m *SEOModule) Dependencies() []string {
	return []string{"tenant", "auth", "rbac"}
}

// Priority returns module loading priority
func (m *SEOModule) Priority() int {
	return 90 // Load after core modules (tenant, auth, rbac)
}

// Enabled returns whether the module is enabled
func (m *SEOModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *SEOModule) GetMigrationPath() string {
	return "modules/seo/migrations" // Đã có migrations cho module SEO
}

// GetMigrationOrder returns migration priority order
func (m *SEOModule) GetMigrationOrder() int {
	return 90 // SEO module runs late (after tenant, auth, rbac)
}

// Module returns FX options for the module
func (m *SEOModule) Module() fx.Option {
	return fx.Module("seo",
		// Providers
		fx.Provide(
			// Configuration
			NewSEOConfig,

			// Repository
			NewSeoMetaRepository,

			// Handlers
			NewSeoMetaHandler,
			NewSEOAPIHandler,
		),

		// Route registration
		fx.Invoke(RegisterSEORoutes),
	)
}

// Register SEO module with global registry
func init() {
	modules.RegisterModule(&SEOModule{})
}
