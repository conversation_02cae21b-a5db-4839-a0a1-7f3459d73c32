package product

import (
	"wnapi/internal/pkg/config"
	"wnapi/modules/product/api"
	"wnapi/modules/product/internal"

	"github.com/gin-gonic/gin"
)

// NewProductConfig creates product configuration
func NewProductConfig(cfg config.Config) (*internal.ProductConfig, error) {
	return internal.NewProductConfigFromAppConfig(cfg)
}

// ProductHandler simple handler for product module
type ProductHandler struct {
	config *internal.ProductConfig
}

// NewProductHandler creates product handler
func NewProductHandler(config *internal.ProductConfig) *ProductHandler {
	return &ProductHandler{
		config: config,
	}
}

// RegisterProductRoutes registers product routes with gin.Engine
func RegisterProductRoutes(handler *api.Handler, engine *gin.Engine) error {
	// Register all product routes using the full API handler
	return handler.RegisterRoutes(engine)
}
