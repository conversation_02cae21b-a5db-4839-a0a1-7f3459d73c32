package site

import (
	"go.uber.org/fx"

	"wnapi/internal/fx/modules"
)

// SiteModule implements the FX module interface
type SiteModule struct{}

// Name returns the module name
func (m *SiteModule) Name() string {
	return "site"
}

// Dependencies returns module dependencies
func (m *SiteModule) Dependencies() []string {
	return []string{} // No dependencies
}

// Priority returns module loading priority
func (m *SiteModule) Priority() int {
	return 10 // Load early for migrations
}

// Enabled returns whether the module is enabled
func (m *SiteModule) Enabled(config map[string]interface{}) bool {
	return true // Always enabled for migrations
}

// GetMigrationPath returns path to module migrations
func (m *SiteModule) GetMigrationPath() string {
	return "modules/website/migrations/mysql"
}

// GetMigrationOrder returns migration priority order
func (m *SiteModule) GetMigrationOrder() int {
	return 10 // Run early
}

// Module returns FX options for the module
func (m *SiteModule) Module() fx.Option {
	return fx.Module("site") // No providers or invokes needed, just for migrations

}

func init() {
	modules.RegisterModule(&SiteModule{})
}
