# Blog Schedules Implementation

## Overview

The Blog Schedules feature allows automatic scheduling of blog post publications. This implementation provides a robust, scalable solution with retry mechanisms, queue integration, and comprehensive error handling.

## Features

- ✅ **Schedule Creation**: Create schedules for future blog post publication
- ✅ **Automatic Execution**: Queue-based processing with cron job integration
- ✅ **Retry Mechanism**: Configurable retry logic with exponential backoff
- ✅ **Multi-tenant Support**: Full tenant isolation and security
- ✅ **Status Tracking**: Comprehensive status tracking (pending, published, failed, cancelled)
- ✅ **Error Handling**: Detailed error messages and logging
- ✅ **API Endpoints**: RESTful API for schedule management
- ✅ **Pagination**: Efficient pagination for schedule listings
- ✅ **Cron Jobs**: Automated processing of pending schedules

## Database Schema

### blog_schedules Table

```sql
CREATE TABLE blog_schedules (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    blog_id INT UNSIGNED NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'published', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    error_message TEXT NULL,
    retry_count TINYINT UNSIGNED DEFAULT 0,
    max_retries TINYINT UNSIGNED DEFAULT 3,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NOT NULL,
    
    -- Foreign Keys
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (blog_id) REFERENCES blogs(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    
    -- Indexes
    INDEX idx_status_scheduled (status, scheduled_at),
    INDEX idx_blog_tenant (blog_id, tenant_id),
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_created_by (created_by),
    
    -- Unique constraint
    UNIQUE KEY unique_blog_schedule (blog_id, scheduled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Architecture

### Components

1. **Models** (`models/schedule.go`)
   - `BlogSchedule`: Main schedule entity
   - `BlogScheduleStatus`: Status enumeration
   - Helper methods for status management

2. **Repository** (`repository/mysql/schedule_repository.go`)
   - Database operations
   - CRUD operations with GORM
   - Efficient querying with indexes

3. **Service** (`service/schedule_service.go`)
   - Business logic implementation
   - Queue integration
   - Retry mechanism

4. **Worker** (`service/schedule_worker.go`)
   - Queue task handlers
   - Background processing

5. **API Handlers** (`api/handlers/schedule_handler.go`)
   - RESTful API endpoints
   - Request/response handling

6. **DTOs** (`dto/schedule_dto.go`)
   - Data transfer objects
   - Request/response structures

7. **Cron Jobs** (`cron/schedule_jobs.go`)
   - Automated schedule processing
   - Cleanup tasks

## API Endpoints

### Schedule Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/blog/schedules` | Create new schedule |
| GET | `/api/v1/blog/schedules` | List schedules with pagination |
| GET | `/api/v1/blog/schedules/{id}` | Get schedule by ID |
| POST | `/api/v1/blog/schedules/{id}/cancel` | Cancel pending schedule |
| POST | `/api/v1/blog/schedules/{id}/retry` | Retry failed schedule |

### Request Examples

#### Create Schedule

```bash
curl -X POST "http://localhost:8080/api/v1/blog/schedules" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "tenant_id": 1,
    "blog_id": 123,
    "scheduled_at": "2024-01-15T10:00:00Z",
    "max_retries": 3,
    "created_by": 1
  }'
```

#### List Schedules

```bash
curl -X GET "http://localhost:8080/api/v1/blog/schedules?tenant_id=1&status=pending&page=1&limit=20" \
  -H "Authorization: Bearer {token}"
```

#### Cancel Schedule

```bash
curl -X POST "http://localhost:8080/api/v1/blog/schedules/123/cancel?tenant_id=1" \
  -H "Authorization: Bearer {token}"
```

## Queue Integration

### Task Types

- `blog.schedule.execute`: Execute individual schedule
- `blog.schedule.process_pending`: Process all pending schedules
- `blog.schedule.cleanup`: Cleanup old schedules

### Queue Configuration

```go
// Queue options for schedule tasks
&queue.TaskOptions{
    Queue:    "blog_schedules",
    MaxRetry: 3,
    Timeout:  30 * time.Second,
}
```

## Cron Jobs

### Schedule Processing

- **Frequency**: Every 5 minutes
- **Purpose**: Process pending schedules that are due
- **Cron Expression**: `*/5 * * * *`

### Cleanup

- **Frequency**: Daily at 2 AM
- **Purpose**: Remove old completed schedules
- **Cron Expression**: `0 2 * * *`

## Usage Examples

### Service Usage

```go
// Create schedule service
scheduleService := service.NewScheduleService(
    scheduleRepo,
    postRepo,
    queueClient,
    logger,
)

// Create a schedule
schedule, err := scheduleService.CreateSchedule(
    ctx,
    tenantID,
    blogID,
    createdBy,
    scheduledAt,
    maxRetries,
)

// List schedules
schedules, total, err := scheduleService.ListSchedules(
    ctx,
    tenantID,
    &status,
    limit,
    offset,
)

// Cancel schedule
err := scheduleService.CancelSchedule(ctx, tenantID, scheduleID)
```

### Worker Registration

```go
// Register queue handlers
worker := service.NewScheduleWorker(scheduleService, logger)
err := worker.RegisterHandlers(queueManager)
```

### Cron Job Setup

```go
// Setup cron jobs
cronJobs := cron.NewScheduleCronJobs(scheduleService, queueClient, logger)
jobs := cronJobs.GetCronJobs()

// Register with cron scheduler
for _, job := range jobs {
    scheduler.AddJob(job.Name, job.Schedule, job.Handler)
}
```

## Error Handling

### Retry Logic

- **Automatic Retries**: Failed schedules are automatically retried
- **Exponential Backoff**: Retry delays increase with each attempt
- **Max Retries**: Configurable maximum retry attempts
- **Error Logging**: Detailed error messages stored in database

### Status Flow

```
pending → published (success)
pending → failed → retry → pending (if retries available)
pending → cancelled (manual cancellation)
failed → cancelled (max retries reached)
```

## Security

### Multi-tenant Isolation

- All operations require tenant ID
- Database queries filtered by tenant
- Foreign key constraints ensure data integrity

### Access Control

- User authentication required
- Creator tracking for audit purposes
- Tenant-based authorization

## Performance Considerations

### Database Optimization

- Composite indexes for efficient querying
- Unique constraints prevent duplicates
- Proper foreign key relationships

### Queue Processing

- Batch processing for efficiency
- Configurable concurrency levels
- Dead letter queue for failed tasks

### Caching

- Repository-level caching can be added
- Schedule status caching for dashboards
- Query result caching for lists

## Monitoring

### Metrics to Track

- Schedule creation rate
- Execution success rate
- Retry frequency
- Queue processing time
- Error rates by type

### Logging

- Structured logging with context
- Error tracking with stack traces
- Performance metrics
- Audit trail for all operations

## Testing

### Unit Tests

```go
// Test schedule creation
func TestCreateSchedule(t *testing.T) {
    // Setup mocks
    // Test creation logic
    // Verify results
}

// Test schedule execution
func TestExecuteSchedule(t *testing.T) {
    // Setup test data
    // Execute schedule
    // Verify post publication
}
```

### Integration Tests

- End-to-end API testing
- Queue processing verification
- Database transaction testing
- Multi-tenant isolation testing

## Deployment

### Migration

1. Run migration: `011_create_blog_schedules.up.sql`
2. Update application configuration
3. Deploy new code
4. Register queue handlers
5. Setup cron jobs

### Configuration

```env
# Blog Schedule Configuration
BLOG_SCHEDULE_ENABLED=true
BLOG_SCHEDULE_MAX_RETRIES=3
BLOG_SCHEDULE_QUEUE_NAME=blog_schedules
BLOG_SCHEDULE_CRON_ENABLED=true
```

## Future Enhancements

- [ ] Recurring schedules (daily, weekly, monthly)
- [ ] Bulk schedule operations
- [ ] Schedule templates
- [ ] Email notifications for failures
- [ ] Schedule analytics and reporting
- [ ] Time zone support
- [ ] Schedule dependencies
- [ ] A/B testing integration

This implementation provides a solid foundation for blog post scheduling with room for future enhancements and scalability.
