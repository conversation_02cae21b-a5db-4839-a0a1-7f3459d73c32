package examples

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
)

// BlogScheduleFlowExample demonstrates the complete blog scheduling flow
type BlogScheduleFlowExample struct {
	postService     service.PostService
	scheduleService service.ScheduleService
}

// NewBlogScheduleFlowExample creates a new example instance
func NewBlogScheduleFlowExample(postService service.PostService, scheduleService service.ScheduleService) *BlogScheduleFlowExample {
	return &BlogScheduleFlowExample{
		postService:     postService,
		scheduleService: scheduleService,
	}
}

// DemonstrateScheduleFlow shows how the blog scheduling works
func (e *BlogScheduleFlowExample) DemonstrateScheduleFlow(ctx context.Context, tenantID uint) error {
	fmt.Println("=== Blog Schedule Flow Demonstration ===")

	// 1. Create a blog post with future publish date
	futureDate := time.Now().Add(24 * time.Hour) // Schedule for tomorrow

	description := "This is a blog post scheduled for future publication"
	content := "This post will be automatically published tomorrow at the scheduled time."

	createReq := request.CreatePostRequest{
		Title:         "Scheduled Blog Post",
		Slug:          "scheduled-blog-post",
		Description:   &description,
		Content:       &content,
		Status:        "draft", // Start as draft
		Visibility:    "public",
		CommentStatus: "open",
		PublishedAt:   &futureDate, // This triggers schedule creation
		CategoryIDs:   []uint{1},
		TagIDs:        []uint{1, 2},
		AuthorID:      1,
		CreatedBy:     1,
	}

	fmt.Printf("1. Creating blog post scheduled for: %s\n", futureDate.Format("2006-01-02 15:04:05"))

	// Create the post (this will automatically create a schedule)
	postResp, err := e.postService.CreatePost(ctx, tenantID, 0, createReq)
	if err != nil {
		return fmt.Errorf("failed to create post: %w", err)
	}

	fmt.Printf("   ✓ Post created with ID: %d\n", postResp.PostID)
	fmt.Printf("   ✓ Post status: %s\n", postResp.Status)
	if postResp.PublishedAt != nil {
		fmt.Printf("   ✓ Scheduled for: %s\n", postResp.PublishedAt.Format("2006-01-02 15:04:05"))
	}

	// 2. List schedules to see the created schedule
	fmt.Println("\n2. Checking created schedules...")
	schedules, total, err := e.scheduleService.ListSchedules(ctx, tenantID, 0, nil, 10, 0)
	if err != nil {
		return fmt.Errorf("failed to list schedules: %w", err)
	}

	fmt.Printf("   ✓ Found %d total schedules\n", total)
	for _, schedule := range schedules {
		if schedule.BlogID == postResp.PostID {
			fmt.Printf("   ✓ Schedule ID: %d, Status: %s, Scheduled: %s\n",
				schedule.ID, schedule.Status, schedule.ScheduledAt.Format("2006-01-02 15:04:05"))
		}
	}

	// 3. Update the post with a different publish date
	newFutureDate := time.Now().Add(2 * time.Hour) // Schedule for 2 hours from now
	newTitle := "Updated Scheduled Blog Post"
	updateReq := request.UpdatePostRequest{
		Title:       &newTitle,
		PublishedAt: &newFutureDate,
	}

	fmt.Printf("\n3. Updating post with new schedule time: %s\n", newFutureDate.Format("2006-01-02 15:04:05"))

	updatedPost, err := e.postService.UpdatePost(ctx, tenantID, 0, postResp.PostID, updateReq)
	if err != nil {
		return fmt.Errorf("failed to update post: %w", err)
	}

	fmt.Printf("   ✓ Post updated, new title: %s\n", updatedPost.Title)
	if updatedPost.PublishedAt != nil {
		fmt.Printf("   ✓ New scheduled time: %s\n", updatedPost.PublishedAt.Format("2006-01-02 15:04:05"))
	}

	// 4. Demonstrate immediate publishing (past date)
	pastDate := time.Now().Add(-1 * time.Hour) // 1 hour ago
	immediateDesc := "This post should be published immediately"
	immediateContent := "This post has a past publish date, so no schedule should be created."

	immediateReq := request.CreatePostRequest{
		Title:         "Immediate Blog Post",
		Slug:          "immediate-blog-post",
		Description:   &immediateDesc,
		Content:       &immediateContent,
		Status:        "published",
		Visibility:    "public",
		CommentStatus: "open",
		PublishedAt:   &pastDate, // Past date - no schedule should be created
		CategoryIDs:   []uint{1},
		TagIDs:        []uint{1},
		AuthorID:      1,
		CreatedBy:     1,
	}

	fmt.Printf("\n4. Creating post with past publish date (immediate): %s\n", pastDate.Format("2006-01-02 15:04:05"))

	immediatePost, err := e.postService.CreatePost(ctx, tenantID, 0, immediateReq)
	if err != nil {
		return fmt.Errorf("failed to create immediate post: %w", err)
	}

	fmt.Printf("   ✓ Immediate post created with ID: %d\n", immediatePost.PostID)
	fmt.Printf("   ✓ Post status: %s (should be published)\n", immediatePost.Status)

	// 5. Show schedule execution simulation
	fmt.Println("\n5. Schedule execution flow:")
	fmt.Println("   • When the scheduled time arrives, the cron job will:")
	fmt.Println("     1. Find all pending schedules that are due")
	fmt.Println("     2. Execute each schedule by:")
	fmt.Println("        - Updating post status to 'published'")
	fmt.Println("        - Setting actual publish_at to current time")
	fmt.Println("        - Marking schedule as 'published'")
	fmt.Println("     3. Handle retries for failed executions")
	fmt.Println("     4. Log all activities for monitoring")

	fmt.Println("\n=== Blog Schedule Flow Demonstration Complete ===")
	return nil
}

// DemonstrateScheduleManagement shows schedule management operations
func (e *BlogScheduleFlowExample) DemonstrateScheduleManagement(ctx context.Context, tenantID uint) error {
	fmt.Println("\n=== Schedule Management Demonstration ===")

	// Create a post with future schedule
	futureDate := time.Now().Add(48 * time.Hour) // 2 days from now
	manageableDesc := "This post demonstrates schedule management"
	manageableContent := "This post will be used to demonstrate schedule cancellation and retry."

	createReq := request.CreatePostRequest{
		Title:         "Manageable Scheduled Post",
		Slug:          "manageable-scheduled-post",
		Description:   &manageableDesc,
		Content:       &manageableContent,
		Status:        "draft",
		Visibility:    "public",
		CommentStatus: "open",
		PublishedAt:   &futureDate,
		CategoryIDs:   []uint{1},
		AuthorID:      1,
		CreatedBy:     1,
	}

	postResp, err := e.postService.CreatePost(ctx, tenantID, 0, createReq)
	if err != nil {
		return fmt.Errorf("failed to create manageable post: %w", err)
	}

	fmt.Printf("1. Created post ID: %d with schedule\n", postResp.PostID)

	// Find the schedule for this post
	schedules, _, err := e.scheduleService.ListSchedules(ctx, tenantID, 0, nil, 100, 0)
	if err != nil {
		return fmt.Errorf("failed to list schedules: %w", err)
	}

	var scheduleID uint
	for _, schedule := range schedules {
		if schedule.BlogID == postResp.PostID {
			scheduleID = schedule.ID
			break
		}
	}

	if scheduleID == 0 {
		return fmt.Errorf("schedule not found for post %d", postResp.PostID)
	}

	fmt.Printf("2. Found schedule ID: %d\n", scheduleID)

	// Demonstrate schedule cancellation
	fmt.Println("3. Cancelling the schedule...")
	if err := e.scheduleService.CancelSchedule(ctx, tenantID, 0, scheduleID); err != nil {
		return fmt.Errorf("failed to cancel schedule: %w", err)
	}
	fmt.Println("   ✓ Schedule cancelled successfully")

	// Verify cancellation
	cancelledSchedule, err := e.scheduleService.GetSchedule(ctx, tenantID, 0, scheduleID)
	if err != nil {
		return fmt.Errorf("failed to get cancelled schedule: %w", err)
	}
	fmt.Printf("   ✓ Schedule status: %s\n", cancelledSchedule.Status)

	fmt.Println("\n=== Schedule Management Demonstration Complete ===")
	return nil
}
