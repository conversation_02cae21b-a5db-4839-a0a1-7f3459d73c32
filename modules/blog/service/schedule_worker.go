package service

import (
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
)

// ScheduleWorker handles queue tasks for blog schedules
type ScheduleWorker struct {
	scheduleService ScheduleService
	logger          logger.Logger
}

// NewScheduleWorker creates a new schedule worker
func NewScheduleWorker(scheduleService ScheduleService, logger logger.Logger) *ScheduleWorker {
	return &ScheduleWorker{
		scheduleService: scheduleService,
		logger:          logger,
	}
}

// RegisterHandlers registers queue handlers for blog schedules
func (w *ScheduleWorker) RegisterHandlers(queueManager *queue.QueueManager) error {
	handlers := []*queue.TaskDefinition{
		{
			Type:    "blog.schedule.execute",
			Handler: w.HandleExecuteSchedule,
			Options: &queue.TaskOptions{
				Queue:    "blog_schedules",
				MaxRetry: 3,
				Timeout:  30 * time.Second,
			},
		},
		{
			Type:    "blog.schedule.process_pending",
			Handler: w.HandleProcessPendingSchedules,
			Options: &queue.TaskOptions{
				Queue:    "blog_schedules",
				MaxRetry: 2,
				Timeout:  60 * time.Second,
			},
		},
		{
			Type:    "blog.schedule.cleanup",
			Handler: w.HandleCleanupOldSchedules,
			Options: &queue.TaskOptions{
				Queue:    "blog_schedules",
				MaxRetry: 1,
				Timeout:  120 * time.Second,
			},
		},
		{
			Type:    "blog.post.publish_scheduled",
			Handler: w.HandlePublishScheduledPosts,
			Options: &queue.TaskOptions{
				Queue:    "blog_posts",
				MaxRetry: 3,
				Timeout:  60 * time.Second,
			},
		},
	}

	for _, handler := range handlers {
		if err := queueManager.RegisterTask(handler); err != nil {
			return fmt.Errorf("failed to register handler %s: %w", handler.Type, err)
		}
	}

	w.logger.Info("Blog schedule handlers registered successfully")
	return nil
}

// HandleExecuteSchedule handles the execution of a specific schedule
func (w *ScheduleWorker) HandleExecuteSchedule(ctx *queue.TaskContext) error {
	w.logger.Info("Processing execute schedule task", "task_id", ctx.Task.ID)

	// Extract payload from map
	scheduleIDRaw, ok := ctx.Task.Payload["schedule_id"]
	if !ok {
		return fmt.Errorf("schedule_id not found in payload")
	}

	scheduleID, ok := scheduleIDRaw.(float64) // JSON numbers are float64
	if !ok {
		return fmt.Errorf("invalid schedule_id type")
	}

	tenantIDRaw, _ := ctx.Task.Payload["tenant_id"]
	tenantID, _ := tenantIDRaw.(float64)

	blogIDRaw, _ := ctx.Task.Payload["blog_id"]
	blogID, _ := blogIDRaw.(float64)

	if err := w.scheduleService.ExecuteSchedule(ctx.Context, uint(scheduleID)); err != nil {
		w.logger.Error("Failed to execute schedule",
			"schedule_id", uint(scheduleID),
			"tenant_id", uint(tenantID),
			"blog_id", uint(blogID),
			"error", err)
		return err
	}

	w.logger.Info("Schedule executed successfully",
		"schedule_id", uint(scheduleID),
		"tenant_id", uint(tenantID),
		"blog_id", uint(blogID))
	return nil
}

// HandleProcessPendingSchedules handles processing of all pending schedules
func (w *ScheduleWorker) HandleProcessPendingSchedules(ctx *queue.TaskContext) error {
	w.logger.Info("Processing pending schedules task", "task_id", ctx.Task.ID)

	if err := w.scheduleService.ProcessPendingSchedules(ctx.Context); err != nil {
		w.logger.Error("Failed to process pending schedules", "error", err)
		return err
	}

	w.logger.Info("Pending schedules processed successfully")
	return nil
}

// HandleCleanupOldSchedules handles cleanup of old completed schedules
func (w *ScheduleWorker) HandleCleanupOldSchedules(ctx *queue.TaskContext) error {
	w.logger.Info("Processing cleanup old schedules task", "task_id", ctx.Task.ID)

	retentionDays := 30 // default
	if retentionRaw, ok := ctx.Task.Payload["retention_days"]; ok {
		if retention, ok := retentionRaw.(float64); ok && retention > 0 {
			retentionDays = int(retention)
		}
	}

	batchSize := 100 // default batch size
	if batchSizeRaw, ok := ctx.Task.Payload["batch_size"]; ok {
		if batch, ok := batchSizeRaw.(float64); ok && batch > 0 {
			batchSize = int(batch)
		}
	}

	tenantID := uint(0) // default to all tenants
	if tenantIDRaw, ok := ctx.Task.Payload["tenant_id"]; ok {
		if tenant, ok := tenantIDRaw.(float64); ok && tenant > 0 {
			tenantID = uint(tenant)
		}
	}

	// Cleanup old schedules using schedule service
	totalDeleted, err := w.scheduleService.CleanupOldSchedules(ctx.Context, retentionDays, batchSize, tenantID)
	if err != nil {
		w.logger.Error("Failed to cleanup old schedules", "error", err)
		return err
	}

	w.logger.Info("Old schedules cleanup completed", 
		"retention_days", retentionDays, 
		"batch_size", batchSize, 
		"tenant_id", tenantID, 
		"total_deleted", totalDeleted)
	return nil
}

// HandlePublishScheduledPosts handles publishing of scheduled blog posts
func (w *ScheduleWorker) HandlePublishScheduledPosts(ctx *queue.TaskContext) error {
	w.logger.Info("Processing publish scheduled posts task", "task_id", ctx.Task.ID)

	batchSize := 10 // default
	if batchSizeRaw, ok := ctx.Task.Payload["batch_size"]; ok {
		if batch, ok := batchSizeRaw.(float64); ok && batch > 0 {
			batchSize = int(batch)
		}
	}

	// Publish scheduled posts using schedule service
	// This will:
	// 1. Find all blog posts that are scheduled to be published and due
	// 2. Update their status to published
	// 3. Update the schedule status to published
	publishedCount, err := w.scheduleService.PublishScheduledPosts(ctx.Context, batchSize)
	if err != nil {
		w.logger.Error("Failed to publish scheduled posts", "error", err)
		return err
	}

	w.logger.Info("Scheduled posts publishing completed", 
		"batch_size", batchSize, 
		"published_count", publishedCount)
	return nil
}
