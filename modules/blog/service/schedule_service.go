package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository/mysql"
)

// ScheduleService handles blog schedule operations
type ScheduleService interface {
	CreateSchedule(ctx context.Context, tenantID, websiteID, blogID, createdBy uint, scheduledAt time.Time, maxRetries uint8) (*models.BlogSchedule, error)
	GetSchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) (*models.BlogSchedule, error)
	ListSchedules(ctx context.Context, tenantID, websiteID uint, status *models.BlogScheduleStatus, limit, offset int) ([]*models.BlogSchedule, int64, error)
	CancelSchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) error
	RetrySchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) error
	ProcessPendingSchedules(ctx context.Context) error
	ExecuteSchedule(ctx context.Context, scheduleID uint) error
	CleanupOldSchedules(ctx context.Context, retentionDays int, batchSize int, tenantID uint) (int64, error)
	PublishScheduledPosts(ctx context.Context, batchSize int) (int, error)
}

type scheduleService struct {
	scheduleRepo *mysql.ScheduleRepository
	postRepo     *mysql.PostRepository
	queueClient  queue.QueueClient
	logger       logger.Logger
}

// NewScheduleService creates a new schedule service
func NewScheduleService(
	scheduleRepo *mysql.ScheduleRepository,
	postRepo *mysql.PostRepository,
	queueClient queue.QueueClient,
	logger logger.Logger,
) ScheduleService {
	return &scheduleService{
		scheduleRepo: scheduleRepo,
		postRepo:     postRepo,
		queueClient:  queueClient,
		logger:       logger,
	}
}

// CreateSchedule creates a new blog schedule
func (s *scheduleService) CreateSchedule(ctx context.Context, tenantID, websiteID, blogID, createdBy uint, scheduledAt time.Time, maxRetries uint8) (*models.BlogSchedule, error) {
	// Validate that the blog post exists
	_, err := s.postRepo.GetByID(ctx, tenantID, websiteID, blogID)
	if err != nil {
		return nil, fmt.Errorf("blog post not found: %w", err)
	}

	// Check if schedule already exists for this blog at this time
	existing, err := s.scheduleRepo.GetByBlogAndTime(ctx, tenantID, websiteID, blogID, scheduledAt)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("schedule already exists for this blog at the specified time")
	}

	// Create new schedule
	schedule := &models.BlogSchedule{
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		BlogID:      blogID,
		ScheduledAt: scheduledAt,
		Status:      models.ScheduleStatusPending,
		MaxRetries:  maxRetries,
		CreatedBy:   createdBy,
	}

	if err := s.scheduleRepo.Create(ctx, schedule); err != nil {
		return nil, fmt.Errorf("failed to create schedule: %w", err)
	}

	s.logger.Info("Blog schedule created", "schedule_id", schedule.ID, "blog_id", blogID, "scheduled_at", scheduledAt)

	// Enqueue the schedule task if it's in the future
	if scheduledAt.After(time.Now()) {
		if err := s.enqueueScheduleTask(ctx, schedule); err != nil {
			s.logger.Error("Failed to enqueue schedule task", "schedule_id", schedule.ID, "error", err)
			// Don't fail the creation, just log the error
		}
	}

	return schedule, nil
}

// GetSchedule retrieves a schedule by ID
func (s *scheduleService) GetSchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) (*models.BlogSchedule, error) {
	schedule, err := s.scheduleRepo.GetByID(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return nil, fmt.Errorf("schedule not found: %w", err)
	}

	if schedule.TenantID != tenantID {
		return nil, fmt.Errorf("schedule does not belong to tenant")
	}

	return schedule, nil
}

// ListSchedules retrieves schedules with pagination
func (s *scheduleService) ListSchedules(ctx context.Context, tenantID, websiteID uint, status *models.BlogScheduleStatus, limit, offset int) ([]*models.BlogSchedule, int64, error) {
	schedules, total, err := s.scheduleRepo.List(ctx, tenantID, websiteID, status, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list schedules: %w", err)
	}

	return schedules, total, nil
}

// CancelSchedule cancels a pending schedule
func (s *scheduleService) CancelSchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) error {
	schedule, err := s.GetSchedule(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return err
	}

	if schedule.Status != models.ScheduleStatusPending {
		return fmt.Errorf("can only cancel pending schedules")
	}

	schedule.MarkAsCancelled()
	if err := s.scheduleRepo.Update(ctx, schedule); err != nil {
		return fmt.Errorf("failed to cancel schedule: %w", err)
	}

	s.logger.Info("Blog schedule cancelled", "schedule_id", scheduleID)
	return nil
}

// RetrySchedule retries a failed schedule
func (s *scheduleService) RetrySchedule(ctx context.Context, tenantID, websiteID, scheduleID uint) error {
	schedule, err := s.GetSchedule(ctx, tenantID, websiteID, scheduleID)
	if err != nil {
		return err
	}

	if !schedule.IsRetryable() {
		return fmt.Errorf("schedule cannot be retried")
	}

	schedule.Status = models.ScheduleStatusPending
	schedule.ErrorMessage = nil

	if err := s.scheduleRepo.Update(ctx, schedule); err != nil {
		return fmt.Errorf("failed to retry schedule: %w", err)
	}

	// Enqueue the schedule task
	if err := s.enqueueScheduleTask(ctx, schedule); err != nil {
		return fmt.Errorf("failed to enqueue retry task: %w", err)
	}

	s.logger.Info("Blog schedule retried", "schedule_id", scheduleID)
	return nil
}

// ProcessPendingSchedules processes all pending schedules that are due
func (s *scheduleService) ProcessPendingSchedules(ctx context.Context) error {
	schedules, err := s.scheduleRepo.GetPendingSchedules(ctx, time.Now())
	if err != nil {
		return fmt.Errorf("failed to get pending schedules: %w", err)
	}

	s.logger.Info("Processing pending schedules", "count", len(schedules))

	for _, schedule := range schedules {
		if err := s.ExecuteSchedule(ctx, schedule.ID); err != nil {
			s.logger.Error("Failed to execute schedule", "schedule_id", schedule.ID, "error", err)
			// Continue processing other schedules
		}
	}

	return nil
}

// ExecuteSchedule executes a specific schedule
func (s *scheduleService) ExecuteSchedule(ctx context.Context, scheduleID uint) error {
	schedule, err := s.scheduleRepo.GetByIDInternal(ctx, scheduleID)
	if err != nil {
		return fmt.Errorf("schedule not found: %w", err)
	}

	if schedule.Status != models.ScheduleStatusPending {
		s.logger.Warn("Schedule is not pending", "schedule_id", scheduleID, "status", schedule.Status)
		return nil
	}

	// Get the blog post
	post, err := s.postRepo.GetByID(ctx, schedule.TenantID, schedule.WebsiteID, schedule.BlogID)
	if err != nil {
		errorMsg := fmt.Sprintf("blog post not found: %v", err)
		schedule.MarkAsFailed(errorMsg)
		s.scheduleRepo.Update(ctx, schedule)
		return fmt.Errorf(errorMsg)
	}

	// Publish the post
	if err := s.publishPost(ctx, post); err != nil {
		errorMsg := fmt.Sprintf("failed to publish post: %v", err)
		schedule.MarkAsFailed(errorMsg)
		s.scheduleRepo.Update(ctx, schedule)

		// Retry if possible
		if schedule.IsRetryable() {
			s.logger.Info("Scheduling retry for failed schedule", "schedule_id", scheduleID, "retry_count", schedule.RetryCount)
			if retryErr := s.enqueueScheduleTaskWithDelay(ctx, schedule, time.Minute*time.Duration(schedule.RetryCount)); retryErr != nil {
				s.logger.Error("Failed to enqueue retry task", "schedule_id", scheduleID, "error", retryErr)
			}
		}

		return fmt.Errorf(errorMsg)
	}

	// Mark as published
	schedule.MarkAsPublished()
	if err := s.scheduleRepo.Update(ctx, schedule); err != nil {
		s.logger.Error("Failed to update schedule status", "schedule_id", scheduleID, "error", err)
		// Don't return error as the post was published successfully
	}

	s.logger.Info("Blog schedule executed successfully", "schedule_id", scheduleID, "blog_id", schedule.BlogID)
	return nil
}

// publishPost publishes a blog post
func (s *scheduleService) publishPost(ctx context.Context, post *models.Post) error {
	// Update post status to published
	post.Status = "published"
	now := time.Now()
	post.PublishedAt = &now

	if err := s.postRepo.Update(ctx, post); err != nil {
		return fmt.Errorf("failed to update post: %w", err)
	}

	s.logger.Info("Blog post published", "post_id", post.PostID, "title", post.Title)
	return nil
}

// enqueueScheduleTask enqueues a schedule task to the queue
func (s *scheduleService) enqueueScheduleTask(ctx context.Context, schedule *models.BlogSchedule) error {
	if s.queueClient == nil {
		s.logger.Warn("Queue client not available, skipping task enqueue")
		return nil
	}

	payload := map[string]interface{}{
		"schedule_id": schedule.ID,
		"tenant_id":   schedule.TenantID,
		"blog_id":     schedule.BlogID,
	}

	delay := time.Until(schedule.ScheduledAt)
	if delay < 0 {
		delay = 0
	}

	_, err := s.queueClient.EnqueueTask(ctx, "blog.schedule.execute", payload, &queue.EnqueueOptions{
		Queue:    "blog_schedules",
		TenantID: schedule.TenantID,
	})

	return err
}

// enqueueScheduleTaskWithDelay enqueues a schedule task with a specific delay
func (s *scheduleService) enqueueScheduleTaskWithDelay(ctx context.Context, schedule *models.BlogSchedule, delay time.Duration) error {
	if s.queueClient == nil {
		s.logger.Warn("Queue client not available, skipping task enqueue")
		return nil
	}

	payload := map[string]interface{}{
		"schedule_id": schedule.ID,
		"tenant_id":   schedule.TenantID,
		"blog_id":     schedule.BlogID,
		"retry":       true,
	}

	_, err := s.queueClient.EnqueueTask(ctx, "blog.schedule.execute", payload, &queue.EnqueueOptions{
		Queue:    "blog_schedules",
		TenantID: schedule.TenantID,
	})

	return err
}

// CleanupOldSchedules removes old completed/cancelled schedules based on retention policy
func (s *scheduleService) CleanupOldSchedules(ctx context.Context, retentionDays int, batchSize int, tenantID uint) (int64, error) {
	s.logger.Info("Starting cleanup of old schedules",
		"retention_days", retentionDays,
		"batch_size", batchSize,
		"tenant_id", tenantID)

	totalDeleted := int64(0)
	for {
		deleted, err := s.scheduleRepo.CleanupOldSchedules(ctx, retentionDays, batchSize, tenantID)
		if err != nil {
			return totalDeleted, fmt.Errorf("failed to cleanup old schedules: %w", err)
		}

		totalDeleted += deleted
		s.logger.Info("Cleaned up schedules batch", "deleted", deleted, "total_deleted", totalDeleted)

		// If we deleted fewer than the batch size, we're done
		if deleted < int64(batchSize) {
			break
		}
	}

	s.logger.Info("Cleanup completed", "total_deleted", totalDeleted)
	return totalDeleted, nil
}

// PublishScheduledPosts finds and publishes posts that are scheduled to be published
func (s *scheduleService) PublishScheduledPosts(ctx context.Context, batchSize int) (int, error) {
	s.logger.Info("Starting publish scheduled posts", "batch_size", batchSize)

	// Get scheduled posts that are due for publication
	scheduledPosts, err := s.scheduleRepo.GetScheduledPosts(ctx, time.Now(), batchSize)
	if err != nil {
		return 0, fmt.Errorf("failed to get scheduled posts: %w", err)
	}

	if len(scheduledPosts) == 0 {
		s.logger.Info("No scheduled posts found for publication")
		return 0, nil
	}

	publishedCount := 0
	for _, schedule := range scheduledPosts {
		if schedule.Post == nil {
			s.logger.Warn("Schedule has no associated post", "schedule_id", schedule.ID)
			continue
		}

		// Check if post is in scheduled status
		if schedule.Post.Status != models.STATUS_SCHEDULE {
			s.logger.Warn("Post is not in scheduled status",
				"schedule_id", schedule.ID,
				"post_id", schedule.Post.PostID,
				"status", schedule.Post.Status)
			continue
		}

		// Publish the post
		if err := s.publishPost(ctx, schedule.Post); err != nil {
			s.logger.Error("Failed to publish scheduled post",
				"schedule_id", schedule.ID,
				"post_id", schedule.Post.PostID,
				"error", err)

			// Mark schedule as failed
			errorMsg := fmt.Sprintf("failed to publish post: %v", err)
			schedule.MarkAsFailed(errorMsg)
			if updateErr := s.scheduleRepo.Update(ctx, schedule); updateErr != nil {
				s.logger.Error("Failed to update schedule status",
					"schedule_id", schedule.ID,
					"error", updateErr)
			}
			continue
		}

		// Mark schedule as published
		schedule.MarkAsPublished()
		if err := s.scheduleRepo.Update(ctx, schedule); err != nil {
			s.logger.Error("Failed to update schedule status",
				"schedule_id", schedule.ID,
				"error", err)
			// Don't return error as the post was published successfully
		}

		publishedCount++
		s.logger.Info("Published scheduled post",
			"schedule_id", schedule.ID,
			"post_id", schedule.Post.PostID,
			"title", schedule.Post.Title)
	}

	s.logger.Info("Publish scheduled posts completed",
		"total_processed", len(scheduledPosts),
		"published_count", publishedCount)
	return publishedCount, nil
}
