package service

import (
	"context"
	"fmt"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogBlockService defines the interface for blog block service operations
type BlogBlockService interface {
	Create(ctx context.Context, tenantID, websiteID uint, req request.CreateBlogBlockRequest) (*response.BlogBlockResponse, error)
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*response.BlogBlockResponse, error)
	GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*response.BlogBlockResponse, error)
	Update(ctx context.Context, tenantID, websiteID, id uint, req request.UpdateBlogBlockRequest) (*response.BlogBlockResponse, error)
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogBlockRequest) (*response.BlogBlockListResponse, error)
	AddPost(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.AddPostToBlogBlockRequest) (*response.BlogBlockPostResponse, error)
	RemovePost(ctx context.Context, tenantID, websiteID, blogBlockID, postID uint) error
	UpdatePost(ctx context.Context, tenantID, websiteID, blogBlockID, postID uint, req request.UpdateBlogBlockPostRequest) (*response.BlogBlockPostResponse, error)
	GetPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.ListBlogBlockPostsRequest) (*response.BlogBlockPostListResponse, error)
	BatchAddPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.BatchAddPostsToBlogBlockRequest) error
	ReorderPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.ReorderBlogBlockPostsRequest) error
}

// blogBlockService implements the BlogBlockService interface
type blogBlockService struct {
	blogBlockRepo     repository.BlogBlockRepository
	blogBlockPostRepo repository.BlogBlockPostRepository
	postRepo          repository.PostRepository
}

// NewBlogBlockService creates a new instance of BlogBlockService
func NewBlogBlockService(
	blogBlockRepo repository.BlogBlockRepository,
	blogBlockPostRepo repository.BlogBlockPostRepository,
	postRepo repository.PostRepository,
) BlogBlockService {
	return &blogBlockService{
		blogBlockRepo:     blogBlockRepo,
		blogBlockPostRepo: blogBlockPostRepo,
		postRepo:          postRepo,
	}
}

// Create creates a new blog block
func (s *blogBlockService) Create(ctx context.Context, tenantID, websiteID uint, req request.CreateBlogBlockRequest) (*response.BlogBlockResponse, error) {
	// Check if code already exists
	existing, err := s.blogBlockRepo.GetByCode(ctx, tenantID, websiteID, req.Code)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("blog block with code '%s' already exists", req.Code)
	}

	// Create blog block model
	blogBlock := &models.BlogBlock{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Name:      req.Name,
		Code:      req.Code,
	}

	// Set active status
	if req.Active != nil {
		blogBlock.Active = *req.Active
	} else {
		blogBlock.Active = true // Default to active
	}

	// Create in database
	if err := s.blogBlockRepo.Create(ctx, blogBlock); err != nil {
		return nil, fmt.Errorf("failed to create blog block: %w", err)
	}

	// Convert to response
	return s.convertToResponse(blogBlock), nil
}

// GetByID retrieves a blog block by its ID
func (s *blogBlockService) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*response.BlogBlockResponse, error) {
	blogBlock, err := s.blogBlockRepo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block: %w", err)
	}

	return s.convertToResponse(blogBlock), nil
}

// GetByCode retrieves a blog block by its code
func (s *blogBlockService) GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*response.BlogBlockResponse, error) {
	blogBlock, err := s.blogBlockRepo.GetByCode(ctx, tenantID, websiteID, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block: %w", err)
	}

	return s.convertToResponse(blogBlock), nil
}

// Update updates an existing blog block
func (s *blogBlockService) Update(ctx context.Context, tenantID, websiteID, id uint, req request.UpdateBlogBlockRequest) (*response.BlogBlockResponse, error) {
	// Get existing blog block
	blogBlock, err := s.blogBlockRepo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block: %w", err)
	}

	// Update fields if provided
	if req.Name != nil {
		blogBlock.Name = *req.Name
	}
	if req.Code != nil {
		// Check if new code already exists (excluding current block)
		existing, err := s.blogBlockRepo.GetByCode(ctx, tenantID, websiteID, *req.Code)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("blog block with code '%s' already exists", *req.Code)
		}
		blogBlock.Code = *req.Code
	}
	if req.Active != nil {
		blogBlock.Active = *req.Active
	}

	// Update in database
	if err := s.blogBlockRepo.Update(ctx, blogBlock); err != nil {
		return nil, fmt.Errorf("failed to update blog block: %w", err)
	}

	return s.convertToResponse(blogBlock), nil
}

// Delete removes a blog block
func (s *blogBlockService) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	if err := s.blogBlockRepo.Delete(ctx, tenantID, websiteID, id); err != nil {
		return fmt.Errorf("failed to delete blog block: %w", err)
	}

	return nil
}

// List retrieves a paginated list of blog blocks
func (s *blogBlockService) List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogBlockRequest) (*response.BlogBlockListResponse, error) {
	blogBlocks, cursor, hasMore, err := s.blogBlockRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to list blog blocks: %w", err)
	}

	// Convert to response
	var responseData []response.BlogBlockResponse
	for _, blogBlock := range blogBlocks {
		responseData = append(responseData, *s.convertToResponse(blogBlock))
	}

	return &response.BlogBlockListResponse{
		Data: responseData,
		Meta: response.BlogBlockListMeta{
			NextCursor: cursor,
			HasMore:    hasMore,
		},
	}, nil
}

// AddPost adds a post to a blog block
func (s *blogBlockService) AddPost(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.AddPostToBlogBlockRequest) (*response.BlogBlockPostResponse, error) {
	// Check if blog block exists
	_, err := s.blogBlockRepo.GetByID(ctx, tenantID, websiteID, blogBlockID)
	if err != nil {
		return nil, fmt.Errorf("blog block not found: %w", err)
	}

	// Check if post exists
	_, err = s.postRepo.GetByID(ctx, tenantID, websiteID, req.PostID)
	if err != nil {
		return nil, fmt.Errorf("post not found: %w", err)
	}

	// Check if relationship already exists
	exists, err := s.blogBlockPostRepo.CheckExists(ctx, blogBlockID, req.PostID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing relationship: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("post is already in this blog block")
	}

	// Create blog block post
	blogBlockPost := &models.BlogBlockPost{
		BlogBlockID: blogBlockID,
		PostID:      req.PostID,
	}

	// Set priority
	if req.Priority != nil {
		blogBlockPost.Priority = *req.Priority
	} else {
		blogBlockPost.Priority = 0 // Default priority
	}

	// Create in database
	if err := s.blogBlockPostRepo.Create(ctx, blogBlockPost); err != nil {
		return nil, fmt.Errorf("failed to add post to blog block: %w", err)
	}

	return s.convertToPostResponse(ctx, blogBlockPost), nil
}

// RemovePost removes a post from a blog block
func (s *blogBlockService) RemovePost(ctx context.Context, tenantID, websiteID, blogBlockID, postID uint) error {
	if err := s.blogBlockPostRepo.DeleteByBlogBlockAndPost(ctx, blogBlockID, postID); err != nil {
		return fmt.Errorf("failed to remove post from blog block: %w", err)
	}

	return nil
}

// UpdatePost updates a post in a blog block
func (s *blogBlockService) UpdatePost(ctx context.Context, tenantID, websiteID, blogBlockID, postID uint, req request.UpdateBlogBlockPostRequest) (*response.BlogBlockPostResponse, error) {
	// Find the blog block post
	blogBlockPosts, err := s.blogBlockPostRepo.GetByBlogBlockID(ctx, blogBlockID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block posts: %w", err)
	}

	var targetPost *models.BlogBlockPost
	for _, post := range blogBlockPosts {
		if post.PostID == postID {
			targetPost = post
			break
		}
	}

	if targetPost == nil {
		return nil, fmt.Errorf("post not found in blog block")
	}

	// Update fields if provided
	if req.Priority != nil {
		targetPost.Priority = *req.Priority
	}

	// Update in database
	if err := s.blogBlockPostRepo.Update(ctx, targetPost); err != nil {
		return nil, fmt.Errorf("failed to update blog block post: %w", err)
	}

	return s.convertToPostResponse(ctx, targetPost), nil
}

// GetPosts retrieves posts in a blog block
func (s *blogBlockService) GetPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.ListBlogBlockPostsRequest) (*response.BlogBlockPostListResponse, error) {
	blogBlockPosts, cursor, hasMore, err := s.blogBlockPostRepo.List(ctx, blogBlockID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block posts: %w", err)
	}

	// Convert to response
	var responseData []response.BlogBlockPostResponse
	for _, blogBlockPost := range blogBlockPosts {
		responseData = append(responseData, *s.convertToPostResponse(ctx, blogBlockPost))
	}

	return &response.BlogBlockPostListResponse{
		Data: responseData,
		Meta: response.BlogBlockPostListMeta{
			NextCursor: cursor,
			HasMore:    hasMore,
		},
	}, nil
}

// BatchAddPosts adds multiple posts to a blog block
func (s *blogBlockService) BatchAddPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.BatchAddPostsToBlogBlockRequest) error {
	// Check if blog block exists
	_, err := s.blogBlockRepo.GetByID(ctx, tenantID, websiteID, blogBlockID)
	if err != nil {
		return fmt.Errorf("blog block not found: %w", err)
	}

	// Prepare blog block posts
	var blogBlockPosts []*models.BlogBlockPost
	for _, postReq := range req.Posts {
		// Check if post exists
		_, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postReq.PostID)
		if err != nil {
			return fmt.Errorf("post %d not found: %w", postReq.PostID, err)
		}

		// Check if relationship already exists
		exists, err := s.blogBlockPostRepo.CheckExists(ctx, blogBlockID, postReq.PostID)
		if err != nil {
			return fmt.Errorf("failed to check existing relationship for post %d: %w", postReq.PostID, err)
		}
		if exists {
			continue // Skip if already exists
		}

		blogBlockPost := &models.BlogBlockPost{
			BlogBlockID: blogBlockID,
			PostID:      postReq.PostID,
		}

		if postReq.Priority != nil {
			blogBlockPost.Priority = *postReq.Priority
		} else {
			blogBlockPost.Priority = 0
		}

		blogBlockPosts = append(blogBlockPosts, blogBlockPost)
	}

	// Batch create
	if len(blogBlockPosts) > 0 {
		if err := s.blogBlockPostRepo.BatchCreate(ctx, blogBlockPosts); err != nil {
			return fmt.Errorf("failed to batch add posts to blog block: %w", err)
		}
	}

	return nil
}

// ReorderPosts reorders posts in a blog block by updating their priorities
func (s *blogBlockService) ReorderPosts(ctx context.Context, tenantID, websiteID, blogBlockID uint, req request.ReorderBlogBlockPostsRequest) error {
	// Check if blog block exists
	_, err := s.blogBlockRepo.GetByID(ctx, tenantID, websiteID, blogBlockID)
	if err != nil {
		return fmt.Errorf("blog block not found: %w", err)
	}

	// Get existing blog block posts
	existingPosts, err := s.blogBlockPostRepo.GetByBlogBlockID(ctx, blogBlockID)
	if err != nil {
		return fmt.Errorf("failed to get existing blog block posts: %w", err)
	}

	// Create a map for quick lookup of existing posts
	existingPostsMap := make(map[uint]*models.BlogBlockPost)
	for _, post := range existingPosts {
		existingPostsMap[post.PostID] = post
	}

	// Validate all post IDs exist in the blog block
	for _, postOrder := range req.PostOrders {
		if _, exists := existingPostsMap[postOrder.PostID]; !exists {
			return fmt.Errorf("post ID %d not found in blog block", postOrder.PostID)
		}
	}

	// Update priorities for each post
	for _, postOrder := range req.PostOrders {
		blogBlockPost := existingPostsMap[postOrder.PostID]
		blogBlockPost.Priority = postOrder.Priority

		if err := s.blogBlockPostRepo.Update(ctx, blogBlockPost); err != nil {
			return fmt.Errorf("failed to update priority for post %d: %w", postOrder.PostID, err)
		}
	}

	return nil
}

// Helper methods

// convertToResponse converts a blog block model to response
func (s *blogBlockService) convertToResponse(blogBlock *models.BlogBlock) *response.BlogBlockResponse {
	return &response.BlogBlockResponse{
		ID:        blogBlock.ID,
		Name:      blogBlock.Name,
		Code:      blogBlock.Code,
		Active:    blogBlock.Active,
		CreatedAt: blogBlock.CreatedAt,
		UpdatedAt: blogBlock.UpdatedAt,
	}
}

// convertToPostResponse converts a blog block post model to response
func (s *blogBlockService) convertToPostResponse(ctx context.Context, blogBlockPost *models.BlogBlockPost) *response.BlogBlockPostResponse {
	resp := &response.BlogBlockPostResponse{
		ID:          blogBlockPost.ID,
		BlogBlockID: blogBlockPost.BlogBlockID,
		PostID:      blogBlockPost.PostID,
		Priority:    blogBlockPost.Priority,
		CreatedAt:   blogBlockPost.CreatedAt,
		UpdatedAt:   blogBlockPost.UpdatedAt,
	}

	// Add Post information if available
	if blogBlockPost.Post != nil {
		resp.Post = &response.PostResponse{
			PostID: blogBlockPost.Post.PostID,
			Title:  blogBlockPost.Post.Title,
			Status: blogBlockPost.Post.Status,
		}
	}

	return resp
}
