package service

import (
	"context"
	"fmt"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogTimelineService defines the interface for blog timeline service operations
type BlogTimelineService interface {
	Create(ctx context.Context, tenantID, websiteID uint, req request.CreateBlogTimelineRequest) (*response.BlogTimelineResponse, error)
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*response.BlogTimelineResponse, error)
	GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*response.BlogTimelineResponse, error)
	Update(ctx context.Context, tenantID, websiteID, id uint, req request.UpdateBlogTimelineRequest) (*response.BlogTimelineResponse, error)
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogTimelineRequest) (*response.BlogTimelineListResponse, error)
	AddPost(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.AddPostToBlogTimelineRequest) (*response.BlogTimelinePostResponse, error)
	RemovePost(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint) error
	UpdatePost(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint, req request.UpdateBlogTimelinePostRequest) (*response.BlogTimelinePostResponse, error)
	GetPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.ListBlogTimelinePostsRequest) (*response.BlogTimelinePostListResponse, error)
	BatchAddPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.BatchAddPostsToBlogTimelineRequest) error
	ReorderPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.ReorderBlogTimelinePostsRequest) error
}

// blogTimelineService implements the BlogTimelineService interface
type blogTimelineService struct {
	blogTimelineRepo     repository.BlogTimelineRepository
	blogTimelinePostRepo repository.BlogTimelinePostRepository
	postRepo             repository.PostRepository
}

// NewBlogTimelineService creates a new instance of BlogTimelineService
func NewBlogTimelineService(
	blogTimelineRepo repository.BlogTimelineRepository,
	blogTimelinePostRepo repository.BlogTimelinePostRepository,
	postRepo repository.PostRepository,
) BlogTimelineService {
	return &blogTimelineService{
		blogTimelineRepo:     blogTimelineRepo,
		blogTimelinePostRepo: blogTimelinePostRepo,
		postRepo:             postRepo,
	}
}

// Create creates a new blog timeline
func (s *blogTimelineService) Create(ctx context.Context, tenantID, websiteID uint, req request.CreateBlogTimelineRequest) (*response.BlogTimelineResponse, error) {
	// Check if code already exists
	existing, err := s.blogTimelineRepo.GetByCode(ctx, tenantID, websiteID, req.Code)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("blog timeline with code '%s' already exists", req.Code)
	}

	// Create blog timeline model
	blogTimeline := &models.BlogTimeline{
		TenantID:  tenantID,
		WebsiteID: websiteID,
		Name:      req.Name,
		Code:      req.Code,
	}

	// Set active status
	if req.Active != nil {
		blogTimeline.Active = *req.Active
	} else {
		blogTimeline.Active = true // Default to active
	}

	// Create in database
	if err := s.blogTimelineRepo.Create(ctx, blogTimeline); err != nil {
		return nil, fmt.Errorf("failed to create blog timeline: %w", err)
	}

	// Convert to response
	return s.convertToResponse(blogTimeline), nil
}

// GetByID retrieves a blog timeline by its ID
func (s *blogTimelineService) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*response.BlogTimelineResponse, error) {
	blogTimeline, err := s.blogTimelineRepo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline: %w", err)
	}

	return s.convertToResponse(blogTimeline), nil
}

// GetByCode retrieves a blog timeline by its code
func (s *blogTimelineService) GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*response.BlogTimelineResponse, error) {
	blogTimeline, err := s.blogTimelineRepo.GetByCode(ctx, tenantID, websiteID, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline: %w", err)
	}

	return s.convertToResponse(blogTimeline), nil
}

// Update updates an existing blog timeline
func (s *blogTimelineService) Update(ctx context.Context, tenantID, websiteID, id uint, req request.UpdateBlogTimelineRequest) (*response.BlogTimelineResponse, error) {
	// Get existing blog timeline
	blogTimeline, err := s.blogTimelineRepo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline: %w", err)
	}

	// Update fields if provided
	if req.Name != nil {
		blogTimeline.Name = *req.Name
	}
	if req.Code != nil {
		// Check if new code already exists (excluding current timeline)
		existing, err := s.blogTimelineRepo.GetByCode(ctx, tenantID, websiteID, *req.Code)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("blog timeline with code '%s' already exists", *req.Code)
		}
		blogTimeline.Code = *req.Code
	}
	if req.Active != nil {
		blogTimeline.Active = *req.Active
	}

	// Update in database
	if err := s.blogTimelineRepo.Update(ctx, blogTimeline); err != nil {
		return nil, fmt.Errorf("failed to update blog timeline: %w", err)
	}

	return s.convertToResponse(blogTimeline), nil
}

// Delete removes a blog timeline
func (s *blogTimelineService) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	if err := s.blogTimelineRepo.Delete(ctx, tenantID, websiteID, id); err != nil {
		return fmt.Errorf("failed to delete blog timeline: %w", err)
	}

	return nil
}

// List retrieves a paginated list of blog timelines
func (s *blogTimelineService) List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogTimelineRequest) (*response.BlogTimelineListResponse, error) {
	blogTimelines, cursor, hasMore, err := s.blogTimelineRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to list blog timelines: %w", err)
	}

	// Convert to response
	var responseData []response.BlogTimelineResponse
	for _, blogTimeline := range blogTimelines {
		responseData = append(responseData, *s.convertToResponse(blogTimeline))
	}

	return &response.BlogTimelineListResponse{
		Data: responseData,
		Meta: response.BlogTimelineListMeta{
			NextCursor: cursor,
			HasMore:    hasMore,
		},
	}, nil
}

// AddPost adds a post to a blog timeline
func (s *blogTimelineService) AddPost(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.AddPostToBlogTimelineRequest) (*response.BlogTimelinePostResponse, error) {
	// Check if blog timeline exists
	_, err := s.blogTimelineRepo.GetByID(ctx, tenantID, websiteID, blogTimelineID)
	if err != nil {
		return nil, fmt.Errorf("blog timeline not found: %w", err)
	}

	// Check if post exists
	_, err = s.postRepo.GetByID(ctx, tenantID, websiteID, req.PostID)
	if err != nil {
		return nil, fmt.Errorf("post not found: %w", err)
	}

	// Check if relationship already exists
	exists, err := s.blogTimelinePostRepo.CheckExists(ctx, tenantID, websiteID, blogTimelineID, req.PostID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing relationship: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("post is already in this blog timeline")
	}

	// Create blog timeline post
	blogTimelinePost := &models.BlogTimelinePost{
		BlogTimelineID: blogTimelineID,
		PostID:         req.PostID,
	}

	// Set priority
	if req.Priority != nil {
		blogTimelinePost.Priority = *req.Priority
	} else {
		blogTimelinePost.Priority = 0 // Default priority
	}

	// Create in database
	if err := s.blogTimelinePostRepo.Create(ctx, blogTimelinePost); err != nil {
		return nil, fmt.Errorf("failed to add post to blog timeline: %w", err)
	}

	// Convert to response
	return s.convertToPostResponse(ctx, blogTimelinePost), nil
}

// RemovePost removes a post from a blog timeline
func (s *blogTimelineService) RemovePost(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint) error {
	if err := s.blogTimelinePostRepo.DeleteByBlogTimelineAndPost(ctx, blogTimelineID, postID); err != nil {
		return fmt.Errorf("failed to remove post from blog timeline: %w", err)
	}

	return nil
}

// UpdatePost updates a post in a blog timeline
func (s *blogTimelineService) UpdatePost(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint, req request.UpdateBlogTimelinePostRequest) (*response.BlogTimelinePostResponse, error) {
	// Get existing blog timeline posts to find the specific one
	blogTimelinePosts, err := s.blogTimelinePostRepo.GetByBlogTimelineID(ctx, blogTimelineID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline posts: %w", err)
	}

	// Find the specific post
	var targetPost *models.BlogTimelinePost
	for _, post := range blogTimelinePosts {
		if post.PostID == postID {
			targetPost = post
			break
		}
	}

	if targetPost == nil {
		return nil, fmt.Errorf("post not found in blog timeline")
	}

	// Update fields if provided
	if req.Priority != nil {
		targetPost.Priority = *req.Priority
	}

	// Update in database
	if err := s.blogTimelinePostRepo.Update(ctx, targetPost); err != nil {
		return nil, fmt.Errorf("failed to update blog timeline post: %w", err)
	}

	// Convert to response
	return s.convertToPostResponse(ctx, targetPost), nil
}

// GetPosts retrieves posts in a blog timeline
func (s *blogTimelineService) GetPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.ListBlogTimelinePostsRequest) (*response.BlogTimelinePostListResponse, error) {
	blogTimelinePosts, cursor, hasMore, err := s.blogTimelinePostRepo.List(ctx, blogTimelineID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline posts: %w", err)
	}

	// Convert to response
	var responseData []response.BlogTimelinePostResponse
	for _, blogTimelinePost := range blogTimelinePosts {
		responseData = append(responseData, *s.convertToPostResponse(ctx, blogTimelinePost))
	}

	return &response.BlogTimelinePostListResponse{
		Data: responseData,
		Meta: response.BlogTimelinePostListMeta{
			NextCursor: cursor,
			HasMore:    hasMore,
		},
	}, nil
}

// BatchAddPosts adds multiple posts to a blog timeline
func (s *blogTimelineService) BatchAddPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.BatchAddPostsToBlogTimelineRequest) error {
	// Check if blog timeline exists
	_, err := s.blogTimelineRepo.GetByID(ctx, tenantID, websiteID, blogTimelineID)
	if err != nil {
		return fmt.Errorf("blog timeline not found: %w", err)
	}

	// Prepare blog timeline posts
	var blogTimelinePosts []*models.BlogTimelinePost
	for _, postReq := range req.Posts {
		// Check if post exists
		_, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postReq.PostID)
		if err != nil {
			return fmt.Errorf("post %d not found: %w", postReq.PostID, err)
		}

		// Check if relationship already exists
		exists, err := s.blogTimelinePostRepo.CheckExists(ctx, tenantID, websiteID, blogTimelineID, postReq.PostID)
		if err != nil {
			return fmt.Errorf("failed to check existing relationship for post %d: %w", postReq.PostID, err)
		}
		if exists {
			continue // Skip if already exists
		}

		blogTimelinePost := &models.BlogTimelinePost{
			BlogTimelineID: blogTimelineID,
			PostID:         postReq.PostID,
		}

		if postReq.Priority != nil {
			blogTimelinePost.Priority = *postReq.Priority
		} else {
			blogTimelinePost.Priority = 0
		}

		blogTimelinePosts = append(blogTimelinePosts, blogTimelinePost)
	}

	// Batch create
	if len(blogTimelinePosts) > 0 {
		if err := s.blogTimelinePostRepo.BatchCreate(ctx, blogTimelinePosts); err != nil {
			return fmt.Errorf("failed to batch add posts to blog timeline: %w", err)
		}
	}

	return nil
}

// ReorderPosts reorders posts in a blog timeline by updating their priorities
func (s *blogTimelineService) ReorderPosts(ctx context.Context, tenantID, websiteID, blogTimelineID uint, req request.ReorderBlogTimelinePostsRequest) error {
	// Check if blog timeline exists
	_, err := s.blogTimelineRepo.GetByID(ctx, tenantID, websiteID, blogTimelineID)
	if err != nil {
		return fmt.Errorf("blog timeline not found: %w", err)
	}

	// Get existing blog timeline posts
	existingPosts, err := s.blogTimelinePostRepo.GetByBlogTimelineID(ctx, blogTimelineID)
	if err != nil {
		return fmt.Errorf("failed to get existing blog timeline posts: %w", err)
	}

	// Create a map for quick lookup of existing posts
	existingPostsMap := make(map[uint]*models.BlogTimelinePost)
	for _, post := range existingPosts {
		existingPostsMap[post.PostID] = post
	}

	// Validate all post IDs exist in the blog timeline
	for _, postOrder := range req.PostOrders {
		if _, exists := existingPostsMap[postOrder.PostID]; !exists {
			return fmt.Errorf("post ID %d not found in blog timeline", postOrder.PostID)
		}
	}

	// Update priorities for each post
	for _, postOrder := range req.PostOrders {
		blogTimelinePost := existingPostsMap[postOrder.PostID]
		blogTimelinePost.Priority = postOrder.Priority

		if err := s.blogTimelinePostRepo.Update(ctx, blogTimelinePost); err != nil {
			return fmt.Errorf("failed to update priority for post %d: %w", postOrder.PostID, err)
		}
	}

	return nil
}

// Helper methods

// convertToResponse converts a blog timeline model to response
func (s *blogTimelineService) convertToResponse(blogTimeline *models.BlogTimeline) *response.BlogTimelineResponse {
	return &response.BlogTimelineResponse{
		ID:        blogTimeline.ID,
		Name:      blogTimeline.Name,
		Code:      blogTimeline.Code,
		Active:    blogTimeline.Active,
		CreatedAt: blogTimeline.CreatedAt,
		UpdatedAt: blogTimeline.UpdatedAt,
	}
}

// convertToPostResponse converts a blog timeline post model to response
func (s *blogTimelineService) convertToPostResponse(ctx context.Context, blogTimelinePost *models.BlogTimelinePost) *response.BlogTimelinePostResponse {
	resp := &response.BlogTimelinePostResponse{
		ID:             blogTimelinePost.ID,
		BlogTimelineID: blogTimelinePost.BlogTimelineID,
		PostID:         blogTimelinePost.PostID,
		Priority:       blogTimelinePost.Priority,
		CreatedAt:      blogTimelinePost.CreatedAt,
		UpdatedAt:      blogTimelinePost.UpdatedAt,
	}

	// Add Post information if available
	if blogTimelinePost.Post != nil {
		resp.Post = &response.PostResponse{
			PostID: blogTimelinePost.Post.PostID,
			Title:  blogTimelinePost.Post.Title,
			Status: blogTimelinePost.Post.Status,
		}
	}

	return resp
}
