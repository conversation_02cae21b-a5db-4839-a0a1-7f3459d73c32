package service

import (
	"context"
	"testing"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockCategoryRepository is a mock implementation of CategoryRepository
type MockCategoryRepository struct {
	mock.Mock
}

// Implement the repository.CategoryRepository interface
func (m *MockCategoryRepository) Create(ctx context.Context, category *models.Category) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

func (m *MockCategoryRepository) GetByID(ctx context.Context, tenantID, websiteID uint, categoryID uint) (*models.Category, error) {
	args := m.Called(ctx, tenantID, websiteID, categoryID)
	return args.Get(0).(*models.Category), args.Error(1)
}

func (m *MockCategoryRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.Category, error) {
	args := m.Called(ctx, tenantID, websiteID, slug)
	return args.Get(0).(*models.Category), args.Error(1)
}

func (m *MockCategoryRepository) Update(ctx context.Context, category *models.Category) error {
	args := m.Called(ctx, category)
	return args.Error(0)
}

func (m *MockCategoryRepository) Delete(ctx context.Context, tenantID, websiteID uint, categoryID uint) error {
	args := m.Called(ctx, tenantID, websiteID, categoryID)
	return args.Error(0)
}

func (m *MockCategoryRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListCategoryRequest) ([]*models.Category, string, bool, error) {
	args := m.Called(ctx, tenantID, websiteID, req)
	return args.Get(0).([]*models.Category), args.String(1), args.Bool(2), args.Error(3)
}

func (m *MockCategoryRepository) GetTree(ctx context.Context, tenantID, websiteID uint) ([]*models.Category, error) {
	args := m.Called(ctx, tenantID, websiteID)
	return args.Get(0).([]*models.Category), args.Error(1)
}

func (m *MockCategoryRepository) GetSubtree(ctx context.Context, tenantID, websiteID uint, categoryID uint) ([]*models.Category, error) {
	args := m.Called(ctx, tenantID, websiteID, categoryID)
	return args.Get(0).([]*models.Category), args.Error(1)
}

func (m *MockCategoryRepository) MoveNode(ctx context.Context, tenantID, websiteID uint, nodeID uint, targetID uint, position string) error {
	args := m.Called(ctx, tenantID, websiteID, nodeID, targetID, position)
	return args.Error(0)
}

func (m *MockCategoryRepository) MoveNodeToRoot(ctx context.Context, tenantID, websiteID uint, nodeID uint, position int) error {
	args := m.Called(ctx, tenantID, websiteID, nodeID, position)
	return args.Error(0)
}

func (m *MockCategoryRepository) UpdatePosition(ctx context.Context, tenantID, websiteID uint, categoryID uint, position int) error {
	args := m.Called(ctx, tenantID, websiteID, categoryID, position)
	return args.Error(0)
}



func TestCategoryService_MoveCategory(t *testing.T) {
	// Arrange
	mockRepo := new(MockCategoryRepository)
	service := &categoryService{
		categoryRepo: mockRepo,
	}

	ctx := context.Background()
	tenantID := uint(1)
	websiteID := uint(1)
	req := request.MoveCategoryRequest{
		CategoryID: 1,
		TargetID:   2,
		Position:   "child",
	}

	// Setup mock expectation for MoveNode
	mockRepo.On("MoveNode", ctx, tenantID, websiteID, req.CategoryID, req.TargetID, req.Position).Return(nil)

	// Act
	err := service.MoveCategory(ctx, tenantID, websiteID, req)

	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
}