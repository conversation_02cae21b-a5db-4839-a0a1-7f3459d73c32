package service

import (
	"context"

	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/repository"
)

// StatisticService defines the interface for statistic business logic
type StatisticService interface {
	GetStatusStatistics(ctx context.Context, tenantID, websiteID uint) (*response.StatisticStatusResponse, error)
}

// statisticService implements the StatisticService interface
type statisticService struct {
	statisticRepo repository.StatisticRepository
}

// NewStatisticService creates a new statistic service instance
func NewStatisticService(statisticRepo repository.StatisticRepository) StatisticService {
	return &statisticService{
		statisticRepo: statisticRepo,
	}
}

// GetStatusStatistics retrieves post count statistics by status
func (s *statisticService) GetStatusStatistics(ctx context.Context, tenantID, websiteID uint) (*response.StatisticStatusResponse, error) {
	// Get statistics from repository
	stats, err := s.statisticRepo.GetStatusStatistics(ctx, tenantID, websiteID)
	if err != nil {
		return nil, err
	}

	// Convert to response format
	statusCounts := &response.StatusStatisticResponse{
		Draft:     stats["draft"],
		Pending:   stats["pending"],
		Approved:  stats["approved"],
		Schedule:  stats["schedule"],
		Published: stats["published"],
		Return:    stats["return"],
		Trash:     stats["trash"],
		Storage:   stats["storage"],
		Request:   stats["request"],
		Auto:      stats["auto"],
		Delete:    stats["delete"],
		Total:     stats["total"],
	}

	return &response.StatisticStatusResponse{
		StatusCounts: statusCounts,
	}, nil
}
