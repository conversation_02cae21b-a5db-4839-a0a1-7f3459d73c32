CREATE TABLE blog_schedules (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    blog_id INT UNSIGNED NOT NULL,
    scheduled_at TIMESTAMP NOT NULL, -- Đ<PERSON><PERSON> tên và NOT NULL
    status ENUM('pending', 'published', 'failed', 'cancelled') NOT NULL DEFAULT 'pending', -- Thêm 'failed'
    error_message TEXT NULL, -- Thêm để lưu lỗi nếu có
    retry_count TINYINT UNSIGNED DEFAULT 0, -- Thêm retry logic
    max_retries TINYINT UNSIGNED DEFAULT 3, -- <PERSON><PERSON><PERSON><PERSON> hạn retry
    published_at TIMESTAMP NULL, -- Timestamp thực tế khi published
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NOT NULL,
    

    -- Indexes for performance
    INDEX idx_status_scheduled (status, scheduled_at), -- <PERSON> cron job tìm pending schedules
    INDEX idx_blog_tenant (blog_id, tenant_id),        -- Cho multi-tenant queries
    INDEX idx_tenant_status (tenant_id, status),       -- Cho tenant dashboard
    INDEX idx_created_by (created_by),                 -- Cho user history
    INDEX idx_website_tenant (website_id, tenant_id),  -- Cho website-tenant queries
    
    -- Unique constraint để tránh duplicate schedule
    UNIQUE KEY unique_blog_schedule (blog_id, scheduled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
