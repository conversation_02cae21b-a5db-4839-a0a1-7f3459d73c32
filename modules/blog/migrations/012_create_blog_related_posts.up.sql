-- Migration: 012_create_blog_related_posts
-- Module: blog
-- Created: 2025-06-11 13:53:22

-- Create table for blog related posts (many-to-many relationship)
CREATE TABLE IF NOT EXISTS blog_related_posts (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    post_id INT UNSIGNED NOT NULL,
    related_post_id INT UNSIGNED NOT NULL,
    priority INT UNSIGNED DEFAULT 0,
    is_bidirectional BOOLEAN DEFAULT TRUE COMMENT 'Whether relation works both ways',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NOT NULL,

    UNIQUE KEY unique_blog_related_posts (tenant_id, post_id, related_post_id),
    INDEX idx_blog_related_posts_tenant_post (tenant_id, post_id),
    INDEX idx_blog_related_posts_tenant_related (tenant_id, related_post_id),
    INDEX idx_blog_related_posts_priority (priority),
    INDEX idx_blog_related_posts_created_at (created_at)
) ENGINE=InnoDB
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
ROW_FORMAT=DYNAMIC
COMMENT='Table for managing related posts relationships';
