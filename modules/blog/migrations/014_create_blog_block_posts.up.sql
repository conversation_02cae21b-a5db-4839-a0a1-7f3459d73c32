CREATE TABLE blog_block_posts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    blog_block_id BIGINT UNSIGNED NOT NULL,
    post_id BIGINT UNSIGNED NOT NULL,
    priority INT NOT NULL DEFAULT 0,
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_block_post (blog_block_id, post_id),
    INDEX idx_blog_block_posts_block_id (blog_block_id),
    INDEX idx_blog_block_posts_post_id (post_id),
    INDEX idx_blog_block_posts_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;