CREATE TABLE blog_timelines (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    code VARCHAR(100) NOT NULL,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_blog_timelines_code_tenant_website (tenant_id, website_id, code),
    INDEX idx_blog_timelines_code (code),
    INDEX idx_blog_timelines_active (active),
    INDEX idx_blog_timelines_website_tenant (website_id, tenant_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;