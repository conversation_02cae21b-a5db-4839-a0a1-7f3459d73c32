CREATE TABLE blog_timeline_posts (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    blog_timeline_id BIGINT UNSIGNED NOT NULL,
    post_id BIGINT UNSIGNED NOT NULL,
    priority INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_timeline_post (blog_timeline_id, post_id),
    INDEX idx_blog_timeline_posts_timeline_id (blog_timeline_id),
    INDEX idx_blog_timeline_posts_post_id (post_id),
    INDEX idx_blog_timeline_posts_priority (priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;