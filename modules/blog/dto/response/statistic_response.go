package response

// StatusStatisticResponse represents the response for status statistics
type StatusStatisticResponse struct {
	Draft     int64 `json:"draft"`
	Pending   int64 `json:"pending"`
	Approved  int64 `json:"approved"`
	Schedule  int64 `json:"schedule"`
	Published int64 `json:"published"`
	Return    int64 `json:"return"`
	Trash     int64 `json:"trash"`
	Storage   int64 `json:"storage"`
	Request   int64 `json:"request"`
	Auto      int64 `json:"auto"`
	Delete    int64 `json:"delete"`
	Total     int64 `json:"total"`
}

// StatisticStatusResponse represents the main response structure for statistic status endpoint
type StatisticStatusResponse struct {
	StatusCounts *StatusStatisticResponse `json:"status_counts"`
}
