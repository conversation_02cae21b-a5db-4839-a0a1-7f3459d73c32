package response

import "time"

// BlogBlockResponse represents a blog block in the response
type BlogBlockResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Code      string    `json:"code"`
	Active    bool      `json:"active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Posts     []BlogBlockPostResponse `json:"posts,omitempty"`
}

// BlogBlockPostResponse represents a blog block post in the response
type BlogBlockPostResponse struct {
	ID          uint         `json:"id"`
	BlogBlockID uint         `json:"blog_block_id"`
	PostID      uint         `json:"post_id"`
	Priority    int          `json:"priority"`
	CreatedAt   time.Time    `json:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at"`
	Post        *PostResponse `json:"post,omitempty"`
}

// BlogBlockListResponse represents the response for listing blog blocks
type BlogBlockListResponse struct {
	Data []BlogBlockResponse `json:"data"`
	Meta BlogBlockListMeta   `json:"meta"`
}

// BlogBlockListMeta represents pagination metadata for blog block lists
type BlogBlockListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// BlogBlockPostListResponse represents the response for listing blog block posts
type BlogBlockPostListResponse struct {
	Data []BlogBlockPostResponse `json:"data"`
	Meta BlogBlockPostListMeta   `json:"meta"`
}

// BlogBlockPostListMeta represents pagination metadata for blog block post lists
type BlogBlockPostListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}