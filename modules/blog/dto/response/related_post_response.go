package response

import "time"

// RelatedPostResponse represents the response for a related post relationship
type RelatedPostResponse struct {
	ID              uint                 `json:"id"`
	TenantID        uint                 `json:"tenant_id"`
	PostID          uint                 `json:"post_id"`
	RelatedPostID   uint                 `json:"related_post_id"`
	Priority        uint                 `json:"priority"`
	IsBidirectional bool                 `json:"is_bidirectional"`
	CreatedAt       time.Time            `json:"created_at"`
	UpdatedAt       time.Time            `json:"updated_at"`
	CreatedBy       uint                 `json:"created_by"`
	Post            *PostSummaryResponse `json:"post,omitempty"`
	RelatedPost     *PostSummaryResponse `json:"related_post,omitempty"`
	Creator         *UserInfo            `json:"creator,omitempty"`
}

// PostSummaryResponse represents a summary of a post for related post responses
type PostSummaryResponse struct {
	PostID      uint       `json:"post_id"`
	Title       string     `json:"title"`
	Slug        string     `json:"slug"`
	Description *string    `json:"description,omitempty"`
	Image       *string    `json:"image,omitempty"`
	Status      string     `json:"status"`
	Visibility  string     `json:"visibility"`
	PublishedAt *time.Time `json:"published_at,omitempty"`
	CreatedAt   time.Time  `json:"created_at"`
	AuthorID    uint       `json:"author_id"`
}

// RelatedPostListResponse represents the response for listing related posts
type RelatedPostListResponse struct {
	Data []RelatedPostResponse `json:"data"`
	Meta RelatedPostListMeta   `json:"meta"`
}

// RelatedPostListMeta represents metadata for related post list response
type RelatedPostListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
	Total      int    `json:"total,omitempty"`
}

// PostRelatedPostsResponse represents the response for getting all related posts of a specific post
type PostRelatedPostsResponse struct {
	Data []PostSummaryResponse       `json:"data"`
	Meta PostRelatedPostsCursorMeta `json:"meta"`
}

// PostRelatedPostsCursorMeta represents cursor-based metadata for post related posts response
type PostRelatedPostsCursorMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
	Total      int    `json:"total,omitempty"`
}

// RelatedPostItemResponse represents a single related post item
type RelatedPostItemResponse struct {
	RelationID      uint                 `json:"relation_id"`
	RelatedPost     *PostSummaryResponse `json:"related_post"`
	Priority        uint                 `json:"priority"`
	IsBidirectional bool                 `json:"is_bidirectional"`
	CreatedAt       time.Time            `json:"created_at"`
}

// PostRelatedPostsMeta represents metadata for post related posts response
type PostRelatedPostsMeta struct {
	Total       int        `json:"total"`
	LastUpdated *time.Time `json:"last_updated,omitempty"`
}

// BulkRelatedPostResponse represents the response for bulk operations
type BulkRelatedPostResponse struct {
	Created []RelatedPostResponse  `json:"created"`
	Failed  []BulkRelatedPostError `json:"failed,omitempty"`
	Meta    BulkRelatedPostMeta    `json:"meta"`
}

// BulkRelatedPostError represents an error in bulk operations
type BulkRelatedPostError struct {
	RelatedPostID uint   `json:"related_post_id"`
	Error         string `json:"error"`
	Code          string `json:"code"`
}

// BulkRelatedPostMeta represents metadata for bulk operations
type BulkRelatedPostMeta struct {
	TotalRequested int `json:"total_requested"`
	TotalCreated   int `json:"total_created"`
	TotalFailed    int `json:"total_failed"`
}
