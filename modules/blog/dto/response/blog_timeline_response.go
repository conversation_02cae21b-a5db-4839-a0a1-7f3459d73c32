package response

import "time"

// BlogTimelineResponse represents a blog timeline in the response
type BlogTimelineResponse struct {
	ID        uint      `json:"id"`
	Name      string    `json:"name"`
	Code      string    `json:"code"`
	Active    bool      `json:"active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Posts     []BlogTimelinePostResponse `json:"posts,omitempty"`
}

// BlogTimelinePostResponse represents a blog timeline post in the response
type BlogTimelinePostResponse struct {
	ID              uint         `json:"id"`
	BlogTimelineID  uint         `json:"blog_timeline_id"`
	PostID          uint         `json:"post_id"`
	Priority        int          `json:"priority"`
	CreatedAt       time.Time    `json:"created_at"`
	UpdatedAt       time.Time    `json:"updated_at"`
	Post            *PostResponse `json:"post,omitempty"`
}

// BlogTimelineListResponse represents the response for listing blog timelines
type BlogTimelineListResponse struct {
	Data []BlogTimelineResponse `json:"data"`
	Meta BlogTimelineListMeta   `json:"meta"`
}

// BlogTimelineListMeta represents pagination metadata for blog timeline lists
type BlogTimelineListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// BlogTimelinePostListResponse represents the response for listing blog timeline posts
type BlogTimelinePostListResponse struct {
	Data []BlogTimelinePostResponse `json:"data"`
	Meta BlogTimelinePostListMeta   `json:"meta"`
}

// BlogTimelinePostListMeta represents pagination metadata for blog timeline post lists
type BlogTimelinePostListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}