package dto

import (
	"time"
	"wnapi/modules/blog/models"
)

// CreateScheduleRequest represents the request to create a blog schedule
type CreateScheduleRequest struct {
	TenantID    uint      `json:"tenant_id" binding:"required"`
	BlogID      uint      `json:"blog_id" binding:"required"`
	ScheduledAt time.Time `json:"scheduled_at" binding:"required"`
	MaxRetries  uint8     `json:"max_retries,omitempty"`
	CreatedBy   uint      `json:"created_by" binding:"required"`
}

// UpdateScheduleRequest represents the request to update a blog schedule
type UpdateScheduleRequest struct {
	ScheduledAt *time.Time `json:"scheduled_at,omitempty"`
	MaxRetries  *uint8     `json:"max_retries,omitempty"`
}

// ListSchedulesRequest represents the request to list blog schedules
type ListSchedulesRequest struct {
	TenantID uint                       `form:"tenant_id" binding:"required"`
	Status   *models.BlogScheduleStatus `form:"status,omitempty"`
	Page     int                        `form:"page,omitempty"`
	Limit    int                        `form:"limit,omitempty"`
}

// ScheduleResponse represents a blog schedule response
type ScheduleResponse struct {
	ID           uint                      `json:"id"`
	TenantID     uint                      `json:"tenant_id"`
	BlogID       uint                      `json:"blog_id"`
	ScheduledAt  time.Time                 `json:"scheduled_at"`
	Status       models.BlogScheduleStatus `json:"status"`
	ErrorMessage *string                   `json:"error_message,omitempty"`
	RetryCount   uint8                     `json:"retry_count"`
	MaxRetries   uint8                     `json:"max_retries"`
	PublishedAt  *time.Time                `json:"published_at,omitempty"`
	CreatedAt    time.Time                 `json:"created_at"`
	UpdatedAt    time.Time                 `json:"updated_at"`
	CreatedBy    uint                      `json:"created_by"`

	// Optional relations
	Post    *PostSummary `json:"post,omitempty"`
	Creator *UserSummary `json:"creator,omitempty"`
}

// ScheduleListResponse represents a paginated list of schedules
type ScheduleListResponse struct {
	Data       []ScheduleResponse `json:"data"`
	Pagination PaginationMeta     `json:"pagination"`
}

// PostSummary represents a summary of a blog post
type PostSummary struct {
	PostID    uint       `json:"post_id"`
	Title     string     `json:"title"`
	Slug      string     `json:"slug"`
	Status    string     `json:"status"`
	PublishAt *time.Time `json:"publish_at,omitempty"`
}

// UserSummary represents a summary of a user
type UserSummary struct {
	UserID    uint   `json:"user_id"`
	Username  string `json:"username"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	CurrentPage int   `json:"current_page"`
	PerPage     int   `json:"per_page"`
	Total       int64 `json:"total"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// ScheduleStatsResponse represents schedule statistics
type ScheduleStatsResponse struct {
	Total     int64 `json:"total"`
	Pending   int64 `json:"pending"`
	Published int64 `json:"published"`
	Failed    int64 `json:"failed"`
	Cancelled int64 `json:"cancelled"`
}

// ConvertToScheduleResponse converts a BlogSchedule model to ScheduleResponse DTO
func ConvertToScheduleResponse(schedule *models.BlogSchedule) *ScheduleResponse {
	response := &ScheduleResponse{
		ID:           schedule.ID,
		TenantID:     schedule.TenantID,
		BlogID:       schedule.BlogID,
		ScheduledAt:  schedule.ScheduledAt,
		Status:       schedule.Status,
		ErrorMessage: schedule.ErrorMessage,
		RetryCount:   schedule.RetryCount,
		MaxRetries:   schedule.MaxRetries,
		PublishedAt:  schedule.PublishedAt,
		CreatedAt:    schedule.CreatedAt,
		UpdatedAt:    schedule.UpdatedAt,
		CreatedBy:    schedule.CreatedBy,
	}

	// Add post summary if available
	if schedule.Post != nil {
		response.Post = &PostSummary{
			PostID:    schedule.Post.PostID,
			Title:     schedule.Post.Title,
			Slug:      schedule.Post.Slug,
			Status:    schedule.Post.Status,
			PublishAt: schedule.Post.PublishedAt,
		}
	}

	// Add creator summary if available
	if schedule.Creator != nil {
		response.Creator = &UserSummary{
			UserID:    schedule.Creator.UserID,
			Username:  schedule.Creator.Username,
			Email:     schedule.Creator.Email,
			FirstName: schedule.Creator.FirstName,
			LastName:  schedule.Creator.LastName,
		}
	}

	return response
}

// ConvertToScheduleListResponse converts a list of BlogSchedule models to ScheduleListResponse DTO
func ConvertToScheduleListResponse(schedules []*models.BlogSchedule, total int64, page, limit int) *ScheduleListResponse {
	data := make([]ScheduleResponse, len(schedules))
	for i, schedule := range schedules {
		data[i] = *ConvertToScheduleResponse(schedule)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	if totalPages == 0 {
		totalPages = 1
	}

	return &ScheduleListResponse{
		Data: data,
		Pagination: PaginationMeta{
			CurrentPage: page,
			PerPage:     limit,
			Total:       total,
			TotalPages:  totalPages,
			HasNext:     page < totalPages,
			HasPrev:     page > 1,
		},
	}
}

// ConvertToScheduleStatsResponse converts schedule stats map to ScheduleStatsResponse DTO
func ConvertToScheduleStatsResponse(stats map[string]int64) *ScheduleStatsResponse {
	return &ScheduleStatsResponse{
		Total:     stats["total"],
		Pending:   stats[string(models.ScheduleStatusPending)],
		Published: stats[string(models.ScheduleStatusPublished)],
		Failed:    stats[string(models.ScheduleStatusFailed)],
		Cancelled: stats[string(models.ScheduleStatusCancelled)],
	}
}
