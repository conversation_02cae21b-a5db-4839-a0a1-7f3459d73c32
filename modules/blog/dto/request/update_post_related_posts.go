package request

// UpdatePostRelatedPostsRequest là request để cập nhật danh sách bài viết liên quan cho một bài viết
type UpdatePostRelatedPostsRequest struct {
	// PostID là ID của bài viế<PERSON> (được set từ URL parameter)
	PostID uint `json:"-"`

	// RelatedPostIDs là danh sách ID của các bài viết liên quan
	RelatedPostIDs []uint `json:"related_post_ids" binding:"required"`

	// IsBidirectional xác định có tạo mối quan hệ ngược lại hay không
	IsBidirectional bool `json:"is_bidirectional" default:"true"`
}