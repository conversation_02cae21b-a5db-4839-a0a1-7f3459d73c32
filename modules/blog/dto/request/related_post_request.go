package request

// CreateRelatedPostRequest represents the request to create a related post relationship
// Note: tenant_id and created_by are obtained from context, not from request body
type CreateRelatedPostRequest struct {
	PostID          uint  `json:"post_id" binding:"required"`
	RelatedPostID   uint  `json:"related_post_id" binding:"required"`
	Priority        uint  `json:"priority" binding:"omitempty"`
	IsBidirectional *bool `json:"is_bidirectional" binding:"omitempty"`
}

// UpdateRelatedPostRequest represents the request to update a related post relationship
type UpdateRelatedPostRequest struct {
	Priority        *uint `json:"priority" binding:"omitempty"`
	IsBidirectional *bool `json:"is_bidirectional" binding:"omitempty"`
}

// ListRelatedPostRequest represents the request to list related posts
type ListRelatedPostRequest struct {
	PostID    uint   `form:"post_id" binding:"omitempty"`
	Cursor    string `form:"cursor" binding:"omitempty"`
	Limit     int    `form:"limit" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty,oneof=priority created_at updated_at"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
}

// BulkCreateRelatedPostRequest represents the request to create multiple related post relationships
type BulkCreateRelatedPostRequest struct {
	PostID          uint                  `json:"post_id" binding:"required"`
	RelatedPosts    []BulkRelatedPostItem `json:"related_posts" binding:"required,min=1,max=50"`
	IsBidirectional *bool                 `json:"is_bidirectional" binding:"omitempty"`
}

// BulkRelatedPostItem represents a single item in bulk create request
type BulkRelatedPostItem struct {
	RelatedPostID uint `json:"related_post_id" binding:"required"`
	Priority      uint `json:"priority" binding:"omitempty"`
}

// DeleteRelatedPostRequest represents the request to delete related post relationships
type DeleteRelatedPostRequest struct {
	PostID        uint `json:"post_id" binding:"required"`
	RelatedPostID uint `json:"related_post_id" binding:"required"`
}
