package request

// CreateBlogTimelineRequest represents the request to create a new blog timeline
type CreateBlogTimelineRequest struct {
	Name   string `json:"name" binding:"required,max=255"`
	Code   string `json:"code" binding:"required,max=100"`
	Active *bool  `json:"active" binding:"omitempty"`
}

// UpdateBlogTimelineRequest represents the request to update a blog timeline
type UpdateBlogTimelineRequest struct {
	Name   *string `json:"name" binding:"omitempty,max=255"`
	Code   *string `json:"code" binding:"omitempty,max=100"`
	Active *bool   `json:"active" binding:"omitempty"`
}

// ListBlogTimelineRequest represents the request to list blog timelines
type ListBlogTimelineRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	Limit    int    `form:"limit" binding:"omitempty,min=1,max=100"`
	Search   string `form:"search" binding:"omitempty,max=255"`
	Active   *bool  `form:"active" binding:"omitempty"`
	SortBy   string `form:"sort_by" binding:"omitempty,oneof=id name code created_at updated_at"`
	SortDesc *bool  `form:"sort_desc" binding:"omitempty"`
}