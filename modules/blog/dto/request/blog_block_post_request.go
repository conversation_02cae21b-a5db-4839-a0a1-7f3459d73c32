package request

// AddPostToBlogBlockRequest represents the request to add a post to a blog block
type AddPostToBlogBlockRequest struct {
	PostID   uint `json:"post_id" binding:"required"`
	Priority *int `json:"priority" binding:"omitempty"`
}

// UpdateBlogBlockPostRequest represents the request to update a blog block post
type UpdateBlogBlockPostRequest struct {
	Priority *int `json:"priority" binding:"omitempty"`
}

// BatchAddPostsToBlogBlockRequest represents the request to add multiple posts to a blog block
type BatchAddPostsToBlogBlockRequest struct {
	Posts []AddPostToBlogBlockRequest `json:"posts" binding:"required,dive"`
}

// ListBlogBlockPostsRequest represents the request to list posts in a blog block
type ListBlogBlockPostsRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	Limit    int    `form:"limit" binding:"omitempty,min=1,max=100"`
	SortBy   string `form:"sort_by" binding:"omitempty,oneof=id priority created_at updated_at"`
	SortDesc *bool  `form:"sort_desc" binding:"omitempty"`
}

// PostOrder represents a single post order in reorder request
type PostOrder struct {
	PostID   uint `json:"post_id" binding:"required"`
	Priority int  `json:"priority" binding:"required"`
}

// ReorderBlogBlockPostsRequest represents the request to reorder posts in a blog block
type ReorderBlogBlockPostsRequest struct {
	PostOrders []PostOrder `json:"post_orders" binding:"required,dive"`
}