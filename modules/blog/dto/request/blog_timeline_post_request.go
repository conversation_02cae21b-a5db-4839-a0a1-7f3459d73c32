package request

// AddPostToBlogTimelineRequest represents the request to add a post to a blog timeline
type AddPostToBlogTimelineRequest struct {
	PostID   uint `json:"post_id" binding:"required"`
	Priority *int `json:"priority" binding:"omitempty"`
}

// UpdateBlogTimelinePostRequest represents the request to update a blog timeline post
type UpdateBlogTimelinePostRequest struct {
	Priority *int `json:"priority" binding:"omitempty"`
}

// BatchAddPostsToBlogTimelineRequest represents the request to add multiple posts to a blog timeline
type BatchAddPostsToBlogTimelineRequest struct {
	Posts []AddPostToBlogTimelineRequest `json:"posts" binding:"required,dive"`
}

// ListBlogTimelinePostsRequest represents the request to list posts in a blog timeline
type ListBlogTimelinePostsRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	Limit    int    `form:"limit" binding:"omitempty,min=1,max=100"`
	SortBy   string `form:"sort_by" binding:"omitempty,oneof=id priority created_at updated_at"`
	SortDesc *bool  `form:"sort_desc" binding:"omitempty"`
}

// BlogTimelinePostOrder represents a single post order in reorder request
type BlogTimelinePostOrder struct {
	PostID   uint `json:"post_id" binding:"required"`
	Priority int  `json:"priority" binding:"required"`
}

// ReorderBlogTimelinePostsRequest represents the request to reorder posts in a blog timeline
type ReorderBlogTimelinePostsRequest struct {
	PostOrders []BlogTimelinePostOrder `json:"post_orders" binding:"required,dive"`
}