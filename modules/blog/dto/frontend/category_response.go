package frontend

import "time"

// CategorySummaryResponse represents a simplified category for listing
type CategorySummaryResponse struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	Slug          string    `json:"slug"`
	Description   string    `json:"description"`
	FeaturedImage string    `json:"image"`
	PostCount     int       `json:"post_count"`
	CreatedAt     time.Time `json:"created_at"`
}

// CategoryDetailResponse represents a full category detail
type CategoryDetailResponse struct {
	ID            uint      `json:"id"`
	ParentID      *uint     `json:"parent_id"`
	Name          string    `json:"name"`
	Slug          string    `json:"slug"`
	Description   string    `json:"description"`
	FeaturedImage string    `json:"image"`
	PostCount     int       `json:"post_count"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	Parent        *CategoryInfo `json:"parent,omitempty"`
	Children      []CategoryInfo `json:"children,omitempty"`
	SeoMeta       *SeoMetaInfo `json:"seo_meta,omitempty"`
}

// CategoryListResponse represents a paginated list of categories
type CategoryListResponse struct {
	Data []CategorySummaryResponse `json:"data"`
	Meta PaginationMeta           `json:"meta"`
}

// CategoryTreeResponse represents a tree structure of categories
type CategoryTreeResponse struct {
	Data []CategoryTreeItem `json:"data"`
	Meta interface{}        `json:"meta"`
}

// CategoryDetailResponseWrapper wraps category detail with meta
type CategoryDetailResponseWrapper struct {
	Data CategoryDetailResponse `json:"data"`
	Meta interface{}           `json:"meta"`
}

// CategoryTreeItem represents a category with its children in a tree structure
type CategoryTreeItem struct {
	ID            uint      `json:"id"`
	Name          string    `json:"name"`
	Slug          string    `json:"slug"`
	Description   string    `json:"description"`
	FeaturedImage string    `json:"image"`
	PostCount     int       `json:"post_count"`
	Depth         int       `json:"depth"`
	Children      []CategoryTreeItem `json:"children,omitempty"`
}