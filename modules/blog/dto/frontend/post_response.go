package frontend

import "time"

// PostSummaryResponse represents a simplified post for listing
type PostSummaryResponse struct {
	ID            uint      `json:"id"`
	Title         string    `json:"title"`
	Slug          string    `json:"slug"`
	Description   *string   `json:"description"`
	Image         *string   `json:"image"`
	PublishedAt   *time.Time `json:"published_at"`
	CreatedAt     time.Time `json:"created_at"`
	Author        *AuthorInfo `json:"author,omitempty"`
	Categories    []CategoryInfo `json:"categories,omitempty"`
	Tags          []TagInfo `json:"tags,omitempty"`
}

// PostDetailResponse represents a full post detail
type PostDetailResponse struct {
	ID            uint      `json:"id"`
	Title         string    `json:"title"`
	Slug          string    `json:"slug"`
	Description   *string   `json:"description"`
	Content       *string   `json:"content"`
	Image         *string   `json:"image"`
	PublishedAt   *time.Time `json:"published_at"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	Author        *AuthorInfo `json:"author,omitempty"`
	Categories    []CategoryInfo `json:"categories,omitempty"`
	Tags          []TagInfo `json:"tags,omitempty"`
	SeoMeta       *SeoMetaInfo `json:"seo_meta,omitempty"`
}

// PostListResponse represents a paginated list of posts
type PostListResponse struct {
	Data []PostSummaryResponse `json:"data"`
	Meta PaginationMeta       `json:"meta"`
}

// PostDetailResponseWrapper wraps post detail with meta
type PostDetailResponseWrapper struct {
	Data PostDetailResponse `json:"data"`
	Meta interface{}        `json:"meta"`
}

// AuthorInfo represents minimal author information
type AuthorInfo struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Bio       *string `json:"bio,omitempty"`
	AvatarURL *string `json:"avatar_url,omitempty"`
}

// CategoryInfo represents minimal category information
type CategoryInfo struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// TagInfo represents minimal tag information
type TagInfo struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}

// SeoMetaInfo represents SEO metadata
type SeoMetaInfo struct {
	Title       *string `json:"title,omitempty"`
	Description *string `json:"description,omitempty"`
	Keywords    *string `json:"keywords,omitempty"`
	OgTitle     *string `json:"og_title,omitempty"`
	OgDescription *string `json:"og_description,omitempty"`
	OgImage     *string `json:"og_image,omitempty"`
}

// PaginationMeta represents pagination metadata
type PaginationMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
	Total      *int   `json:"total,omitempty"`
}