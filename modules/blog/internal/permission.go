package internal

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "blog"

// Blog Posts permissions
const (
	// Standard CRUD permissions for posts
	CreatePostPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + permission.ActionCreate
	ReadPostPermission   = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + permission.ActionRead
	UpdatePostPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + permission.ActionUpdate
	DeletePostPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + permission.ActionDelete
	ListPostPermission   = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + permission.ActionList

	// Post workflow permissions
	PublishPostPermission  = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "publish"
	ApprovePostPermission  = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "approve"
	SchedulePostPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "schedule"
	TrashPostPermission    = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "trash"
	RestorePostPermission  = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "restore"

	// Post status management permissions
	ManagePostStatusPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "status"
	ManagePostVisibilityPermission = ModuleName + permission.PermissionSeparator + "posts" + permission.PermissionSeparator + "visibility"
)

// Categories permissions
const (
	// Standard CRUD permissions for categories
	CreateCategoryPermission = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + permission.ActionCreate
	ReadCategoryPermission   = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + permission.ActionRead
	UpdateCategoryPermission = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteCategoryPermission = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + permission.ActionDelete
	ListCategoryPermission   = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + permission.ActionList

	// Category hierarchy permissions
	ManageCategoryHierarchyPermission = ModuleName + permission.PermissionSeparator + "categories" + permission.PermissionSeparator + "hierarchy"
)

// Tags permissions
const (
	// Standard CRUD permissions for tags
	CreateTagPermission = ModuleName + permission.PermissionSeparator + "tags" + permission.PermissionSeparator + permission.ActionCreate
	ReadTagPermission   = ModuleName + permission.PermissionSeparator + "tags" + permission.PermissionSeparator + permission.ActionRead
	UpdateTagPermission = ModuleName + permission.PermissionSeparator + "tags" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteTagPermission = ModuleName + permission.PermissionSeparator + "tags" + permission.PermissionSeparator + permission.ActionDelete
	ListTagPermission   = ModuleName + permission.PermissionSeparator + "tags" + permission.PermissionSeparator + permission.ActionList
)

// Authors permissions
const (
	// Standard CRUD permissions for authors
	CreateAuthorPermission = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + permission.ActionCreate
	ReadAuthorPermission   = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + permission.ActionRead
	UpdateAuthorPermission = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteAuthorPermission = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + permission.ActionDelete
	ListAuthorPermission   = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + permission.ActionList

	// Author status management permissions
	ManageAuthorStatusPermission = ModuleName + permission.PermissionSeparator + "authors" + permission.PermissionSeparator + "status"
)

// Related Posts permissions
const (
	// Standard CRUD permissions for related posts
	CreateRelatedPostPermission = ModuleName + permission.PermissionSeparator + "related_posts" + permission.PermissionSeparator + permission.ActionCreate
	ReadRelatedPostPermission   = ModuleName + permission.PermissionSeparator + "related_posts" + permission.PermissionSeparator + permission.ActionRead
	UpdateRelatedPostPermission = ModuleName + permission.PermissionSeparator + "related_posts" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteRelatedPostPermission = ModuleName + permission.PermissionSeparator + "related_posts" + permission.PermissionSeparator + permission.ActionDelete
	ListRelatedPostPermission   = ModuleName + permission.PermissionSeparator + "related_posts" + permission.PermissionSeparator + permission.ActionList
)

// Statistics permissions
const (
	ReadStatisticsPermission = ModuleName + permission.PermissionSeparator + "statistics" + permission.PermissionSeparator + permission.ActionRead
)

// Blog Blocks permissions
const (
	// Standard CRUD permissions for blog blocks
	CreateBlogBlockPermission = ModuleName + permission.PermissionSeparator + "blog_blocks" + permission.PermissionSeparator + permission.ActionCreate
	ReadBlogBlockPermission   = ModuleName + permission.PermissionSeparator + "blog_blocks" + permission.PermissionSeparator + permission.ActionRead
	UpdateBlogBlockPermission = ModuleName + permission.PermissionSeparator + "blog_blocks" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteBlogBlockPermission = ModuleName + permission.PermissionSeparator + "blog_blocks" + permission.PermissionSeparator + permission.ActionDelete
	ListBlogBlockPermission   = ModuleName + permission.PermissionSeparator + "blog_blocks" + permission.PermissionSeparator + permission.ActionList
)

// Blog Timelines permissions
const (
	// Standard CRUD permissions for blog timelines
	CreateBlogTimelinePermission = ModuleName + permission.PermissionSeparator + "blog_timelines" + permission.PermissionSeparator + permission.ActionCreate
	ReadBlogTimelinePermission   = ModuleName + permission.PermissionSeparator + "blog_timelines" + permission.PermissionSeparator + permission.ActionRead
	UpdateBlogTimelinePermission = ModuleName + permission.PermissionSeparator + "blog_timelines" + permission.PermissionSeparator + permission.ActionUpdate
	DeleteBlogTimelinePermission = ModuleName + permission.PermissionSeparator + "blog_timelines" + permission.PermissionSeparator + permission.ActionDelete
	ListBlogTimelinePermission   = ModuleName + permission.PermissionSeparator + "blog_timelines" + permission.PermissionSeparator + permission.ActionList
)

// Full management permission
const (
	ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// GetPostCRUDPermissions trả về tất cả CRUD permissions cho post management
func GetPostCRUDPermissions() []string {
	return []string{
		CreatePostPermission,
		ReadPostPermission,
		UpdatePostPermission,
		DeletePostPermission,
		ListPostPermission,
	}
}

// GetPostWorkflowPermissions trả về tất cả workflow permissions cho posts
func GetPostWorkflowPermissions() []string {
	return []string{
		PublishPostPermission,
		ApprovePostPermission,
		SchedulePostPermission,
		TrashPostPermission,
		RestorePostPermission,
		ManagePostStatusPermission,
		ManagePostVisibilityPermission,
	}
}

// GetCategoryCRUDPermissions trả về tất cả CRUD permissions cho category management
func GetCategoryCRUDPermissions() []string {
	return []string{
		CreateCategoryPermission,
		ReadCategoryPermission,
		UpdateCategoryPermission,
		DeleteCategoryPermission,
		ListCategoryPermission,
	}
}

// GetCategoryManagementPermissions trả về tất cả management permissions cho categories
func GetCategoryManagementPermissions() []string {
	return []string{
		ManageCategoryHierarchyPermission,
	}
}

// GetTagCRUDPermissions trả về tất cả CRUD permissions cho tag management
func GetTagCRUDPermissions() []string {
	return []string{
		CreateTagPermission,
		ReadTagPermission,
		UpdateTagPermission,
		DeleteTagPermission,
		ListTagPermission,
	}
}

// GetAuthorCRUDPermissions trả về tất cả CRUD permissions cho author management
func GetAuthorCRUDPermissions() []string {
	return []string{
		CreateAuthorPermission,
		ReadAuthorPermission,
		UpdateAuthorPermission,
		DeleteAuthorPermission,
		ListAuthorPermission,
	}
}

// GetAuthorManagementPermissions trả về tất cả management permissions cho authors
func GetAuthorManagementPermissions() []string {
	return []string{
		ManageAuthorStatusPermission,
	}
}

// GetRelatedPostCRUDPermissions trả về tất cả CRUD permissions cho related post management
func GetRelatedPostCRUDPermissions() []string {
	return []string{
		CreateRelatedPostPermission,
		ReadRelatedPostPermission,
		UpdateRelatedPostPermission,
		DeleteRelatedPostPermission,
		ListRelatedPostPermission,
	}
}

// GetStatisticsPermissions trả về tất cả statistics permissions
func GetStatisticsPermissions() []string {
	return []string{
		ReadStatisticsPermission,
	}
}

// GetBlogBlockCRUDPermissions trả về tất cả CRUD permissions cho blog block management
func GetBlogBlockCRUDPermissions() []string {
	return []string{
		CreateBlogBlockPermission,
		ReadBlogBlockPermission,
		UpdateBlogBlockPermission,
		DeleteBlogBlockPermission,
		ListBlogBlockPermission,
	}
}

// GetBlogTimelineCRUDPermissions trả về tất cả CRUD permissions cho blog timeline management
func GetBlogTimelineCRUDPermissions() []string {
	return []string{
		CreateBlogTimelinePermission,
		ReadBlogTimelinePermission,
		UpdateBlogTimelinePermission,
		DeleteBlogTimelinePermission,
		ListBlogTimelinePermission,
	}
}

// GetAllPermissions trả về tất cả permissions của module
func GetAllPermissions() []string {
	permissions := []string{}
	permissions = append(permissions, GetPostCRUDPermissions()...)
	permissions = append(permissions, GetPostWorkflowPermissions()...)
	permissions = append(permissions, GetCategoryCRUDPermissions()...)
	permissions = append(permissions, GetCategoryManagementPermissions()...)
	permissions = append(permissions, GetTagCRUDPermissions()...)
	permissions = append(permissions, GetAuthorCRUDPermissions()...)
	permissions = append(permissions, GetAuthorManagementPermissions()...)
	permissions = append(permissions, GetRelatedPostCRUDPermissions()...)
	permissions = append(permissions, GetStatisticsPermissions()...)
	permissions = append(permissions, GetBlogBlockCRUDPermissions()...)
	permissions = append(permissions, GetBlogTimelineCRUDPermissions()...)
	permissions = append(permissions, ManagePermission)
	return permissions
}
