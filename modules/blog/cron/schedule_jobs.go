package cron

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/blog/service"
)

// ScheduleCronJobs handles cron jobs for blog schedules
type ScheduleCronJobs struct {
	scheduleService service.ScheduleService
	queueClient     queue.QueueClient
	logger          logger.Logger
}

// NewScheduleCronJobs creates a new schedule cron jobs handler
func NewScheduleCronJobs(
	scheduleService service.ScheduleService,
	queueClient queue.QueueClient,
	logger logger.Logger,
) *ScheduleCronJobs {
	return &ScheduleCronJobs{
		scheduleService: scheduleService,
		queueClient:     queueClient,
		logger:          logger,
	}
}

// GetCronJobs returns the list of cron jobs for blog schedules
func (s *ScheduleCronJobs) GetCronJobs() []CronJob {
	return []CronJob{
		{
			Name:        "blog_process_pending_schedules",
			Schedule:    "*/5 * * * *", // Every 5 minutes
			Description: "Process pending blog schedules",
			Enabled:     true,
			Handler:     s.ProcessPendingSchedules,
		},
		{
			Name:        "blog_cleanup_old_schedules",
			Schedule:    "0 2 * * *", // Daily at 2 AM
			Description: "Cleanup old completed blog schedules",
			Enabled:     true,
			Handler:     s.CleanupOldSchedules,
		},
		{
			Name:        "blog_publish_scheduled_posts",
			Schedule:    "* * * * *", // Every minute
			Description: "Publish scheduled blog posts",
			Enabled:     true,
			Handler:     s.PublishScheduledPosts,
		},
	}
}

// CronJob represents a cron job definition
type CronJob struct {
	Name        string
	Schedule    string
	Description string
	Enabled     bool
	Handler     func(context.Context) error
}

// ProcessPendingSchedules processes all pending schedules that are due
func (s *ScheduleCronJobs) ProcessPendingSchedules(ctx context.Context) error {
	s.logger.Info("Starting process pending schedules cron job")

	if err := s.scheduleService.ProcessPendingSchedules(ctx); err != nil {
		s.logger.Error("Failed to process pending schedules", "error", err)
		return err
	}

	s.logger.Info("Process pending schedules cron job completed successfully")
	return nil
}

// CleanupOldSchedules removes old completed schedules
func (s *ScheduleCronJobs) CleanupOldSchedules(ctx context.Context) error {
	s.logger.Info("Starting cleanup old schedules cron job")

	// Enqueue cleanup task if queue client is available
	if s.queueClient != nil {
		_, err := s.queueClient.EnqueueTask(ctx, "blog.schedule.cleanup", map[string]interface{}{
			"retention_days": 30, // Keep schedules for 30 days
		}, &queue.EnqueueOptions{
			Queue:    "blog_schedules",
			TenantID: 0, // Process for all tenants
		})

		if err != nil {
			s.logger.Error("Failed to enqueue cleanup task", "error", err)
			return err
		}

		s.logger.Info("Cleanup old schedules task enqueued successfully")
	} else {
		s.logger.Warn("Queue client not available, skipping cleanup task")
	}

	return nil
}

// ProcessSchedulesByTenant processes pending schedules for a specific tenant
func (s *ScheduleCronJobs) ProcessSchedulesByTenant(ctx context.Context, tenantID uint) error {
	s.logger.Info("Processing pending schedules for tenant", "tenant_id", tenantID)

	// This could be used for tenant-specific processing if needed
	// For now, we use the general ProcessPendingSchedules method
	if err := s.scheduleService.ProcessPendingSchedules(ctx); err != nil {
		s.logger.Error("Failed to process pending schedules for tenant", "tenant_id", tenantID, "error", err)
		return err
	}

	s.logger.Info("Pending schedules processed successfully for tenant", "tenant_id", tenantID)
	return nil
}

// GetJobStatus returns the status of cron jobs
func (s *ScheduleCronJobs) GetJobStatus() map[string]interface{} {
	jobs := s.GetCronJobs()
	status := make(map[string]interface{})

	for _, job := range jobs {
		status[job.Name] = map[string]interface{}{
			"schedule":    job.Schedule,
			"description": job.Description,
			"enabled":     job.Enabled,
			"last_run":    nil, // This would be tracked by the cron scheduler
			"next_run":    nil, // This would be calculated by the cron scheduler
		}
	}

	return status
}

// EnableJob enables a specific cron job
func (s *ScheduleCronJobs) EnableJob(jobName string) error {
	// This would be implemented by the cron scheduler
	s.logger.Info("Enabling cron job", "job_name", jobName)
	return nil
}

// DisableJob disables a specific cron job
func (s *ScheduleCronJobs) DisableJob(jobName string) error {
	// This would be implemented by the cron scheduler
	s.logger.Info("Disabling cron job", "job_name", jobName)
	return nil
}

// PublishScheduledPosts publishes blog posts that are scheduled to be published
func (s *ScheduleCronJobs) PublishScheduledPosts(ctx context.Context) error {
	s.logger.Info("Starting publish scheduled posts cron job")

	// Enqueue publish task if queue client is available
	if s.queueClient != nil {
		_, err := s.queueClient.EnqueueTask(ctx, "blog.post.publish_scheduled", map[string]interface{}{
			"batch_size": 10, // Process 10 posts at a time
		}, &queue.EnqueueOptions{
			Queue:    "blog_posts",
			TenantID: 0, // Process for all tenants
		})

		if err != nil {
			s.logger.Error("Failed to enqueue publish scheduled posts task", "error", err)
			return err
		}

		s.logger.Info("Publish scheduled posts task enqueued successfully")
	} else {
		s.logger.Warn("Queue client not available, skipping publish scheduled posts task")
	}

	return nil
}

// RunJobManually runs a cron job manually
func (s *ScheduleCronJobs) RunJobManually(ctx context.Context, jobName string) error {
	s.logger.Info("Running cron job manually", "job_name", jobName)

	switch jobName {
	case "blog_process_pending_schedules":
		return s.ProcessPendingSchedules(ctx)
	case "blog_cleanup_old_schedules":
		return s.CleanupOldSchedules(ctx)
	case "blog_publish_scheduled_posts":
		return s.PublishScheduledPosts(ctx)
	default:
		s.logger.Error("Unknown cron job", "job_name", jobName)
		return nil
	}
}
