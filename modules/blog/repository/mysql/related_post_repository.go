package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

type relatedPostRepository struct {
	db *gorm.DB
}

// NewRelatedPostRepository creates a new related post repository
func NewRelatedPostRepository(db *gorm.DB) repository.RelatedPostRepository {
	return &relatedPostRepository{db: db}
}

// Create creates a new related post relationship using GORM
func (r *relatedPostRepository) Create(ctx context.Context, relatedPost *models.RelatedPost) error {
	if err := r.db.WithContext(ctx).Create(relatedPost).Error; err != nil {
		return fmt.Errorf("failed to create related post: %w", err)
	}
	return nil
}

// GetByID retrieves a related post relationship by ID using GORM
func (r *relatedPostRepository) GetByID(ctx context.Context, tenantID, websiteID, relationID uint) (*models.RelatedPost, error) {
	var relatedPost models.RelatedPost

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, relationID).
		First(&relatedPost).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("related post not found")
		}
		return nil, fmt.Errorf("failed to get related post: %w", err)
	}

	return &relatedPost, nil
}

// Update updates a related post relationship using GORM
func (r *relatedPostRepository) Update(ctx context.Context, relatedPost *models.RelatedPost) error {
	result := r.db.WithContext(ctx).
		Model(relatedPost).
		Where("tenant_id = ? AND id = ?", relatedPost.TenantID, relatedPost.ID).
		Updates(map[string]interface{}{
			"priority":         relatedPost.Priority,
			"is_bidirectional": relatedPost.IsBidirectional,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update related post: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("related post not found")
	}

	return nil
}

// Delete deletes a related post relationship by ID using GORM
func (r *relatedPostRepository) Delete(ctx context.Context, tenantID, websiteID, relationID uint) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, relationID).
		Delete(&models.RelatedPost{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete related post: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("related post not found")
	}

	return nil
}

// DeleteByPosts deletes a specific relationship between two posts using GORM
func (r *relatedPostRepository) DeleteByPosts(ctx context.Context, tenantID, websiteID, postID, relatedPostID uint) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND post_id = ? AND related_post_id = ?", tenantID, websiteID, postID, relatedPostID).
		Delete(&models.RelatedPost{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete related post: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("related post relationship not found")
	}

	return nil
}

// CheckRelationExists checks if a relationship between two posts exists using GORM
func (r *relatedPostRepository) CheckRelationExists(ctx context.Context, tenantID, websiteID, postID, relatedPostID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.RelatedPost{}).
		Where("tenant_id = ? AND website_id = ? AND post_id = ? AND related_post_id = ?", tenantID, websiteID, postID, relatedPostID).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check relation exists: %w", err)
	}

	return count > 0, nil
}

// BulkCreate creates multiple related post relationships using GORM
func (r *relatedPostRepository) BulkCreate(ctx context.Context, relatedPosts []*models.RelatedPost) error {
	if len(relatedPosts) == 0 {
		return nil
	}

	// Create a new context with a 30-second timeout for batch operations
	batchCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	if err := r.db.WithContext(batchCtx).CreateInBatches(relatedPosts, 100).Error; err != nil {
		return fmt.Errorf("failed to bulk create related posts: %w", err)
	}

	return nil
}

// DeleteAllByPost deletes all related post relationships for a specific post using GORM
func (r *relatedPostRepository) DeleteAllByPost(ctx context.Context, tenantID, websiteID, postID uint) error {
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND (post_id = ? OR related_post_id = ?)", tenantID, websiteID, postID, postID).
		Delete(&models.RelatedPost{}).Error

	if err != nil {
		return fmt.Errorf("failed to delete all related posts for post: %w", err)
	}

	return nil
}

// List retrieves related posts with pagination using raw SQL
func (r *relatedPostRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListRelatedPostRequest) ([]*models.RelatedPost, string, bool, error) {
	var conditions []string
	var args []interface{}

	// Base condition
	conditions = append(conditions, "tenant_id = ?")
	args = append(args, tenantID)

	conditions = append(conditions, "website_id = ?")
	args = append(args, websiteID)

	// Filter by post_id if provided
	if req.PostID != 0 {
		conditions = append(conditions, "post_id = ?")
		args = append(args, req.PostID)
	}

	// Build WHERE clause
	whereClause := strings.Join(conditions, " AND ")

	// Set default values
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.SortBy == "" {
		req.SortBy = "priority"
	}
	if req.SortOrder == "" {
		req.SortOrder = "asc"
	}

	// Build ORDER BY clause
	orderBy := fmt.Sprintf("ORDER BY %s %s, id ASC", req.SortBy, strings.ToUpper(req.SortOrder))

	// Cursor pagination
	if req.Cursor != "" {
		// Add cursor condition based on sort field
		conditions = append(conditions, fmt.Sprintf("(%s > ? OR (%s = ? AND id > ?))", req.SortBy, req.SortBy))
		args = append(args, req.Cursor, req.Cursor, req.Cursor) // Simplified - in real implementation, decode cursor
	}

	query := fmt.Sprintf(`
		SELECT id, tenant_id, post_id, related_post_id, priority,
			   is_bidirectional, created_at, updated_at, created_by
		FROM blog_related_posts
		WHERE %s
		%s
		LIMIT ?
	`, whereClause, orderBy)

	args = append(args, req.Limit+1) // +1 to check if there are more records

	var relatedPosts []*models.RelatedPost
	err := r.db.WithContext(ctx).Raw(query, args...).Scan(&relatedPosts).Error
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list related posts: %w", err)
	}

	// Check for more records
	hasMore := len(relatedPosts) > req.Limit
	if hasMore {
		relatedPosts = relatedPosts[:req.Limit]
	}

	// Generate next cursor
	var nextCursor string
	if hasMore && len(relatedPosts) > 0 {
		lastItem := relatedPosts[len(relatedPosts)-1]
		nextCursor = fmt.Sprintf("%d", lastItem.ID) // Simplified cursor
	}

	return relatedPosts, nextCursor, hasMore, nil
}

// GetRelatedPosts retrieves related posts for a specific post using raw SQL
func (r *relatedPostRepository) GetRelatedPosts(ctx context.Context, tenantID, websiteID, postID uint, limit int) ([]*models.RelatedPost, error) {
	if limit <= 0 {
		limit = 10
	}

	query := `
		SELECT id, tenant_id, website_id, post_id, related_post_id, priority,
			   is_bidirectional, created_at, updated_at, created_by
		FROM blog_related_posts
		WHERE tenant_id = ? AND website_id = ? AND post_id = ?
		ORDER BY priority ASC, created_at DESC
		LIMIT ?
	`

	var relatedPosts []*models.RelatedPost
	err := r.db.WithContext(ctx).Raw(query, tenantID, websiteID, postID, limit).Scan(&relatedPosts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get related posts: %w", err)
	}

	return relatedPosts, nil
}

// GetRelatedPostsWithCursor gets related posts for a specific post with cursor-based pagination
func (r *relatedPostRepository) GetRelatedPostsWithCursor(ctx context.Context, tenantID, websiteID, postID, cursorID uint, limit int) ([]*models.RelatedPost, error) {
	if limit <= 0 {
		limit = 10
	}

	var query string
	var args []interface{}

	if cursorID > 0 {
		// With cursor - get posts after the cursor
		query = `
			SELECT id, tenant_id, website_id, post_id, related_post_id, priority,
				   is_bidirectional, created_at, updated_at, created_by
			FROM blog_related_posts
			WHERE tenant_id = ? AND website_id = ? AND post_id = ? AND related_post_id > ?
			ORDER BY related_post_id ASC
			LIMIT ?
		`
		args = []interface{}{tenantID, websiteID, postID, cursorID, limit}
	} else {
		// Without cursor - get first page
		query = `
			SELECT id, tenant_id, website_id, post_id, related_post_id, priority,
				   is_bidirectional, created_at, updated_at, created_by
			FROM blog_related_posts
			WHERE tenant_id = ? AND website_id = ? AND post_id = ?
			ORDER BY related_post_id ASC
			LIMIT ?
		`
		args = []interface{}{tenantID, websiteID, postID, limit}
	}

	var relatedPosts []*models.RelatedPost
	err := r.db.WithContext(ctx).Raw(query, args...).Scan(&relatedPosts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get related posts with cursor: %w", err)
	}

	return relatedPosts, nil
}
