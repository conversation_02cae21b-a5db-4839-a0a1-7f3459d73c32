package mysql

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/blog/models"

	"gorm.io/gorm"
)

// ScheduleRepository handles database operations for blog schedules
type ScheduleRepository struct {
	db *gorm.DB
}

// NewScheduleRepository creates a new schedule repository
func NewScheduleRepository(db *gorm.DB) *ScheduleRepository {
	return &ScheduleRepository{db: db}
}

// Create creates a new blog schedule
func (r *ScheduleRepository) Create(ctx context.Context, schedule *models.BlogSchedule) error {
	if err := r.db.WithContext(ctx).Create(schedule).Error; err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}
	return nil
}

// GetByID retrieves a schedule by ID
func (r *ScheduleRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogSchedule, error) {
	var schedule models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Preload("Post").
		Preload("Creator").
		First(&schedule, id).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}
	return &schedule, nil
}

// GetByIDInternal retrieves a schedule by ID without tenant/website filtering (for internal operations)
func (r *ScheduleRepository) GetByIDInternal(ctx context.Context, id uint) (*models.BlogSchedule, error) {
	var schedule models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Preload("Post").
		Preload("Creator").
		First(&schedule, id).Error; err != nil {
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}
	return &schedule, nil
}

// GetByBlogAndTime retrieves a schedule by blog ID and scheduled time
func (r *ScheduleRepository) GetByBlogAndTime(ctx context.Context, tenantID, websiteID, blogID uint, scheduledAt time.Time) (*models.BlogSchedule, error) {
	var schedule models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND blog_id = ? AND scheduled_at = ?", tenantID, websiteID, blogID, scheduledAt).
		First(&schedule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}
	return &schedule, nil
}

// List retrieves schedules with pagination and filtering
func (r *ScheduleRepository) List(ctx context.Context, tenantID, websiteID uint, status *models.BlogScheduleStatus, limit, offset int) ([]*models.BlogSchedule, int64, error) {
	var schedules []*models.BlogSchedule
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// Count total records
	if err := query.Model(&models.BlogSchedule{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count schedules: %w", err)
	}

	// Get records with pagination
	if err := query.
		Preload("Post").
		Preload("Creator").
		Order("scheduled_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&schedules).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list schedules: %w", err)
	}

	return schedules, total, nil
}

// Update updates a blog schedule
func (r *ScheduleRepository) Update(ctx context.Context, schedule *models.BlogSchedule) error {
	if err := r.db.WithContext(ctx).Save(schedule).Error; err != nil {
		return fmt.Errorf("failed to update schedule: %w", err)
	}
	return nil
}

// Delete deletes a blog schedule
func (r *ScheduleRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Delete(&models.BlogSchedule{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}
	return nil
}

// GetPendingSchedules retrieves all pending schedules that are due for execution
func (r *ScheduleRepository) GetPendingSchedules(ctx context.Context, beforeTime time.Time) ([]*models.BlogSchedule, error) {
	var schedules []*models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Where("status = ? AND scheduled_at <= ?", models.ScheduleStatusPending, beforeTime).
		Order("scheduled_at ASC").
		Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get pending schedules: %w", err)
	}
	return schedules, nil
}

// GetByBlogID retrieves a schedule by blog ID
func (r *ScheduleRepository) GetByBlogID(ctx context.Context, tenantID, websiteID, blogID uint) (*models.BlogSchedule, error) {
	var schedule models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND blog_id = ?", tenantID, websiteID, blogID).
		First(&schedule).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get schedule by blog ID: %w", err)
	}
	return &schedule, nil
}

// CleanupOldSchedules removes old completed/cancelled schedules based on retention policy
func (r *ScheduleRepository) CleanupOldSchedules(ctx context.Context, retentionDays int, batchSize int, tenantID uint) (int64, error) {
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)

	query := r.db.WithContext(ctx).
		Where("status IN (?, ?) AND updated_at < ?",
			models.ScheduleStatusPublished,
			models.ScheduleStatusCancelled,
			cutoffDate)

	// Filter by tenant if specified (0 means all tenants)
	if tenantID > 0 {
		query = query.Where("tenant_id = ?", tenantID)
	}

	// Delete in batches to avoid locking the table for too long
	result := query.Limit(batchSize).Delete(&models.BlogSchedule{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old schedules: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// GetScheduledPosts retrieves posts that are scheduled to be published
func (r *ScheduleRepository) GetScheduledPosts(ctx context.Context, beforeTime time.Time, batchSize int) ([]*models.BlogSchedule, error) {
	var schedules []*models.BlogSchedule
	if err := r.db.WithContext(ctx).
		Preload("Post").
		Where("status = ? AND scheduled_at <= ?", models.ScheduleStatusPending, beforeTime).
		Order("scheduled_at ASC").
		Limit(batchSize).
		Find(&schedules).Error; err != nil {
		return nil, fmt.Errorf("failed to get scheduled posts: %w", err)
	}
	return schedules, nil
}
