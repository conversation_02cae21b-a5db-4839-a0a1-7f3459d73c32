package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogBlockRepository implements the repository.BlogBlockRepository interface
type BlogBlockRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewBlogBlockRepository creates a new instance of BlogBlockRepository
func NewBlogBlockRepository(db *sqlx.DB) repository.BlogBlockRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for blog block repository: %v\n", err)
		gormDB = nil
	}

	return &BlogBlockRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create inserts a new blog block into the database using GORM
func (r *BlogBlockRepository) Create(ctx context.Context, blogBlock *models.BlogBlock) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Set timestamps
	now := time.Now()
	blogBlock.CreatedAt = now
	blogBlock.UpdatedAt = now

	if err := r.gormDB.WithContext(ctx).Create(blogBlock).Error; err != nil {
		return fmt.Errorf("failed to create blog block: %w", err)
	}

	return nil
}

// GetByID retrieves a blog block by its ID
func (r *BlogBlockRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogBlock, error) {
	var blogBlock models.BlogBlock
	query := `SELECT id, tenant_id, website_id, name, code, active, created_at, updated_at FROM blog_blocks WHERE tenant_id = ? AND website_id = ? AND id = ?`

	err := r.db.GetContext(ctx, &blogBlock, query, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog block not found")
		}
		return nil, fmt.Errorf("failed to get blog block: %w", err)
	}

	return &blogBlock, nil
}

// GetByCode retrieves a blog block by its code
func (r *BlogBlockRepository) GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*models.BlogBlock, error) {
	var blogBlock models.BlogBlock
	query := `SELECT id, tenant_id, website_id, name, code, active, created_at, updated_at FROM blog_blocks WHERE tenant_id = ? AND website_id = ? AND code = ?`

	err := r.db.GetContext(ctx, &blogBlock, query, tenantID, websiteID, code)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog block not found")
		}
		return nil, fmt.Errorf("failed to get blog block: %w", err)
	}

	return &blogBlock, nil
}

// Update updates an existing blog block
func (r *BlogBlockRepository) Update(ctx context.Context, blogBlock *models.BlogBlock) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Update operation")
	}

	// Set updated timestamp
	blogBlock.UpdatedAt = time.Now()

	if err := r.gormDB.WithContext(ctx).Save(blogBlock).Error; err != nil {
		return fmt.Errorf("failed to update blog block: %w", err)
	}

	return nil
}

// Delete removes a blog block from the database
func (r *BlogBlockRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	query := `DELETE FROM blog_blocks WHERE tenant_id = ? AND website_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, websiteID, id)
	if err != nil {
		return fmt.Errorf("failed to delete blog block: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog block not found")
	}

	return nil
}

// List retrieves a paginated list of blog blocks
func (r *BlogBlockRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogBlockRequest) ([]*models.BlogBlock, string, bool, error) {
	// Set default values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}

	// Build WHERE clause
	var whereConditions []string
	var args []interface{}

	// Add tenant and website filters
	whereConditions = append(whereConditions, "tenant_id = ?", "website_id = ?")
	args = append(args, tenantID, websiteID)

	if req.Search != "" {
		whereConditions = append(whereConditions, "(name LIKE ? OR code LIKE ?)")
		searchTerm := "%" + req.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	if req.Active != nil {
		whereConditions = append(whereConditions, "active = ?")
		args = append(args, *req.Active)
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// Build ORDER BY clause
	sortDirection := "ASC"
	if req.SortDesc != nil && *req.SortDesc {
		sortDirection = "DESC"
	}
	orderClause := fmt.Sprintf("ORDER BY %s %s", req.SortBy, sortDirection)

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM blog_blocks %s", whereClause)
	var total int64
	err := r.db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to count blog blocks: %w", err)
	}

	// Calculate pagination
	offset := (req.Page - 1) * req.Limit
	hasMore := int64(offset+req.Limit) < total

	// Build main query
	query := fmt.Sprintf(`
		SELECT id, name, code, active, created_at, updated_at 
		FROM blog_blocks 
		%s 
		%s 
		LIMIT ? OFFSET ?
	`, whereClause, orderClause)

	args = append(args, req.Limit, offset)

	var blogBlocks []*models.BlogBlock
	err = r.db.SelectContext(ctx, &blogBlocks, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list blog blocks: %w", err)
	}

	// Generate cursor for pagination
	cursor := ""
	if len(blogBlocks) > 0 {
		lastBlock := blogBlocks[len(blogBlocks)-1]
		cursor = strconv.FormatUint(uint64(lastBlock.ID), 10)
	}

	return blogBlocks, cursor, hasMore, nil
}