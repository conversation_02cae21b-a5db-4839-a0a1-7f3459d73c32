package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogBlockPostRepository implements the repository.BlogBlockPostRepository interface
type BlogBlockPostRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewBlogBlockPostRepository creates a new instance of BlogBlockPostRepository
func NewBlogBlockPostRepository(db *sqlx.DB) repository.BlogBlockPostRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for blog block post repository: %v\n", err)
		gormDB = nil
	}

	return &BlogBlockPostRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create inserts a new blog block post into the database using GORM
func (r *BlogBlockPostRepository) Create(ctx context.Context, blogBlockPost *models.BlogBlockPost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Set timestamps
	now := time.Now()
	blogBlockPost.CreatedAt = now
	blogBlockPost.UpdatedAt = now

	if err := r.gormDB.WithContext(ctx).Create(blogBlockPost).Error; err != nil {
		return fmt.Errorf("failed to create blog block post: %w", err)
	}

	return nil
}

// GetByID retrieves a blog block post by its ID
func (r *BlogBlockPostRepository) GetByID(ctx context.Context, id uint) (*models.BlogBlockPost, error) {
	var blogBlockPost models.BlogBlockPost
	query := `SELECT id, blog_block_id, post_id, priority, created_at, updated_at FROM blog_block_posts WHERE id = ?`

	err := r.db.GetContext(ctx, &blogBlockPost, query, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog block post not found")
		}
		return nil, fmt.Errorf("failed to get blog block post: %w", err)
	}

	return &blogBlockPost, nil
}

// Update updates an existing blog block post
func (r *BlogBlockPostRepository) Update(ctx context.Context, blogBlockPost *models.BlogBlockPost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Update operation")
	}

	// Set updated timestamp
	blogBlockPost.UpdatedAt = time.Now()

	if err := r.gormDB.WithContext(ctx).Save(blogBlockPost).Error; err != nil {
		return fmt.Errorf("failed to update blog block post: %w", err)
	}

	return nil
}

// Delete removes a blog block post from the database
func (r *BlogBlockPostRepository) Delete(ctx context.Context, id uint) error {
	query := `DELETE FROM blog_block_posts WHERE id = ?`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete blog block post: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog block post not found")
	}

	return nil
}

// DeleteByBlogBlockAndPost removes a blog block post by blog block ID and post ID
func (r *BlogBlockPostRepository) DeleteByBlogBlockAndPost(ctx context.Context, blogBlockID, postID uint) error {
	query := `DELETE FROM blog_block_posts WHERE blog_block_id = ? AND post_id = ?`

	result, err := r.db.ExecContext(ctx, query, blogBlockID, postID)
	if err != nil {
		return fmt.Errorf("failed to delete blog block post: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog block post not found")
	}

	return nil
}

// BlogBlockPostWithPost represents a blog block post with post details
type BlogBlockPostWithPost struct {
	ID          uint      `db:"id"`
	BlogBlockID uint      `db:"blog_block_id"`
	PostID      uint      `db:"post_id"`
	Priority    int       `db:"priority"`
	CreatedAt   time.Time `db:"created_at"`
	UpdatedAt   time.Time `db:"updated_at"`
	PostTitle   *string   `db:"post_title"`
	PostStatus  *string   `db:"post_status"`
}

// List retrieves a paginated list of blog block posts
func (r *BlogBlockPostRepository) List(ctx context.Context, blogBlockID uint, req request.ListBlogBlockPostsRequest) ([]*models.BlogBlockPost, string, bool, error) {
	// Set default values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.SortBy == "" {
		req.SortBy = "priority"
	}

	// Build ORDER BY clause
	sortDirection := "ASC"
	if req.SortDesc != nil && *req.SortDesc {
		sortDirection = "DESC"
	}
	orderClause := fmt.Sprintf("ORDER BY bbp.%s %s", req.SortBy, sortDirection)

	// Count total records
	countQuery := "SELECT COUNT(*) FROM blog_block_posts WHERE blog_block_id = ?"
	var total int64
	err := r.db.GetContext(ctx, &total, countQuery, blogBlockID)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to count blog block posts: %w", err)
	}

	// Calculate pagination
	offset := (req.Page - 1) * req.Limit
	hasMore := int64(offset+req.Limit) < total

	// Build main query with LEFT JOIN to get post title and status
	query := fmt.Sprintf(`
		SELECT 
			bbp.id, 
			bbp.blog_block_id, 
			bbp.post_id, 
			bbp.priority, 
			bbp.created_at, 
			bbp.updated_at,
			bp.title as post_title,
			bp.status as post_status
		FROM blog_block_posts bbp 
		LEFT JOIN blog_posts bp ON bbp.post_id = bp.post_id
		WHERE bbp.blog_block_id = ? 
		%s 
		LIMIT ? OFFSET ?
	`, orderClause)

	var blogBlockPostsWithPost []*BlogBlockPostWithPost
	err = r.db.SelectContext(ctx, &blogBlockPostsWithPost, query, blogBlockID, req.Limit, offset)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list blog block posts: %w", err)
	}

	// Convert to BlogBlockPost models and populate Post information
	var blogBlockPosts []*models.BlogBlockPost
	for _, bbpWithPost := range blogBlockPostsWithPost {
		bbp := &models.BlogBlockPost{
			ID:          bbpWithPost.ID,
			BlogBlockID: bbpWithPost.BlogBlockID,
			PostID:      bbpWithPost.PostID,
			Priority:    bbpWithPost.Priority,
			CreatedAt:   bbpWithPost.CreatedAt,
			UpdatedAt:   bbpWithPost.UpdatedAt,
		}

		// Populate Post information if available
		if bbpWithPost.PostTitle != nil || bbpWithPost.PostStatus != nil {
			bbp.Post = &models.Post{
				PostID: bbpWithPost.PostID,
			}
			if bbpWithPost.PostTitle != nil {
				bbp.Post.Title = *bbpWithPost.PostTitle
			}
			if bbpWithPost.PostStatus != nil {
				bbp.Post.Status = *bbpWithPost.PostStatus
			}
		}

		blogBlockPosts = append(blogBlockPosts, bbp)
	}

	// Generate cursor for pagination
	cursor := ""
	if len(blogBlockPosts) > 0 {
		lastPost := blogBlockPosts[len(blogBlockPosts)-1]
		cursor = strconv.FormatUint(uint64(lastPost.ID), 10)
	}

	return blogBlockPosts, cursor, hasMore, nil
}

// GetByBlogBlockID retrieves all posts in a blog block
func (r *BlogBlockPostRepository) GetByBlogBlockID(ctx context.Context, blogBlockID uint) ([]*models.BlogBlockPost, error) {
	query := `
		SELECT 
			bbp.id, 
			bbp.blog_block_id, 
			bbp.post_id, 
			bbp.priority, 
			bbp.created_at, 
			bbp.updated_at,
			bp.title as post_title,
			bp.status as post_status
		FROM blog_block_posts bbp 
		LEFT JOIN blog_posts bp ON bbp.post_id = bp.post_id
		WHERE bbp.blog_block_id = ? 
		ORDER BY bbp.priority ASC, bbp.created_at ASC
	`

	var blogBlockPostsWithPost []*BlogBlockPostWithPost
	err := r.db.SelectContext(ctx, &blogBlockPostsWithPost, query, blogBlockID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog block posts: %w", err)
	}

	// Convert to BlogBlockPost models and populate Post information
	var blogBlockPosts []*models.BlogBlockPost
	for _, bbpWithPost := range blogBlockPostsWithPost {
		bbp := &models.BlogBlockPost{
			ID:          bbpWithPost.ID,
			BlogBlockID: bbpWithPost.BlogBlockID,
			PostID:      bbpWithPost.PostID,
			Priority:    bbpWithPost.Priority,
			CreatedAt:   bbpWithPost.CreatedAt,
			UpdatedAt:   bbpWithPost.UpdatedAt,
		}

		// Populate Post information if available
		if bbpWithPost.PostTitle != nil || bbpWithPost.PostStatus != nil {
			bbp.Post = &models.Post{
				PostID: bbpWithPost.PostID,
			}
			if bbpWithPost.PostTitle != nil {
				bbp.Post.Title = *bbpWithPost.PostTitle
			}
			if bbpWithPost.PostStatus != nil {
				bbp.Post.Status = *bbpWithPost.PostStatus
			}
		}

		blogBlockPosts = append(blogBlockPosts, bbp)
	}

	return blogBlockPosts, nil
}

// BatchCreate inserts multiple blog block posts into the database
func (r *BlogBlockPostRepository) BatchCreate(ctx context.Context, blogBlockPosts []*models.BlogBlockPost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for BatchCreate operation")
	}

	if len(blogBlockPosts) == 0 {
		return nil
	}

	// Set timestamps for all posts
	now := time.Now()
	for _, post := range blogBlockPosts {
		post.CreatedAt = now
		post.UpdatedAt = now
	}

	if err := r.gormDB.WithContext(ctx).CreateInBatches(blogBlockPosts, 100).Error; err != nil {
		return fmt.Errorf("failed to batch create blog block posts: %w", err)
	}

	return nil
}

// CheckExists checks if a blog block post relationship exists
func (r *BlogBlockPostRepository) CheckExists(ctx context.Context, blogBlockID, postID uint) (bool, error) {
	query := `SELECT COUNT(*) FROM blog_block_posts WHERE blog_block_id = ? AND post_id = ?`

	var count int
	err := r.db.GetContext(ctx, &count, query, blogBlockID, postID)
	if err != nil {
		return false, fmt.Errorf("failed to check blog block post existence: %w", err)
	}

	return count > 0, nil
}