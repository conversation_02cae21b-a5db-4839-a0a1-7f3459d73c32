package mysql

import (
	"context"
	"fmt"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/blog/repository"
)

// statisticRepository implements the StatisticRepository interface
type statisticRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewStatisticRepository creates a new statistic repository instance
func NewStatisticRepository(db *sqlx.DB, gormDB *gorm.DB) repository.StatisticRepository {
	return &statisticRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// GetStatusStatistics retrieves post count statistics by status for a tenant and website
func (r *statisticRepository) GetStatusStatistics(ctx context.Context, tenantID, websiteID uint) (map[string]int64, error) {
	query := `
		SELECT 
			status,
			COUNT(*) as count
		FROM blog_posts 
		WHERE tenant_id = ? AND website_id = ?
		GROUP BY status
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to query status statistics: %w", err)
	}
	defer rows.Close()

	statistics := make(map[string]int64)
	var total int64

	// Initialize all possible statuses with 0
	statuses := []string{
		"draft", "pending", "approved", "schedule", "published",
		"return", "trash", "storage", "request", "auto", "delete",
	}
	for _, status := range statuses {
		statistics[status] = 0
	}

	// Populate with actual counts
	for rows.Next() {
		var status string
		var count int64
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("failed to scan status statistics: %w", err)
		}
		statistics[status] = count
		total += count
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating status statistics: %w", err)
	}

	// Add total count
	statistics["total"] = total

	return statistics, nil
}
