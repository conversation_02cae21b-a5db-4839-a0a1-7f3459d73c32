package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogTimelinePostRepository implements the repository.BlogTimelinePostRepository interface
type BlogTimelinePostRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewBlogTimelinePostRepository creates a new instance of BlogTimelinePostRepository
func NewBlogTimelinePostRepository(db *sqlx.DB) repository.BlogTimelinePostRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for blog timeline post repository: %v\n", err)
		gormDB = nil
	}

	return &BlogTimelinePostRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create inserts a new blog timeline post into the database using GORM
func (r *BlogTimelinePostRepository) Create(ctx context.Context, blogTimelinePost *models.BlogTimelinePost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Set timestamps
	now := time.Now()
	blogTimelinePost.CreatedAt = now
	blogTimelinePost.UpdatedAt = now

	if err := r.gormDB.WithContext(ctx).Create(blogTimelinePost).Error; err != nil {
		return fmt.Errorf("failed to create blog timeline post: %w", err)
	}

	return nil
}

// GetByID retrieves a blog timeline post by its ID
func (r *BlogTimelinePostRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogTimelinePost, error) {
	var blogTimelinePost models.BlogTimelinePost
	query := `SELECT id, tenant_id, website_id, blog_timeline_id, post_id, priority, created_at, updated_at FROM blog_timeline_posts WHERE tenant_id = ? AND website_id = ? AND id = ?`

	err := r.db.GetContext(ctx, &blogTimelinePost, query, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog timeline post not found")
		}
		return nil, fmt.Errorf("failed to get blog timeline post: %w", err)
	}

	return &blogTimelinePost, nil
}

// Update updates an existing blog timeline post
func (r *BlogTimelinePostRepository) Update(ctx context.Context, blogTimelinePost *models.BlogTimelinePost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Update operation")
	}

	// Set updated timestamp
	blogTimelinePost.UpdatedAt = time.Now()

	if err := r.gormDB.WithContext(ctx).Save(blogTimelinePost).Error; err != nil {
		return fmt.Errorf("failed to update blog timeline post: %w", err)
	}

	return nil
}

// Delete removes a blog timeline post from the database
func (r *BlogTimelinePostRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	query := `DELETE FROM blog_timeline_posts WHERE tenant_id = ? AND website_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, websiteID, id)
	if err != nil {
		return fmt.Errorf("failed to delete blog timeline post: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog timeline post not found")
	}

	return nil
}

// DeleteByBlogTimelineAndPost removes a blog timeline post by blog timeline ID and post ID
func (r *BlogTimelinePostRepository) DeleteByBlogTimelineAndPost(ctx context.Context, blogTimelineID, postID uint) error {
	query := `DELETE FROM blog_timeline_posts WHERE blog_timeline_id = ? AND post_id = ?`

	result, err := r.db.ExecContext(ctx, query, blogTimelineID, postID)
	if err != nil {
		return fmt.Errorf("failed to delete blog timeline post: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog timeline post not found")
	}

	return nil
}

// BlogTimelinePostWithPost represents a blog timeline post with post details
type BlogTimelinePostWithPost struct {
	ID             uint      `db:"id"`
	TenantID       uint      `db:"tenant_id"`
	WebsiteID      uint      `db:"website_id"`
	BlogTimelineID uint      `db:"blog_timeline_id"`
	PostID         uint      `db:"post_id"`
	Priority       int       `db:"priority"`
	CreatedAt      time.Time `db:"created_at"`
	UpdatedAt      time.Time `db:"updated_at"`
	PostTitle      *string   `db:"post_title"`
	PostStatus     *string   `db:"post_status"`
}

// List retrieves a paginated list of blog timeline posts
func (r *BlogTimelinePostRepository) List(ctx context.Context, blogTimelineID uint, req request.ListBlogTimelinePostsRequest) ([]*models.BlogTimelinePost, string, bool, error) {
	// Set default values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.SortBy == "" {
		req.SortBy = "priority"
	}

	// Build ORDER BY clause
	sortDirection := "ASC"
	if req.SortDesc != nil && *req.SortDesc {
		sortDirection = "DESC"
	}
	orderClause := fmt.Sprintf("ORDER BY btp.%s %s", req.SortBy, sortDirection)

	// Count total records
	countQuery := "SELECT COUNT(*) FROM blog_timeline_posts WHERE blog_timeline_id = ?"
	var total int64
	err := r.db.GetContext(ctx, &total, countQuery, blogTimelineID)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to count blog timeline posts: %w", err)
	}

	// Calculate pagination
	offset := (req.Page - 1) * req.Limit
	hasMore := int64(offset+req.Limit) < total

	// Build main query with LEFT JOIN to get post title and status
	query := fmt.Sprintf(`
		SELECT 
			btp.id, 
			btp.tenant_id,
			btp.website_id,
			btp.blog_timeline_id, 
			btp.post_id, 
			btp.priority, 
			btp.created_at, 
			btp.updated_at,
			bp.title as post_title,
			bp.status as post_status
		FROM blog_timeline_posts btp 
		LEFT JOIN blog_posts bp ON btp.post_id = bp.post_id
		WHERE btp.blog_timeline_id = ? 
		%s 
		LIMIT ? OFFSET ?
	`, orderClause)

	var blogTimelinePostsWithPost []*BlogTimelinePostWithPost
	err = r.db.SelectContext(ctx, &blogTimelinePostsWithPost, query, blogTimelineID, req.Limit, offset)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list blog timeline posts: %w", err)
	}

	// Convert to BlogTimelinePost models and populate Post information
	var blogTimelinePosts []*models.BlogTimelinePost
	for _, btpWithPost := range blogTimelinePostsWithPost {
		btp := &models.BlogTimelinePost{
			ID:             btpWithPost.ID,
			TenantID:       btpWithPost.TenantID,
			WebsiteID:      btpWithPost.WebsiteID,
			BlogTimelineID: btpWithPost.BlogTimelineID,
			PostID:         btpWithPost.PostID,
			Priority:       btpWithPost.Priority,
			CreatedAt:      btpWithPost.CreatedAt,
			UpdatedAt:      btpWithPost.UpdatedAt,
		}

		// Populate Post information if available
		if btpWithPost.PostTitle != nil || btpWithPost.PostStatus != nil {
			btp.Post = &models.Post{
				PostID: btpWithPost.PostID,
			}
			if btpWithPost.PostTitle != nil {
				btp.Post.Title = *btpWithPost.PostTitle
			}
			if btpWithPost.PostStatus != nil {
				btp.Post.Status = *btpWithPost.PostStatus
			}
		}

		blogTimelinePosts = append(blogTimelinePosts, btp)
	}

	// Generate cursor for pagination
	cursor := ""
	if len(blogTimelinePosts) > 0 {
		lastPost := blogTimelinePosts[len(blogTimelinePosts)-1]
		cursor = strconv.FormatUint(uint64(lastPost.ID), 10)
	}

	return blogTimelinePosts, cursor, hasMore, nil
}

// GetByBlogTimelineID retrieves all posts in a blog timeline
func (r *BlogTimelinePostRepository) GetByBlogTimelineID(ctx context.Context, blogTimelineID uint) ([]*models.BlogTimelinePost, error) {
	query := `
		SELECT 
			btp.id, 
			btp.tenant_id,
			btp.website_id,
			btp.blog_timeline_id, 
			btp.post_id, 
			btp.priority, 
			btp.created_at, 
			btp.updated_at,
			bp.title as post_title,
			bp.status as post_status
		FROM blog_timeline_posts btp 
		LEFT JOIN blog_posts bp ON btp.post_id = bp.post_id
		WHERE btp.blog_timeline_id = ? 
		ORDER BY btp.priority ASC, btp.created_at ASC
	`

	var blogTimelinePostsWithPost []*BlogTimelinePostWithPost
	err := r.db.SelectContext(ctx, &blogTimelinePostsWithPost, query, blogTimelineID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog timeline posts: %w", err)
	}

	// Convert to BlogTimelinePost models and populate Post information
	var blogTimelinePosts []*models.BlogTimelinePost
	for _, btpWithPost := range blogTimelinePostsWithPost {
		btp := &models.BlogTimelinePost{
			ID:             btpWithPost.ID,
			TenantID:       btpWithPost.TenantID,
			WebsiteID:      btpWithPost.WebsiteID,
			BlogTimelineID: btpWithPost.BlogTimelineID,
			PostID:         btpWithPost.PostID,
			Priority:       btpWithPost.Priority,
			CreatedAt:      btpWithPost.CreatedAt,
			UpdatedAt:      btpWithPost.UpdatedAt,
		}

		// Populate Post information if available
		if btpWithPost.PostTitle != nil || btpWithPost.PostStatus != nil {
			btp.Post = &models.Post{
				PostID: btpWithPost.PostID,
			}
			if btpWithPost.PostTitle != nil {
				btp.Post.Title = *btpWithPost.PostTitle
			}
			if btpWithPost.PostStatus != nil {
				btp.Post.Status = *btpWithPost.PostStatus
			}
		}

		blogTimelinePosts = append(blogTimelinePosts, btp)
	}

	return blogTimelinePosts, nil
}

// BatchCreate inserts multiple blog timeline posts into the database
func (r *BlogTimelinePostRepository) BatchCreate(ctx context.Context, blogTimelinePosts []*models.BlogTimelinePost) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for BatchCreate operation")
	}

	if len(blogTimelinePosts) == 0 {
		return nil
	}

	// Set timestamps for all posts
	now := time.Now()
	for _, post := range blogTimelinePosts {
		post.CreatedAt = now
		post.UpdatedAt = now
	}

	if err := r.gormDB.WithContext(ctx).CreateInBatches(blogTimelinePosts, 100).Error; err != nil {
		return fmt.Errorf("failed to batch create blog timeline posts: %w", err)
	}

	return nil
}

// CheckExists checks if a blog timeline post relationship exists
func (r *BlogTimelinePostRepository) CheckExists(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint) (bool, error) {
	query := `SELECT COUNT(*) FROM blog_timeline_posts WHERE tenant_id = ? AND website_id = ? AND blog_timeline_id = ? AND post_id = ?`

	var count int
	err := r.db.GetContext(ctx, &count, query, tenantID, websiteID, blogTimelineID, postID)
	if err != nil {
		return false, fmt.Errorf("failed to check blog timeline post existence: %w", err)
	}

	return count > 0, nil
}
