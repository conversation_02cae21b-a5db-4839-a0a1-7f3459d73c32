package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
)

// BlogTimelineRepository implements the repository.BlogTimelineRepository interface
type BlogTimelineRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewBlogTimelineRepository creates a new instance of BlogTimelineRepository
func NewBlogTimelineRepository(db *sqlx.DB) repository.BlogTimelineRepository {
	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		// If GORM fails, log error but continue with sqlx only
		fmt.Printf("Warning: Failed to initialize GORM for blog timeline repository: %v\n", err)
		gormDB = nil
	}

	return &BlogTimelineRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create inserts a new blog timeline into the database using GORM
func (r *BlogTimelineRepository) Create(ctx context.Context, blogTimeline *models.BlogTimeline) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Create operation")
	}

	// Set timestamps
	now := time.Now()
	blogTimeline.CreatedAt = now
	blogTimeline.UpdatedAt = now

	if err := r.gormDB.WithContext(ctx).Create(blogTimeline).Error; err != nil {
		return fmt.Errorf("failed to create blog timeline: %w", err)
	}

	return nil
}

// GetByID retrieves a blog timeline by its ID
func (r *BlogTimelineRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogTimeline, error) {
	var blogTimeline models.BlogTimeline
	query := `SELECT id, tenant_id, website_id, name, code, active, created_at, updated_at FROM blog_timelines WHERE tenant_id = ? AND website_id = ? AND id = ?`

	err := r.db.GetContext(ctx, &blogTimeline, query, tenantID, websiteID, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog timeline not found")
		}
		return nil, fmt.Errorf("failed to get blog timeline: %w", err)
	}

	return &blogTimeline, nil
}

// GetByCode retrieves a blog timeline by its code
func (r *BlogTimelineRepository) GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*models.BlogTimeline, error) {
	var blogTimeline models.BlogTimeline
	query := `SELECT id, tenant_id, website_id, name, code, active, created_at, updated_at FROM blog_timelines WHERE tenant_id = ? AND website_id = ? AND code = ?`

	err := r.db.GetContext(ctx, &blogTimeline, query, tenantID, websiteID, code)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("blog timeline not found")
		}
		return nil, fmt.Errorf("failed to get blog timeline: %w", err)
	}

	return &blogTimeline, nil
}

// Update updates an existing blog timeline
func (r *BlogTimelineRepository) Update(ctx context.Context, blogTimeline *models.BlogTimeline) error {
	if r.gormDB == nil {
		return fmt.Errorf("GORM not available for Update operation")
	}

	// Set updated timestamp
	blogTimeline.UpdatedAt = time.Now()

	if err := r.gormDB.WithContext(ctx).Save(blogTimeline).Error; err != nil {
		return fmt.Errorf("failed to update blog timeline: %w", err)
	}

	return nil
}

// Delete removes a blog timeline from the database
func (r *BlogTimelineRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	query := `DELETE FROM blog_timelines WHERE tenant_id = ? AND website_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, websiteID, id)
	if err != nil {
		return fmt.Errorf("failed to delete blog timeline: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("blog timeline not found")
	}

	return nil
}

// List retrieves a paginated list of blog timelines
func (r *BlogTimelineRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogTimelineRequest) ([]*models.BlogTimeline, string, bool, error) {
	// Set default values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}

	// Build WHERE clause
	var whereConditions []string
	var args []interface{}

	// Add tenant_id and website_id conditions
	whereConditions = append(whereConditions, "tenant_id = ? AND website_id = ?")
	args = append(args, tenantID, websiteID)

	if req.Search != "" {
		whereConditions = append(whereConditions, "(name LIKE ? OR code LIKE ?)")
		searchTerm := "%" + req.Search + "%"
		args = append(args, searchTerm, searchTerm)
	}

	if req.Active != nil {
		whereConditions = append(whereConditions, "active = ?")
		args = append(args, *req.Active)
	}

	whereClause := "WHERE " + strings.Join(whereConditions, " AND ")

	// Build ORDER BY clause
	sortDirection := "ASC"
	if req.SortDesc != nil && *req.SortDesc {
		sortDirection = "DESC"
	}
	orderClause := fmt.Sprintf("ORDER BY %s %s", req.SortBy, sortDirection)

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM blog_timelines %s", whereClause)
	var total int64
	err := r.db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to count blog timelines: %w", err)
	}

	// Calculate pagination
	offset := (req.Page - 1) * req.Limit
	hasMore := int64(offset+req.Limit) < total

	// Build main query
	query := fmt.Sprintf(`
		SELECT id, tenant_id, website_id, name, code, active, created_at, updated_at 
		FROM blog_timelines 
		%s 
		%s 
		LIMIT ? OFFSET ?
	`, whereClause, orderClause)

	args = append(args, req.Limit, offset)

	var blogTimelines []*models.BlogTimeline
	err = r.db.SelectContext(ctx, &blogTimelines, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list blog timelines: %w", err)
	}

	// Generate cursor for pagination
	cursor := ""
	if len(blogTimelines) > 0 {
		lastTimeline := blogTimelines[len(blogTimelines)-1]
		cursor = strconv.FormatUint(uint64(lastTimeline.ID), 10)
	}

	return blogTimelines, cursor, hasMore, nil
}