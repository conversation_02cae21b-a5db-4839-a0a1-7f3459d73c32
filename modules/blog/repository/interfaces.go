package repository

import (
	"context"
	"time"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/models"
)

// AuthorRepository interface defines methods for author data access
type AuthorRepository interface {
	Create(ctx context.Context, author *models.BlogAuthor) error
	GetByID(ctx context.Context, tenantID, websiteID, authorID uint) (*models.BlogAuthor, error)
	GetByUserID(ctx context.Context, tenantID, websiteID, userID uint) (*models.BlogAuthor, error)
	Update(ctx context.Context, author *models.BlogAuthor) error
	Delete(ctx context.Context, tenantID, websiteID, authorID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListAuthorRequest) ([]*models.BlogAuthor, string, bool, error)
}

// TagRepository interface defines methods for tag data access
type TagRepository interface {
	Create(ctx context.Context, tag *models.Tag) error
	GetByID(ctx context.Context, tenantID, websiteID, tagID uint) (*models.Tag, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.Tag, error)
	Update(ctx context.Context, tag *models.Tag) error
	Delete(ctx context.Context, tenantID, websiteID, tagID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListTagRequest) ([]*models.Tag, string, bool, error)
	GetTagsWithPostCount(ctx context.Context, tenantID, websiteID uint) ([]*models.Tag, error)
}

// CategoryRepository interface defines methods for category data access
type CategoryRepository interface {
	Create(ctx context.Context, category *models.Category) error
	GetByID(ctx context.Context, tenantID, websiteID uint, categoryID uint) (*models.Category, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.Category, error)
	Update(ctx context.Context, category *models.Category) error
	Delete(ctx context.Context, tenantID, websiteID uint, categoryID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListCategoryRequest) ([]*models.Category, string, bool, error)
	GetTree(ctx context.Context, tenantID, websiteID uint) ([]*models.Category, error)
	GetSubtree(ctx context.Context, tenantID, websiteID uint, categoryID uint) ([]*models.Category, error)
	MoveNode(ctx context.Context, tenantID, websiteID uint, nodeID uint, targetID uint, position string) error
	MoveNodeToRoot(ctx context.Context, tenantID, websiteID uint, nodeID uint, position int) error
	UpdatePosition(ctx context.Context, tenantID, websiteID uint, categoryID uint, position int) error
}

// PostRepository interface defines methods for post data access
type PostRepository interface {
	Create(ctx context.Context, post *models.Post) error
	GetByID(ctx context.Context, tenantID, websiteID uint, postID uint) (*models.Post, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.Post, error)
	Update(ctx context.Context, post *models.Post) error
	Delete(ctx context.Context, tenantID, websiteID uint, postID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListPostRequest) ([]*models.Post, string, bool, error)
	GetPostCategories(ctx context.Context, tenantID, websiteID uint, postID uint) ([]uint, error)
	GetPostTags(ctx context.Context, tenantID, websiteID uint, postID uint) ([]uint, error)
}

// ScheduleRepository interface defines methods for schedule data access
type ScheduleRepository interface {
	Create(ctx context.Context, schedule *models.BlogSchedule) error
	GetByID(ctx context.Context, tenantID, websiteID uint, scheduleID uint) (*models.BlogSchedule, error)
	GetByIDInternal(ctx context.Context, scheduleID uint) (*models.BlogSchedule, error) // For internal operations without tenant/website filtering
	Update(ctx context.Context, schedule *models.BlogSchedule) error
	Delete(ctx context.Context, tenantID, websiteID uint, scheduleID uint) error
	List(ctx context.Context, tenantID, websiteID uint, cursor *string, limit int, offset int) ([]*models.BlogSchedule, int64, error)
	GetPendingSchedules(ctx context.Context, limit int) ([]*models.BlogSchedule, error)
	GetByBlogID(ctx context.Context, tenantID, websiteID uint, blogID uint) (*models.BlogSchedule, error)
	GetByBlogAndTime(ctx context.Context, tenantID, websiteID uint, blogID uint, scheduledAt time.Time) (*models.BlogSchedule, error)
	CleanupOldSchedules(ctx context.Context, retentionDays int, batchSize int, tenantID, websiteID uint) (int64, error)
	GetScheduledPosts(ctx context.Context, beforeTime time.Time, batchSize int) ([]*models.BlogSchedule, error)
}

// RelatedPostRepository interface defines methods for related post data access
type RelatedPostRepository interface {
	Create(ctx context.Context, relatedPost *models.RelatedPost) error
	GetByID(ctx context.Context, tenantID, websiteID uint, relationID uint) (*models.RelatedPost, error)
	Update(ctx context.Context, relatedPost *models.RelatedPost) error
	Delete(ctx context.Context, tenantID, websiteID uint, relationID uint) error
	DeleteByPosts(ctx context.Context, tenantID, websiteID uint, postID uint, relatedPostID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListRelatedPostRequest) ([]*models.RelatedPost, string, bool, error)
	GetRelatedPosts(ctx context.Context, tenantID, websiteID uint, postID uint, limit int) ([]*models.RelatedPost, error)
	GetRelatedPostsWithCursor(ctx context.Context, tenantID, websiteID uint, postID uint, cursorID uint, limit int) ([]*models.RelatedPost, error)
	BulkCreate(ctx context.Context, relatedPosts []*models.RelatedPost) error
	DeleteAllByPost(ctx context.Context, tenantID, websiteID uint, postID uint) error
	CheckRelationExists(ctx context.Context, tenantID, websiteID, postID, relatedPostID uint) (bool, error)
}

// StatisticRepository interface defines methods for statistic data access
type StatisticRepository interface {
	GetStatusStatistics(ctx context.Context, tenantID, websiteID uint) (map[string]int64, error)
}

// BlogBlockRepository interface defines methods for blog block data access
type BlogBlockRepository interface {
	Create(ctx context.Context, blogBlock *models.BlogBlock) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogBlock, error)
	GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*models.BlogBlock, error)
	Update(ctx context.Context, blogBlock *models.BlogBlock) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogBlockRequest) ([]*models.BlogBlock, string, bool, error)
}

// BlogBlockPostRepository interface defines methods for blog block post data access
type BlogBlockPostRepository interface {
	Create(ctx context.Context, blogBlockPost *models.BlogBlockPost) error
	GetByID(ctx context.Context, id uint) (*models.BlogBlockPost, error)
	Update(ctx context.Context, blogBlockPost *models.BlogBlockPost) error
	Delete(ctx context.Context, id uint) error
	DeleteByBlogBlockAndPost(ctx context.Context, blogBlockID, postID uint) error
	List(ctx context.Context, blogBlockID uint, req request.ListBlogBlockPostsRequest) ([]*models.BlogBlockPost, string, bool, error)
	GetByBlogBlockID(ctx context.Context, blogBlockID uint) ([]*models.BlogBlockPost, error)
	BatchCreate(ctx context.Context, blogBlockPosts []*models.BlogBlockPost) error
	CheckExists(ctx context.Context, blogBlockID, postID uint) (bool, error)
}

// BlogTimelineRepository interface defines methods for blog timeline data access
type BlogTimelineRepository interface {
	Create(ctx context.Context, blogTimeline *models.BlogTimeline) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogTimeline, error)
	GetByCode(ctx context.Context, tenantID, websiteID uint, code string) (*models.BlogTimeline, error)
	Update(ctx context.Context, blogTimeline *models.BlogTimeline) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListBlogTimelineRequest) ([]*models.BlogTimeline, string, bool, error)
}

// BlogTimelinePostRepository interface defines methods for blog timeline post data access
type BlogTimelinePostRepository interface {
	Create(ctx context.Context, blogTimelinePost *models.BlogTimelinePost) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.BlogTimelinePost, error)
	Update(ctx context.Context, blogTimelinePost *models.BlogTimelinePost) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error
	DeleteByBlogTimelineAndPost(ctx context.Context, blogTimelineID, postID uint) error
	List(ctx context.Context, blogTimelineID uint, req request.ListBlogTimelinePostsRequest) ([]*models.BlogTimelinePost, string, bool, error)
	GetByBlogTimelineID(ctx context.Context, blogTimelineID uint) ([]*models.BlogTimelinePost, error)
	BatchCreate(ctx context.Context, blogTimelinePosts []*models.BlogTimelinePost) error
	CheckExists(ctx context.Context, tenantID, websiteID, blogTimelineID, postID uint) (bool, error)
}
