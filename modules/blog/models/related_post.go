package models

import "time"

// RelatedPost represents a relationship between two blog posts
type RelatedPost struct {
	ID              uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID        uint      `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id"`
	PostID          uint      `db:"post_id" json:"post_id" gorm:"column:post_id"`
	RelatedPostID   uint      `db:"related_post_id" json:"related_post_id" gorm:"column:related_post_id"`
	Priority        uint      `db:"priority" json:"priority" gorm:"column:priority"`
	IsBidirectional bool      `db:"is_bidirectional" json:"is_bidirectional" gorm:"column:is_bidirectional"`
	CreatedAt       time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`
	CreatedBy       uint      `db:"created_by" json:"created_by" gorm:"column:created_by"`

	// Virtual fields for relationships
	Post        *Post `db:"-" gorm:"-" json:"post,omitempty"`
	RelatedPost *Post `db:"-" gorm:"-" json:"related_post,omitempty"`
	Creator     *User `db:"-" gorm:"-" json:"creator,omitempty"`
}

// TableName returns the table name for the RelatedPost model
func (RelatedPost) TableName() string {
	return "blog_related_posts"
}
