package models

import (
	"time"
)

type BlogBlock struct {
	ID        uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID  uint      `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id"`
	WebsiteID uint      `db:"website_id" json:"website_id" gorm:"column:website_id"`
	Name      string    `db:"name" json:"name" gorm:"column:name"`
	Code      string    `db:"code" json:"code" gorm:"column:code"`
	Active    bool      `db:"active" json:"active" gorm:"column:active"`
	CreatedAt time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`

	// Relations
	Posts []BlogBlockPost `db:"-" gorm:"-" json:"posts,omitempty"`
}

type BlogBlockPost struct {
	ID          uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	BlogBlockID uint      `db:"blog_block_id" json:"blog_block_id" gorm:"column:blog_block_id"`
	PostID      uint      `db:"post_id" json:"post_id" gorm:"column:post_id"`
	Priority    int       `db:"priority" json:"priority" gorm:"column:priority"`
	CreatedAt   time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`

	// Relations
	BlogBlock *BlogBlock `db:"-" gorm:"-" json:"blog_block,omitempty"`
	Post      *Post      `db:"-" gorm:"-" json:"post,omitempty"`
}

// TableName returns the table name for BlogBlock
func (BlogBlock) TableName() string {
	return "blog_blocks"
}

// TableName returns the table name for BlogBlockPost
func (BlogBlockPost) TableName() string {
	return "blog_block_posts"
}