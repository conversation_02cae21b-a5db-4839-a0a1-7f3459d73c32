package models

import "time"

// BlogSchedule represents a scheduled blog post publication
type BlogSchedule struct {
	ID           uint               `db:"id" json:"id" gorm:"primaryKey"`
	TenantID     uint               `db:"tenant_id" json:"tenant_id"`
	WebsiteID    uint               `db:"website_id" json:"website_id"`
	BlogID       uint               `db:"blog_id" json:"blog_id"`
	ScheduledAt  time.Time          `db:"scheduled_at" json:"scheduled_at"`
	Status       BlogScheduleStatus `db:"status" json:"status"`
	ErrorMessage *string            `db:"error_message" json:"error_message,omitempty"`
	RetryCount   uint8              `db:"retry_count" json:"retry_count"`
	MaxRetries   uint8              `db:"max_retries" json:"max_retries"`
	PublishedAt  *time.Time         `db:"published_at" json:"published_at,omitempty"`
	CreatedAt    time.Time          `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time          `db:"updated_at" json:"updated_at"`
	CreatedBy    uint               `db:"created_by" json:"created_by"`

	// Relations
	Post    *Post `db:"-" gorm:"foreignKey:BlogID;references:PostID" json:"post,omitempty"`
	Creator *User `db:"-" gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
}

// BlogScheduleStatus represents the status of a blog schedule
type BlogScheduleStatus string

const (
	ScheduleStatusPending   BlogScheduleStatus = "pending"
	ScheduleStatusPublished BlogScheduleStatus = "published"
	ScheduleStatusFailed    BlogScheduleStatus = "failed"
	ScheduleStatusCancelled BlogScheduleStatus = "cancelled"
)

// TableName sets the table name for GORM
func (BlogSchedule) TableName() string {
	return "blog_schedules"
}

// IsRetryable checks if the schedule can be retried
func (s *BlogSchedule) IsRetryable() bool {
	return s.Status == ScheduleStatusFailed && s.RetryCount < s.MaxRetries
}

// CanBeExecuted checks if the schedule can be executed now
func (s *BlogSchedule) CanBeExecuted() bool {
	return s.Status == ScheduleStatusPending && time.Now().After(s.ScheduledAt)
}

// MarkAsPublished marks the schedule as successfully published
func (s *BlogSchedule) MarkAsPublished() {
	now := time.Now()
	s.Status = ScheduleStatusPublished
	s.PublishedAt = &now
	s.ErrorMessage = nil
}

// MarkAsFailed marks the schedule as failed with error message
func (s *BlogSchedule) MarkAsFailed(errorMsg string) {
	s.Status = ScheduleStatusFailed
	s.ErrorMessage = &errorMsg
	s.RetryCount++
}

// MarkAsCancelled marks the schedule as cancelled
func (s *BlogSchedule) MarkAsCancelled() {
	s.Status = ScheduleStatusCancelled
	s.ErrorMessage = nil
}
