package models

import (
	"time"
)

type BlogTimeline struct {
	ID        uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID  uint      `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id"`
	WebsiteID uint      `db:"website_id" json:"website_id" gorm:"column:website_id"`
	Name      string    `db:"name" json:"name" gorm:"column:name"`
	Code      string    `db:"code" json:"code" gorm:"column:code"`
	Active    bool      `db:"active" json:"active" gorm:"column:active"`
	CreatedAt time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`

	// Relations
	Posts []BlogTimelinePost `db:"-" gorm:"-" json:"posts,omitempty"`
}

type BlogTimelinePost struct {
	ID              uint      `db:"id" json:"id" gorm:"column:id;primaryKey"`
	TenantID        uint      `db:"tenant_id" json:"tenant_id" gorm:"column:tenant_id"`
	WebsiteID       uint      `db:"website_id" json:"website_id" gorm:"column:website_id"`
	BlogTimelineID  uint      `db:"blog_timeline_id" json:"blog_timeline_id" gorm:"column:blog_timeline_id"`
	PostID          uint      `db:"post_id" json:"post_id" gorm:"column:post_id"`
	Priority        int       `db:"priority" json:"priority" gorm:"column:priority"`
	CreatedAt       time.Time `db:"created_at" json:"created_at" gorm:"column:created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at" gorm:"column:updated_at"`

	// Relations
	BlogTimeline *BlogTimeline `db:"-" gorm:"-" json:"blog_timeline,omitempty"`
	Post         *Post         `db:"-" gorm:"-" json:"post,omitempty"`
}

// TableName returns the table name for BlogTimeline
func (BlogTimeline) TableName() string {
	return "blog_timelines"
}

// TableName returns the table name for BlogTimelinePost
func (BlogTimelinePost) TableName() string {
	return "blog_timeline_posts"
}