# Blog Schedule Flow Documentation

## Overview

The blog schedule system allows users to create blog posts with future publication dates. When a post is created or updated with a `publish_at` in the future, the system automatically creates a schedule entry and queues a background job to publish the post at the specified time.

## Architecture

### Components

1. **Post Service** (`service/post_service.go`)
   - Handles blog post CRUD operations
   - Automatically creates schedules for future-dated posts
   - Integrates with schedule service

2. **Schedule Service** (`service/schedule_service.go`)
   - Manages blog schedules
   - <PERSON><PERSON> schedule execution and retries
   - Integrates with queue system

3. **Schedule Repository** (`repository/mysql/schedule_repository.go`)
   - Database operations for schedules
   - Queries for pending schedules

4. **Cron Jobs** (`cron/schedule_jobs.go`)
   - Periodic execution of pending schedules
   - Background processing

5. **Queue Integration**
   - Asynchronous task processing
   - Retry mechanisms
   - Error handling

## Flow Diagram

```
User Creates/Updates Post
         ↓
Post Service Checks publish_at
         ↓
If future date → Create Schedule
         ↓
Schedule Service → Queue Task
         ↓
Cron Job Processes → Execute Schedule
         ↓
Update Post Status → Mark Schedule Complete
```

## Detailed Flow

### 1. Post Creation with Future Date

When a user creates a post via API:

```json
POST /api/admin/v1/blog/posts
{
  "title": "My Scheduled Post",
  "content": "Post content...",
  "status": "draft",
  "publish_at": "2024-12-25T10:00:00Z"
}
```

**Process:**
1. `PostService.CreatePost()` saves the post to database
2. `handlePostScheduling()` checks if `publish_at` is in future
3. If yes, calls `ScheduleService.CreateSchedule()`
4. Schedule entry created in `blog_schedules` table
5. Background task queued for execution at scheduled time

### 2. Post Update with New Date

When updating a post's publish date:

```json
PUT /api/admin/v1/blog/posts/123
{
  "publish_at": "2024-12-26T15:00:00Z"
}
```

**Process:**
1. `PostService.UpdatePost()` updates the post
2. If `publish_at` changed, `handlePostScheduling()` is called
3. New schedule created (old one should be cancelled - future enhancement)

### 3. Schedule Execution

**Cron Job Process:**
1. `ProcessPendingSchedules()` runs every minute
2. Queries for schedules where `status = 'pending'` and `scheduled_at <= NOW()`
3. For each pending schedule:
   - Calls `ExecuteSchedule()`
   - Updates post status to 'published'
   - Sets post `publish_at` to current time
   - Marks schedule as 'published'

**Error Handling:**
- If execution fails, schedule marked as 'failed'
- Retry mechanism with exponential backoff
- Maximum retry attempts configurable
- Error messages logged for debugging

## Database Schema

### blog_schedules Table

```sql
CREATE TABLE blog_schedules (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT UNSIGNED NOT NULL,
    blog_id INT UNSIGNED NOT NULL,
    scheduled_at DATETIME NOT NULL,
    status ENUM('pending', 'published', 'failed', 'cancelled') DEFAULT 'pending',
    error_message TEXT NULL,
    retry_count TINYINT UNSIGNED DEFAULT 0,
    max_retries TINYINT UNSIGNED DEFAULT 3,
    published_at DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT UNSIGNED NOT NULL,
    
    FOREIGN KEY (blog_id) REFERENCES blog_posts(post_id) ON DELETE RESTRICT,
    INDEX idx_tenant_status_scheduled (tenant_id, status, scheduled_at),
    INDEX idx_blog_scheduled (blog_id, scheduled_at)
);
```

## API Endpoints

### Schedule Management

```
GET    /api/admin/v1/blog/schedules           # List schedules
GET    /api/admin/v1/blog/schedules/:id       # Get schedule details
POST   /api/admin/v1/blog/schedules/:id/cancel # Cancel schedule
POST   /api/admin/v1/blog/schedules/:id/retry  # Retry failed schedule
```

### Post Endpoints (with scheduling)

```
POST   /api/admin/v1/blog/posts              # Create post (auto-schedule if future date)
PUT    /api/admin/v1/blog/posts/:id          # Update post (auto-schedule if date changed)
```

## Configuration

### Environment Variables

```env
# Blog Schedule Settings
BLOG_SCHEDULE_MAX_RETRIES=3
BLOG_SCHEDULE_RETRY_DELAY=60  # seconds
BLOG_SCHEDULE_CRON_INTERVAL=60 # seconds

# Queue Settings
QUEUE_BLOG_SCHEDULES_QUEUE=blog_schedules
```

## Usage Examples

### 1. Create Scheduled Post

```go
// Create post with future publish date
req := request.CreatePostRequest{
    Title:       "My Scheduled Post",
    Content:     "Post content...",
    Status:      "draft",
    PublishDate: &futureTime, // Automatically creates schedule
}

post, err := postService.CreatePost(ctx, tenantID, req)
```

### 2. Manual Schedule Management

```go
// Create schedule manually
schedule, err := scheduleService.CreateSchedule(
    ctx, tenantID, postID, userID, scheduledTime, maxRetries,
)

// Cancel schedule
err = scheduleService.CancelSchedule(ctx, tenantID, scheduleID)

// Retry failed schedule
err = scheduleService.RetrySchedule(ctx, tenantID, scheduleID)
```

### 3. Process Pending Schedules (Cron)

```go
// This runs automatically via cron job
err := scheduleService.ProcessPendingSchedules(ctx)
```

## Monitoring and Logging

### Key Metrics
- Number of pending schedules
- Schedule execution success rate
- Average execution time
- Failed schedule count
- Retry attempts

### Log Events
- Schedule creation
- Schedule execution start/completion
- Schedule failures and retries
- Schedule cancellations

## Future Enhancements

1. **Schedule Conflict Detection**
   - Prevent multiple schedules for same post
   - Automatic cancellation of old schedules

2. **Bulk Operations**
   - Bulk schedule creation
   - Bulk cancellation

3. **Advanced Scheduling**
   - Recurring schedules
   - Timezone support
   - Schedule templates

4. **Notifications**
   - Email notifications for schedule events
   - Webhook integrations

5. **Analytics**
   - Schedule performance metrics
   - Publishing patterns analysis
