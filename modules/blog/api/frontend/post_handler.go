package frontend

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/frontend"
	"wnapi/modules/blog/dto/request"
	blogResponse "wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/service"
)

// PostHandler handles HTTP requests for frontend blog posts
type PostHandler struct {
	postService service.PostService
}

// NewPostHandler creates a new frontend post handler instance
func NewPostHandler(postService service.PostService) *PostHandler {
	return &PostHandler{
		postService: postService,
	}
}

// GetPosts handles getting a list of published posts for frontend
func (h *PostHandler) GetPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 10
	}

	cursor := c.Query("cursor")
	categoryID := c.Query("category_id")
	tag := c.Query("tag")
	search := c.Query("search")

	// Build request for service
	req := request.ListPostRequest{
		Limit:  limit,
		Cursor: cursor,
		Status: "published", // Only published posts for frontend
		Search: search,
	}

	if categoryID != "" {
		if catID, err := strconv.ParseUint(categoryID, 10, 32); err == nil {
			catIDUint := uint(catID)
			req.CategoryID = &catIDUint
		}
	}

	if tag != "" {
		if tagID, err := strconv.ParseUint(tag, 10, 32); err == nil {
			tagIDUint := uint(tagID)
			req.TagID = &tagIDUint
		}
	}

	// Call service
	result, err := h.postService.ListPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Không thể lấy danh sách bài viết", "INTERNAL_ERROR")
		return
	}

	// Convert to frontend response
	frontendResponse := h.convertToFrontendPostList(result)
	response.Success(c, frontendResponse.Data, &response.Meta{
		NextCursor: frontendResponse.Meta.NextCursor,
		HasMore:    frontendResponse.Meta.HasMore,
	})
}

// GetPostBySlug handles getting a single post by slug for frontend
func (h *PostHandler) GetPostBySlug(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, http.StatusBadRequest, "Thiếu slug bài viết", "SLUG_REQUIRED")
		return
	}

	// Call service
	post, err := h.postService.GetPostBySlug(c.Request.Context(), tenantID, websiteID, slug)
	if err != nil {
		response.Error(c, http.StatusNotFound, "Không tìm thấy bài viết", "POST_NOT_FOUND")
		return
	}

	// Check if post is published
	if post.Status != "published" {
		response.Error(c, http.StatusNotFound, "Bài viết không tồn tại", "POST_NOT_FOUND")
		return
	}

	// Convert to frontend response
	frontendPost := h.convertToFrontendPostDetail(post)
	frontendResponse := &frontend.PostDetailResponseWrapper{
		Data: *frontendPost,
		Meta: nil,
	}
	response.Success(c, frontendResponse.Data, nil)
}

// convertToFrontendPostList converts service response to frontend response
func (h *PostHandler) convertToFrontendPostList(serviceResponse *blogResponse.PostListResponse) *frontend.PostListResponse {
	frontendPosts := make([]frontend.PostSummaryResponse, len(serviceResponse.Data))

	for i, post := range serviceResponse.Data {
		frontendPosts[i] = frontend.PostSummaryResponse{
			ID:          post.PostID,
			Title:       post.Title,
			Slug:        post.Slug,
			Description: post.Description,
			Image:       post.Image,
			PublishedAt: post.PublishedAt,
			CreatedAt:   post.CreatedAt,
		}

		// Convert author
		if post.Author != nil {
			frontendPosts[i].Author = &frontend.AuthorInfo{
				ID:        post.Author.ID,
				Name:      post.Author.FirstName + " " + post.Author.LastName,
				AvatarURL: &post.Author.AvatarURL,
			}
		}

		// Convert categories
		if len(post.Categories) > 0 {
			frontendPosts[i].Categories = make([]frontend.CategoryInfo, len(post.Categories))
			for j, cat := range post.Categories {
				frontendPosts[i].Categories[j] = frontend.CategoryInfo{
					ID:   cat.ID,
					Name: cat.Name,
					Slug: cat.Slug,
				}
			}
		}

		// Convert tags
		if len(post.Tags) > 0 {
			frontendPosts[i].Tags = make([]frontend.TagInfo, len(post.Tags))
			for j, tag := range post.Tags {
				frontendPosts[i].Tags[j] = frontend.TagInfo{
					ID:   tag.ID,
					Name: tag.Name,
					Slug: tag.Slug,
				}
			}
		}
	}

	return &frontend.PostListResponse{
		Data: frontendPosts,
		Meta: frontend.PaginationMeta{
			NextCursor: serviceResponse.Meta.NextCursor,
			HasMore:    serviceResponse.Meta.HasMore,
		},
	}
}

// convertToFrontendPostDetail converts service response to frontend detail response
func (h *PostHandler) convertToFrontendPostDetail(post *blogResponse.PostResponse) *frontend.PostDetailResponse {
	frontendPost := &frontend.PostDetailResponse{
		ID:          post.PostID,
		Title:       post.Title,
		Slug:        post.Slug,
		Description: post.Description,
		Content:     post.Content,
		Image:       post.Image,
		PublishedAt: post.PublishedAt,
		CreatedAt:   post.CreatedAt,
		UpdatedAt:   post.UpdatedAt,
	}

	// Convert author
	if post.Author != nil {
		frontendPost.Author = &frontend.AuthorInfo{
			ID:        post.Author.ID,
			Name:      post.Author.FirstName + " " + post.Author.LastName,
			AvatarURL: &post.Author.AvatarURL,
		}
	}

	// Convert categories
	if len(post.Categories) > 0 {
		frontendPost.Categories = make([]frontend.CategoryInfo, len(post.Categories))
		for i, cat := range post.Categories {
			frontendPost.Categories[i] = frontend.CategoryInfo{
				ID:   cat.ID,
				Name: cat.Name,
				Slug: cat.Slug,
			}
		}
	}

	// Convert tags
	if len(post.Tags) > 0 {
		frontendPost.Tags = make([]frontend.TagInfo, len(post.Tags))
		for i, tag := range post.Tags {
			frontendPost.Tags[i] = frontend.TagInfo{
				ID:   tag.ID,
				Name: tag.Name,
				Slug: tag.Slug,
			}
		}
	}

	// Convert SEO meta
	if post.SeoMeta != nil {
		frontendPost.SeoMeta = &frontend.SeoMetaInfo{
			Title:         &post.SeoMeta.MetaTitle,
			Description:   &post.SeoMeta.MetaDescription,
			Keywords:      &post.SeoMeta.Keywords,
			OgTitle:       &post.SeoMeta.OgTitle,
			OgDescription: &post.SeoMeta.OgDescription,
			OgImage:       &post.SeoMeta.OgImage,
		}
	}

	return frontendPost
}
