package frontend

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/frontend"
	blogResponse "wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/service"
)

// CategoryHandler handles HTTP requests for frontend blog categories
type CategoryHandler struct {
	categoryService service.CategoryService
}

// NewCategoryHandler creates a new frontend category handler instance
func NewCategoryHandler(categoryService service.CategoryService) *CategoryHandler {
	return &CategoryHandler{
		categoryService: categoryService,
	}
}

// GetCategories handles getting a list of active categories for frontend
func (h *CategoryHandler) GetCategories(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Check if tree format is requested
	treeFormat := c.Query("tree") == "true"

	if treeFormat {
		// Get category tree
		treeResult, err := h.categoryService.GetCategoryTree(c.Request.Context(), tenantID, websiteID)
		if err != nil {
			response.Error(c, http.StatusInternalServerError, "Không thể lấy cây danh mục", "INTERNAL_ERROR")
			return
		}

		// Convert to frontend tree response
		frontendResponse := h.convertToFrontendCategoryTree(treeResult)
		response.Success(c, frontendResponse.Data, nil)
		return
	}

	// For now, we'll use the tree endpoint and flatten it
	// In a real implementation, you might want to add a separate list method to the service
	treeResult, err := h.categoryService.GetCategoryTree(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Không thể lấy danh sách danh mục", "INTERNAL_ERROR")
		return
	}

	// Flatten tree to list
	categories := h.flattenCategoryTree(treeResult.Categories)

	// Convert to frontend list response
	frontendResponse := &frontend.CategoryListResponse{
		Data: categories,
		Meta: frontend.PaginationMeta{
			HasMore: false, // Tree doesn't have pagination
		},
	}

	response.Success(c, frontendResponse.Data, &response.Meta{
		HasMore: frontendResponse.Meta.HasMore,
	})
}

// GetCategoryBySlug handles getting a single category by slug for frontend
func (h *CategoryHandler) GetCategoryBySlug(c *gin.Context) {
	// Get tenant ID from query or header
	tenantIDStr := c.Query("tenant_id")
	if tenantIDStr == "" {
		tenantIDStr = c.GetHeader("X-Tenant-ID")
	}

	if tenantIDStr == "" {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_TENANT_ID")
		return
	}

	// Get website ID from query or header
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		websiteIDStr = c.GetHeader("X-Website-ID")
	}

	if websiteIDStr == "" {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Invalid website ID", "INVALID_WEBSITE_ID")
		return
	}

	slug := c.Param("slug")
	if slug == "" {
		response.Error(c, http.StatusBadRequest, "Thiếu slug danh mục", "SLUG_REQUIRED")
		return
	}

	// Call service
	category, err := h.categoryService.GetCategoryBySlug(c.Request.Context(), uint(tenantID), uint(websiteID), slug)
	if err != nil {
		response.Error(c, http.StatusNotFound, "Không tìm thấy danh mục", "CATEGORY_NOT_FOUND")
		return
	}

	// Check if category is active
	if !category.IsActive {
		response.Error(c, http.StatusNotFound, "Danh mục không tồn tại", "CATEGORY_NOT_FOUND")
		return
	}

	// Convert to frontend response
	frontendCategory := h.convertToFrontendCategoryDetail(category)
	frontendResponse := &frontend.CategoryDetailResponseWrapper{
		Data: *frontendCategory,
		Meta: nil,
	}
	response.Success(c, frontendResponse.Data, nil)
}

// GetCategoryTree handles getting the category tree for frontend
func (h *CategoryHandler) GetCategoryTree(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Call service
	treeResult, err := h.categoryService.GetCategoryTree(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Không thể lấy cây danh mục", "INTERNAL_ERROR")
		return
	}

	// Convert to frontend response
	frontendResponse := h.convertToFrontendCategoryTree(treeResult)
	response.Success(c, frontendResponse.Data, nil)
}

// convertToFrontendCategoryTree converts service tree response to frontend response
func (h *CategoryHandler) convertToFrontendCategoryTree(serviceResponse *blogResponse.CategoryTreeResponse) *frontend.CategoryTreeResponse {
	frontendCategories := make([]frontend.CategoryTreeItem, len(serviceResponse.Categories))

	for i, cat := range serviceResponse.Categories {
		frontendCategories[i] = h.convertTreeItem(cat)
	}

	return &frontend.CategoryTreeResponse{
		Data: frontendCategories,
		Meta: nil,
	}
}

// convertTreeItem recursively converts a tree item
func (h *CategoryHandler) convertTreeItem(item *blogResponse.CategoryTreeItem) frontend.CategoryTreeItem {
	frontendItem := frontend.CategoryTreeItem{
		ID:            item.ID,
		Name:          item.Name,
		Slug:          item.Slug,
		Description:   item.Description,
		FeaturedImage: item.FeaturedImage,
		PostCount:     item.PostCount,
		Depth:         item.Depth,
	}

	// Convert children
	if len(item.Children) > 0 {
		frontendItem.Children = make([]frontend.CategoryTreeItem, len(item.Children))
		for i, child := range item.Children {
			frontendItem.Children[i] = h.convertTreeItem(child)
		}
	}

	return frontendItem
}

// convertToFrontendCategoryDetail converts service response to frontend detail response
func (h *CategoryHandler) convertToFrontendCategoryDetail(category *blogResponse.CategoryResponse) *frontend.CategoryDetailResponse {
	frontendCategory := &frontend.CategoryDetailResponse{
		ID:            category.ID,
		ParentID:      category.ParentID,
		Name:          category.Name,
		Slug:          category.Slug,
		Description:   category.Description,
		FeaturedImage: category.FeaturedImage,
		PostCount:     category.PostCount,
		CreatedAt:     category.CreatedAt,
		UpdatedAt:     category.UpdatedAt,
	}

	// Convert parent info if exists
	if category.ParentID != nil {
		// Note: In a real implementation, you might want to fetch parent details
		// For now, we'll leave it nil since the service response doesn't include parent details
	}

	// Convert SEO meta
	if category.MetaTitle != "" || category.MetaDescription != "" {
		frontendCategory.SeoMeta = &frontend.SeoMetaInfo{
			Title:       &category.MetaTitle,
			Description: &category.MetaDescription,
		}
	}

	return frontendCategory
}

// flattenCategoryTree flattens a category tree into a list
func (h *CategoryHandler) flattenCategoryTree(treeItems []*blogResponse.CategoryTreeItem) []frontend.CategorySummaryResponse {
	var categories []frontend.CategorySummaryResponse

	for _, item := range treeItems {
		// Add current item
		categories = append(categories, frontend.CategorySummaryResponse{
			ID:            item.ID,
			Name:          item.Name,
			Slug:          item.Slug,
			Description:   item.Description,
			FeaturedImage: item.FeaturedImage,
			PostCount:     item.PostCount,
			CreatedAt:     item.CreatedAt,
		})

		// Recursively add children
		if len(item.Children) > 0 {
			childCategories := h.flattenCategoryTree(item.Children)
			categories = append(categories, childCategories...)
		}
	}

	return categories
}
