package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
)

// BlogTimelineHandler handles HTTP requests for blog timelines
type BlogTimelineHandler struct {
	blogTimelineService service.BlogTimelineService
	jwtService          *auth.JWTService
}

// NewBlogTimelineHandler creates a new BlogTimelineHandler instance
func NewBlogTimelineHandler(blogTimelineService service.BlogTimelineService, jwtService *auth.JWTService) *BlogTimelineHandler {
	return &BlogTimelineHandler{
		blogTimelineService: blogTimelineService,
		jwtService:          jwtService,
	}
}

// Create handles creating a new blog timeline
func (h *BlogTimelineHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse request
	var req request.CreateBlogTimelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Create blog timeline
	blogTimeline, err := h.blogTimelineService.Create(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimeline, nil)
}

// Get handles getting a blog timeline by ID
func (h *BlogTimelineHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Get blog timeline
	blogTimeline, err := h.blogTimelineService.GetByID(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID))
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimeline, nil)
}

// GetByCode handles getting a blog timeline by code
func (h *BlogTimelineHandler) GetByCode(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get code from URL
	code := c.Param("code")
	if code == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline code is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline code", "MISSING_CODE", details)
		return
	}

	// Get blog timeline
	blogTimeline, err := h.blogTimelineService.GetByCode(c.Request.Context(), tenantID, websiteID, code)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimeline, nil)
}

// Update handles updating a blog timeline
func (h *BlogTimelineHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.UpdateBlogTimelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Update blog timeline
	blogTimeline, err := h.blogTimelineService.Update(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimeline, nil)
}

// Delete handles deleting a blog timeline
func (h *BlogTimelineHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Delete blog timeline
	err = h.blogTimelineService.Delete(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID))
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// List handles listing blog timelines with pagination
func (h *BlogTimelineHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse query
	var req request.ListBlogTimelineRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Limit should be between 1 and 100
	if req.Limit <= 0 {
		req.Limit = 10
	} else if req.Limit > 100 {
		req.Limit = 100
	}

	// Get blog timelines
	blogTimelines, err := h.blogTimelineService.List(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	meta := &response.Meta{
		NextCursor: blogTimelines.Meta.NextCursor,
		HasMore:    blogTimelines.Meta.HasMore,
	}
	response.Success(c, blogTimelines.Data, meta)
}

// AddPost handles adding a post to a blog timeline
func (h *BlogTimelineHandler) AddPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.AddPostToBlogTimelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Add post to blog timeline
	blogTimelinePost, err := h.blogTimelineService.AddPost(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimelinePost, nil)
}

// RemovePost handles removing a post from a blog timeline
func (h *BlogTimelineHandler) RemovePost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Get post ID from URL
	postIDStr := c.Param("postId")
	if postIDStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_POST_ID", details)
		return
	}

	// Convert to uint
	postID, err := strconv.ParseUint(postIDStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
		return
	}

	// Remove post from blog timeline
	err = h.blogTimelineService.RemovePost(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), uint(postID))
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// UpdatePost handles updating a post in a blog timeline
func (h *BlogTimelineHandler) UpdatePost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Get post ID from URL
	postIDStr := c.Param("postId")
	if postIDStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_POST_ID", details)
		return
	}

	// Convert to uint
	postID, err := strconv.ParseUint(postIDStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
		return
	}

	// Parse request
	var req request.UpdateBlogTimelinePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Update post in blog timeline
	blogTimelinePost, err := h.blogTimelineService.UpdatePost(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), uint(postID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogTimelinePost, nil)
}

// GetPosts handles getting posts in a blog timeline
func (h *BlogTimelineHandler) GetPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Parse query
	var req request.ListBlogTimelinePostsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Limit should be between 1 and 100
	if req.Limit <= 0 {
		req.Limit = 10
	} else if req.Limit > 100 {
		req.Limit = 100
	}

	// Get posts in blog timeline
	blogTimelinePosts, err := h.blogTimelineService.GetPosts(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	meta := &response.Meta{
		NextCursor: blogTimelinePosts.Meta.NextCursor,
		HasMore:    blogTimelinePosts.Meta.HasMore,
	}
	response.Success(c, blogTimelinePosts.Data, meta)
}

// BatchAddPosts handles adding multiple posts to a blog timeline
func (h *BlogTimelineHandler) BatchAddPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.BatchAddPostsToBlogTimelineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Batch add posts to blog timeline
	err = h.blogTimelineService.BatchAddPosts(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// ReorderPosts handles reordering posts in a blog timeline
func (h *BlogTimelineHandler) ReorderPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog timeline ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog timeline ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog timeline ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogTimelineID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog timeline ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog timeline ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.ReorderBlogTimelinePostsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Reorder posts in blog timeline
	err = h.blogTimelineService.ReorderPosts(c.Request.Context(), tenantID, websiteID, uint(blogTimelineID), req)
	if err != nil {
		handleBlogTimelineServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// handleBlogTimelineServiceError handles blog timeline service errors
func handleBlogTimelineServiceError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	errMsg := err.Error()

	switch {
	case errMsg == "blog timeline not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Blog timeline not found", "BLOG_TIMELINE_NOT_FOUND", nil)
	case errMsg == "post not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Post not found", "POST_NOT_FOUND", nil)
	case strings.Contains(errMsg, "code") && strings.Contains(errMsg, "already exists"):
		response.ErrorWithDetails(c, http.StatusConflict, errMsg, "BLOG_TIMELINE_CODE_EXISTS", nil)
	case strings.Contains(errMsg, "already in this blog timeline"):
		response.ErrorWithDetails(c, http.StatusConflict, errMsg, "POST_ALREADY_IN_TIMELINE", nil)
	case strings.Contains(errMsg, "not found in blog timeline"):
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
	default:
		details := []interface{}{map[string]string{"message": errMsg}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "SERVER_ERROR", details)
	}
}
