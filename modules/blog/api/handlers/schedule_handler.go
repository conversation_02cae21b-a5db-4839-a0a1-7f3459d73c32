package handlers

import (
	"net/http"
	"strconv"
	"time"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto"
	"wnapi/modules/blog/service"

	"github.com/gin-gonic/gin"
)

// ScheduleHandler handles HTTP requests for blog schedules
type ScheduleHandler struct {
	scheduleService service.ScheduleService
}

// NewScheduleHandler creates a new schedule handler
func NewScheduleHandler(scheduleService service.ScheduleService) *ScheduleHandler {
	return &ScheduleHandler{
		scheduleService: scheduleService,
	}
}

// CreateSchedule creates a new blog schedule
// @Summary Create blog schedule
// @Description Create a new schedule for blog post publication
// @Tags blog-schedules
// @Accept json
// @Produce json
// @Param schedule body dto.CreateScheduleRequest true "Schedule data"
// @Success 201 {object} response.APIResponse{data=dto.ScheduleResponse}
// @Failure 400 {object} response.APIResponse
// @Failure 500 {object} response.APIResponse
// @Router /api/v1/blog/schedules [post]
func (h *ScheduleHandler) CreateSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req dto.CreateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "VALIDATION_ERROR", map[string]interface{}{
			"validation_errors": err.Error(),
		})
		return
	}

	// Set default max retries if not provided
	if req.MaxRetries == 0 {
		req.MaxRetries = 3
	}

	// Validate scheduled time is in the future
	if req.ScheduledAt.Before(time.Now()) {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Scheduled time must be in the future", "INVALID_SCHEDULE_TIME", nil)
		return
	}

	schedule, err := h.scheduleService.CreateSchedule(
		c.Request.Context(),
		tenantID,
		websiteID,
		req.BlogID,
		req.CreatedBy,
		req.ScheduledAt,
		req.MaxRetries,
	)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to create schedule", "INTERNAL_ERROR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	scheduleResponse := dto.ConvertToScheduleResponse(schedule)
	response.Created(c, scheduleResponse, nil)
}

// GetSchedule retrieves a schedule by ID
// @Summary Get blog schedule
// @Description Get a blog schedule by ID
// @Tags blog-schedules
// @Produce json
// @Param id path int true "Schedule ID"
// @Param tenant_id query int true "Tenant ID"
// @Success 200 {object} response.APIResponse{data=dto.ScheduleResponse}
// @Failure 400 {object} response.APIResponse
// @Failure 404 {object} response.APIResponse
// @Router /api/v1/blog/schedules/{id} [get]
func (h *ScheduleHandler) GetSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	idStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid schedule ID", "INVALID_REQUEST", map[string]interface{}{
			"schedule_id": idStr,
		})
		return
	}

	schedule, err := h.scheduleService.GetSchedule(c.Request.Context(), tenantID, websiteID, uint(scheduleID))
	if err != nil {
		response.ErrorWithDetails(c, http.StatusNotFound, "Schedule not found", "NOT_FOUND", map[string]interface{}{
			"schedule_id": scheduleID,
		})
		return
	}

	scheduleResponse := dto.ConvertToScheduleResponse(schedule)
	response.Success(c, scheduleResponse, nil)
}

// ListSchedules lists schedules with pagination and filtering
// @Summary List blog schedules
// @Description List blog schedules with pagination and filtering
// @Tags blog-schedules
// @Produce json
// @Param tenant_id query int true "Tenant ID"
// @Param status query string false "Filter by status"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} response.APIResponse{data=dto.ScheduleListResponse}
// @Failure 400 {object} response.APIResponse
// @Failure 500 {object} response.APIResponse
// @Router /api/v1/blog/schedules [get]
func (h *ScheduleHandler) ListSchedules(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req dto.ListSchedulesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "VALIDATION_ERROR", map[string]interface{}{
			"validation_errors": err.Error(),
		})
		return
	}

	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100 // Max limit
	}

	offset := (req.Page - 1) * req.Limit

	schedules, total, err := h.scheduleService.ListSchedules(
		c.Request.Context(),
		tenantID,
		websiteID,
		req.Status,
		req.Limit,
		offset,
	)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to list schedules", "INTERNAL_ERROR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	scheduleListResponse := dto.ConvertToScheduleListResponse(schedules, total, req.Page, req.Limit)
	response.Success(c, scheduleListResponse, nil)
}

// CancelSchedule cancels a pending schedule
// @Summary Cancel blog schedule
// @Description Cancel a pending blog schedule
// @Tags blog-schedules
// @Produce json
// @Param id path int true "Schedule ID"
// @Param tenant_id query int true "Tenant ID"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIResponse
// @Failure 404 {object} response.APIResponse
// @Failure 500 {object} response.APIResponse
// @Router /api/v1/blog/schedules/{id}/cancel [post]
func (h *ScheduleHandler) CancelSchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	idStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid schedule ID", "INVALID_REQUEST", map[string]interface{}{
			"schedule_id": idStr,
		})
		return
	}

	if err := h.scheduleService.CancelSchedule(c.Request.Context(), tenantID, websiteID, uint(scheduleID)); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to cancel schedule", "INTERNAL_ERROR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	response.Success(c, gin.H{"message": "Schedule cancelled successfully"}, nil)
}

// RetrySchedule retries a failed schedule
// @Summary Retry blog schedule
// @Description Retry a failed blog schedule
// @Tags blog-schedules
// @Produce json
// @Param id path int true "Schedule ID"
// @Param tenant_id query int true "Tenant ID"
// @Success 200 {object} response.APIResponse
// @Failure 400 {object} response.APIResponse
// @Failure 404 {object} response.APIResponse
// @Failure 500 {object} response.APIResponse
// @Router /api/v1/blog/schedules/{id}/retry [post]
func (h *ScheduleHandler) RetrySchedule(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	idStr := c.Param("id")
	scheduleID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid schedule ID", "INVALID_REQUEST", map[string]interface{}{
			"schedule_id": idStr,
		})
		return
	}

	if err := h.scheduleService.RetrySchedule(c.Request.Context(), tenantID, websiteID, uint(scheduleID)); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retry schedule", "INTERNAL_ERROR", map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	response.Success(c, gin.H{"message": "Schedule retry initiated successfully"}, nil)
}
