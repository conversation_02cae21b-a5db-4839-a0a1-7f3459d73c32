package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/service"
)

// StatisticHandler handles HTTP requests for blog statistics
type StatisticHandler struct {
	statisticService service.StatisticService
	jwtService       *auth.JWTService
}

// NewStatisticHandler creates a new statistic handler instance
func NewStatisticHandler(statisticService service.StatisticService, jwtService *auth.JWTService) *StatisticHandler {
	return &StatisticHandler{
		statisticService: statisticService,
		jwtService:       jwtService,
	}
}

// GetStatusStatistics handles getting post count statistics by status
func (h *StatisticHandler) GetStatusStatistics(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get status statistics
	stats, err := h.statisticService.GetStatusStatistics(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		handleStatisticServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, stats, nil)
}

// handleStatisticServiceError handles statistic service errors
func handleStatisticServiceError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// Handle specific error types if needed
	details := []interface{}{map[string]string{"message": err.Error()}}
	response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "INTERNAL_SERVER_ERROR", details)
}
