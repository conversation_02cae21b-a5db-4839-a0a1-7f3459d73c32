package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
)

// RelatedPostHandler handles HTTP requests for related posts
type RelatedPostHandler struct {
	relatedPostService service.RelatedPostService
}

// NewRelatedPostHandler creates a new RelatedPostHandler
func NewRelatedPostHandler(relatedPostService service.RelatedPostService) *RelatedPostHandler {
	return &RelatedPostHandler{
		relatedPostService: relatedPostService,
	}
}

// CreateRelatedPost creates a new related post relationship
func (h *RelatedPostHandler) CreateRelatedPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req request.CreateRelatedPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	result, err := h.relatedPostService.CreateRelatedPost(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	c.Status(http.StatusCreated)
	response.Success(c, result, nil)
}

// GetRelatedPost retrieves a related post relationship by ID
func (h *RelatedPostHandler) GetRelatedPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	relationIDStr := c.Param("relation_id")
	relationID, err := strconv.ParseUint(relationIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid relation ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid relation ID", "INVALID_ID", details)
		return
	}

	result, err := h.relatedPostService.GetRelatedPost(c.Request.Context(), tenantID, websiteID, uint(relationID))
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, result, nil)
}

// UpdateRelatedPost updates a related post relationship
func (h *RelatedPostHandler) UpdateRelatedPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	relationIDStr := c.Param("relation_id")
	relationID, err := strconv.ParseUint(relationIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid relation ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid relation ID", "INVALID_ID", details)
		return
	}

	var req request.UpdateRelatedPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	result, err := h.relatedPostService.UpdateRelatedPost(c.Request.Context(), tenantID, websiteID, uint(relationID), req)
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, result, nil)
}

// DeleteRelatedPost deletes a related post relationship by ID
func (h *RelatedPostHandler) DeleteRelatedPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	relationIDStr := c.Param("relation_id")
	relationID, err := strconv.ParseUint(relationIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid relation ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid relation ID", "INVALID_ID", details)
		return
	}

	err = h.relatedPostService.DeleteRelatedPost(c.Request.Context(), tenantID, websiteID, uint(relationID))
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, nil, nil)
}

// DeleteRelatedPostByPosts deletes a relationship between two specific posts
func (h *RelatedPostHandler) DeleteRelatedPostByPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req request.DeleteRelatedPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	err := h.relatedPostService.DeleteRelatedPostByPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, nil, nil)
}

// ListRelatedPosts lists related posts with pagination
func (h *RelatedPostHandler) ListRelatedPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req request.ListRelatedPostRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	result, err := h.relatedPostService.ListRelatedPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with pagination metadata
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}
	response.Success(c, result.Data, meta)
}

// GetPostRelatedPosts gets all related posts for a specific post
func (h *RelatedPostHandler) GetPostRelatedPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	if limit > 50 {
		limit = 50
	}

	// Get cursor parameter for pagination
	cursor := c.Query("cursor")

	result, err := h.relatedPostService.GetPostRelatedPostsWithCursor(c.Request.Context(), tenantID, websiteID, uint(postID), limit, cursor)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with pagination metadata
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}
	response.Success(c, result.Data, meta)
}

// BulkCreateRelatedPosts creates multiple related post relationships
func (h *RelatedPostHandler) BulkCreateRelatedPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	var req request.BulkCreateRelatedPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	result, err := h.relatedPostService.BulkCreateRelatedPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	c.Status(http.StatusCreated)
	response.Success(c, result, nil)
}

// DeleteAllRelatedPosts deletes all related post relationships for a specific post
func (h *RelatedPostHandler) DeleteAllRelatedPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	err = h.relatedPostService.DeleteAllRelatedPosts(c.Request.Context(), tenantID, websiteID, uint(postID))
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, nil, nil)
}

// UpdatePostRelatedPosts updates all related posts for a specific post (manual update)
func (h *RelatedPostHandler) UpdatePostRelatedPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	var req request.UpdatePostRelatedPostsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Set the post ID from the URL parameter
	req.PostID = uint(postID)

	result, err := h.relatedPostService.UpdatePostRelatedPosts(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, result, nil)
}

// DeletePostRelatedPost deletes a specific related post relationship
func (h *RelatedPostHandler) DeletePostRelatedPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	postIDStr := c.Param("id")
	postID, err := strconv.ParseUint(postIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		return
	}

	relatedIDStr := c.Param("related_id")
	relatedID, err := strconv.ParseUint(relatedIDStr, 10, 32)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid related post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid related post ID", "INVALID_ID", details)
		return
	}

	req := request.DeletePostRelatedPostRequest{
		PostID:    uint(postID),
		RelatedID: uint(relatedID),
	}

	err = h.relatedPostService.DeletePostRelatedPost(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	response.Success(c, nil, nil)
}
