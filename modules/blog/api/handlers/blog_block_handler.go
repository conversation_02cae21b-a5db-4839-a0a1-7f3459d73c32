package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
)

// BlogBlockHandler handles HTTP requests for blog blocks
type BlogBlockHandler struct {
	blogBlockService service.BlogBlockService
	jwtService       *auth.JWTService
}

// NewBlogBlockHandler creates a new BlogBlockHandler instance
func NewBlogBlockHandler(blogBlockService service.BlogBlockService, jwtService *auth.JWTService) *BlogBlockHandler {
	return &BlogBlockHandler{
		blogBlockService: blogBlockService,
		jwtService:       jwtService,
	}
}

// Create handles creating a new blog block
func (h *BlogBlockHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse request
	var req request.CreateBlogBlockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Create blog block
	blogBlock, err := h.blogBlockService.Create(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlock, nil)
}

// Get handles getting a blog block by ID
func (h *BlogBlockHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Get blog block
	blogBlock, err := h.blogBlockService.GetByID(c.Request.Context(), tenantID, websiteID, uint(blogBlockID))
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlock, nil)
}

// GetByCode handles getting a blog block by code
func (h *BlogBlockHandler) GetByCode(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get code from URL
	code := c.Param("code")
	if code == "" {
		details := []interface{}{map[string]string{"message": "Blog block code is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block code", "MISSING_CODE", details)
		return
	}

	// Get blog block
	blogBlock, err := h.blogBlockService.GetByCode(c.Request.Context(), tenantID, websiteID, code)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlock, nil)
}

// Update handles updating a blog block
func (h *BlogBlockHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.UpdateBlogBlockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Update blog block
	blogBlock, err := h.blogBlockService.Update(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlock, nil)
}

// Delete handles deleting a blog block
func (h *BlogBlockHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Delete blog block
	err = h.blogBlockService.Delete(c.Request.Context(), tenantID, websiteID, uint(blogBlockID))
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// List handles listing blog blocks with pagination
func (h *BlogBlockHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse query
	var req request.ListBlogBlockRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Limit should be between 1 and 100
	if req.Limit <= 0 {
		req.Limit = 10
	} else if req.Limit > 100 {
		req.Limit = 100
	}

	// Get blog blocks
	blogBlocks, err := h.blogBlockService.List(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	meta := &response.Meta{
		NextCursor: blogBlocks.Meta.NextCursor,
		HasMore:    blogBlocks.Meta.HasMore,
	}
	response.Success(c, blogBlocks.Data, meta)
}

// AddPost handles adding a post to a blog block
func (h *BlogBlockHandler) AddPost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.AddPostToBlogBlockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Add post to blog block
	blogBlockPost, err := h.blogBlockService.AddPost(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlockPost, nil)
}

// RemovePost handles removing a post from a blog block
func (h *BlogBlockHandler) RemovePost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Get post ID from URL
	postIDStr := c.Param("postId")
	if postIDStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_POST_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	postID, err := strconv.ParseUint(postIDStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
		return
	}

	// Remove post from blog block
	err = h.blogBlockService.RemovePost(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), uint(postID))
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// UpdatePost handles updating a post in a blog block
func (h *BlogBlockHandler) UpdatePost(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Get post ID from URL
	postIDStr := c.Param("postId")
	if postIDStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_POST_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	postID, err := strconv.ParseUint(postIDStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
		return
	}

	// Parse request
	var req request.UpdateBlogBlockPostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Update post in blog block
	blogBlockPost, err := h.blogBlockService.UpdatePost(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), uint(postID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, blogBlockPost, nil)
}

// GetPosts handles getting posts in a blog block
func (h *BlogBlockHandler) GetPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Parse query
	var req request.ListBlogBlockPostsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Limit should be between 1 and 100
	if req.Limit <= 0 {
		req.Limit = 10
	} else if req.Limit > 100 {
		req.Limit = 100
	}

	// Get posts in blog block
	blogBlockPosts, err := h.blogBlockService.GetPosts(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	meta := &response.Meta{
		NextCursor: blogBlockPosts.Meta.NextCursor,
		HasMore:    blogBlockPosts.Meta.HasMore,
	}
	response.Success(c, blogBlockPosts.Data, meta)
}

// BatchAddPosts handles adding multiple posts to a blog block
func (h *BlogBlockHandler) BatchAddPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.BatchAddPostsToBlogBlockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Batch add posts to blog block
	err = h.blogBlockService.BatchAddPosts(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// ReorderPosts handles reordering posts in a blog block
func (h *BlogBlockHandler) ReorderPosts(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get blog block ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Blog block ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing blog block ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	blogBlockID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid blog block ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog block ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.ReorderBlogBlockPostsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Reorder posts in blog block
	err = h.blogBlockService.ReorderPosts(c.Request.Context(), tenantID, websiteID, uint(blogBlockID), req)
	if err != nil {
		handleBlogBlockServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// handleBlogBlockServiceError handles blog block service errors
func handleBlogBlockServiceError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	errMsg := err.Error()

	switch {
	case errMsg == "blog block not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Blog block not found", "BLOG_BLOCK_NOT_FOUND", nil)
	case errMsg == "post not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Post not found", "POST_NOT_FOUND", nil)
	case strings.Contains(errMsg, "code") && strings.Contains(errMsg, "already exists"):
		response.ErrorWithDetails(c, http.StatusConflict, errMsg, "BLOG_BLOCK_CODE_EXISTS", nil)
	case strings.Contains(errMsg, "already in this blog block"):
		response.ErrorWithDetails(c, http.StatusConflict, errMsg, "POST_ALREADY_IN_BLOCK", nil)
	case strings.Contains(errMsg, "not found in blog block"):
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_POST_ID", details)
	default:
		details := []interface{}{map[string]string{"message": errMsg}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "SERVER_ERROR", details)
	}
}
