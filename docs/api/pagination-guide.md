# API Pagination Guide - Cursor-Based Pagination

## Tổng Quan

Hệ thống sử dụng **Cursor-based Pagination** thay vì page-based pagination để đảm bảo:
- ✅ **Consistency**: Không bị duplicate/missing items khi data thay đổi
- ✅ **Performance**: Hi<PERSON><PERSON> suất tốt hơn với large datasets
- ✅ **Real-time**: <PERSON><PERSON> hợp với data thay đổi liên tục

## Request Format

### Query Parameters

```typescript
interface PaginationParams {
  limit?: number;     // Số lượng items mỗi page (1-100, default: 10)
  cursor?: string;    // Cursor token để lấy page tiếp theo
  search?: string;    // Tìm kiếm (tùy endpoint)
  // ... other filters
}
```

### Example Requests

```bash
# Page đầu tiên
GET /api/admin/v1/rbac/roles?limit=10

# Page tiếp theo với cursor
GET /api/admin/v1/rbac/roles?limit=10&cursor=eyJ0eXBlIjoiaWQiLCJ2YWx1ZSI6MTAsImRpcmVjdGlvbiI6Im5leHQifQ==

# Với search
GET /api/admin/v1/rbac/roles?limit=10&search=admin
```

## Response Format

### Successful Response

```typescript
interface PaginationResponse<T> {
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code: string;
    path: string;
    timestamp: string;
    details: any;
  };
  data: T[];           // Array of items
  meta: {
    next_cursor?: string;  // Token cho page tiếp theo (nếu có)
    has_more?: boolean;    // Có còn data không
  };
}
```

### Example Response

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": "",
    "path": "/api/admin/v1/rbac/roles",
    "timestamp": "2025-06-13T16:05:18+07:00",
    "details": null
  },
  "data": [
    {
      "role_id": 1,
      "role_code": "super_admin",
      "role_name": "Super Admin",
      "role_description": "Quyền cao nhất trong hệ thống"
    },
    {
      "role_id": 2,
      "role_code": "tenant_admin", 
      "role_name": "Tenant Admin",
      "role_description": "Quyền quản trị một tenant cụ thể"
    }
  ],
  "meta": {
    "next_cursor": "eyJ0eXBlIjoiaWQiLCJ2YWx1ZSI6MiwiZGlyZWN0aW9uIjoibmV4dCJ9",
    "has_more": true
  }
}
```

## UI Implementation Guide

### 1. React Hook Example

```typescript
import { useState, useEffect } from 'react';

interface UsePaginationOptions {
  endpoint: string;
  limit?: number;
  filters?: Record<string, any>;
}

interface PaginationState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  nextCursor: string | null;
}

function usePagination<T>({ endpoint, limit = 10, filters = {} }: UsePaginationOptions) {
  const [state, setState] = useState<PaginationState<T>>({
    data: [],
    loading: false,
    error: null,
    hasMore: false,
    nextCursor: null,
  });

  const loadData = async (cursor?: string, reset = false) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        ...filters,
        ...(cursor && { cursor }),
      });

      const response = await fetch(`${endpoint}?${params}`, {
        headers: {
          'Authorization': `Bearer ${getAccessToken()}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      setState(prev => ({
        ...prev,
        data: reset ? result.data : [...prev.data, ...result.data],
        hasMore: result.meta?.has_more || false,
        nextCursor: result.meta?.next_cursor || null,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error.message,
        loading: false,
      }));
    }
  };

  const loadMore = () => {
    if (state.nextCursor && !state.loading) {
      loadData(state.nextCursor);
    }
  };

  const refresh = () => {
    loadData(undefined, true);
  };

  useEffect(() => {
    loadData(undefined, true);
  }, [endpoint, limit, JSON.stringify(filters)]);

  return {
    ...state,
    loadMore,
    refresh,
  };
}
```

### 2. Component Usage Example

```typescript
import React from 'react';

interface Role {
  role_id: number;
  role_code: string;
  role_name: string;
  role_description: string;
}

function RolesList() {
  const { data, loading, error, hasMore, loadMore } = usePagination<Role>({
    endpoint: '/api/admin/v1/rbac/roles',
    limit: 10,
  });

  if (error) {
    return <div className="error">Error: {error}</div>;
  }

  return (
    <div className="roles-list">
      <div className="roles-grid">
        {data.map((role) => (
          <div key={role.role_id} className="role-card">
            <h3>{role.role_name}</h3>
            <p>{role.role_description}</p>
            <span className="role-code">{role.role_code}</span>
          </div>
        ))}
      </div>

      {loading && <div className="loading">Loading...</div>}
      
      {hasMore && !loading && (
        <button onClick={loadMore} className="load-more-btn">
          Load More
        </button>
      )}
      
      {!hasMore && data.length > 0 && (
        <div className="end-message">No more items to load</div>
      )}
    </div>
  );
}
```

### 3. Infinite Scroll Implementation

```typescript
import { useEffect, useRef } from 'react';

function useInfiniteScroll(callback: () => void, hasMore: boolean, loading: boolean) {
  const observer = useRef<IntersectionObserver>();
  const lastElementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (loading) return;
    
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        callback();
      }
    });
    
    if (lastElementRef.current) {
      observer.current.observe(lastElementRef.current);
    }
  }, [callback, hasMore, loading]);

  return lastElementRef;
}

// Usage in component
function InfiniteRolesList() {
  const { data, loading, hasMore, loadMore } = usePagination<Role>({
    endpoint: '/api/admin/v1/rbac/roles',
    limit: 20,
  });

  const lastElementRef = useInfiniteScroll(loadMore, hasMore, loading);

  return (
    <div className="infinite-list">
      {data.map((role, index) => (
        <div 
          key={role.role_id}
          ref={index === data.length - 1 ? lastElementRef : null}
          className="role-item"
        >
          <h3>{role.role_name}</h3>
          <p>{role.role_description}</p>
        </div>
      ))}
      
      {loading && <div className="loading-spinner">Loading...</div>}
    </div>
  );
}
```

## Available Endpoints

### RBAC Module

| Endpoint | Description | Filters |
|----------|-------------|---------|
| `GET /api/admin/v1/rbac/roles` | List roles | `search` |
| `GET /api/admin/v1/rbac/permissions` | List permissions | `group_id`, `search` |
| `GET /api/admin/v1/rbac/permission-groups` | List permission groups | `search` |

### Example Filters

```typescript
// Roles with search
const { data } = usePagination<Role>({
  endpoint: '/api/admin/v1/rbac/roles',
  filters: { search: 'admin' }
});

// Permissions by group
const { data } = usePagination<Permission>({
  endpoint: '/api/admin/v1/rbac/permissions',
  filters: { group_id: 1 }
});
```

## Error Handling

### Common Error Responses

```typescript
interface ErrorResponse {
  status: {
    code: number;
    message: string;
    success: false;
    error_code: string;
    details?: any;
  };
}

// Example error handling
const handleApiError = (error: ErrorResponse) => {
  switch (error.status.error_code) {
    case 'INVALID_CURSOR':
      // Cursor không hợp lệ - reset pagination
      refresh();
      break;
    case 'UNAUTHORIZED':
      // Token hết hạn - redirect to login
      redirectToLogin();
      break;
    default:
      // Show generic error
      showErrorMessage(error.status.message);
  }
};
```

## Best Practices

### 1. ✅ DO

- **Always check `has_more`** trước khi load more
- **Store cursor** để có thể load more
- **Handle loading states** để UX tốt hơn
- **Implement error handling** cho network issues
- **Use reasonable limit** (10-50 items per page)
- **Reset pagination** khi filters thay đổi

### 2. ❌ DON'T

- **Don't manipulate cursor** - treat as opaque token
- **Don't assume cursor format** - có thể thay đổi
- **Don't skip error handling** - network có thể fail
- **Don't use very large limits** - impact performance
- **Don't cache cursors long-term** - có thể expire

## TypeScript Definitions

```typescript
// Common types for all pagination responses
export interface PaginationMeta {
  next_cursor?: string;
  has_more?: boolean;
}

export interface ApiResponse<T> {
  status: {
    code: number;
    message: string;
    success: boolean;
    error_code: string;
    path: string;
    timestamp: string;
    details?: any;
  };
  data: T;
  meta?: PaginationMeta;
}

export interface PaginationParams {
  limit?: number;
  cursor?: string;
  [key: string]: any; // For additional filters
}

// Specific entity types
export interface Role {
  role_id: number;
  role_code: string;
  tenant_id: number | null;
  role_name: string;
  role_description: string;
  created_by: number | null;
  created_at: string;
  updated_by: number | null;
  updated_at: string;
}

export interface Permission {
  permission_id: number;
  permission_code: string;
  permission_name: string;
  permission_description: string;
  group_id: number;
  // ... other fields
}

export interface PermissionGroup {
  group_id: number;
  permission_group_name: string;
  permission_group_description: string;
  // ... other fields
}
```

## Testing

### Unit Test Example

```typescript
import { renderHook, act } from '@testing-library/react-hooks';
import { usePagination } from './usePagination';

// Mock fetch
global.fetch = jest.fn();

describe('usePagination', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should load initial data', async () => {
    const mockResponse = {
      data: [{ id: 1, name: 'Test' }],
      meta: { has_more: true, next_cursor: 'cursor123' }
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    });

    const { result, waitForNextUpdate } = renderHook(() =>
      usePagination({ endpoint: '/api/test' })
    );

    await waitForNextUpdate();

    expect(result.current.data).toEqual(mockResponse.data);
    expect(result.current.hasMore).toBe(true);
    expect(result.current.nextCursor).toBe('cursor123');
  });

  it('should load more data', async () => {
    // Test load more functionality
    // ...
  });
});
```

---

## 📞 Support

Nếu có vấn đề với pagination implementation:

1. **Check Network Tab** - verify request/response format
2. **Check Console** - look for JavaScript errors  
3. **Verify Authentication** - ensure valid Bearer token
4. **Contact Backend Team** - for API-specific issues

**Happy Coding!** 🚀
