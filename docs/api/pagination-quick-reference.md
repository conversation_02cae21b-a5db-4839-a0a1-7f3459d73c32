# Pagination Quick Reference

## 🚀 Quick Start

### Basic Request
```bash
GET /api/admin/v1/rbac/roles?limit=10
```

### With Cursor (Next Page)
```bash
GET /api/admin/v1/rbac/roles?limit=10&cursor=eyJ0eXBlIjoiaWQiLCJ2YWx1ZSI6MTAsImRpcmVjdGlvbiI6Im5leHQifQ==
```

## 📋 Request Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `limit` | number | No | 10 | Items per page (1-100) |
| `cursor` | string | No | - | Cursor token for next page |
| `search` | string | No | - | Search query (endpoint specific) |

## 📤 Response Format

```typescript
{
  "status": { /* Standard status object */ },
  "data": T[],                    // Array of items
  "meta": {
    "next_cursor"?: string,       // Token for next page
    "has_more"?: boolean          // More data available
  }
}
```

## 🔗 Available Endpoints

| Endpoint | Description | Extra Filters |
|----------|-------------|---------------|
| `/api/admin/v1/rbac/roles` | List roles | `search` |
| `/api/admin/v1/rbac/permissions` | List permissions | `group_id`, `search` |
| `/api/admin/v1/rbac/permission-groups` | List permission groups | `search` |

## 💡 Implementation Patterns

### React Hook Pattern
```typescript
const { data, loading, hasMore, loadMore } = usePagination({
  endpoint: '/api/admin/v1/rbac/roles',
  limit: 10
});
```

### Vue Composable Pattern
```typescript
const { data, loading, hasMore, loadMore } = usePagination({
  endpoint: '/api/admin/v1/rbac/roles',
  limit: 10
});
```

### Angular Service Pattern
```typescript
const pagination = this.paginationService.createPagination<Role>(
  '/api/admin/v1/rbac/roles',
  10
);
```

## 🎯 Key Concepts

### ✅ DO
- Check `has_more` before loading more
- Handle loading states
- Implement error handling
- Use reasonable limits (10-50)
- Reset on filter changes

### ❌ DON'T
- Manipulate cursor tokens
- Cache cursors long-term
- Use very large limits
- Skip error handling

## 🔧 Common Code Snippets

### Fetch with Cursor
```javascript
const fetchData = async (cursor) => {
  const params = new URLSearchParams({
    limit: '10',
    ...(cursor && { cursor })
  });
  
  const response = await fetch(`/api/endpoint?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  return response.json();
};
```

### Load More Logic
```javascript
const loadMore = () => {
  if (nextCursor && !loading) {
    loadData(nextCursor);
  }
};
```

### Infinite Scroll
```javascript
const observer = new IntersectionObserver(entries => {
  if (entries[0].isIntersecting && hasMore && !loading) {
    loadMore();
  }
});
```

## 🐛 Error Handling

### Common Errors
| Error Code | Description | Action |
|------------|-------------|--------|
| `INVALID_CURSOR` | Cursor malformed/expired | Reset pagination |
| `UNAUTHORIZED` | Token invalid | Redirect to login |
| `VALIDATION_ERROR` | Invalid parameters | Check request params |

### Error Handler
```javascript
const handleError = (error) => {
  switch (error.status?.error_code) {
    case 'INVALID_CURSOR':
      refresh(); // Reset pagination
      break;
    case 'UNAUTHORIZED':
      redirectToLogin();
      break;
    default:
      showErrorMessage(error.status?.message);
  }
};
```

## 🧪 Testing Examples

### Mock Response
```javascript
const mockResponse = {
  status: { code: 200, success: true },
  data: [
    { role_id: 1, role_name: 'Admin' },
    { role_id: 2, role_name: 'User' }
  ],
  meta: {
    next_cursor: 'eyJ0eXBlIjoiaWQiLCJ2YWx1ZSI6MiwiZGlyZWN0aW9uIjoibmV4dCJ9',
    has_more: true
  }
};
```

### Test Load More
```javascript
test('should load more data', async () => {
  // Mock first page
  fetch.mockResolvedValueOnce({
    ok: true,
    json: () => Promise.resolve(mockFirstPage)
  });
  
  // Mock second page
  fetch.mockResolvedValueOnce({
    ok: true,
    json: () => Promise.resolve(mockSecondPage)
  });
  
  const { result } = renderHook(() => usePagination({ endpoint: '/api/test' }));
  
  await waitFor(() => expect(result.current.data).toHaveLength(2));
  
  act(() => {
    result.current.loadMore();
  });
  
  await waitFor(() => expect(result.current.data).toHaveLength(4));
});
```

## 🎨 UI States

### Loading State
```jsx
{loading && (
  <div className="loading">
    <Spinner /> Loading...
  </div>
)}
```

### Load More Button
```jsx
{hasMore && !loading && (
  <button onClick={loadMore}>
    Load More
  </button>
)}
```

### End State
```jsx
{!hasMore && data.length > 0 && (
  <div className="end-message">
    No more items to load
  </div>
)}
```

### Empty State
```jsx
{!loading && data.length === 0 && (
  <div className="empty-state">
    No items found
  </div>
)}
```

## 📱 Mobile Considerations

### Touch-Friendly Load More
```css
.load-more-btn {
  min-height: 44px; /* iOS touch target */
  padding: 12px 24px;
  font-size: 16px; /* Prevent zoom on iOS */
}
```

### Pull to Refresh
```javascript
let startY = 0;
let pullDistance = 0;

container.addEventListener('touchstart', (e) => {
  startY = e.touches[0].clientY;
});

container.addEventListener('touchmove', (e) => {
  if (container.scrollTop === 0) {
    pullDistance = e.touches[0].clientY - startY;
    if (pullDistance > 100) {
      // Show refresh indicator
    }
  }
});

container.addEventListener('touchend', () => {
  if (pullDistance > 100) {
    refresh();
  }
  pullDistance = 0;
});
```

## 🔍 Debugging

### Check Network Tab
1. Verify request URL and parameters
2. Check response format
3. Verify Authorization header
4. Look for HTTP status codes

### Console Debugging
```javascript
console.log('Pagination State:', {
  dataLength: data.length,
  hasMore,
  nextCursor,
  loading
});
```

### Cursor Inspection
```javascript
// Decode cursor to see content (for debugging only)
const decodeCursor = (cursor) => {
  try {
    return JSON.parse(atob(cursor));
  } catch (e) {
    return 'Invalid cursor';
  }
};

console.log('Cursor content:', decodeCursor(nextCursor));
```

## 📊 Performance Tips

### Optimize Re-renders
```javascript
// React: Use useMemo for expensive operations
const processedData = useMemo(() => {
  return data.map(item => ({
    ...item,
    displayName: formatName(item.name)
  }));
}, [data]);

// Vue: Use computed properties
const processedData = computed(() => {
  return data.value.map(item => ({
    ...item,
    displayName: formatName(item.name)
  }));
});
```

### Virtual Scrolling (Large Lists)
```javascript
// Use libraries like react-window, vue-virtual-scroller
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={80}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        {data[index].name}
      </div>
    )}
  </List>
);
```

## 🔐 Security Notes

- **Never decode/modify cursors** - treat as opaque tokens
- **Always include Authorization header** for protected endpoints
- **Validate user permissions** before showing sensitive data
- **Implement rate limiting** on client side if needed

## 📞 Support Contacts

- **Backend API Issues**: Backend Team
- **Authentication Problems**: Auth Team  
- **UI/UX Questions**: Frontend Team
- **Performance Issues**: DevOps Team

---

**Happy Coding!** 🚀

*Last updated: 2025-06-13*
