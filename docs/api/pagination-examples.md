# Pagination Implementation Examples

## Vue.js 3 + Composition API

### 1. Composable Hook

```typescript
// composables/usePagination.ts
import { ref, reactive, computed } from 'vue'

interface PaginationOptions {
  endpoint: string
  limit?: number
  filters?: Record<string, any>
}

export function usePagination<T>(options: PaginationOptions) {
  const { endpoint, limit = 10, filters = {} } = options
  
  const state = reactive({
    data: [] as T[],
    loading: false,
    error: null as string | null,
    nextCursor: null as string | null,
    hasMore: false
  })

  const loadData = async (cursor?: string, reset = false) => {
    state.loading = true
    state.error = null

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        ...filters,
        ...(cursor && { cursor })
      })

      const response = await $fetch(`${endpoint}?${params}`)
      
      if (reset) {
        state.data = response.data
      } else {
        state.data.push(...response.data)
      }
      
      state.hasMore = response.meta?.has_more || false
      state.nextCursor = response.meta?.next_cursor || null
    } catch (error) {
      state.error = error.message
    } finally {
      state.loading = false
    }
  }

  const loadMore = () => {
    if (state.nextCursor && !state.loading) {
      loadData(state.nextCursor)
    }
  }

  const refresh = () => {
    loadData(undefined, true)
  }

  // Auto load on mount
  loadData(undefined, true)

  return {
    ...toRefs(state),
    loadMore,
    refresh
  }
}
```

### 2. Component Usage

```vue
<!-- components/RolesList.vue -->
<template>
  <div class="roles-list">
    <div v-if="error" class="error">
      {{ error }}
    </div>

    <div class="roles-grid">
      <div 
        v-for="role in data" 
        :key="role.role_id"
        class="role-card"
      >
        <h3>{{ role.role_name }}</h3>
        <p>{{ role.role_description }}</p>
        <span class="badge">{{ role.role_code }}</span>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      Loading...
    </div>

    <button 
      v-if="hasMore && !loading"
      @click="loadMore"
      class="load-more-btn"
    >
      Load More
    </button>

    <div v-if="!hasMore && data.length > 0" class="end-message">
      No more roles to load
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePagination } from '@/composables/usePagination'

interface Role {
  role_id: number
  role_code: string
  role_name: string
  role_description: string
}

const { data, loading, error, hasMore, loadMore } = usePagination<Role>({
  endpoint: '/api/admin/v1/rbac/roles',
  limit: 12
})
</script>
```

## Angular + RxJS

### 1. Service

```typescript
// services/pagination.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

interface PaginationState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  nextCursor: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class PaginationService {
  constructor(private http: HttpClient) {}

  createPagination<T>(endpoint: string, limit = 10) {
    const initialState: PaginationState<T> = {
      data: [],
      loading: false,
      error: null,
      hasMore: false,
      nextCursor: null
    };

    const state$ = new BehaviorSubject(initialState);

    const loadData = (cursor?: string, reset = false) => {
      const currentState = state$.value;
      state$.next({ ...currentState, loading: true, error: null });

      const params: any = { limit: limit.toString() };
      if (cursor) params.cursor = cursor;

      this.http.get<any>(`${endpoint}`, { params })
        .pipe(
          catchError(error => {
            state$.next({
              ...currentState,
              loading: false,
              error: error.message
            });
            return throwError(error);
          }),
          finalize(() => {
            const current = state$.value;
            state$.next({ ...current, loading: false });
          })
        )
        .subscribe(response => {
          const current = state$.value;
          state$.next({
            ...current,
            data: reset ? response.data : [...current.data, ...response.data],
            hasMore: response.meta?.has_more || false,
            nextCursor: response.meta?.next_cursor || null
          });
        });
    };

    const loadMore = () => {
      const current = state$.value;
      if (current.nextCursor && !current.loading) {
        loadData(current.nextCursor);
      }
    };

    const refresh = () => {
      loadData(undefined, true);
    };

    // Auto load initial data
    loadData(undefined, true);

    return {
      state$: state$.asObservable(),
      loadMore,
      refresh
    };
  }
}
```

### 2. Component

```typescript
// components/roles-list.component.ts
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PaginationService } from '../services/pagination.service';

interface Role {
  role_id: number;
  role_code: string;
  role_name: string;
  role_description: string;
}

@Component({
  selector: 'app-roles-list',
  template: `
    <div class="roles-list">
      <div *ngIf="error" class="error">{{ error }}</div>
      
      <div class="roles-grid">
        <div *ngFor="let role of data" class="role-card">
          <h3>{{ role.role_name }}</h3>
          <p>{{ role.role_description }}</p>
          <span class="badge">{{ role.role_code }}</span>
        </div>
      </div>

      <div *ngIf="loading" class="loading">Loading...</div>
      
      <button 
        *ngIf="hasMore && !loading"
        (click)="loadMore()"
        class="load-more-btn"
      >
        Load More
      </button>
    </div>
  `
})
export class RolesListComponent implements OnInit, OnDestroy {
  data: Role[] = [];
  loading = false;
  error: string | null = null;
  hasMore = false;

  private destroy$ = new Subject<void>();
  private pagination: any;

  constructor(private paginationService: PaginationService) {}

  ngOnInit() {
    this.pagination = this.paginationService.createPagination<Role>(
      '/api/admin/v1/rbac/roles',
      10
    );

    this.pagination.state$
      .pipe(takeUntil(this.destroy$))
      .subscribe(state => {
        this.data = state.data;
        this.loading = state.loading;
        this.error = state.error;
        this.hasMore = state.hasMore;
      });
  }

  loadMore() {
    this.pagination.loadMore();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
```

## Svelte/SvelteKit

### 1. Store

```typescript
// stores/pagination.ts
import { writable, derived } from 'svelte/store';

interface PaginationState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  nextCursor: string | null;
}

export function createPagination<T>(endpoint: string, limit = 10) {
  const initialState: PaginationState<T> = {
    data: [],
    loading: false,
    error: null,
    hasMore: false,
    nextCursor: null
  };

  const { subscribe, update } = writable(initialState);

  const loadData = async (cursor?: string, reset = false) => {
    update(state => ({ ...state, loading: true, error: null }));

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        ...(cursor && { cursor })
      });

      const response = await fetch(`${endpoint}?${params}`);
      const result = await response.json();

      update(state => ({
        ...state,
        data: reset ? result.data : [...state.data, ...result.data],
        hasMore: result.meta?.has_more || false,
        nextCursor: result.meta?.next_cursor || null,
        loading: false
      }));
    } catch (error) {
      update(state => ({
        ...state,
        error: error.message,
        loading: false
      }));
    }
  };

  const loadMore = () => {
    const state = get({ subscribe });
    if (state.nextCursor && !state.loading) {
      loadData(state.nextCursor);
    }
  };

  const refresh = () => {
    loadData(undefined, true);
  };

  // Auto load
  loadData(undefined, true);

  return {
    subscribe,
    loadMore,
    refresh
  };
}
```

### 2. Component

```svelte
<!-- RolesList.svelte -->
<script lang="ts">
  import { createPagination } from '../stores/pagination';
  
  interface Role {
    role_id: number;
    role_code: string;
    role_name: string;
    role_description: string;
  }

  const pagination = createPagination<Role>('/api/admin/v1/rbac/roles', 10);
  
  $: ({ data, loading, error, hasMore } = $pagination);
</script>

<div class="roles-list">
  {#if error}
    <div class="error">{error}</div>
  {/if}

  <div class="roles-grid">
    {#each data as role (role.role_id)}
      <div class="role-card">
        <h3>{role.role_name}</h3>
        <p>{role.role_description}</p>
        <span class="badge">{role.role_code}</span>
      </div>
    {/each}
  </div>

  {#if loading}
    <div class="loading">Loading...</div>
  {/if}

  {#if hasMore && !loading}
    <button on:click={pagination.loadMore} class="load-more-btn">
      Load More
    </button>
  {/if}

  {#if !hasMore && data.length > 0}
    <div class="end-message">No more roles to load</div>
  {/if}
</div>
```

## Vanilla JavaScript

### 1. Pagination Class

```javascript
// pagination.js
class Pagination {
  constructor(endpoint, options = {}) {
    this.endpoint = endpoint;
    this.limit = options.limit || 10;
    this.filters = options.filters || {};
    
    this.state = {
      data: [],
      loading: false,
      error: null,
      hasMore: false,
      nextCursor: null
    };
    
    this.listeners = [];
  }

  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  notify() {
    this.listeners.forEach(listener => listener(this.state));
  }

  async loadData(cursor, reset = false) {
    this.state.loading = true;
    this.state.error = null;
    this.notify();

    try {
      const params = new URLSearchParams({
        limit: this.limit.toString(),
        ...this.filters,
        ...(cursor && { cursor })
      });

      const response = await fetch(`${this.endpoint}?${params}`, {
        headers: {
          'Authorization': `Bearer ${getAccessToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      this.state.data = reset ? result.data : [...this.state.data, ...result.data];
      this.state.hasMore = result.meta?.has_more || false;
      this.state.nextCursor = result.meta?.next_cursor || null;
    } catch (error) {
      this.state.error = error.message;
    } finally {
      this.state.loading = false;
      this.notify();
    }
  }

  loadMore() {
    if (this.state.nextCursor && !this.state.loading) {
      this.loadData(this.state.nextCursor);
    }
  }

  refresh() {
    this.loadData(undefined, true);
  }

  // Auto load initial data
  init() {
    this.loadData(undefined, true);
  }
}
```

### 2. Usage

```javascript
// app.js
const rolesPagination = new Pagination('/api/admin/v1/rbac/roles', {
  limit: 10
});

const rolesContainer = document.getElementById('roles-container');
const loadMoreBtn = document.getElementById('load-more-btn');
const loadingDiv = document.getElementById('loading');

rolesPagination.subscribe(state => {
  // Update UI
  if (state.loading) {
    loadingDiv.style.display = 'block';
  } else {
    loadingDiv.style.display = 'none';
  }

  if (state.error) {
    rolesContainer.innerHTML = `<div class="error">${state.error}</div>`;
    return;
  }

  // Render roles
  rolesContainer.innerHTML = state.data.map(role => `
    <div class="role-card">
      <h3>${role.role_name}</h3>
      <p>${role.role_description}</p>
      <span class="badge">${role.role_code}</span>
    </div>
  `).join('');

  // Show/hide load more button
  if (state.hasMore && !state.loading) {
    loadMoreBtn.style.display = 'block';
  } else {
    loadMoreBtn.style.display = 'none';
  }
});

loadMoreBtn.addEventListener('click', () => {
  rolesPagination.loadMore();
});

// Initialize
rolesPagination.init();
```

## CSS Styles (Common)

```css
/* pagination-styles.css */
.roles-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.roles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.role-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.role-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.role-card h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 1.2em;
}

.role-card p {
  margin: 0 0 15px 0;
  color: #7f8c8d;
  line-height: 1.4;
}

.badge {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

.load-more-btn {
  display: block;
  width: 200px;
  margin: 20px auto;
  padding: 12px 24px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.load-more-btn:hover {
  background: #2980b9;
}

.load-more-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #7f8c8d;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #e74c3c;
  color: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
}

.end-message {
  text-align: center;
  padding: 20px;
  color: #7f8c8d;
  font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
  .roles-grid {
    grid-template-columns: 1fr;
  }
  
  .roles-list {
    padding: 10px;
  }
}
```

---

Các examples này cung cấp implementation hoàn chỉnh cho các framework phổ biến, giúp UI team có thể nhanh chóng integrate cursor pagination vào ứng dụng của họ! 🚀
