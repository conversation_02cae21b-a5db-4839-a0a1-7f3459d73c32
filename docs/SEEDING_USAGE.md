# Seeding System Usage Guide

## Overview
The seeding system provides a robust way to populate your database with initial data across different environments and modules.

## Quick Start

### 1. List Available Seeds
```bash
# Using CLI
go run cmd/cli/main.go seed list

# Using standalone command
go run cmd/seed/main.go -list
```

### 2. Run All Seeds (Dry Run)
```bash
# Test what would be seeded
go run cmd/cli/main.go seed all --dry-run --verbose

# Or standalone
go run cmd/seed/main.go -dry-run -verbose
```

### 3. Seed Specific Module
```bash
# Seed auth module
go run cmd/cli/main.go seed module auth --env=dev --verbose

# Or standalone
go run cmd/seed/main.go -module=auth -env=dev -verbose
```

### 4. Seed Specific Seeder
```bash
# Seed only tenants
go run cmd/cli/main.go seed specific auth tenants --verbose

# Or standalone
go run cmd/seed/main.go -module=auth -seeder=tenants -verbose
```

### 5. Rollback Seeds
```bash
# Rollback auth module
go run cmd/cli/main.go seed rollback module auth

# Rollback specific seeder
go run cmd/cli/main.go seed rollback specific auth tenants
```

## Environment Support

### Development Environment
```bash
go run cmd/seed/main.go -env=dev -module=auth
```
- Seeds all data including test users
- Creates default tenants and roles
- Sets up complete RBAC structure

### Staging Environment
```bash
go run cmd/seed/main.go -env=staging -module=auth
```
- Seeds production-like data
- Includes test users for staging
- Full RBAC setup

### Production Environment
```bash
go run cmd/seed/main.go -env=prod -module=auth
```
- Seeds only essential data
- No test users
- Minimal required roles and permissions

## Data Files Structure

### Auth Module Data Files
```
modules/auth/seeds/data/
├── tenants.json          # Default tenants
├── roles.json            # RBAC roles
├── permissions.json      # System permissions
├── users.json            # Default users (dev/staging only)
├── user_roles.json       # User-role assignments
└── role_permissions.json # Role-permission mappings
```

### Example Data File Format
```json
{
  "version": "1.0.0",
  "description": "Default tenants for the system",
  "environment": {
    "dev": true,
    "staging": true,
    "prod": true
  },
  "metadata": {
    "created_at": "2024-01-01T00:00:00Z",
    "created_by": "system",
    "tags": ["auth", "tenants", "default"]
  },
  "data": [
    {
      "tenant_id": 1,
      "tenant_name": "Default Tenant",
      "tenant_code": "default",
      "status": "active",
      "plan_type": "standard"
    }
  ]
}
```

## Advanced Features

### Dry Run Mode
Test seeding without making database changes:
```bash
go run cmd/seed/main.go -dry-run -verbose
```

### Verbose Logging
Get detailed information about the seeding process:
```bash
go run cmd/seed/main.go -verbose
```

### Skip Existing Data
The system automatically skips existing data based on unique constraints.

### Dependency Management
Seeders run in the correct order based on dependencies:
1. Tenants (no dependencies)
2. Roles (depends on tenants)
3. Permissions (no dependencies)
4. Users (depends on tenants)
5. User Roles (depends on users and roles)
6. Role Permissions (depends on roles and permissions)

## Configuration

### Environment Variables
```bash
# Database configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=wnapi

# Seed configuration
SEED_DATA_PATH=./
SEED_BATCH_SIZE=100
```

### Config File (.env)
```env
# Database
db_host=localhost
db_port=3306
db_username=root
db_password=password
db_database=wnapi

# Logger
logger.format=json
logger.level=info
```

## Error Handling

The seeding system provides comprehensive error handling:
- Validation errors for malformed data
- Database constraint violations
- File not found errors
- Network connectivity issues

### Common Issues and Solutions

1. **Database Connection Failed**
   ```bash
   # Check database configuration
   go run cmd/seed/main.go -config=@.env.local
   ```

2. **Data File Not Found**
   ```bash
   # Verify data files exist
   ls -la modules/auth/seeds/data/
   ```

3. **Permission Denied**
   ```bash
   # Check database user permissions
   # Ensure user has CREATE, INSERT, UPDATE, DELETE permissions
   ```

## Testing

### Test Script
Use the provided test script to verify functionality:
```bash
./test_seed.sh
```

### Manual Testing
```bash
# 1. Test dry run
go run cmd/seed/main.go -dry-run -verbose

# 2. Test specific module
go run cmd/seed/main.go -module=auth -dry-run

# 3. Test rollback
go run cmd/seed/main.go -module=auth
go run cmd/cli/main.go seed rollback module auth
```

## Extending the System

### Adding New Seeders
1. Create seeder struct implementing `seed.Seeder` interface
2. Add data JSON file
3. Register seeder in module's `init.go`
4. Update dependencies if needed

### Adding New Modules
1. Create module seed structure
2. Implement `seed.ModuleSeed` interface
3. Register module in main application
4. Add CLI integration

## Best Practices

1. **Always test with dry-run first**
2. **Use environment-specific data files**
3. **Implement proper validation**
4. **Handle rollbacks gracefully**
5. **Use meaningful seeder names and descriptions**
6. **Document data dependencies**
7. **Keep data files version controlled**
8. **Use consistent data formats**

## Troubleshooting

### Enable Debug Logging
```bash
go run cmd/seed/main.go -verbose -env=dev
```

### Check Database State
```sql
-- Check seeded data
SELECT * FROM tenants;
SELECT * FROM rbac_roles;
SELECT * FROM rbac_permissions;
SELECT * FROM users;
```

### Reset Database
```bash
# Rollback all seeds
go run cmd/cli/main.go seed rollback module auth

# Or manually truncate tables
# TRUNCATE TABLE tenants, rbac_roles, rbac_permissions, users;
```
