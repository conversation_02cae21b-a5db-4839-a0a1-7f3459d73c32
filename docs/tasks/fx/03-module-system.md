# Task 03: Module System Migration

## Objective
Tạo hệ thống module mới sử dụng fx.Module để thay thế GlobalModuleRegistry. Implement module discovery, loading, và integration patterns cho fx-based architecture.

## Input
- Core providers từ Task 02
- Existing GlobalModuleRegistry system
- Current module interface và patterns

## Output
- FX-based module system
- Module discovery mechanism
- Module loading utilities
- Integration với fx.App

## Requirements

### 1. Technical Requirements
- Support dynamic module loading
- Maintain module loading order
- Backward compatibility với existing modules
- Proper error handling và logging

### 2. Architecture Requirements
- Multi-tenant aware module system
- Service registry integration
- Lifecycle management
- Dependency injection support

## Implementation Steps

### Step 1: Module Interface Definition

**File: `internal/fx/modules/interface.go`**
```go
package modules

import (
	"context"
	"go.uber.org/fx"
)

// FXModule defines interface for fx-based modules
type FXModule interface {
	// Name returns module name
	Name() string
	
	// Module returns fx.Module for this module
	Module() fx.Option
	
	// Dependencies returns list of module dependencies
	Dependencies() []string
	
	// Priority returns loading priority (lower = higher priority)
	Priority() int
	
	// Enabled checks if module should be loaded
	Enabled(config map[string]interface{}) bool
}

// ModuleInfo contains module metadata
type ModuleInfo struct {
	Name         string
	Version      string
	Description  string
	Dependencies []string
	Priority     int
	Module       fx.Option
}

// ModuleRegistry manages fx modules
type ModuleRegistry struct {
	modules map[string]FXModule
	loaded  map[string]bool
}

// NewModuleRegistry creates new module registry
func NewModuleRegistry() *ModuleRegistry {
	return &ModuleRegistry{
		modules: make(map[string]FXModule),
		loaded:  make(map[string]bool),
	}
}

// Register registers a module
func (r *ModuleRegistry) Register(module FXModule) error {
	if module == nil {
		return fmt.Errorf("module cannot be nil")
	}
	
	name := module.Name()
	if name == "" {
		return fmt.Errorf("module name cannot be empty")
	}
	
	if _, exists := r.modules[name]; exists {
		return fmt.Errorf("module %s already registered", name)
	}
	
	r.modules[name] = module
	return nil
}

// Get retrieves a module by name
func (r *ModuleRegistry) Get(name string) (FXModule, bool) {
	module, ok := r.modules[name]
	return module, ok
}

// List returns all registered modules
func (r *ModuleRegistry) List() []FXModule {
	modules := make([]FXModule, 0, len(r.modules))
	for _, module := range r.modules {
		modules = append(modules, module)
	}
	return modules
}
```

### Step 2: Module Loader Implementation

**File: `internal/fx/modules/loader.go`**
```go
package modules

import (
	"fmt"
	"sort"
	"strings"
	
	"go.uber.org/fx"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// LoaderConfig contains module loading configuration
type LoaderConfig struct {
	EnabledModules []string
	ModuleConfigs  map[string]map[string]interface{}
	Logger         logger.Logger
}

// ModuleLoader handles module loading and dependency resolution
type ModuleLoader struct {
	registry *ModuleRegistry
	config   LoaderConfig
	logger   logger.Logger
}

// NewModuleLoader creates new module loader
func NewModuleLoader(registry *ModuleRegistry, config LoaderConfig) *ModuleLoader {
	return &ModuleLoader{
		registry: registry,
		config:   config,
		logger:   config.Logger,
	}
}

// LoadModules loads enabled modules in dependency order
func (l *ModuleLoader) LoadModules() ([]fx.Option, error) {
	// Get enabled modules
	enabledModules, err := l.getEnabledModules()
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled modules: %w", err)
	}
	
	// Resolve dependencies
	orderedModules, err := l.resolveDependencies(enabledModules)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve dependencies: %w", err)
	}
	
	// Create fx options
	var options []fx.Option
	for _, module := range orderedModules {
		l.logger.Info("Loading module", "name", module.Name())
		options = append(options, module.Module())
	}
	
	return options, nil
}

// getEnabledModules returns list of enabled modules
func (l *ModuleLoader) getEnabledModules() ([]FXModule, error) {
	var enabled []FXModule
	
	for _, moduleName := range l.config.EnabledModules {
		module, ok := l.registry.Get(moduleName)
		if !ok {
			l.logger.Warn("Module not found", "name", moduleName)
			continue
		}
		
		// Check if module is enabled
		moduleConfig := l.config.ModuleConfigs[moduleName]
		if !module.Enabled(moduleConfig) {
			l.logger.Info("Module disabled", "name", moduleName)
			continue
		}
		
		enabled = append(enabled, module)
	}
	
	return enabled, nil
}

// resolveDependencies resolves module dependencies and returns ordered list
func (l *ModuleLoader) resolveDependencies(modules []FXModule) ([]FXModule, error) {
	// Create dependency graph
	graph := make(map[string][]string)
	moduleMap := make(map[string]FXModule)
	
	for _, module := range modules {
		name := module.Name()
		graph[name] = module.Dependencies()
		moduleMap[name] = module
	}
	
	// Topological sort
	ordered, err := l.topologicalSort(graph)
	if err != nil {
		return nil, err
	}
	
	// Convert to module list
	var result []FXModule
	for _, name := range ordered {
		if module, ok := moduleMap[name]; ok {
			result = append(result, module)
		}
	}
	
	// Sort by priority within dependency groups
	sort.SliceStable(result, func(i, j int) bool {
		return result[i].Priority() < result[j].Priority()
	})
	
	return result, nil
}

// topologicalSort performs topological sort on dependency graph
func (l *ModuleLoader) topologicalSort(graph map[string][]string) ([]string, error) {
	// Kahn's algorithm
	inDegree := make(map[string]int)
	
	// Initialize in-degree
	for node := range graph {
		inDegree[node] = 0
	}
	
	// Calculate in-degree
	for _, deps := range graph {
		for _, dep := range deps {
			inDegree[dep]++
		}
	}
	
	// Find nodes with no incoming edges
	var queue []string
	for node, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, node)
		}
	}
	
	var result []string
	
	for len(queue) > 0 {
		// Remove node from queue
		node := queue[0]
		queue = queue[1:]
		result = append(result, node)
		
		// Remove edges from this node
		for _, neighbor := range graph[node] {
			inDegree[neighbor]--
			if inDegree[neighbor] == 0 {
				queue = append(queue, neighbor)
			}
		}
	}
	
	// Check for cycles
	if len(result) != len(graph) {
		return nil, fmt.Errorf("circular dependency detected")
	}
	
	return result, nil
}
```

### Step 3: Module Discovery

**File: `internal/fx/modules/discovery.go`**
```go
package modules

import (
	"fmt"
	"strings"
	
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// GlobalRegistry is the global module registry
var GlobalRegistry = NewModuleRegistry()

// RegisterModule registers a module with global registry
func RegisterModule(module FXModule) error {
	return GlobalRegistry.Register(module)
}

// DiscoverModules discovers and loads modules from configuration
func DiscoverModules(cfg config.Config, log logger.Logger) ([]fx.Option, error) {
	// Get enabled modules from config
	enabledModulesStr := cfg.GetStringWithDefault("MODULES_ENABLED", "")
	enabledModules := strings.Split(enabledModulesStr, ",")
	
	// Clean module names
	for i, module := range enabledModules {
		enabledModules[i] = strings.TrimSpace(module)
	}
	
	// Filter empty strings
	var filteredModules []string
	for _, module := range enabledModules {
		if module != "" {
			filteredModules = append(filteredModules, module)
		}
	}
	
	// Create loader config
	loaderConfig := LoaderConfig{
		EnabledModules: filteredModules,
		ModuleConfigs:  getModuleConfigs(cfg),
		Logger:         log,
	}
	
	// Create loader and load modules
	loader := NewModuleLoader(GlobalRegistry, loaderConfig)
	return loader.LoadModules()
}

// getModuleConfigs extracts module-specific configurations
func getModuleConfigs(cfg config.Config) map[string]map[string]interface{} {
	configs := make(map[string]map[string]interface{})
	
	// Get all config keys
	allKeys := cfg.AllKeys()
	
	for _, key := range allKeys {
		// Look for MODULE_<name>_<setting> pattern
		if strings.HasPrefix(key, "MODULE_") {
			parts := strings.Split(key, "_")
			if len(parts) >= 3 {
				moduleName := strings.ToLower(parts[1])
				settingName := strings.Join(parts[2:], "_")
				
				if configs[moduleName] == nil {
					configs[moduleName] = make(map[string]interface{})
				}
				
				configs[moduleName][settingName] = cfg.Get(key)
			}
		}
	}
	
	return configs
}

// ListAvailableModules returns list of available modules
func ListAvailableModules() []ModuleInfo {
	modules := GlobalRegistry.List()
	var info []ModuleInfo
	
	for _, module := range modules {
		info = append(info, ModuleInfo{
			Name:         module.Name(),
			Dependencies: module.Dependencies(),
			Priority:     module.Priority(),
			Module:       module.Module(),
		})
	}
	
	return info
}
```

### Step 4: Integration với FX App

**File: `internal/fx/app.go` (update)**
```go
// Add module loading to NewApp function
func NewApp(opts ...fx.Option) *fx.App {
	defaultOpts := []fx.Option{
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
			providers.NewAppCache,
			providers.NewJWTConfig,
			providers.NewJWTService,
			providers.NewPermissionFactory,
			providers.NewGinEngine,
			providers.NewTracingProvider,
		),
		
		// Module loading
		fx.Invoke(loadModules),
		
		// Lifecycle management
		fx.Invoke(
			lifecycle.RegisterServerHooks,
			func(tp *tracing.Provider) {}, // Ensure tracing is initialized
		),
	}
	
	// Merge with user options
	allOpts := append(defaultOpts, opts...)
	
	return fx.New(allOpts...)
}

// loadModules loads modules dynamically
func loadModules(cfg config.Config, log logger.Logger) error {
	moduleOptions, err := modules.DiscoverModules(cfg, log)
	if err != nil {
		return fmt.Errorf("failed to discover modules: %w", err)
	}
	
	log.Info("Discovered modules", "count", len(moduleOptions))
	return nil
}

// NewAppWithModules creates app with specific modules
func NewAppWithModules(moduleNames []string, opts ...fx.Option) *fx.App {
	// Create temporary config for module loading
	moduleOpts := []fx.Option{
		fx.Supply(moduleNames),
		fx.Invoke(func(names []string, cfg config.Config, log logger.Logger) error {
			// Override enabled modules
			loaderConfig := modules.LoaderConfig{
				EnabledModules: names,
				ModuleConfigs:  modules.getModuleConfigs(cfg),
				Logger:         log,
			}
			
			loader := modules.NewModuleLoader(modules.GlobalRegistry, loaderConfig)
			moduleOptions, err := loader.LoadModules()
			if err != nil {
				return err
			}
			
			// Apply module options
			for _, opt := range moduleOptions {
				opts = append(opts, opt)
			}
			
			return nil
		}),
	}
	
	allOpts := append(moduleOpts, opts...)
	return NewApp(allOpts...)
}
```

### Step 5: Create Module Tests

**File: `internal/fx/modules/modules_test.go`**
```go
package modules

import (
	"testing"
	
	"go.uber.org/fx"
)

// TestModule implements FXModule for testing
type TestModule struct {
	name         string
	dependencies []string
	priority     int
	enabled      bool
}

func (m *TestModule) Name() string                                    { return m.name }
func (m *TestModule) Dependencies() []string                         { return m.dependencies }
func (m *TestModule) Priority() int                                   { return m.priority }
func (m *TestModule) Enabled(config map[string]interface{}) bool     { return m.enabled }
func (m *TestModule) Module() fx.Option                              { return fx.Options() }

func TestModuleRegistry(t *testing.T) {
	registry := NewModuleRegistry()
	
	module := &TestModule{
		name:    "test",
		enabled: true,
	}
	
	// Test registration
	err := registry.Register(module)
	if err != nil {
		t.Fatalf("Failed to register module: %v", err)
	}
	
	// Test retrieval
	retrieved, ok := registry.Get("test")
	if !ok {
		t.Fatal("Failed to retrieve module")
	}
	
	if retrieved.Name() != "test" {
		t.Errorf("Expected module name 'test', got '%s'", retrieved.Name())
	}
}

func TestDependencyResolution(t *testing.T) {
	registry := NewModuleRegistry()
	
	// Create modules with dependencies
	moduleA := &TestModule{name: "a", enabled: true, priority: 1}
	moduleB := &TestModule{name: "b", dependencies: []string{"a"}, enabled: true, priority: 2}
	moduleC := &TestModule{name: "c", dependencies: []string{"b"}, enabled: true, priority: 3}
	
	registry.Register(moduleA)
	registry.Register(moduleB)
	registry.Register(moduleC)
	
	config := LoaderConfig{
		EnabledModules: []string{"c", "a", "b"}, // Intentionally out of order
		ModuleConfigs:  make(map[string]map[string]interface{}),
	}
	
	loader := NewModuleLoader(registry, config)
	options, err := loader.LoadModules()
	if err != nil {
		t.Fatalf("Failed to load modules: %v", err)
	}
	
	if len(options) != 3 {
		t.Errorf("Expected 3 modules, got %d", len(options))
	}
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Module registry implemented và working
- [ ] Module discovery mechanism functional
- [ ] Dependency resolution working correctly
- [ ] Integration với fx.App successful
- [ ] Module loading order respected

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] Circular dependency detection working
- [ ] Proper error handling và logging
- [ ] Memory efficient implementation
- [ ] Thread-safe operations

### Code Quality
- [ ] All functions documented
- [ ] Error messages descriptive
- [ ] Test coverage > 80%
- [ ] No memory leaks
- [ ] Proper abstraction levels

## File Paths

### New Files
- `internal/fx/modules/interface.go`
- `internal/fx/modules/loader.go`
- `internal/fx/modules/discovery.go`
- `internal/fx/modules/modules_test.go`

### Modified Files
- `internal/fx/app.go` (add module loading)

## Dependencies
- Task 02: Core Providers Implementation

## Estimated Time
2 hours

## Notes
- Focus on module system infrastructure
- Don't migrate actual modules yet
- Ensure backward compatibility
- Prepare for module-specific migration tasks
