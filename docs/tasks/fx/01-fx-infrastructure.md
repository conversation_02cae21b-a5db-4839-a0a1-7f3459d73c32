# Task 01: FX Infrastructure Setup

## Objective
Thiết lập infrastructure cơ bản cho uber-go/fx dependency injection framework, bao gồm cài đặt dependencies, tạo c<PERSON>u trú<PERSON> thư mục, và setup basic fx.App.

## Input
- Current AppBootstrap-based architecture
- Existing module system với GlobalModuleRegistry
- Manual dependency injection patterns

## Output
- FX dependencies được cài đặt
- Basic fx.App structure
- Core provider functions skeleton
- Updated go.mod với fx dependencies

## Requirements

### 1. Technical Requirements
- Go 1.21+ compatibility
- Uber FX v1.20+
- Backward compatibility với existing code
- Zero breaking changes trong phase này

### 2. Architecture Requirements
- Follow multi-tenant patterns
- Maintain existing service interfaces
- Support graceful degradation
- Proper error handling

## Implementation Steps

### Step 1: Install FX Dependencies
```bash
# Add fx dependencies
go get go.uber.org/fx@latest
go get go.uber.org/dig@latest

# Verify installation
go mod tidy
go mod verify
```

### Step 2: Create FX Package Structure
```
internal/fx/
├── app.go              # Main fx.App configuration
├── providers/          # Core provider functions
│   ├── config.go       # Config providers
│   ├── logger.go       # Logger providers
│   ├── database.go     # Database providers
│   ├── cache.go        # Cache providers
│   ├── auth.go         # Auth providers
│   └── server.go       # Server providers
├── lifecycle/          # Lifecycle hooks
│   ├── server.go       # Server lifecycle
│   └── cleanup.go      # Cleanup hooks
└── modules/            # Module integration helpers
    ├── registry.go     # Module registry for fx
    └── loader.go       # Module loading utilities
```

### Step 3: Create Basic FX App Structure

**File: `internal/fx/app.go`**
```go
package fx

import (
	"go.uber.org/fx"
	"wnapi/internal/fx/providers"
	"wnapi/internal/fx/lifecycle"
)

// NewApp creates a new fx.App with core providers
func NewApp(opts ...fx.Option) *fx.App {
	defaultOpts := []fx.Option{
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
			providers.NewAppCache,
		),
		
		// Lifecycle management
		fx.Invoke(
			lifecycle.RegisterServerHooks,
		),
	}
	
	// Merge with user options
	allOpts := append(defaultOpts, opts...)
	
	return fx.New(allOpts...)
}

// AppOptions contains configuration for fx.App
type AppOptions struct {
	// EnableModules specifies which modules to load
	EnableModules []string
	
	// EnablePlugins specifies which plugins to load
	EnablePlugins []string
	
	// Development mode settings
	Development bool
	
	// CLI mode (skip certain services)
	CLIMode bool
}

// NewAppWithOptions creates fx.App with specific options
func NewAppWithOptions(opts AppOptions) *fx.App {
	var fxOpts []fx.Option
	
	// Add CLI mode configuration
	if opts.CLIMode {
		fxOpts = append(fxOpts, fx.Provide(func() AppOptions { return opts }))
	}
	
	// Add module loading
	if len(opts.EnableModules) > 0 {
		// Will be implemented in module migration tasks
	}
	
	return NewApp(fxOpts...)
}
```

### Step 4: Create Provider Skeletons

**File: `internal/fx/providers/config.go`**
```go
package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/config/viperconfig"
)

// NewConfig provides config.Config for the application
func NewConfig() (config.Config, error) {
	return viperconfig.NewConfigLoader().Load("@.env")
}
```

**File: `internal/fx/providers/logger.go`**
```go
package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/logger/zaplogger"
)

// NewLogger provides logger.Logger
func NewLogger(cfg config.Config) (logger.Logger, error) {
	return zaplogger.NewLogger(
		cfg.GetString("LOG_LEVEL"),
		cfg.GetString("LOGGER_FORMAT"),
	)
}
```

### Step 5: Create Basic Lifecycle Management

**File: `internal/fx/lifecycle/server.go`**
```go
package lifecycle

import (
	"context"
	"net/http"
	"time"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/fx"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// RegisterServerHooks registers HTTP server lifecycle hooks
func RegisterServerHooks(
	lc fx.Lifecycle,
	engine *gin.Engine,
	cfg config.Config,
	log logger.Logger,
) {
	serverAddr := cfg.GetStringWithDefault("SERVER_ADDR", ":8080")
	srv := &http.Server{
		Addr:    serverAddr,
		Handler: engine,
	}

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.Info("Starting HTTP server", "addr", serverAddr)
			go func() {
				if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
					log.Fatal("Failed to run server", "error", err)
				}
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			log.Info("Stopping HTTP server")
			shutdownTimeout := cfg.GetDurationWithDefault("SERVER_SHUTDOWN_TIMEOUT", 10*time.Second)
			shutdownCtx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
			defer cancel()
			return srv.Shutdown(shutdownCtx)
		},
	})
}
```

### Step 6: Update go.mod

Ensure go.mod includes:
```go
require (
    go.uber.org/fx v1.20.0
    go.uber.org/dig v1.17.0
    // ... existing dependencies
)
```

### Step 7: Create Integration Test

**File: `internal/fx/app_test.go`**
```go
package fx

import (
	"context"
	"testing"
	"time"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
)

func TestNewApp(t *testing.T) {
	app := NewApp(
		fx.Invoke(func() {
			// Test that app can start
		}),
	)
	
	// Test app creation
	if app == nil {
		t.Fatal("Expected app to be created")
	}
	
	// Test app can start and stop
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := app.Start(ctx); err != nil {
		t.Fatalf("Failed to start app: %v", err)
	}
	
	if err := app.Stop(ctx); err != nil {
		t.Fatalf("Failed to stop app: %v", err)
	}
}

func TestNewAppWithOptions(t *testing.T) {
	opts := AppOptions{
		EnableModules: []string{"auth", "tenant"},
		Development:   true,
		CLIMode:       false,
	}
	
	app := NewAppWithOptions(opts)
	if app == nil {
		t.Fatal("Expected app to be created with options")
	}
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] FX dependencies installed successfully
- [ ] Basic fx.App can be created và started
- [ ] Core provider functions defined
- [ ] Lifecycle hooks working
- [ ] Integration tests passing

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] No breaking changes to existing code
- [ ] Proper error handling in providers
- [ ] Memory leaks prevention
- [ ] Graceful shutdown working

### Code Quality
- [ ] All functions have proper documentation
- [ ] Error messages are descriptive
- [ ] Code follows project conventions
- [ ] Test coverage > 80%

## File Paths

### New Files
- `internal/fx/app.go`
- `internal/fx/providers/config.go`
- `internal/fx/providers/logger.go`
- `internal/fx/lifecycle/server.go`
- `internal/fx/app_test.go`

### Modified Files
- `go.mod` (add fx dependencies)
- `go.sum` (dependency checksums)

## Dependencies
- None (this is the foundation task)

## Estimated Time
2 hours

## Notes
- Không modify existing code trong task này
- Focus on infrastructure setup only
- Ensure backward compatibility
- Prepare foundation cho subsequent tasks
