# FX Implementation Plan

## Tổng Quan

Triển khai uber-go/fx dependency injection framework để thay thế hệ thống AppBootstrap và module registry hiện tại. Việc này sẽ cải thiện kiến trúc ứng dụng với dependency injection tường minh, quản lý lifecycle tự động, và loại bỏ global state.

## Mục Tiêu Chính

1. **Thay thế AppBootstrap**: Loại bỏ manual dependency injection container
2. **Modernize Module System**: Chuyển từ GlobalModuleRegistry sang fx.Module
3. **Lifecycle Management**: Sử dụng fx.Lifecycle cho startup/shutdown
4. **Type Safety**: Cải thiện type safety với fx.Annotate
5. **Testability**: Dễ dàng mock dependencies trong testing

## Kiến Trúc Mục Tiêu

### Core Components
- **fx.App**: <PERSON><PERSON> thế AppBootstrap
- **fx.Module**: Thay thế Module interface
- **fx.Provide**: <PERSON><PERSON> thế manual service creation
- **fx.Invoke**: <PERSON><PERSON> thế manual initialization
- **fx.Lifecycle**: Quản lý server startup/shutdown

### Module Structure
```
modules/{module}/
├── fx.go              # fx.Module definition
├── providers.go       # fx.Provide functions
├── api/
├── service/
├── repository/
└── ...
```

## Task Categories

### 1. Infrastructure Setup (Tasks 01-03)
- Dependency installation và configuration
- Core provider functions
- Basic fx.App setup

### 2. Core Providers (Tasks 04-08)
- Config, Logger, Database providers
- Cache, JWT, Permission providers
- Tracing và monitoring providers

### 3. Module Migration (Tasks 09-15)
- Auth module fx conversion
- Tenant module fx conversion
- RBAC module fx conversion
- Notification module fx conversion
- Other modules conversion

### 4. Application Integration (Tasks 16-20)
- Main.go refactoring
- Server lifecycle management
- Route registration system
- CLI integration

### 5. Legacy Cleanup (Tasks 21-25)
- AppBootstrap removal
- GlobalModuleRegistry cleanup
- ServiceRegistry migration
- Testing updates

### 6. Advanced Features (Tasks 26-30)
- Event system integration
- Queue system integration
- Plugin system migration
- Performance optimization

## Implementation Principles

### 1. Multi-Tenant Architecture
- Tất cả providers phải support tenant context
- Middleware ordering: tenant -> auth -> rbac
- Repository patterns với tenantID parameters

### 2. Service Interfaces
- Maintain existing service interfaces
- Use fx.Annotate for interface binding
- Type-safe dependency resolution

### 3. Configuration Management
- Centralized config từ internal/pkg/config
- Environment-based configuration
- Module-specific config sections

### 4. Error Handling
- Graceful degradation patterns
- Proper error propagation
- Logging integration

### 5. Testing Strategy
- Unit tests với fx.Test
- Integration tests với test modules
- Mock providers cho testing

## Dependencies và Prerequisites

### Required Packages
```go
go.uber.org/fx
go.uber.org/dig
```

### Module Loading Order
1. Core providers (config, logger, database)
2. Infrastructure providers (cache, auth, permissions)
3. Business modules (tenant, auth, rbac)
4. Feature modules (notification, media, etc.)

## Success Criteria

### Technical Metrics
- [ ] Loại bỏ hoàn toàn AppBootstrap
- [ ] Tất cả modules sử dụng fx.Module
- [ ] Zero global state dependencies
- [ ] Improved startup time
- [ ] Better error messages

### Quality Metrics
- [ ] 100% test coverage cho providers
- [ ] Documentation đầy đủ
- [ ] Performance benchmarks
- [ ] Memory usage optimization

## Risk Mitigation

### 1. Backward Compatibility
- Maintain existing APIs
- Gradual migration approach
- Feature flags cho rollback

### 2. Testing Strategy
- Comprehensive integration tests
- Performance regression tests
- Load testing với fx.App

### 3. Rollback Plan
- Git branch strategy
- Feature toggles
- Monitoring và alerting

## Timeline Estimate

- **Phase 1** (Tasks 01-08): Infrastructure - 2 weeks
- **Phase 2** (Tasks 09-15): Module Migration - 3 weeks  
- **Phase 3** (Tasks 16-20): Integration - 1 week
- **Phase 4** (Tasks 21-25): Cleanup - 1 week
- **Phase 5** (Tasks 26-30): Advanced Features - 2 weeks

**Total**: ~9 weeks (45 working days)

## Next Steps

1. Review và approve implementation plan
2. Setup development environment
3. Begin với Task 01: FX Infrastructure Setup
4. Implement tasks theo thứ tự dependency
5. Continuous testing và validation

## References

- [Uber FX Documentation](https://uber-go.github.io/fx/)
- [Dependency Injection Patterns](https://github.com/uber-go/fx/blob/master/docs/README.md)
- [Multi-tenant Architecture Guide](docs/features/multi-tenant.md)
- [Service Registry Documentation](docs/features/ServiceRegistry.md)
