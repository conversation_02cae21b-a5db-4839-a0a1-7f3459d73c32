# Task 02: Core Providers Implementation

## Objective
Implement core provider functions cho database, cache, authentication, và permission systems. Các providers này sẽ thay thế logic khởi tạo trong AppBootstrap.

## Input
- Basic fx infrastructure từ Task 01
- Existing AppBootstrap initialization logic
- Current database, cache, auth configurations

## Output
- Complete core provider implementations
- Database connection providers (GORM + sqlx)
- Cache provider implementation
- JWT authentication provider
- Permission middleware factory provider

## Requirements

### 1. Technical Requirements
- Support both GORM và sqlx database connections
- Multi-tenant aware providers
- Proper error handling và logging
- Configuration-driven initialization

### 2. Architecture Requirements
- Follow existing patterns từ AppBootstrap
- Maintain interface compatibility
- Support graceful degradation
- Proper dependency injection ordering

## Implementation Steps

### Step 1: Database Providers

**File: `internal/fx/providers/database.go`**
```go
package providers

import (
	"time"
	
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
)

// NewDBManager provides *database.Manager (sqlx-based)
func NewDBManager(cfg config.Config, log logger.Logger) (*database.Manager, error) {
	dbConfig := database.Config{
		Type:            cfg.GetString("DB_TYPE"),
		Host:            cfg.GetString("DB_HOST"),
		Port:            cfg.GetInt("DB_PORT"),
		Username:        cfg.GetString("DB_USERNAME"),
		Password:        cfg.GetString("DB_PASSWORD"),
		Database:        cfg.GetString("DB_DATABASE"),
		MaxOpenConns:    cfg.GetIntWithDefault("DB_MAX_OPEN_CONNS", 25),
		MaxIdleConns:    cfg.GetIntWithDefault("DB_MAX_IDLE_CONNS", 5),
		ConnMaxLifetime: cfg.GetDurationWithDefault("DB_CONN_MAX_LIFETIME", 5*time.Minute),
	}
	return database.NewManager(dbConfig, log)
}

// NewGormDB provides *gorm.DB using existing database connection
func NewGormDB(dbManager *database.Manager) (*gorm.DB, error) {
	sqlDB := dbManager.GetDB()
	return gorm.Open(mysql.New(mysql.Config{
		Conn: sqlDB.DB,
	}), &gorm.Config{})
}

// NewSqlxDB provides *sqlx.DB from database manager
func NewSqlxDB(dbManager *database.Manager) *sqlx.DB {
	return dbManager.GetDB()
}

// DBManagerParams defines parameters for database manager
type DBManagerParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewDBManagerWithParams creates database manager with structured params
func NewDBManagerWithParams(params DBManagerParams) (*database.Manager, error) {
	return NewDBManager(params.Config, params.Logger)
}
```

### Step 2: Cache Provider

**File: `internal/fx/providers/cache.go`**
```go
package providers

import (
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
)

// NewAppCache provides cache.Cache implementation
func NewAppCache(cfg config.Config, log logger.Logger) cache.Cache {
	cacheType := cfg.GetStringWithDefault("CACHE_TYPE", "memory")
	
	switch cacheType {
	case "memory":
		return memorycache.NewMemoryCache()
	case "redis":
		// TODO: Implement Redis cache provider
		log.Warn("Redis cache not implemented, falling back to memory cache")
		return memorycache.NewMemoryCache()
	default:
		log.Warn("Unknown cache type, using memory cache", "type", cacheType)
		return memorycache.NewMemoryCache()
	}
}

// CacheParams defines parameters for cache creation
type CacheParams struct {
	Config config.Config
	Logger logger.Logger
}

// NewAppCacheWithParams creates cache with structured params
func NewAppCacheWithParams(params CacheParams) cache.Cache {
	return NewAppCache(params.Config, params.Logger)
}
```

### Step 3: Authentication Providers

**File: `internal/fx/providers/auth.go`**
```go
package providers

import (
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
)

// JWTConfig provides JWT configuration
type JWTConfig struct {
	AccessSigningKey       string
	RefreshSigningKey      string
	AccessTokenExpiration  time.Duration
	RefreshTokenExpiration time.Duration
	Issuer                 string
}

// NewJWTConfig provides JWT configuration from environment
func NewJWTConfig(cfg config.Config) auth.JWTConfig {
	return auth.JWTConfig{
		AccessSigningKey:       cfg.GetString("JWT_ACCESS_SIGNING_KEY"),
		RefreshSigningKey:      cfg.GetString("JWT_REFRESH_SIGNING_KEY"),
		AccessTokenExpiration:  cfg.GetDuration("JWT_ACCESS_TOKEN_EXPIRATION"),
		RefreshTokenExpiration: cfg.GetDuration("JWT_REFRESH_TOKEN_EXPIRATION"),
		Issuer:                 cfg.GetString("JWT_ISSUER"),
	}
}

// NewJWTService provides JWT service implementation
func NewJWTService(jwtConfig auth.JWTConfig) *auth.JWTService {
	return auth.NewJWTService(jwtConfig)
}

// AuthParams defines parameters for auth services
type AuthParams struct {
	Config config.Config
}

// NewJWTConfigWithParams creates JWT config with structured params
func NewJWTConfigWithParams(params AuthParams) auth.JWTConfig {
	return NewJWTConfig(params.Config)
}
```

### Step 4: Permission Provider

**File: `internal/fx/providers/permission.go`**
```go
package providers

import (
	"wnapi/internal/bootstrap"
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
)

// NewPermissionFactory provides permission middleware factory
func NewPermissionFactory(
	dbManager *database.Manager,
	cache cache.Cache,
	log logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	// Use existing bootstrap logic
	dbWrapper := &database.DB{DB: dbManager.GetDB().DB}
	return bootstrap.BootstrapRBAC(dbWrapper, cache, log, cfg)
}

// PermissionParams defines parameters for permission factory
type PermissionParams struct {
	DBManager *database.Manager
	Cache     cache.Cache
	Logger    logger.Logger
	Config    config.Config
}

// NewPermissionFactoryWithParams creates permission factory with structured params
func NewPermissionFactoryWithParams(params PermissionParams) (*permission.MiddlewareFactory, error) {
	return NewPermissionFactory(params.DBManager, params.Cache, params.Logger, params.Config)
}
```

### Step 5: Server Provider

**File: `internal/fx/providers/server.go`**
```go
package providers

import (
	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/config"
)

// NewGinEngine provides configured Gin engine
func NewGinEngine(cfg config.Config) *gin.Engine {
	if cfg.GetString("APP_ENV") == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	
	engine := gin.Default()
	
	// Add common middleware
	// TODO: Add CORS, logging, recovery middleware
	
	return engine
}

// ServerParams defines parameters for server creation
type ServerParams struct {
	Config config.Config
}

// NewGinEngineWithParams creates Gin engine with structured params
func NewGinEngineWithParams(params ServerParams) *gin.Engine {
	return NewGinEngine(params.Config)
}
```

### Step 6: Tracing Provider

**File: `internal/fx/providers/tracing.go`**
```go
package providers

import (
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/tracing"
)

// NewTracingProvider provides tracing configuration
func NewTracingProvider(cfg config.Config) (*tracing.Provider, error) {
	tracingConfig := tracing.LoadConfigFromEnv(cfg)
	return tracing.NewProvider(tracingConfig)
}

// TracingParams defines parameters for tracing
type TracingParams struct {
	Config config.Config
}

// NewTracingProviderWithParams creates tracing provider with structured params
func NewTracingProviderWithParams(params TracingParams) (*tracing.Provider, error) {
	return NewTracingProvider(params.Config)
}
```

### Step 7: Update Main App Configuration

**File: `internal/fx/app.go` (update)**
```go
// Update NewApp function to include all core providers
func NewApp(opts ...fx.Option) *fx.App {
	defaultOpts := []fx.Option{
		// Core providers
		fx.Provide(
			providers.NewConfig,
			providers.NewLogger,
			providers.NewDBManager,
			providers.NewGormDB,
			providers.NewSqlxDB,
			providers.NewAppCache,
			providers.NewJWTConfig,
			providers.NewJWTService,
			providers.NewPermissionFactory,
			providers.NewGinEngine,
			providers.NewTracingProvider,
		),
		
		// Lifecycle management
		fx.Invoke(
			lifecycle.RegisterServerHooks,
			func(tp *tracing.Provider) {}, // Ensure tracing is initialized
		),
	}
	
	// Merge with user options
	allOpts := append(defaultOpts, opts...)
	
	return fx.New(allOpts...)
}
```

### Step 8: Create Provider Tests

**File: `internal/fx/providers/providers_test.go`**
```go
package providers

import (
	"testing"
	"time"
	
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"wnapi/internal/pkg/config/viperconfig"
)

func TestProviders(t *testing.T) {
	app := fxtest.New(t,
		fx.Provide(
			NewConfig,
			NewLogger,
			NewAppCache,
			NewJWTConfig,
			NewJWTService,
		),
		fx.Invoke(func(
			cfg config.Config,
			log logger.Logger,
			cache cache.Cache,
			jwtConfig auth.JWTConfig,
			jwtService *auth.JWTService,
		) {
			// Test that all providers work
			if cfg == nil {
				t.Error("Config provider failed")
			}
			if log == nil {
				t.Error("Logger provider failed")
			}
			if cache == nil {
				t.Error("Cache provider failed")
			}
			if jwtService == nil {
				t.Error("JWT service provider failed")
			}
		}),
	)
	
	app.RequireStart()
	app.RequireStop()
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] All core providers implemented và working
- [ ] Database connections (GORM + sqlx) established
- [ ] Cache system initialized
- [ ] JWT authentication configured
- [ ] Permission middleware factory created
- [ ] Tracing system initialized

### Technical Requirements
- [ ] `make build` passes without errors
- [ ] All providers have proper error handling
- [ ] Configuration-driven initialization
- [ ] Memory efficient implementations
- [ ] Proper cleanup in lifecycle hooks

### Code Quality
- [ ] Provider functions well documented
- [ ] Error messages descriptive
- [ ] Test coverage > 80%
- [ ] No memory leaks
- [ ] Proper dependency ordering

## File Paths

### New Files
- `internal/fx/providers/database.go`
- `internal/fx/providers/cache.go`
- `internal/fx/providers/auth.go`
- `internal/fx/providers/permission.go`
- `internal/fx/providers/server.go`
- `internal/fx/providers/tracing.go`
- `internal/fx/providers/providers_test.go`

### Modified Files
- `internal/fx/app.go` (add providers)

## Dependencies
- Task 01: FX Infrastructure Setup

## Estimated Time
2 hours

## Notes
- Reuse existing bootstrap logic where possible
- Maintain compatibility với current interfaces
- Focus on provider implementation, not usage
- Prepare for module integration in next tasks
