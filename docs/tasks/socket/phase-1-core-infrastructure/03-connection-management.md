# Task 03: Connection Management

## 📋 Tổng Quan

Triển khai WebSocket Connection wrapper và connection management system. Component n<PERSON><PERSON> chịu trách nhiệm wrap raw WebSocket connections thành Connection interface với authentication, message handling, và lifecycle management.

## 🎯 Mục Tiêu

- Wrap WebSocket connections với rich functionality
- Integrate với authentication system
- Handle incoming/outgoing messages với proper serialization
- Implement heartbeat/ping-pong mechanism
- Manage connection state và lifecycle
- Support room membership management

## 📁 Files Cần Tạo

```
internal/pkg/socket/
├── connection.go         # Main Connection implementation
├── connection_manager.go # Connection lifecycle management
├── message_handler.go    # Message processing
└── heartbeat.go         # Heartbeat/ping mechanism
```

## 🔧 Implementation Steps

### Step 1: Implement Core Connection (connection.go)

```go
package socket

import (
    "context"
    "encoding/json"
    "fmt"
    "net"
    "sync"
    "time"
    "github.com/gorilla/websocket"
    "wnapi/internal/pkg/logger"
)

// socketConnection implementation của Connection interface
type socketConnection struct {
    // Connection info
    id        string
    userID    uint
    tenantID  uint
    websiteID uint
    
    // WebSocket connection
    conn      *websocket.Conn
    
    // Context và lifecycle
    ctx       context.Context
    cancel    context.CancelFunc
    
    // Message channels
    send      chan *Message
    receive   chan *Message
    
    // State management
    state     string
    stateMutex sync.RWMutex
    
    // Room management
    rooms     map[string]bool
    roomsMutex sync.RWMutex
    
    // Activity tracking
    lastActivity time.Time
    activityMutex sync.RWMutex
    
    // Configuration
    writeTimeout time.Duration
    readTimeout  time.Duration
    
    // Logging
    logger logger.Logger
    
    // Hub reference
    hub Hub
}

// NewSocketConnection tạo connection wrapper mới
func NewSocketConnection(conn *websocket.Conn, userID, tenantID, websiteID uint, logger logger.Logger) (Connection, error) {
    if conn == nil {
        return nil, NewConnectionError("websocket connection is nil", ErrInvalidConnection)
    }
    
    ctx, cancel := context.WithCancel(context.Background())
    
    connID := generateConnectionID()
    
    sc := &socketConnection{
        id:           connID,
        userID:       userID,
        tenantID:     tenantID,
        websiteID:    websiteID,
        conn:         conn,
        ctx:          ctx,
        cancel:       cancel,
        send:         make(chan *Message, MessageBufferSize),
        receive:      make(chan *Message, MessageBufferSize),
        state:        StateConnecting,
        rooms:        make(map[string]bool),
        lastActivity: time.Now(),
        writeTimeout: WriteTimeout,
        readTimeout:  ReadTimeout,
        logger:       logger,
    }
    
    // Start message handlers
    go sc.readPump()
    go sc.writePump()
    
    sc.setState(StateConnected)
    
    sc.logger.Info("Socket connection created",
        "conn_id", connID,
        "user_id", userID,
        "tenant_id", tenantID,
        "remote_addr", sc.RemoteAddr(),
    )
    
    return sc, nil
}

// ID trả về connection ID
func (c *socketConnection) ID() string {
    return c.id
}

// UserID trả về user ID
func (c *socketConnection) UserID() uint {
    return c.userID
}

// TenantID trả về tenant ID
func (c *socketConnection) TenantID() uint {
    return c.tenantID
}

// WebsiteID trả về website ID
func (c *socketConnection) WebsiteID() uint {
    return c.websiteID
}

// Send gửi message đến client
func (c *socketConnection) Send(message *Message) error {
    if !c.IsAlive() {
        return NewConnectionError("connection is not alive", ErrConnectionClosed)
    }
    
    select {
    case c.send <- message:
        return nil
    case <-c.ctx.Done():
        return NewConnectionError("connection context cancelled", c.ctx.Err())
    case <-time.After(5 * time.Second):
        return NewConnectionError("send timeout", nil)
    }
}

// SendEvent gửi event với data
func (c *socketConnection) SendEvent(eventType string, data map[string]interface{}) error {
    message := NewMessage(eventType, data)
    return c.Send(message)
}

// SendError gửi error message
func (c *socketConnection) SendError(code, message string) error {
    errorMsg := NewErrorMessage(code, message, nil)
    return c.Send(errorMsg)
}

// Close đóng connection
func (c *socketConnection) Close() error {
    c.stateMutex.Lock()
    if c.state == StateDisconnected || c.state == StateDisconnecting {
        c.stateMutex.Unlock()
        return nil
    }
    
    c.setState(StateDisconnecting)
    c.stateMutex.Unlock()
    
    // Cancel context
    c.cancel()
    
    // Close WebSocket connection
    if c.conn != nil {
        c.conn.WriteMessage(websocket.CloseMessage, []byte{})
        c.conn.Close()
    }
    
    // Close channels
    close(c.send)
    
    c.setState(StateDisconnected)
    
    c.logger.Info("Socket connection closed",
        "conn_id", c.id,
        "user_id", c.userID,
    )
    
    return nil
}

// IsAlive kiểm tra connection có sống không
func (c *socketConnection) IsAlive() bool {
    c.stateMutex.RLock()
    defer c.stateMutex.RUnlock()
    
    return c.state == StateConnected
}

// JoinRoom join vào room
func (c *socketConnection) JoinRoom(roomID string) error {
    if !c.IsAlive() {
        return NewConnectionError("connection not alive", ErrConnectionClosed)
    }
    
    c.roomsMutex.Lock()
    defer c.roomsMutex.Unlock()
    
    if len(c.rooms) >= MaxRoomsPerUser {
        return NewRoomError(CodeRoomFull, "too many rooms", nil)
    }
    
    c.rooms[roomID] = true
    
    c.logger.Debug("Connection joined room",
        "conn_id", c.id,
        "room_id", roomID,
        "total_rooms", len(c.rooms),
    )
    
    return nil
}

// LeaveRoom rời khỏi room
func (c *socketConnection) LeaveRoom(roomID string) error {
    c.roomsMutex.Lock()
    defer c.roomsMutex.Unlock()
    
    delete(c.rooms, roomID)
    
    c.logger.Debug("Connection left room",
        "conn_id", c.id,
        "room_id", roomID,
        "total_rooms", len(c.rooms),
    )
    
    return nil
}

// GetRooms trả về danh sách rooms
func (c *socketConnection) GetRooms() []string {
    c.roomsMutex.RLock()
    defer c.roomsMutex.RUnlock()
    
    rooms := make([]string, 0, len(c.rooms))
    for roomID := range c.rooms {
        rooms = append(rooms, roomID)
    }
    
    return rooms
}

// Context trả về connection context
func (c *socketConnection) Context() context.Context {
    return c.ctx
}

// RemoteAddr trả về remote address
func (c *socketConnection) RemoteAddr() string {
    if c.conn != nil {
        return c.conn.RemoteAddr().String()
    }
    return "unknown"
}

// LastActivity trả về thời gian activity cuối
func (c *socketConnection) LastActivity() time.Time {
    c.activityMutex.RLock()
    defer c.activityMutex.RUnlock()
    
    return c.lastActivity
}

// setState internal method set state
func (c *socketConnection) setState(state string) {
    c.stateMutex.Lock()
    defer c.stateMutex.Unlock()
    
    c.state = state
}

// updateActivity cập nhật last activity
func (c *socketConnection) updateActivity() {
    c.activityMutex.Lock()
    defer c.activityMutex.Unlock()
    
    c.lastActivity = time.Now()
}

// readPump đọc messages từ WebSocket
func (c *socketConnection) readPump() {
    defer func() {
        c.Close()
    }()
    
    // Set read deadline
    c.conn.SetReadDeadline(time.Now().Add(c.readTimeout))
    
    // Set pong handler
    c.conn.SetPongHandler(func(string) error {
        c.conn.SetReadDeadline(time.Now().Add(c.readTimeout))
        c.updateActivity()
        return nil
    })
    
    for {
        select {
        case <-c.ctx.Done():
            return
        default:
            messageType, data, err := c.conn.ReadMessage()
            if err != nil {
                if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                    c.logger.Error("WebSocket read error",
                        "conn_id", c.id,
                        "error", err,
                    )
                }
                return
            }
            
            if messageType != websocket.TextMessage {
                continue
            }
            
            c.updateActivity()
            
            // Parse incoming message
            var incomingMsg IncomingMessage
            if err := json.Unmarshal(data, &incomingMsg); err != nil {
                c.logger.Warn("Invalid message format",
                    "conn_id", c.id,
                    "error", err,
                    "data", string(data),
                )
                c.SendError(CodeInvalidMessage, "Invalid message format")
                continue
            }
            
            // Convert to internal message
            message := &Message{
                Type:      incomingMsg.Type,
                Data:      incomingMsg.Data,
                RequestID: incomingMsg.RequestID,
                Timestamp: time.Now().Unix(),
            }
            
            // Forward to hub for processing
            if c.hub != nil {
                // Hub will route to event router
                c.logger.Debug("Message received",
                    "conn_id", c.id,
                    "type", message.Type,
                    "request_id", message.RequestID,
                )
            }
        }
    }
}

// writePump ghi messages đến WebSocket
func (c *socketConnection) writePump() {
    ticker := time.NewTicker(PingInterval)
    defer func() {
        ticker.Stop()
        c.Close()
    }()
    
    for {
        select {
        case message, ok := <-c.send:
            c.conn.SetWriteDeadline(time.Now().Add(c.writeTimeout))
            
            if !ok {
                c.conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }
            
            // Convert to outgoing message
            outgoingMsg := OutgoingMessage{
                Type:      message.Type,
                Data:      message.Data,
                Error:     message.Error,
                Timestamp: message.Timestamp,
                RequestID: message.RequestID,
            }
            
            data, err := json.Marshal(outgoingMsg)
            if err != nil {
                c.logger.Error("Failed to marshal message",
                    "conn_id", c.id,
                    "error", err,
                )
                continue
            }
            
            if err := c.conn.WriteMessage(websocket.TextMessage, data); err != nil {
                c.logger.Error("Failed to write message",
                    "conn_id", c.id,
                    "error", err,
                )
                return
            }
            
            c.updateActivity()
            
        case <-ticker.C:
            c.conn.SetWriteDeadline(time.Now().Add(c.writeTimeout))
            if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                c.logger.Debug("Failed to send ping",
                    "conn_id", c.id,
                    "error", err,
                )
                return
            }
            
        case <-c.ctx.Done():
            return
        }
    }
}

// generateConnectionID tạo unique connection ID
func generateConnectionID() string {
    return fmt.Sprintf("conn_%d_%d", time.Now().UnixNano(), time.Now().Nanosecond())
}
```

### Step 2: Implement Connection Manager (connection_manager.go)

```go
package socket

import (
    "context"
    "sync"
    "time"
    "wnapi/internal/pkg/logger"
)

// ConnectionManager quản lý lifecycle của connections
type ConnectionManager struct {
    connections map[string]*connectionState
    mutex       sync.RWMutex
    logger      logger.Logger
    hub         Hub
    
    // Cleanup
    cleanupTicker *time.Ticker
    stopCleanup   chan struct{}
}

// connectionState track state của một connection
type connectionState struct {
    connection   Connection
    createdAt    time.Time
    lastPing     time.Time
    pingCount    int
    errorCount   int
    messageCount int64
}

// NewConnectionManager tạo connection manager mới
func NewConnectionManager(hub Hub, logger logger.Logger) *ConnectionManager {
    cm := &ConnectionManager{
        connections: make(map[string]*connectionState),
        logger:      logger,
        hub:         hub,
        stopCleanup: make(chan struct{}),
    }
    
    // Start cleanup goroutine
    cm.startCleanup()
    
    return cm
}

// RegisterConnection đăng ký connection với manager
func (cm *ConnectionManager) RegisterConnection(conn Connection) {
    cm.mutex.Lock()
    defer cm.mutex.Unlock()
    
    state := &connectionState{
        connection:   conn,
        createdAt:    time.Now(),
        lastPing:     time.Now(),
        pingCount:    0,
        errorCount:   0,
        messageCount: 0,
    }
    
    cm.connections[conn.ID()] = state
    
    cm.logger.Info("Connection registered with manager",
        "conn_id", conn.ID(),
        "user_id", conn.UserID(),
    )
}

// UnregisterConnection hủy đăng ký connection
func (cm *ConnectionManager) UnregisterConnection(connID string) {
    cm.mutex.Lock()
    defer cm.mutex.Unlock()
    
    if state, exists := cm.connections[connID]; exists {
        delete(cm.connections, connID)
        
        cm.logger.Info("Connection unregistered from manager",
            "conn_id", connID,
            "duration", time.Since(state.createdAt),
            "message_count", state.messageCount,
        )
    }
}

// GetConnectionState lấy state của connection
func (cm *ConnectionManager) GetConnectionState(connID string) (*connectionState, bool) {
    cm.mutex.RLock()
    defer cm.mutex.RUnlock()
    
    state, exists := cm.connections[connID]
    return state, exists
}

// UpdateActivity cập nhật activity của connection
func (cm *ConnectionManager) UpdateActivity(connID string, activityType string) {
    cm.mutex.Lock()
    defer cm.mutex.Unlock()
    
    state, exists := cm.connections[connID]
    if !exists {
        return
    }
    
    switch activityType {
    case "ping":
        state.lastPing = time.Now()
        state.pingCount++
    case "message":
        state.messageCount++
    case "error":
        state.errorCount++
    }
}

// GetHealthyConnections trả về danh sách connections healthy
func (cm *ConnectionManager) GetHealthyConnections() []Connection {
    cm.mutex.RLock()
    defer cm.mutex.RUnlock()
    
    healthy := make([]Connection, 0)
    now := time.Now()
    
    for _, state := range cm.connections {
        if state.connection.IsAlive() && 
           now.Sub(state.lastPing) < PongTimeout {
            healthy = append(healthy, state.connection)
        }
    }
    
    return healthy
}

// GetConnectionStats trả về thống kê connections
func (cm *ConnectionManager) GetConnectionStats() map[string]interface{} {
    cm.mutex.RLock()
    defer cm.mutex.RUnlock()
    
    totalConns := len(cm.connections)
    healthyConns := 0
    totalMessages := int64(0)
    totalErrors := 0
    
    for _, state := range cm.connections {
        if state.connection.IsAlive() {
            healthyConns++
        }
        totalMessages += state.messageCount
        totalErrors += state.errorCount
    }
    
    return map[string]interface{}{
        "total_connections":   totalConns,
        "healthy_connections": healthyConns,
        "total_messages":      totalMessages,
        "total_errors":        totalErrors,
        "health_ratio":        float64(healthyConns) / float64(totalConns),
    }
}

// startCleanup bắt đầu cleanup goroutine
func (cm *ConnectionManager) startCleanup() {
    cm.cleanupTicker = time.NewTicker(CleanupInterval)
    
    go func() {
        for {
            select {
            case <-cm.cleanupTicker.C:
                cm.cleanupInactiveConnections()
            case <-cm.stopCleanup:
                cm.cleanupTicker.Stop()
                return
            }
        }
    }()
}

// cleanupInactiveConnections dọn dẹp connections không active
func (cm *ConnectionManager) cleanupInactiveConnections() {
    cm.mutex.Lock()
    toRemove := make([]string, 0)
    now := time.Now()
    
    for connID, state := range cm.connections {
        // Check if connection is dead or inactive too long
        if !state.connection.IsAlive() || 
           now.Sub(state.lastPing) > PongTimeout*2 {
            toRemove = append(toRemove, connID)
        }
    }
    cm.mutex.Unlock()
    
    // Remove inactive connections
    for _, connID := range toRemove {
        if state, exists := cm.GetConnectionState(connID); exists {
            state.connection.Close()
            cm.UnregisterConnection(connID)
        }
    }
    
    if len(toRemove) > 0 {
        cm.logger.Info("Cleaned up inactive connections",
            "count", len(toRemove),
        )
    }
}

// Stop dừng connection manager
func (cm *ConnectionManager) Stop() {
    close(cm.stopCleanup)
    
    cm.mutex.Lock()
    defer cm.mutex.Unlock()
    
    // Close all connections
    for _, state := range cm.connections {
        state.connection.Close()
    }
    
    cm.connections = make(map[string]*connectionState)
    
    cm.logger.Info("Connection manager stopped")
}
```

### Step 3: Implement Message Handler (message_handler.go)

```go
package socket

import (
    "context"
    "encoding/json"
    "time"
    "wnapi/internal/pkg/logger"
)

// MessageProcessor xử lý incoming messages
type MessageProcessor struct {
    hub         Hub
    eventRouter EventRouter
    logger      logger.Logger
    
    // Rate limiting
    rateLimiter map[string]*rateLimitState
    rateMutex   sync.RWMutex
}

// rateLimitState track rate limiting cho connection
type rateLimitState struct {
    messageCount int
    lastReset    time.Time
}

// NewMessageProcessor tạo message processor mới
func NewMessageProcessor(hub Hub, eventRouter EventRouter, logger logger.Logger) *MessageProcessor {
    return &MessageProcessor{
        hub:         hub,
        eventRouter: eventRouter,
        logger:      logger,
        rateLimiter: make(map[string]*rateLimitState),
    }
}

// ProcessMessage xử lý message từ connection
func (mp *MessageProcessor) ProcessMessage(ctx context.Context, conn Connection, rawData []byte) error {
    // Rate limiting check
    if !mp.checkRateLimit(conn.ID()) {
        mp.logger.Warn("Rate limit exceeded",
            "conn_id", conn.ID(),
            "user_id", conn.UserID(),
        )
        return conn.SendError(CodeSystemOverload, "Rate limit exceeded")
    }
    
    // Parse message
    var incomingMsg IncomingMessage
    if err := json.Unmarshal(rawData, &incomingMsg); err != nil {
        mp.logger.Warn("Invalid message format",
            "conn_id", conn.ID(),
            "error", err,
        )
        return conn.SendError(CodeInvalidMessage, "Invalid message format")
    }
    
    // Validate message
    if err := mp.validateMessage(&incomingMsg); err != nil {
        mp.logger.Warn("Message validation failed",
            "conn_id", conn.ID(),
            "type", incomingMsg.Type,
            "error", err,
        )
        return conn.SendError(CodeInvalidMessage, err.Error())
    }
    
    // Handle system messages
    if mp.isSystemMessage(incomingMsg.Type) {
        return mp.handleSystemMessage(ctx, conn, &incomingMsg)
    }
    
    // Route to event router
    if mp.eventRouter != nil {
        if err := mp.eventRouter.RouteEvent(ctx, conn, incomingMsg.Type, incomingMsg.Data); err != nil {
            mp.logger.Error("Failed to route event",
                "conn_id", conn.ID(),
                "type", incomingMsg.Type,
                "error", err,
            )
            return conn.SendError(CodeHandlerError, "Failed to process event")
        }
    }
    
    mp.logger.Debug("Message processed successfully",
        "conn_id", conn.ID(),
        "type", incomingMsg.Type,
        "request_id", incomingMsg.RequestID,
    )
    
    return nil
}

// checkRateLimit kiểm tra rate limiting
func (mp *MessageProcessor) checkRateLimit(connID string) bool {
    mp.rateMutex.Lock()
    defer mp.rateMutex.Unlock()
    
    now := time.Now()
    state, exists := mp.rateLimiter[connID]
    
    if !exists {
        mp.rateLimiter[connID] = &rateLimitState{
            messageCount: 1,
            lastReset:    now,
        }
        return true
    }
    
    // Reset counter if more than 1 second passed
    if now.Sub(state.lastReset) > time.Second {
        state.messageCount = 1
        state.lastReset = now
        return true
    }
    
    // Check limit
    if state.messageCount >= MaxMessagesPerSecond {
        return false
    }
    
    state.messageCount++
    return true
}

// validateMessage validate incoming message
func (mp *MessageProcessor) validateMessage(msg *IncomingMessage) error {
    if msg.Type == "" {
        return NewMessageError("message type is required", ErrMissingData)
    }
    
    if len(msg.Type) > 100 {
        return NewMessageError("message type too long", ErrInvalidMessage)
    }
    
    // Validate data size
    if msg.Data != nil {
        dataBytes, err := json.Marshal(msg.Data)
        if err != nil {
            return NewMessageError("invalid data format", err)
        }
        
        if len(dataBytes) > MaxMessageSize {
            return NewMessageError("message too large", ErrMessageTooLarge)
        }
    }
    
    return nil
}

// isSystemMessage kiểm tra có phải system message không
func (mp *MessageProcessor) isSystemMessage(msgType string) bool {
    systemTypes := []string{
        EventTypePing,
        EventTypePong,
        EventTypeJoinRoom,
        EventTypeLeaveRoom,
    }
    
    for _, sysType := range systemTypes {
        if msgType == sysType {
            return true
        }
    }
    
    return false
}

// handleSystemMessage xử lý system messages
func (mp *MessageProcessor) handleSystemMessage(ctx context.Context, conn Connection, msg *IncomingMessage) error {
    switch msg.Type {
    case EventTypePing:
        return mp.handlePing(conn, msg)
    case EventTypePong:
        return mp.handlePong(conn, msg)
    case EventTypeJoinRoom:
        return mp.handleJoinRoom(conn, msg)
    case EventTypeLeaveRoom:
        return mp.handleLeaveRoom(conn, msg)
    default:
        return NewMessageError("unknown system message type", ErrInvalidEventType)
    }
}

// handlePing xử lý ping message
func (mp *MessageProcessor) handlePing(conn Connection, msg *IncomingMessage) error {
    pongMsg := NewMessage(EventTypePong, map[string]interface{}{
        "timestamp": time.Now().Unix(),
    }).WithRequestID(msg.RequestID)
    
    return conn.Send(pongMsg)
}

// handlePong xử lý pong message
func (mp *MessageProcessor) handlePong(conn Connection, msg *IncomingMessage) error {
    mp.logger.Debug("Received pong",
        "conn_id", conn.ID(),
        "request_id", msg.RequestID,
    )
    return nil
}

// handleJoinRoom xử lý join room request
func (mp *MessageProcessor) handleJoinRoom(conn Connection, msg *IncomingMessage) error {
    roomID, ok := msg.Data["room_id"].(string)
    if !ok {
        return conn.SendError(CodeMissingData, "room_id is required")
    }
    
    if err := conn.JoinRoom(roomID); err != nil {
        return conn.SendError(CodeRoomError, err.Error())
    }
    
    // Notify hub to add user to room
    if room, exists := mp.hub.GetRoom(roomID); exists {
        if err := room.AddUser(conn.UserID()); err != nil {
            conn.LeaveRoom(roomID)
            return conn.SendError(CodeRoomError, err.Error())
        }
    } else {
        // Create room if not exists
        if err := mp.hub.CreateRoom(roomID, MaxRoomSize); err != nil {
            return conn.SendError(CodeRoomError, err.Error())
        }
        
        if room, exists := mp.hub.GetRoom(roomID); exists {
            if err := room.AddUser(conn.UserID()); err != nil {
                return conn.SendError(CodeRoomError, err.Error())
            }
        }
    }
    
    response := NewSuccessMessage(msg.RequestID, map[string]interface{}{
        "room_id": roomID,
        "status":  "joined",
    })
    
    return conn.Send(response)
}

// handleLeaveRoom xử lý leave room request
func (mp *MessageProcessor) handleLeaveRoom(conn Connection, msg *IncomingMessage) error {
    roomID, ok := msg.Data["room_id"].(string)
    if !ok {
        return conn.SendError(CodeMissingData, "room_id is required")
    }
    
    if err := conn.LeaveRoom(roomID); err != nil {
        return conn.SendError(CodeRoomError, err.Error())
    }
    
    // Notify hub to remove user from room
    if room, exists := mp.hub.GetRoom(roomID); exists {
        room.RemoveUser(conn.UserID())
    }
    
    response := NewSuccessMessage(msg.RequestID, map[string]interface{}{
        "room_id": roomID,
        "status":  "left",
    })
    
    return conn.Send(response)
}

// CleanupRateLimit dọn dẹp rate limit data
func (mp *MessageProcessor) CleanupRateLimit() {
    mp.rateMutex.Lock()
    defer mp.rateMutex.Unlock()
    
    now := time.Now()
    toDelete := make([]string, 0)
    
    for connID, state := range mp.rateLimiter {
        if now.Sub(state.lastReset) > 5*time.Minute {
            toDelete = append(toDelete, connID)
        }
    }
    
    for _, connID := range toDelete {
        delete(mp.rateLimiter, connID)
    }
}
```

### Step 4: Implement Heartbeat (heartbeat.go)

```go
package socket

import (
    "sync"
    "time"
    "wnapi/internal/pkg/logger"
)

// HeartbeatManager quản lý heartbeat cho connections
type HeartbeatManager struct {
    connections map[string]*heartbeatState
    mutex       sync.RWMutex
    logger      logger.Logger
    
    // Configuration
    pingInterval time.Duration
    pongTimeout  time.Duration
    
    // Control
    ticker *time.Ticker
    stop   chan struct{}
}

// heartbeatState track heartbeat state của connection
type heartbeatState struct {
    connection   Connection
    lastPing     time.Time
    lastPong     time.Time
    missedPings  int
    totalPings   int
}

// NewHeartbeatManager tạo heartbeat manager mới
func NewHeartbeatManager(logger logger.Logger) *HeartbeatManager {
    hm := &HeartbeatManager{
        connections:  make(map[string]*heartbeatState),
        logger:       logger,
        pingInterval: PingInterval,
        pongTimeout:  PongTimeout,
        stop:         make(chan struct{}),
    }
    
    hm.start()
    return hm
}

// RegisterConnection đăng ký connection cho heartbeat
func (hm *HeartbeatManager) RegisterConnection(conn Connection) {
    hm.mutex.Lock()
    defer hm.mutex.Unlock()
    
    hm.connections[conn.ID()] = &heartbeatState{
        connection:  conn,
        lastPing:    time.Now(),
        lastPong:    time.Now(),
        missedPings: 0,
        totalPings:  0,
    }
    
    hm.logger.Debug("Connection registered for heartbeat",
        "conn_id", conn.ID(),
    )
}

// UnregisterConnection hủy đăng ký connection
func (hm *HeartbeatManager) UnregisterConnection(connID string) {
    hm.mutex.Lock()
    defer hm.mutex.Unlock()
    
    delete(hm.connections, connID)
    
    hm.logger.Debug("Connection unregistered from heartbeat",
        "conn_id", connID,
    )
}

// UpdatePong cập nhật pong response
func (hm *HeartbeatManager) UpdatePong(connID string) {
    hm.mutex.Lock()
    defer hm.mutex.Unlock()
    
    if state, exists := hm.connections[connID]; exists {
        state.lastPong = time.Now()
        state.missedPings = 0
        
        hm.logger.Debug("Pong received",
            "conn_id", connID,
        )
    }
}

// start bắt đầu heartbeat loop
func (hm *HeartbeatManager) start() {
    hm.ticker = time.NewTicker(hm.pingInterval)
    
    go func() {
        for {
            select {
            case <-hm.ticker.C:
                hm.sendPings()
                hm.checkTimeouts()
            case <-hm.stop:
                hm.ticker.Stop()
                return
            }
        }
    }()
}

// sendPings gửi ping đến tất cả connections
func (hm *HeartbeatManager) sendPings() {
    hm.mutex.RLock()
    connections := make([]*heartbeatState, 0, len(hm.connections))
    for _, state := range hm.connections {
        connections = append(connections, state)
    }
    hm.mutex.RUnlock()
    
    now := time.Now()
    
    for _, state := range connections {
        if !state.connection.IsAlive() {
            continue
        }
        
        pingMsg := NewMessage(EventTypePing, map[string]interface{}{
            "timestamp": now.Unix(),
        })
        
        if err := state.connection.Send(pingMsg); err != nil {
            hm.logger.Warn("Failed to send ping",
                "conn_id", state.connection.ID(),
                "error", err,
            )
            
            hm.mutex.Lock()
            state.missedPings++
            hm.mutex.Unlock()
        } else {
            hm.mutex.Lock()
            state.lastPing = now
            state.totalPings++
            hm.mutex.Unlock()
        }
    }
}

// checkTimeouts kiểm tra timeout connections
func (hm *HeartbeatManager) checkTimeouts() {
    hm.mutex.RLock()
    timedOut := make([]Connection, 0)
    now := time.Now()
    
    for _, state := range hm.connections {
        if now.Sub(state.lastPong) > hm.pongTimeout || state.missedPings > 3 {
            timedOut = append(timedOut, state.connection)
        }
    }
    hm.mutex.RUnlock()
    
    // Close timed out connections
    for _, conn := range timedOut {
        hm.logger.Warn("Connection timed out",
            "conn_id", conn.ID(),
            "user_id", conn.UserID(),
        )
        
        conn.Close()
        hm.UnregisterConnection(conn.ID())
    }
}

// GetStats trả về heartbeat statistics
func (hm *HeartbeatManager) GetStats() map[string]interface{} {
    hm.mutex.RLock()
    defer hm.mutex.RUnlock()
    
    totalConns := len(hm.connections)
    healthyConns := 0
    totalPings := 0
    totalMissed := 0
    
    for _, state := range hm.connections {
        if state.connection.IsAlive() {
            healthyConns++
        }
        totalPings += state.totalPings
        totalMissed += state.missedPings
    }
    
    return map[string]interface{}{
        "total_connections":   totalConns,
        "healthy_connections": healthyConns,
        "total_pings_sent":    totalPings,
        "total_pings_missed":  totalMissed,
        "ping_success_rate":   float64(totalPings-totalMissed) / float64(totalPings),
    }
}

// Stop dừng heartbeat manager
func (hm *HeartbeatManager) Stop() {
    close(hm.stop)
    
    hm.mutex.Lock()
    defer hm.mutex.Unlock()
    
    hm.connections = make(map[string]*heartbeatState)
    
    hm.logger.Info("Heartbeat manager stopped")
}
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **connection_test.go**:
   - Test connection creation và lifecycle
   - Test message send/receive
   - Test room join/leave
   - Test connection state management

2. **connection_manager_test.go**:
   - Test connection registration/unregistration
   - Test health checking
   - Test cleanup functionality

3. **message_handler_test.go**:
   - Test message parsing và validation
   - Test rate limiting
   - Test system message handling

4. **heartbeat_test.go**:
   - Test ping/pong mechanism
   - Test timeout detection
   - Test connection cleanup

### Integration Test Example:

```go
package socket_test

import (
    "testing"
    "time"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
)

func TestConnectionLifecycle(t *testing.T) {
    logger := logger.NewTestLogger()
    
    // Create mock WebSocket connection
    mockConn := createMockWebSocketConnection()
    
    // Create socket connection
    conn, err := socket.NewSocketConnection(mockConn, 1, 1, 1, logger)
    if err != nil {
        t.Fatalf("Failed to create connection: %v", err)
    }
    
    // Test connection info
    if conn.UserID() != 1 {
        t.Errorf("Expected user ID 1, got %d", conn.UserID())
    }
    
    // Test send message
    msg := socket.NewMessage("test.event", map[string]interface{}{
        "test": "data",
    })
    
    err = conn.Send(msg)
    if err != nil {
        t.Errorf("Failed to send message: %v", err)
    }
    
    // Test close
    err = conn.Close()
    if err != nil {
        t.Errorf("Failed to close connection: %v", err)
    }
    
    if conn.IsAlive() {
        t.Error("Connection should not be alive after close")
    }
}

func TestRoomManagement(t *testing.T) {
    logger := logger.NewTestLogger()
    mockConn := createMockWebSocketConnection()
    
    conn, err := socket.NewSocketConnection(mockConn, 1, 1, 1, logger)
    if err != nil {
        t.Fatalf("Failed to create connection: %v", err)
    }
    defer conn.Close()
    
    // Test join room
    err = conn.JoinRoom("test-room")
    if err != nil {
        t.Errorf("Failed to join room: %v", err)
    }
    
    // Test get rooms
    rooms := conn.GetRooms()
    if len(rooms) != 1 || rooms[0] != "test-room" {
        t.Errorf("Expected 1 room 'test-room', got %v", rooms)
    }
    
    // Test leave room
    err = conn.LeaveRoom("test-room")
    if err != nil {
        t.Errorf("Failed to leave room: %v", err)
    }
    
    rooms = conn.GetRooms()
    if len(rooms) != 0 {
        t.Errorf("Expected 0 rooms, got %v", rooms)
    }
}
```

## ✅ Completion Criteria

- [ ] Connection wrapper hoạt động đúng với WebSocket
- [ ] Message serialization/deserialization chính xác
- [ ] Authentication integration hoạt động
- [ ] Room management functionality complete
- [ ] Heartbeat mechanism reliable
- [ ] Rate limiting effective
- [ ] Connection cleanup automatic
- [ ] Unit tests pass với coverage >= 85%
- [ ] Integration tests với real WebSocket connections
- [ ] Performance tests với concurrent connections
- [ ] Memory leak tests

## 🔗 Dependencies

- **Task 01**: Socket Package Infrastructure (Complete)
- **Task 02**: WebSocket Hub Implementation (Parallel)

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 04**: Event Router System
2. **Task 05**: Socket Module Business Logic
3. **Integration**: Integrate với existing authentication system

## 📝 Notes

- Connection phải thread-safe cho concurrent operations
- Message handling cần efficient serialization
- Heartbeat mechanism không được impact performance
- Room management phải sync với Hub
- Error handling comprehensive và user-friendly
- Rate limiting configurable và fair
- Connection cleanup graceful và reliable
