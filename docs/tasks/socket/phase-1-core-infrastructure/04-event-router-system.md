# Task 04: Event Router System

## 📋 Tổng Quan

Triển khai Event Router - hệ thống định tuyến events trung tâm cho WebSocket system. Component này chịu trách nhiệm route incoming events đến appropriate handlers và enable cross-module communication thông qua event publishing/subscribing.

## 🎯 Mục Tiêu

- Route WebSocket events đến module-specific handlers
- Enable cross-module event publishing và subscribing  
- Implement event middleware và filtering
- Support event transformation và validation
- Provide event analytics và monitoring
- Handle event queuing và retry mechanisms

## 📁 Files Cần Tạo

```
internal/pkg/socket/
├── event_router.go       # Main EventRouter implementation
├── event_registry.go     # Handler registration management
├── event_middleware.go   # Event processing middleware
├── event_publisher.go    # Event publishing system
└── event_metrics.go      # Event analytics và monitoring
```

## 🔧 Implementation Steps

### Step 1: Implement Core Event Router (event_router.go)

```go
package socket

import (
    "context"
    "fmt"
    "strings"
    "sync"
    "time"
    "wnapi/internal/pkg/logger"
)

// eventRouter implementation của EventRouter interface
type eventRouter struct {
    // Handler management
    handlers    map[string]EventHandler
    handlersMux sync.RWMutex
    
    // Publisher management
    publishers  map[string][]EventPublisher
    pubMux      sync.RWMutex
    
    // Middleware
    middlewares []EventMiddleware
    
    // Dependencies
    hub         Hub
    logger      logger.Logger
    
    // Event queue
    eventQueue   chan *queuedEvent
    queueWorkers int
    
    // Metrics
    metrics *EventMetrics
    
    // Configuration
    config *EventRouterConfig
    
    // Lifecycle
    ctx    context.Context
    cancel context.CancelFunc
    wg     sync.WaitGroup
}

// EventRouterConfig cấu hình cho EventRouter
type EventRouterConfig struct {
    QueueSize       int
    WorkerCount     int
    EnableMetrics   bool
    EnableRetry     bool
    RetryAttempts   int
    RetryDelay      time.Duration
    EventTimeout    time.Duration
}

// DefaultEventRouterConfig trả về cấu hình mặc định
func DefaultEventRouterConfig() *EventRouterConfig {
    return &EventRouterConfig{
        QueueSize:     1000,
        WorkerCount:   10,
        EnableMetrics: true,
        EnableRetry:   true,
        RetryAttempts: 3,
        RetryDelay:    time.Second,
        EventTimeout:  30 * time.Second,
    }
}

// queuedEvent đại diện cho event trong queue
type queuedEvent struct {
    ctx       context.Context
    conn      Connection
    eventType string
    data      map[string]interface{}
    attempts  int
    timestamp time.Time
    result    chan error
}

// EventMiddleware interface cho event middleware
type EventMiddleware interface {
    ProcessEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}, next EventHandler) error
}

// NewEventRouter tạo EventRouter instance mới
func NewEventRouter(hub Hub, logger logger.Logger, config *EventRouterConfig) EventRouter {
    if config == nil {
        config = DefaultEventRouterConfig()
    }
    
    ctx, cancel := context.WithCancel(context.Background())
    
    router := &eventRouter{
        handlers:     make(map[string]EventHandler),
        publishers:   make(map[string][]EventPublisher),
        middlewares:  make([]EventMiddleware, 0),
        hub:          hub,
        logger:       logger,
        eventQueue:   make(chan *queuedEvent, config.QueueSize),
        queueWorkers: config.WorkerCount,
        config:       config,
        ctx:          ctx,
        cancel:       cancel,
    }
    
    if config.EnableMetrics {
        router.metrics = NewEventMetrics()
    }
    
    // Start queue workers
    router.startWorkers()
    
    return router
}

// RegisterHandler đăng ký event handler
func (r *eventRouter) RegisterHandler(eventType string, handler EventHandler) {
    r.handlersMux.Lock()
    defer r.handlersMux.Unlock()
    
    if handler == nil {
        r.logger.Warn("Attempted to register nil handler", "event_type", eventType)
        return
    }
    
    r.handlers[eventType] = handler
    
    r.logger.Info("Event handler registered",
        "event_type", eventType,
        "total_handlers", len(r.handlers),
    )
}

// UnregisterHandler hủy đăng ký event handler
func (r *eventRouter) UnregisterHandler(eventType string) {
    r.handlersMux.Lock()
    defer r.handlersMux.Unlock()
    
    delete(r.handlers, eventType)
    
    r.logger.Info("Event handler unregistered",
        "event_type", eventType,
    )
}

// GetHandler lấy handler cho event type
func (r *eventRouter) GetHandler(eventType string) (EventHandler, bool) {
    r.handlersMux.RLock()
    defer r.handlersMux.RUnlock()
    
    handler, exists := r.handlers[eventType]
    return handler, exists
}

// RegisterPublisher đăng ký event publisher
func (r *eventRouter) RegisterPublisher(eventType string, publisher EventPublisher) {
    r.pubMux.Lock()
    defer r.pubMux.Unlock()
    
    if r.publishers[eventType] == nil {
        r.publishers[eventType] = make([]EventPublisher, 0)
    }
    
    r.publishers[eventType] = append(r.publishers[eventType], publisher)
    
    r.logger.Info("Event publisher registered",
        "event_type", eventType,
        "total_publishers", len(r.publishers[eventType]),
    )
}

// AddMiddleware thêm middleware vào chain
func (r *eventRouter) AddMiddleware(middleware EventMiddleware) {
    r.middlewares = append(r.middlewares, middleware)
    
    r.logger.Info("Event middleware added",
        "total_middlewares", len(r.middlewares),
    )
}

// RouteEvent route event đến appropriate handler
func (r *eventRouter) RouteEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}) error {
    if r.config.EnableMetrics {
        r.metrics.IncrementEventReceived(eventType)
    }
    
    // Validate input
    if eventType == "" {
        return NewMessageError("event type is required", ErrInvalidEventType)
    }
    
    if conn == nil {
        return NewConnectionError("connection is required", ErrInvalidConnection)
    }
    
    // Create queued event
    queuedEvent := &queuedEvent{
        ctx:       ctx,
        conn:      conn,
        eventType: eventType,
        data:      data,
        attempts:  0,
        timestamp: time.Now(),
        result:    make(chan error, 1),
    }
    
    // Add to queue
    select {
    case r.eventQueue <- queuedEvent:
        // Wait for result with timeout
        select {
        case err := <-queuedEvent.result:
            return err
        case <-time.After(r.config.EventTimeout):
            if r.config.EnableMetrics {
                r.metrics.IncrementEventTimeout(eventType)
            }
            return NewSystemError("event processing timeout", nil)
        case <-ctx.Done():
            return ctx.Err()
        }
    case <-ctx.Done():
        return ctx.Err()
    default:
        if r.config.EnableMetrics {
            r.metrics.IncrementEventQueueFull(eventType)
        }
        return NewSystemError("event queue full", ErrSystemOverload)
    }
}

// PublishEvent publish event đến registered publishers
func (r *eventRouter) PublishEvent(eventType string, data map[string]interface{}) error {
    r.pubMux.RLock()
    publishers, exists := r.publishers[eventType]
    if !exists {
        r.pubMux.RUnlock()
        r.logger.Debug("No publishers for event type", "event_type", eventType)
        return nil
    }
    
    // Copy publishers slice để avoid holding lock
    publishersCopy := make([]EventPublisher, len(publishers))
    copy(publishersCopy, publishers)
    r.pubMux.RUnlock()
    
    // Publish to all publishers
    var lastError error
    successCount := 0
    
    for _, publisher := range publishersCopy {
        if err := publisher.Publish(eventType, data); err != nil {
            r.logger.Warn("Failed to publish event",
                "event_type", eventType,
                "error", err,
            )
            lastError = err
        } else {
            successCount++
        }
    }
    
    if r.config.EnableMetrics {
        r.metrics.IncrementEventPublished(eventType, successCount, len(publishersCopy)-successCount)
    }
    
    r.logger.Debug("Event published",
        "event_type", eventType,
        "success_count", successCount,
        "total_publishers", len(publishersCopy),
    )
    
    return lastError
}

// PublishToUser publish event đến specific user
func (r *eventRouter) PublishToUser(userID uint, eventType string, data map[string]interface{}) error {
    message := NewMessage(eventType, data)
    return r.hub.BroadcastToUser(userID, message)
}

// PublishToRoom publish event đến specific room
func (r *eventRouter) PublishToRoom(roomID string, eventType string, data map[string]interface{}) error {
    message := NewMessage(eventType, data)
    return r.hub.BroadcastToRoom(roomID, message)
}

// startWorkers khởi động queue workers
func (r *eventRouter) startWorkers() {
    for i := 0; i < r.queueWorkers; i++ {
        r.wg.Add(1)
        go r.worker(i)
    }
    
    r.logger.Info("Event router workers started",
        "worker_count", r.queueWorkers,
    )
}

// worker xử lý events trong queue
func (r *eventRouter) worker(workerID int) {
    defer r.wg.Done()
    
    r.logger.Debug("Event worker started", "worker_id", workerID)
    
    for {
        select {
        case event := <-r.eventQueue:
            r.processQueuedEvent(event, workerID)
        case <-r.ctx.Done():
            r.logger.Debug("Event worker stopping", "worker_id", workerID)
            return
        }
    }
}

// processQueuedEvent xử lý một queued event
func (r *eventRouter) processQueuedEvent(event *queuedEvent, workerID int) {
    startTime := time.Now()
    event.attempts++
    
    r.logger.Debug("Processing event",
        "worker_id", workerID,
        "event_type", event.eventType,
        "conn_id", event.conn.ID(),
        "attempt", event.attempts,
    )
    
    err := r.executeEvent(event)
    
    if err != nil && r.config.EnableRetry && event.attempts < r.config.RetryAttempts {
        // Retry after delay
        go func() {
            time.Sleep(r.config.RetryDelay * time.Duration(event.attempts))
            select {
            case r.eventQueue <- event:
            case <-r.ctx.Done():
            }
        }()
        return
    }
    
    // Send result
    select {
    case event.result <- err:
    default:
    }
    
    // Update metrics
    if r.config.EnableMetrics {
        duration := time.Since(startTime)
        if err != nil {
            r.metrics.IncrementEventError(event.eventType, err.Error())
        } else {
            r.metrics.RecordEventProcessed(event.eventType, duration)
        }
    }
}

// executeEvent thực thi event với middleware chain
func (r *eventRouter) executeEvent(event *queuedEvent) error {
    // Find handler
    handler, exists := r.GetHandler(event.eventType)
    if !exists {
        // Try wildcard handlers
        handler = r.findWildcardHandler(event.eventType)
        if handler == nil {
            return NewMessageError(
                fmt.Sprintf("no handler found for event type: %s", event.eventType),
                ErrHandlerNotFound,
            )
        }
    }
    
    // Execute with middleware chain
    finalHandler := r.buildMiddlewareChain(handler)
    
    return finalHandler(event.ctx, event.conn, event.data)
}

// findWildcardHandler tìm wildcard handler
func (r *eventRouter) findWildcardHandler(eventType string) EventHandler {
    r.handlersMux.RLock()
    defer r.handlersMux.RUnlock()
    
    // Try pattern matching
    parts := strings.Split(eventType, ".")
    
    // Try module.* patterns
    if len(parts) >= 2 {
        pattern := parts[0] + ".*"
        if handler, exists := r.handlers[pattern]; exists {
            return handler
        }
    }
    
    // Try global wildcard
    if handler, exists := r.handlers["*"]; exists {
        return handler
    }
    
    return nil
}

// buildMiddlewareChain xây dựng middleware chain
func (r *eventRouter) buildMiddlewareChain(finalHandler EventHandler) EventHandler {
    handler := finalHandler
    
    // Apply middlewares in reverse order
    for i := len(r.middlewares) - 1; i >= 0; i-- {
        middleware := r.middlewares[i]
        currentHandler := handler
        
        handler = func(ctx context.Context, conn Connection, data map[string]interface{}) error {
            return middleware.ProcessEvent(ctx, conn, "", data, currentHandler)
        }
    }
    
    return handler
}

// GetStats trả về router statistics
func (r *eventRouter) GetStats() map[string]interface{} {
    stats := map[string]interface{}{
        "total_handlers":   len(r.handlers),
        "total_publishers": r.getTotalPublishers(),
        "queue_size":       len(r.eventQueue),
        "worker_count":     r.queueWorkers,
    }
    
    if r.config.EnableMetrics {
        metricsStats := r.metrics.GetStats()
        for k, v := range metricsStats {
            stats[k] = v
        }
    }
    
    return stats
}

// getTotalPublishers đếm total publishers
func (r *eventRouter) getTotalPublishers() int {
    r.pubMux.RLock()
    defer r.pubMux.RUnlock()
    
    total := 0
    for _, publishers := range r.publishers {
        total += len(publishers)
    }
    
    return total
}

// Stop dừng event router
func (r *eventRouter) Stop() error {
    r.logger.Info("Stopping event router...")
    
    r.cancel()
    
    // Wait for workers to finish
    done := make(chan struct{})
    go func() {
        r.wg.Wait()
        close(done)
    }()
    
    select {
    case <-done:
        r.logger.Info("Event router stopped gracefully")
    case <-time.After(30 * time.Second):
        r.logger.Warn("Event router stop timeout")
    }
    
    return nil
}
```

### Step 2: Implement Event Registry (event_registry.go)

```go
package socket

import (
    "fmt"
    "reflect"
    "sync"
)

// EventRegistry quản lý handler registration và discovery
type EventRegistry struct {
    handlers    map[string]*HandlerInfo
    modules     map[string]*ModuleInfo
    mutex       sync.RWMutex
    logger      logger.Logger
}

// HandlerInfo thông tin về một event handler
type HandlerInfo struct {
    Handler     EventHandler
    Module      string
    EventType   string
    Description string
    Version     string
    Metadata    map[string]interface{}
}

// ModuleInfo thông tin về một module
type ModuleInfo struct {
    Name        string
    Version     string
    Handlers    []string
    Publishers  []string
    Description string
}

// NewEventRegistry tạo event registry mới
func NewEventRegistry(logger logger.Logger) *EventRegistry {
    return &EventRegistry{
        handlers: make(map[string]*HandlerInfo),
        modules:  make(map[string]*ModuleInfo),
        logger:   logger,
    }
}

// RegisterModule đăng ký module với registry
func (r *EventRegistry) RegisterModule(name, version, description string) {
    r.mutex.Lock()
    defer r.mutex.Unlock()
    
    r.modules[name] = &ModuleInfo{
        Name:        name,
        Version:     version,
        Description: description,
        Handlers:    make([]string, 0),
        Publishers:  make([]string, 0),
    }
    
    r.logger.Info("Module registered",
        "module", name,
        "version", version,
    )
}

// RegisterModuleHandler đăng ký handler cho module
func (r *EventRegistry) RegisterModuleHandler(module, eventType, description string, handler EventHandler) error {
    r.mutex.Lock()
    defer r.mutex.Unlock()
    
    // Validate module exists
    moduleInfo, exists := r.modules[module]
    if !exists {
        return fmt.Errorf("module %s not registered", module)
    }
    
    // Check if handler already exists
    if _, exists := r.handlers[eventType]; exists {
        return fmt.Errorf("handler for event type %s already exists", eventType)
    }
    
    // Register handler
    handlerInfo := &HandlerInfo{
        Handler:     handler,
        Module:      module,
        EventType:   eventType,
        Description: description,
        Version:     moduleInfo.Version,
        Metadata:    make(map[string]interface{}),
    }
    
    r.handlers[eventType] = handlerInfo
    moduleInfo.Handlers = append(moduleInfo.Handlers, eventType)
    
    r.logger.Info("Module handler registered",
        "module", module,
        "event_type", eventType,
    )
    
    return nil
}

// UnregisterModuleHandler hủy đăng ký handler
func (r *EventRegistry) UnregisterModuleHandler(module, eventType string) error {
    r.mutex.Lock()
    defer r.mutex.Unlock()
    
    handlerInfo, exists := r.handlers[eventType]
    if !exists {
        return fmt.Errorf("handler for event type %s not found", eventType)
    }
    
    if handlerInfo.Module != module {
        return fmt.Errorf("handler %s does not belong to module %s", eventType, module)
    }
    
    delete(r.handlers, eventType)
    
    // Remove from module
    if moduleInfo, exists := r.modules[module]; exists {
        for i, h := range moduleInfo.Handlers {
            if h == eventType {
                moduleInfo.Handlers = append(moduleInfo.Handlers[:i], moduleInfo.Handlers[i+1:]...)
                break
            }
        }
    }
    
    r.logger.Info("Module handler unregistered",
        "module", module,
        "event_type", eventType,
    )
    
    return nil
}

// GetHandler lấy handler info
func (r *EventRegistry) GetHandler(eventType string) (*HandlerInfo, bool) {
    r.mutex.RLock()
    defer r.mutex.RUnlock()
    
    info, exists := r.handlers[eventType]
    return info, exists
}

// GetModuleHandlers lấy tất cả handlers của module
func (r *EventRegistry) GetModuleHandlers(module string) []*HandlerInfo {
    r.mutex.RLock()
    defer r.mutex.RUnlock()
    
    handlers := make([]*HandlerInfo, 0)
    
    for _, info := range r.handlers {
        if info.Module == module {
            handlers = append(handlers, info)
        }
    }
    
    return handlers
}

// GetAllModules lấy tất cả modules
func (r *EventRegistry) GetAllModules() map[string]*ModuleInfo {
    r.mutex.RLock()
    defer r.mutex.RUnlock()
    
    modules := make(map[string]*ModuleInfo)
    for name, info := range r.modules {
        modules[name] = info
    }
    
    return modules
}

// ValidateHandler validate handler function signature
func (r *EventRegistry) ValidateHandler(handler EventHandler) error {
    if handler == nil {
        return fmt.Errorf("handler cannot be nil")
    }
    
    // Use reflection to validate signature
    handlerType := reflect.TypeOf(handler)
    
    if handlerType.Kind() != reflect.Func {
        return fmt.Errorf("handler must be a function")
    }
    
    if handlerType.NumIn() != 3 {
        return fmt.Errorf("handler must have exactly 3 parameters")
    }
    
    if handlerType.NumOut() != 1 {
        return fmt.Errorf("handler must return exactly 1 value")
    }
    
    // Check parameter types
    if handlerType.In(0).String() != "context.Context" {
        return fmt.Errorf("first parameter must be context.Context")
    }
    
    // Check return type
    if handlerType.Out(0).String() != "error" {
        return fmt.Errorf("return type must be error")
    }
    
    return nil
}

// GetRegistryStats trả về registry statistics
func (r *EventRegistry) GetRegistryStats() map[string]interface{} {
    r.mutex.RLock()
    defer r.mutex.RUnlock()
    
    moduleStats := make(map[string]interface{})
    
    for name, module := range r.modules {
        moduleStats[name] = map[string]interface{}{
            "version":       module.Version,
            "handler_count": len(module.Handlers),
            "handlers":      module.Handlers,
        }
    }
    
    return map[string]interface{}{
        "total_modules":  len(r.modules),
        "total_handlers": len(r.handlers),
        "modules":        moduleStats,
    }
}
```

### Step 3: Implement Event Middleware (event_middleware.go)

```go
package socket

import (
    "context"
    "time"
    "wnapi/internal/pkg/logger"
)

// AuthenticationMiddleware kiểm tra authentication
type AuthenticationMiddleware struct {
    logger logger.Logger
}

// NewAuthenticationMiddleware tạo authentication middleware
func NewAuthenticationMiddleware(logger logger.Logger) *AuthenticationMiddleware {
    return &AuthenticationMiddleware{
        logger: logger,
    }
}

// ProcessEvent xử lý authentication
func (m *AuthenticationMiddleware) ProcessEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}, next EventHandler) error {
    // Skip authentication cho system events
    if isSystemEvent(eventType) {
        return next(ctx, conn, data)
    }
    
    // Kiểm tra user authentication
    if conn.UserID() == 0 {
        m.logger.Warn("Unauthenticated event attempt",
            "conn_id", conn.ID(),
            "event_type", eventType,
        )
        return NewAuthError("authentication required", ErrAuthenticationFailed)
    }
    
    return next(ctx, conn, data)
}

// LoggingMiddleware log events
type LoggingMiddleware struct {
    logger logger.Logger
}

// NewLoggingMiddleware tạo logging middleware
func NewLoggingMiddleware(logger logger.Logger) *LoggingMiddleware {
    return &LoggingMiddleware{
        logger: logger,
    }
}

// ProcessEvent log event processing
func (m *LoggingMiddleware) ProcessEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}, next EventHandler) error {
    start := time.Now()
    
    m.logger.Debug("Event processing started",
        "event_type", eventType,
        "conn_id", conn.ID(),
        "user_id", conn.UserID(),
    )
    
    err := next(ctx, conn, data)
    
    duration := time.Since(start)
    
    if err != nil {
        m.logger.Error("Event processing failed",
            "event_type", eventType,
            "conn_id", conn.ID(),
            "duration", duration,
            "error", err,
        )
    } else {
        m.logger.Debug("Event processing completed",
            "event_type", eventType,
            "conn_id", conn.ID(),
            "duration", duration,
        )
    }
    
    return err
}

// RateLimitMiddleware rate limiting
type RateLimitMiddleware struct {
    limits map[string]*rateLimitInfo
    mutex  sync.RWMutex
    logger logger.Logger
}

type rateLimitInfo struct {
    limit     int
    window    time.Duration
    requests  map[string]*requestInfo
    mutex     sync.RWMutex
}

type requestInfo struct {
    count     int
    resetTime time.Time
}

// NewRateLimitMiddleware tạo rate limit middleware
func NewRateLimitMiddleware(logger logger.Logger) *RateLimitMiddleware {
    return &RateLimitMiddleware{
        limits: make(map[string]*rateLimitInfo),
        logger: logger,
    }
}

// SetLimit set rate limit cho event type
func (m *RateLimitMiddleware) SetLimit(eventType string, limit int, window time.Duration) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.limits[eventType] = &rateLimitInfo{
        limit:    limit,
        window:   window,
        requests: make(map[string]*requestInfo),
    }
}

// ProcessEvent check rate limit
func (m *RateLimitMiddleware) ProcessEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}, next EventHandler) error {
    if !m.checkRateLimit(eventType, conn.ID()) {
        m.logger.Warn("Rate limit exceeded",
            "event_type", eventType,
            "conn_id", conn.ID(),
        )
        return NewSystemError("rate limit exceeded", ErrSystemOverload)
    }
    
    return next(ctx, conn, data)
}

// checkRateLimit kiểm tra rate limit
func (m *RateLimitMiddleware) checkRateLimit(eventType, connID string) bool {
    m.mutex.RLock()
    limitInfo, exists := m.limits[eventType]
    m.mutex.RUnlock()
    
    if !exists {
        return true // No limit set
    }
    
    limitInfo.mutex.Lock()
    defer limitInfo.mutex.Unlock()
    
    now := time.Now()
    reqInfo, exists := limitInfo.requests[connID]
    
    if !exists {
        limitInfo.requests[connID] = &requestInfo{
            count:     1,
            resetTime: now.Add(limitInfo.window),
        }
        return true
    }
    
    // Reset if window expired
    if now.After(reqInfo.resetTime) {
        reqInfo.count = 1
        reqInfo.resetTime = now.Add(limitInfo.window)
        return true
    }
    
    // Check limit
    if reqInfo.count >= limitInfo.limit {
        return false
    }
    
    reqInfo.count++
    return true
}

// ValidationMiddleware validate event data
type ValidationMiddleware struct {
    validators map[string]EventValidator
    mutex      sync.RWMutex
    logger     logger.Logger
}

// EventValidator interface cho validation
type EventValidator interface {
    Validate(ctx context.Context, conn Connection, data map[string]interface{}) error
}

// NewValidationMiddleware tạo validation middleware
func NewValidationMiddleware(logger logger.Logger) *ValidationMiddleware {
    return &ValidationMiddleware{
        validators: make(map[string]EventValidator),
        logger:     logger,
    }
}

// RegisterValidator đăng ký validator cho event type
func (m *ValidationMiddleware) RegisterValidator(eventType string, validator EventValidator) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.validators[eventType] = validator
}

// ProcessEvent validate event data
func (m *ValidationMiddleware) ProcessEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}, next EventHandler) error {
    m.mutex.RLock()
    validator, exists := m.validators[eventType]
    m.mutex.RUnlock()
    
    if exists {
        if err := validator.Validate(ctx, conn, data); err != nil {
            m.logger.Warn("Event validation failed",
                "event_type", eventType,
                "conn_id", conn.ID(),
                "error", err,
            )
            return NewMessageError("validation failed", err)
        }
    }
    
    return next(ctx, conn, data)
}

// Helper functions
func isSystemEvent(eventType string) bool {
    systemEvents := []string{
        EventTypePing,
        EventTypePong,
        EventTypeConnect,
        EventTypeDisconnect,
    }
    
    for _, sysEvent := range systemEvents {
        if eventType == sysEvent {
            return true
        }
    }
    
    return false
}
```

### Step 4: Implement Event Metrics (event_metrics.go)

```go
package socket

import (
    "sync"
    "time"
)

// EventMetrics thu thập metrics về event processing
type EventMetrics struct {
    mutex sync.RWMutex
    
    // Event counters
    eventsReceived map[string]int64
    eventsProcessed map[string]int64
    eventsErrored  map[string]int64
    eventsTimeout  map[string]int64
    eventsQueueFull map[string]int64
    
    // Event publishing
    eventsPublished map[string]int64
    publishSucccess map[string]int64
    publishFailed   map[string]int64
    
    // Processing times
    processingTimes map[string][]time.Duration
    
    // Error details
    errorsByType map[string]map[string]int64
    
    // General stats
    startTime time.Time
}

// NewEventMetrics tạo event metrics instance
func NewEventMetrics() *EventMetrics {
    return &EventMetrics{
        eventsReceived:  make(map[string]int64),
        eventsProcessed: make(map[string]int64),
        eventsErrored:   make(map[string]int64),
        eventsTimeout:   make(map[string]int64),
        eventsQueueFull: make(map[string]int64),
        eventsPublished: make(map[string]int64),
        publishSucccess: make(map[string]int64),
        publishFailed:   make(map[string]int64),
        processingTimes: make(map[string][]time.Duration),
        errorsByType:    make(map[string]map[string]int64),
        startTime:       time.Now(),
    }
}

// IncrementEventReceived tăng counter event received
func (m *EventMetrics) IncrementEventReceived(eventType string) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsReceived[eventType]++
}

// RecordEventProcessed record event processed với timing
func (m *EventMetrics) RecordEventProcessed(eventType string, duration time.Duration) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsProcessed[eventType]++
    
    // Store processing time (keep last 1000 entries)
    if m.processingTimes[eventType] == nil {
        m.processingTimes[eventType] = make([]time.Duration, 0)
    }
    
    times := m.processingTimes[eventType]
    if len(times) >= 1000 {
        times = times[1:]
    }
    m.processingTimes[eventType] = append(times, duration)
}

// IncrementEventError tăng counter event error
func (m *EventMetrics) IncrementEventError(eventType, errorType string) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsErrored[eventType]++
    
    if m.errorsByType[eventType] == nil {
        m.errorsByType[eventType] = make(map[string]int64)
    }
    m.errorsByType[eventType][errorType]++
}

// IncrementEventTimeout tăng counter event timeout
func (m *EventMetrics) IncrementEventTimeout(eventType string) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsTimeout[eventType]++
}

// IncrementEventQueueFull tăng counter queue full
func (m *EventMetrics) IncrementEventQueueFull(eventType string) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsQueueFull[eventType]++
}

// IncrementEventPublished record event published
func (m *EventMetrics) IncrementEventPublished(eventType string, success, failed int) {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsPublished[eventType]++
    m.publishSucccess[eventType] += int64(success)
    m.publishFailed[eventType] += int64(failed)
}

// GetEventStats lấy stats cho specific event type
func (m *EventMetrics) GetEventStats(eventType string) map[string]interface{} {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    stats := map[string]interface{}{
        "received":     m.eventsReceived[eventType],
        "processed":    m.eventsProcessed[eventType],
        "errored":      m.eventsErrored[eventType],
        "timeout":      m.eventsTimeout[eventType],
        "queue_full":   m.eventsQueueFull[eventType],
        "published":    m.eventsPublished[eventType],
        "pub_success":  m.publishSucccess[eventType],
        "pub_failed":   m.publishFailed[eventType],
    }
    
    // Calculate average processing time
    if times := m.processingTimes[eventType]; len(times) > 0 {
        var total time.Duration
        for _, t := range times {
            total += t
        }
        stats["avg_processing_time"] = total / time.Duration(len(times))
        stats["min_processing_time"] = m.getMinDuration(times)
        stats["max_processing_time"] = m.getMaxDuration(times)
    }
    
    // Error breakdown
    if errors := m.errorsByType[eventType]; len(errors) > 0 {
        stats["errors_by_type"] = errors
    }
    
    return stats
}

// GetStats lấy tất cả metrics
func (m *EventMetrics) GetStats() map[string]interface{} {
    m.mutex.RLock()
    defer m.mutex.RUnlock()
    
    totalReceived := int64(0)
    totalProcessed := int64(0)
    totalErrored := int64(0)
    
    for _, count := range m.eventsReceived {
        totalReceived += count
    }
    
    for _, count := range m.eventsProcessed {
        totalProcessed += count
    }
    
    for _, count := range m.eventsErrored {
        totalErrored += count
    }
    
    return map[string]interface{}{
        "uptime":            time.Since(m.startTime),
        "total_received":    totalReceived,
        "total_processed":   totalProcessed,
        "total_errored":     totalErrored,
        "success_rate":      float64(totalProcessed) / float64(totalReceived),
        "error_rate":        float64(totalErrored) / float64(totalReceived),
        "events_by_type":    m.getEventTypeStats(),
    }
}

// getEventTypeStats lấy stats theo event type
func (m *EventMetrics) getEventTypeStats() map[string]interface{} {
    eventStats := make(map[string]interface{})
    
    for eventType := range m.eventsReceived {
        eventStats[eventType] = m.GetEventStats(eventType)
    }
    
    return eventStats
}

// getMinDuration tìm min duration
func (m *EventMetrics) getMinDuration(durations []time.Duration) time.Duration {
    if len(durations) == 0 {
        return 0
    }
    
    min := durations[0]
    for _, d := range durations[1:] {
        if d < min {
            min = d
        }
    }
    
    return min
}

// getMaxDuration tìm max duration
func (m *EventMetrics) getMaxDuration(durations []time.Duration) time.Duration {
    if len(durations) == 0 {
        return 0
    }
    
    max := durations[0]
    for _, d := range durations[1:] {
        if d > max {
            max = d
        }
    }
    
    return max
}

// Reset reset tất cả metrics
func (m *EventMetrics) Reset() {
    m.mutex.Lock()
    defer m.mutex.Unlock()
    
    m.eventsReceived = make(map[string]int64)
    m.eventsProcessed = make(map[string]int64)
    m.eventsErrored = make(map[string]int64)
    m.eventsTimeout = make(map[string]int64)
    m.eventsQueueFull = make(map[string]int64)
    m.eventsPublished = make(map[string]int64)
    m.publishSucccess = make(map[string]int64)
    m.publishFailed = make(map[string]int64)
    m.processingTimes = make(map[string][]time.Duration)
    m.errorsByType = make(map[string]map[string]int64)
    m.startTime = time.Now()
}
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **event_router_test.go**:
   - Test handler registration/unregistration
   - Test event routing và queue processing
   - Test middleware chain execution
   - Test error handling và retries

2. **event_registry_test.go**:
   - Test module registration
   - Test handler validation
   - Test registry stats

3. **event_middleware_test.go**:
   - Test authentication middleware
   - Test rate limiting
   - Test validation
   - Test logging

4. **event_metrics_test.go**:
   - Test metrics collection
   - Test statistics calculation
   - Test concurrent updates

### Integration Test Example:

```go
package socket_test

import (
    "context"
    "testing"
    "time"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
)

func TestEventRouterLifecycle(t *testing.T) {
    logger := logger.NewTestLogger()
    hub := socket.NewTestHub()
    router := socket.NewEventRouter(hub, logger, nil)
    
    // Test handler registration
    handler := func(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
        return nil
    }
    
    router.RegisterHandler("test.event", handler)
    
    // Test get handler
    retrievedHandler, exists := router.GetHandler("test.event")
    if !exists {
        t.Fatal("Handler should exist")
    }
    
    if retrievedHandler == nil {
        t.Fatal("Retrieved handler should not be nil")
    }
    
    // Test event routing
    mockConn := socket.NewMockConnection(1, 1, 1)
    err := router.RouteEvent(context.Background(), mockConn, "test.event", nil)
    if err != nil {
        t.Errorf("Event routing failed: %v", err)
    }
    
    // Test stop
    err = router.Stop()
    if err != nil {
        t.Errorf("Failed to stop router: %v", err)
    }
}

func TestEventMiddleware(t *testing.T) {
    logger := logger.NewTestLogger()
    
    // Test authentication middleware
    authMiddleware := socket.NewAuthenticationMiddleware(logger)
    
    mockConn := socket.NewMockConnection(0, 1, 1) // No user ID
    
    handler := func(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
        return nil
    }
    
    err := authMiddleware.ProcessEvent(context.Background(), mockConn, "test.event", nil, handler)
    if err == nil {
        t.Error("Expected authentication error")
    }
    
    // Test with authenticated user
    authConn := socket.NewMockConnection(1, 1, 1)
    err = authMiddleware.ProcessEvent(context.Background(), authConn, "test.event", nil, handler)
    if err != nil {
        t.Errorf("Unexpected error with authenticated user: %v", err)
    }
}
```

## ✅ Completion Criteria

- [ ] Event routing works correctly với handlers
- [ ] Middleware chain execution proper order
- [ ] Event queue processing reliable
- [ ] Publisher registration và event publishing works
- [ ] Metrics collection accurate
- [ ] Error handling comprehensive
- [ ] Unit tests pass với coverage >= 85%
- [ ] Integration tests với real modules
- [ ] Performance tests với high event load
- [ ] Documentation complete

## 🔗 Dependencies

- **Task 01**: Socket Package Infrastructure (Complete)
- **Task 02**: WebSocket Hub Implementation (Complete)
- **Task 03**: Connection Management (Complete)

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 05**: Socket Module Business Logic
2. **Task 09**: Notification Module Integration
3. **Module Integration**: Auth, Blog, E-commerce modules

## 📝 Notes

- Event router phải handle high throughput efficiently
- Middleware chain không được impact performance significantly
- Event queue cần configurable size và worker count
- Metrics collection lightweight và non-blocking
- Handler registration thread-safe
- Event publishing reliable với retry mechanisms
- Error handling informative và actionable
