# Task 02: WebSocket Hub Implementation

## 📋 Tổng Quan

Triển khai WebSocket Hub - component trung tâm quản lý tất cả WebSocket connections, rooms và broadcasting. Hub là trái tim của hệ thống WebSocket, chị<PERSON> tr<PERSON><PERSON>hi<PERSON> maintain 1000+ concurrent connections một cách hiệu quả.

## 🎯 Mục Tiêu

- Quản lý lifecycle của WebSocket connections
- Implement room-based message broadcasting
- Hỗ trợ horizontal scaling với connection management
- Monitoring và health checking cho connections
- Thread-safe operations với high concurrency

## 📁 Files Cần Tạo

```
internal/pkg/socket/
├── hub.go            # Main Hub implementation
├── room.go           # Room management
├── connection_pool.go # Connection pooling
└── statistics.go     # Hub statistics
```

## 🔧 Implementation Steps

### Step 1: Implement Core Hub (hub.go)

```go
package socket

import (
    "context"
    "fmt"
    "sync"
    "time"
    "github.com/gorilla/websocket"
    "wnapi/internal/pkg/logger"
)

// HubConfig cấu hình cho Hub
type HubConfig struct {
    MaxConnections        int
    MaxConnectionsPerUser int
    MaxRoomsPerUser      int
    PingInterval         time.Duration
    WriteTimeout         time.Duration
    ReadTimeout          time.Duration
    CleanupInterval      time.Duration
    EnableStatistics     bool
}

// DefaultHubConfig trả về cấu hình mặc định
func DefaultHubConfig() *HubConfig {
    return &HubConfig{
        MaxConnections:        MaxConnections,
        MaxConnectionsPerUser: MaxConnectionsPerUser,
        MaxRoomsPerUser:      MaxRoomsPerUser,
        PingInterval:         PingInterval,
        WriteTimeout:         WriteTimeout,
        ReadTimeout:          ReadTimeout,
        CleanupInterval:      CleanupInterval,
        EnableStatistics:     true,
    }
}

// hub implementation của Hub interface
type hub struct {
    // Configuration
    config *HubConfig
    logger logger.Logger
    
    // Connection management
    connections map[string]Connection      // connID -> Connection
    userConns   map[uint][]string         // userID -> []connID
    connMutex   sync.RWMutex
    
    // Room management
    rooms     map[string]Room              // roomID -> Room
    roomMutex sync.RWMutex
    
    // Event handling
    register   chan *connectionRequest
    unregister chan string
    broadcast  chan *broadcastRequest
    
    // Lifecycle
    ctx      context.Context
    cancel   context.CancelFunc
    done     chan struct{}
    running  bool
    
    // Statistics
    stats *HubStatistics
    
    // Event router integration
    eventRouter EventRouter
}

// connectionRequest cho registration
type connectionRequest struct {
    conn      *websocket.Conn
    userID    uint
    tenantID  uint
    websiteID uint
    result    chan connectionResult
}

type connectionResult struct {
    connection Connection
    err        error
}

// broadcastRequest cho broadcasting
type broadcastRequest struct {
    target  broadcastTarget
    message *Message
}

type broadcastTarget struct {
    Type   string // "all", "user", "room"
    ID     string // userID hoặc roomID
}

// NewHub tạo Hub instance mới
func NewHub(config *HubConfig, logger logger.Logger) Hub {
    if config == nil {
        config = DefaultHubConfig()
    }
    
    ctx, cancel := context.WithCancel(context.Background())
    
    h := &hub{
        config:      config,
        logger:      logger,
        connections: make(map[string]Connection),
        userConns:   make(map[uint][]string),
        rooms:       make(map[string]Room),
        register:    make(chan *connectionRequest, 100),
        unregister:  make(chan string, 100),
        broadcast:   make(chan *broadcastRequest, 1000),
        ctx:         ctx,
        cancel:      cancel,
        done:        make(chan struct{}),
        stats:       NewHubStatistics(),
    }
    
    return h
}

// Start khởi động Hub
func (h *hub) Start() error {
    h.connMutex.Lock()
    defer h.connMutex.Unlock()
    
    if h.running {
        return ErrHubAlreadyStarted
    }
    
    h.running = true
    
    // Start main event loop
    go h.run()
    
    // Start cleanup goroutine
    go h.cleanup()
    
    // Start statistics collection
    if h.config.EnableStatistics {
        go h.collectStatistics()
    }
    
    h.logger.Info("WebSocket Hub started",
        "max_connections", h.config.MaxConnections,
        "max_connections_per_user", h.config.MaxConnectionsPerUser,
    )
    
    return nil
}

// Stop dừng Hub
func (h *hub) Stop() error {
    h.connMutex.Lock()
    defer h.connMutex.Unlock()
    
    if !h.running {
        return nil
    }
    
    h.running = false
    h.cancel()
    
    // Close all connections
    for _, conn := range h.connections {
        conn.Close()
    }
    
    // Wait for cleanup
    select {
    case <-h.done:
    case <-time.After(30 * time.Second):
        h.logger.Warn("Hub shutdown timeout")
    }
    
    h.logger.Info("WebSocket Hub stopped")
    return nil
}

// RegisterConnection đăng ký connection mới
func (h *hub) RegisterConnection(conn *websocket.Conn, userID, tenantID, websiteID uint) (Connection, error) {
    if !h.running {
        return nil, ErrHubNotStarted
    }
    
    // Check connection limits
    if h.GetConnectionCount() >= h.config.MaxConnections {
        return nil, NewConnectionError("connection limit reached", ErrConnectionLimit)
    }
    
    userConnCount := len(h.getUserConnections(userID))
    if userConnCount >= h.config.MaxConnectionsPerUser {
        return nil, NewConnectionError("user connection limit reached", ErrConnectionLimit)
    }
    
    req := &connectionRequest{
        conn:      conn,
        userID:    userID,
        tenantID:  tenantID,
        websiteID: websiteID,
        result:    make(chan connectionResult, 1),
    }
    
    select {
    case h.register <- req:
        result := <-req.result
        return result.connection, result.err
    case <-h.ctx.Done():
        return nil, NewConnectionError("hub shutting down", h.ctx.Err())
    case <-time.After(5 * time.Second):
        return nil, NewConnectionError("registration timeout", nil)
    }
}

// UnregisterConnection hủy đăng ký connection
func (h *hub) UnregisterConnection(connID string) error {
    if !h.running {
        return ErrHubNotStarted
    }
    
    select {
    case h.unregister <- connID:
        return nil
    case <-h.ctx.Done():
        return h.ctx.Err()
    default:
        return NewConnectionError("unregister queue full", nil)
    }
}

// GetConnection lấy connection theo ID
func (h *hub) GetConnection(connID string) (Connection, bool) {
    h.connMutex.RLock()
    defer h.connMutex.RUnlock()
    
    conn, exists := h.connections[connID]
    return conn, exists
}

// GetUserConnections lấy tất cả connections của user
func (h *hub) GetUserConnections(userID uint) []Connection {
    h.connMutex.RLock()
    defer h.connMutex.RUnlock()
    
    return h.getUserConnections(userID)
}

// getUserConnections internal method (not thread-safe)
func (h *hub) getUserConnections(userID uint) []Connection {
    connIDs, exists := h.userConns[userID]
    if !exists {
        return []Connection{}
    }
    
    connections := make([]Connection, 0, len(connIDs))
    for _, connID := range connIDs {
        if conn, exists := h.connections[connID]; exists {
            connections = append(connections, conn)
        }
    }
    
    return connections
}

// GetAllConnections lấy tất cả connections
func (h *hub) GetAllConnections() []Connection {
    h.connMutex.RLock()
    defer h.connMutex.RUnlock()
    
    connections := make([]Connection, 0, len(h.connections))
    for _, conn := range h.connections {
        connections = append(connections, conn)
    }
    
    return connections
}

// BroadcastToRoom broadcast message đến room
func (h *hub) BroadcastToRoom(roomID string, message *Message) error {
    req := &broadcastRequest{
        target: broadcastTarget{
            Type: "room",
            ID:   roomID,
        },
        message: message,
    }
    
    select {
    case h.broadcast <- req:
        return nil
    case <-h.ctx.Done():
        return h.ctx.Err()
    default:
        return NewSystemError("broadcast queue full", nil)
    }
}

// BroadcastToUser broadcast message đến user
func (h *hub) BroadcastToUser(userID uint, message *Message) error {
    req := &broadcastRequest{
        target: broadcastTarget{
            Type: "user",
            ID:   fmt.Sprintf("%d", userID),
        },
        message: message,
    }
    
    select {
    case h.broadcast <- req:
        return nil
    case <-h.ctx.Done():
        return h.ctx.Err()
    default:
        return NewSystemError("broadcast queue full", nil)
    }
}

// BroadcastToAll broadcast đến tất cả connections
func (h *hub) BroadcastToAll(message *Message) error {
    req := &broadcastRequest{
        target: broadcastTarget{
            Type: "all",
        },
        message: message,
    }
    
    select {
    case h.broadcast <- req:
        return nil
    case <-h.ctx.Done():
        return h.ctx.Err()
    default:
        return NewSystemError("broadcast queue full", nil)
    }
}

// GetConnectionCount trả về số lượng connections hiện tại
func (h *hub) GetConnectionCount() int {
    h.connMutex.RLock()
    defer h.connMutex.RUnlock()
    
    return len(h.connections)
}

// GetRoomCount trả về số lượng rooms hiện tại
func (h *hub) GetRoomCount() int {
    h.roomMutex.RLock()
    defer h.roomMutex.RUnlock()
    
    return len(h.rooms)
}

// SetEventRouter set event router
func (h *hub) SetEventRouter(router EventRouter) {
    h.eventRouter = router
}

// run main event loop
func (h *hub) run() {
    defer close(h.done)
    
    pingTicker := time.NewTicker(h.config.PingInterval)
    defer pingTicker.Stop()
    
    for {
        select {
        case req := <-h.register:
            h.handleRegister(req)
            
        case connID := <-h.unregister:
            h.handleUnregister(connID)
            
        case req := <-h.broadcast:
            h.handleBroadcast(req)
            
        case <-pingTicker.C:
            h.sendPingToAll()
            
        case <-h.ctx.Done():
            return
        }
    }
}

// handleRegister xử lý đăng ký connection mới
func (h *hub) handleRegister(req *connectionRequest) {
    conn, err := NewSocketConnection(req.conn, req.userID, req.tenantID, req.websiteID, h.logger)
    if err != nil {
        req.result <- connectionResult{nil, err}
        return
    }
    
    h.connMutex.Lock()
    h.connections[conn.ID()] = conn
    
    // Add to user connections
    if h.userConns[req.userID] == nil {
        h.userConns[req.userID] = make([]string, 0)
    }
    h.userConns[req.userID] = append(h.userConns[req.userID], conn.ID())
    h.connMutex.Unlock()
    
    // Update statistics
    h.stats.IncrementConnections()
    
    // Start connection handler
    go h.handleConnection(conn)
    
    req.result <- connectionResult{conn, nil}
    
    h.logger.Info("Connection registered",
        "conn_id", conn.ID(),
        "user_id", req.userID,
        "tenant_id", req.tenantID,
        "total_connections", h.GetConnectionCount(),
    )
}

// handleUnregister xử lý hủy đăng ký connection
func (h *hub) handleUnregister(connID string) {
    h.connMutex.Lock()
    conn, exists := h.connections[connID]
    if !exists {
        h.connMutex.Unlock()
        return
    }
    
    delete(h.connections, connID)
    
    // Remove from user connections
    userConns := h.userConns[conn.UserID()]
    for i, id := range userConns {
        if id == connID {
            h.userConns[conn.UserID()] = append(userConns[:i], userConns[i+1:]...)
            break
        }
    }
    
    // Clean up empty user entry
    if len(h.userConns[conn.UserID()]) == 0 {
        delete(h.userConns, conn.UserID())
    }
    h.connMutex.Unlock()
    
    // Remove from all rooms
    for _, roomID := range conn.GetRooms() {
        h.removeFromRoom(roomID, conn.UserID())
    }
    
    // Update statistics
    h.stats.DecrementConnections()
    
    h.logger.Info("Connection unregistered",
        "conn_id", connID,
        "user_id", conn.UserID(),
        "total_connections", h.GetConnectionCount(),
    )
}

// handleBroadcast xử lý broadcast messages
func (h *hub) handleBroadcast(req *broadcastRequest) {
    switch req.target.Type {
    case "all":
        h.broadcastToAll(req.message)
    case "user":
        if userID, err := parseUserID(req.target.ID); err == nil {
            h.broadcastToUser(userID, req.message)
        }
    case "room":
        h.broadcastToRoom(req.target.ID, req.message)
    }
}

// broadcastToAll internal broadcast to all
func (h *hub) broadcastToAll(message *Message) {
    h.connMutex.RLock()
    connections := make([]Connection, 0, len(h.connections))
    for _, conn := range h.connections {
        connections = append(connections, conn)
    }
    h.connMutex.RUnlock()
    
    for _, conn := range connections {
        go func(c Connection) {
            if err := c.Send(message); err != nil {
                h.logger.Warn("Failed to send message to connection",
                    "conn_id", c.ID(),
                    "error", err,
                )
            }
        }(conn)
    }
    
    h.stats.IncrementMessagesSent(len(connections))
}

// broadcastToUser internal broadcast to user
func (h *hub) broadcastToUser(userID uint, message *Message) {
    connections := h.getUserConnections(userID)
    
    for _, conn := range connections {
        go func(c Connection) {
            if err := c.Send(message); err != nil {
                h.logger.Warn("Failed to send message to user connection",
                    "conn_id", c.ID(),
                    "user_id", userID,
                    "error", err,
                )
            }
        }(conn)
    }
    
    h.stats.IncrementMessagesSent(len(connections))
}

// handleConnection xử lý một connection
func (h *hub) handleConnection(conn Connection) {
    defer func() {
        h.UnregisterConnection(conn.ID())
        conn.Close()
    }()
    
    for {
        select {
        case <-h.ctx.Done():
            return
        case <-conn.Context().Done():
            return
        default:
            // Connection sẽ handle incoming messages
            // và forward đến event router
            if !conn.IsAlive() {
                return
            }
            time.Sleep(100 * time.Millisecond)
        }
    }
}

// sendPingToAll gửi ping đến tất cả connections
func (h *hub) sendPingToAll() {
    pingMessage := NewMessage(EventTypePing, nil)
    
    h.connMutex.RLock()
    connections := make([]Connection, 0, len(h.connections))
    for _, conn := range h.connections {
        connections = append(connections, conn)
    }
    h.connMutex.RUnlock()
    
    for _, conn := range connections {
        go func(c Connection) {
            if err := c.Send(pingMessage); err != nil {
                h.logger.Debug("Failed to send ping",
                    "conn_id", c.ID(),
                    "error", err,
                )
            }
        }(conn)
    }
}

// cleanup dọn dẹp connections không hoạt động
func (h *hub) cleanup() {
    ticker := time.NewTicker(h.config.CleanupInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            h.cleanupInactiveConnections()
        case <-h.ctx.Done():
            return
        }
    }
}

// cleanupInactiveConnections dọn dẹp connections không active
func (h *hub) cleanupInactiveConnections() {
    h.connMutex.RLock()
    inactiveConns := make([]string, 0)
    
    for connID, conn := range h.connections {
        if !conn.IsAlive() {
            inactiveConns = append(inactiveConns, connID)
        }
    }
    h.connMutex.RUnlock()
    
    for _, connID := range inactiveConns {
        h.UnregisterConnection(connID)
    }
    
    if len(inactiveConns) > 0 {
        h.logger.Info("Cleaned up inactive connections",
            "count", len(inactiveConns),
        )
    }
}

// collectStatistics thu thập thống kê
func (h *hub) collectStatistics() {
    ticker := time.NewTicker(StatsInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            h.stats.Update(h.GetConnectionCount(), h.GetRoomCount())
        case <-h.ctx.Done():
            return
        }
    }
}

// Helper functions
func parseUserID(idStr string) (uint, error) {
    // Implementation to parse user ID from string
    // This would depend on your ID format
    return 0, nil
}
```

### Step 2: Implement Room Management (room.go)

```go
package socket

import (
    "fmt"
    "sync"
    "time"
)

// room implementation của Room interface
type room struct {
    id          string
    maxUsers    int
    users       map[uint]bool
    usersMutex  sync.RWMutex
    createdAt   time.Time
    lastActivity time.Time
    hub         *hub
    closed      bool
}

// NewRoom tạo room mới
func NewRoom(id string, maxUsers int, hub *hub) Room {
    return &room{
        id:           id,
        maxUsers:     maxUsers,
        users:        make(map[uint]bool),
        createdAt:    time.Now(),
        lastActivity: time.Now(),
        hub:          hub,
    }
}

// ID trả về room ID
func (r *room) ID() string {
    return r.id
}

// MaxUsers trả về số user tối đa
func (r *room) MaxUsers() int {
    return r.maxUsers
}

// CurrentUsers trả về số user hiện tại
func (r *room) CurrentUsers() int {
    r.usersMutex.RLock()
    defer r.usersMutex.RUnlock()
    
    return len(r.users)
}

// Users trả về danh sách user IDs
func (r *room) Users() []uint {
    r.usersMutex.RLock()
    defer r.usersMutex.RUnlock()
    
    users := make([]uint, 0, len(r.users))
    for userID := range r.users {
        users = append(users, userID)
    }
    
    return users
}

// AddUser thêm user vào room
func (r *room) AddUser(userID uint) error {
    r.usersMutex.Lock()
    defer r.usersMutex.Unlock()
    
    if r.closed {
        return NewRoomError(CodeRoomNotFound, "room is closed", nil)
    }
    
    if len(r.users) >= r.maxUsers {
        return NewRoomError(CodeRoomFull, "room is full", ErrRoomFull)
    }
    
    r.users[userID] = true
    r.lastActivity = time.Now()
    
    return nil
}

// RemoveUser loại bỏ user khỏi room
func (r *room) RemoveUser(userID uint) error {
    r.usersMutex.Lock()
    defer r.usersMutex.Unlock()
    
    if _, exists := r.users[userID]; !exists {
        return NewRoomError(CodeUserNotInRoom, "user not in room", ErrUserNotInRoom)
    }
    
    delete(r.users, userID)
    r.lastActivity = time.Now()
    
    return nil
}

// HasUser kiểm tra user có trong room không
func (r *room) HasUser(userID uint) bool {
    r.usersMutex.RLock()
    defer r.usersMutex.RUnlock()
    
    _, exists := r.users[userID]
    return exists
}

// Broadcast gửi message đến tất cả users trong room
func (r *room) Broadcast(message *Message) error {
    if r.closed {
        return NewRoomError(CodeRoomNotFound, "room is closed", nil)
    }
    
    users := r.Users()
    
    for _, userID := range users {
        connections := r.hub.getUserConnections(userID)
        for _, conn := range connections {
            go func(c Connection) {
                if err := c.Send(message); err != nil {
                    r.hub.logger.Warn("Failed to send room message",
                        "room_id", r.id,
                        "user_id", userID,
                        "conn_id", c.ID(),
                        "error", err,
                    )
                }
            }(conn)
        }
    }
    
    r.lastActivity = time.Now()
    return nil
}

// Close đóng room
func (r *room) Close() error {
    r.usersMutex.Lock()
    defer r.usersMutex.Unlock()
    
    r.closed = true
    r.users = make(map[uint]bool)
    
    return nil
}

// Room management methods for Hub
func (h *hub) CreateRoom(roomID string, maxUsers int) error {
    h.roomMutex.Lock()
    defer h.roomMutex.Unlock()
    
    if _, exists := h.rooms[roomID]; exists {
        return NewRoomError(CodeInvalidRoomID, "room already exists", nil)
    }
    
    room := NewRoom(roomID, maxUsers, h)
    h.rooms[roomID] = room
    
    h.logger.Info("Room created",
        "room_id", roomID,
        "max_users", maxUsers,
    )
    
    return nil
}

func (h *hub) DeleteRoom(roomID string) error {
    h.roomMutex.Lock()
    defer h.roomMutex.Unlock()
    
    room, exists := h.rooms[roomID]
    if !exists {
        return NewRoomError(CodeRoomNotFound, "room not found", ErrRoomNotFound)
    }
    
    room.Close()
    delete(h.rooms, roomID)
    
    h.logger.Info("Room deleted", "room_id", roomID)
    
    return nil
}

func (h *hub) GetRoom(roomID string) (Room, bool) {
    h.roomMutex.RLock()
    defer h.roomMutex.RUnlock()
    
    room, exists := h.rooms[roomID]
    return room, exists
}

func (h *hub) addToRoom(roomID string, userID uint) error {
    room, exists := h.GetRoom(roomID)
    if !exists {
        // Auto-create room if not exists
        if err := h.CreateRoom(roomID, MaxRoomSize); err != nil {
            return err
        }
        room, _ = h.GetRoom(roomID)
    }
    
    return room.AddUser(userID)
}

func (h *hub) removeFromRoom(roomID string, userID uint) error {
    room, exists := h.GetRoom(roomID)
    if !exists {
        return nil // Room doesn't exist, nothing to do
    }
    
    return room.RemoveUser(userID)
}

func (h *hub) broadcastToRoom(roomID string, message *Message) {
    room, exists := h.GetRoom(roomID)
    if !exists {
        h.logger.Warn("Attempted to broadcast to non-existent room",
            "room_id", roomID,
        )
        return
    }
    
    if err := room.Broadcast(message); err != nil {
        h.logger.Error("Failed to broadcast to room",
            "room_id", roomID,
            "error", err,
        )
    }
}
```

### Step 3: Implement Statistics (statistics.go)

```go
package socket

import (
    "sync"
    "time"
)

// HubStatistics thu thập thống kê về Hub
type HubStatistics struct {
    mutex sync.RWMutex
    
    // Connection stats
    ConnectionsCount    int
    PeakConnections     int
    TotalConnections    int64
    DisconnectedTotal   int64
    
    // Room stats
    RoomsCount          int
    PeakRooms           int
    TotalRooms          int64
    
    // Message stats
    MessagesSent        int64
    MessagesReceived    int64
    MessagesPerSecond   float64
    
    // Performance stats
    AverageLatency      time.Duration
    LastUpdateTime      time.Time
    
    // Error stats
    ErrorsTotal         int64
    ConnectionErrors    int64
    MessageErrors       int64
    RoomErrors          int64
}

// NewHubStatistics tạo statistics tracker mới
func NewHubStatistics() *HubStatistics {
    return &HubStatistics{
        LastUpdateTime: time.Now(),
    }
}

// IncrementConnections tăng connection count
func (s *HubStatistics) IncrementConnections() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.ConnectionsCount++
    s.TotalConnections++
    
    if s.ConnectionsCount > s.PeakConnections {
        s.PeakConnections = s.ConnectionsCount
    }
}

// DecrementConnections giảm connection count
func (s *HubStatistics) DecrementConnections() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    if s.ConnectionsCount > 0 {
        s.ConnectionsCount--
    }
    s.DisconnectedTotal++
}

// IncrementMessagesSent tăng messages sent
func (s *HubStatistics) IncrementMessagesSent(count int) {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.MessagesSent += int64(count)
}

// IncrementMessagesReceived tăng messages received
func (s *HubStatistics) IncrementMessagesReceived() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.MessagesReceived++
}

// IncrementErrors tăng error count
func (s *HubStatistics) IncrementErrors(errorType string) {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.ErrorsTotal++
    
    switch errorType {
    case "connection":
        s.ConnectionErrors++
    case "message":
        s.MessageErrors++
    case "room":
        s.RoomErrors++
    }
}

// Update cập nhật statistics
func (s *HubStatistics) Update(connectionCount, roomCount int) {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    now := time.Now()
    timeDiff := now.Sub(s.LastUpdateTime).Seconds()
    
    if timeDiff > 0 {
        s.MessagesPerSecond = float64(s.MessagesSent) / timeDiff
    }
    
    s.ConnectionsCount = connectionCount
    s.RoomsCount = roomCount
    s.LastUpdateTime = now
    
    if roomCount > s.PeakRooms {
        s.PeakRooms = roomCount
    }
}

// GetSnapshot trả về snapshot của statistics
func (s *HubStatistics) GetSnapshot() HubStatistics {
    s.mutex.RLock()
    defer s.mutex.RUnlock()
    
    return *s
}

// Reset reset tất cả statistics
func (s *HubStatistics) Reset() {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    *s = HubStatistics{
        LastUpdateTime: time.Now(),
    }
}
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **hub_test.go**:
   - Test connection registration/unregistration
   - Test room management
   - Test broadcasting functionality
   - Test concurrency safety
   - Test error handling

2. **room_test.go**:
   - Test room creation và management
   - Test user join/leave
   - Test room broadcasting
   - Test room limits

3. **statistics_test.go**:
   - Test statistics collection
   - Test concurrent updates
   - Test snapshot functionality

### Integration Test Example:

```go
package socket_test

import (
    "testing"
    "time"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
)

func TestHubLifecycle(t *testing.T) {
    logger := logger.NewTestLogger()
    hub := socket.NewHub(socket.DefaultHubConfig(), logger)
    
    // Test start
    err := hub.Start()
    if err != nil {
        t.Fatalf("Failed to start hub: %v", err)
    }
    
    // Test stop
    err = hub.Stop()
    if err != nil {
        t.Fatalf("Failed to stop hub: %v", err)
    }
}

func TestConnectionManagement(t *testing.T) {
    logger := logger.NewTestLogger()
    hub := socket.NewHub(socket.DefaultHubConfig(), logger)
    
    err := hub.Start()
    if err != nil {
        t.Fatalf("Failed to start hub: %v", err)
    }
    defer hub.Stop()
    
    // Test connection registration
    // (Need mock WebSocket connection)
    
    // Test connection retrieval
    // Test connection limits
    // Test user connections
}

func TestRoomManagement(t *testing.T) {
    logger := logger.NewTestLogger()
    hub := socket.NewHub(socket.DefaultHubConfig(), logger)
    
    err := hub.Start()
    if err != nil {
        t.Fatalf("Failed to start hub: %v", err)
    }
    defer hub.Stop()
    
    // Test room creation
    err = hub.CreateRoom("test-room", 10)
    if err != nil {
        t.Fatalf("Failed to create room: %v", err)
    }
    
    // Test room retrieval
    room, exists := hub.GetRoom("test-room")
    if !exists {
        t.Fatal("Room should exist")
    }
    
    if room.ID() != "test-room" {
        t.Errorf("Expected room ID 'test-room', got '%s'", room.ID())
    }
    
    // Test room deletion
    err = hub.DeleteRoom("test-room")
    if err != nil {
        t.Fatalf("Failed to delete room: %v", err)
    }
}
```

## ✅ Completion Criteria

- [ ] Hub start/stop lifecycle works correctly
- [ ] Connection registration/unregistration thread-safe
- [ ] Room management với full functionality
- [ ] Broadcasting hoạt động đúng cho all/user/room targets
- [ ] Statistics collection accurate
- [ ] Cleanup mechanisms hoạt động
- [ ] Unit tests pass với coverage >= 85%
- [ ] Concurrent load test with 1000+ connections
- [ ] Memory leaks không xảy ra
- [ ] Integration tests pass

## 🔗 Dependencies

- **Task 01**: Socket Package Infrastructure (Complete)
- **Task 03**: Connection Management (Parallel)

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 03**: Connection Management (Parallel development)
2. **Task 04**: Event Router System  
3. **Task 05**: Socket Module Business Logic

## 📝 Notes

- Hub phải handle graceful shutdown khi có nhiều connections
- Room management cần support auto-cleanup empty rooms
- Statistics collection không được impact performance
- Broadcasting phải asynchronous để avoid blocking
- Error handling phải comprehensive và informative
- Memory usage cần được monitor và optimize
