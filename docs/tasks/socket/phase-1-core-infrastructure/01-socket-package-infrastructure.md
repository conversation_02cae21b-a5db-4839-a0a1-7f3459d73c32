# Task 01: Socket Package Infrastructure

## 📋 Tổng Quan

Tạo foundation cho WebSocket system bằng cách xây dựng package `internal/pkg/socket` với các interfaces, types và constants cơ bản. <PERSON><PERSON><PERSON> là nền tảng cho tất cả components kh<PERSON><PERSON> trong hệ thống WebSocket.

## 🎯 Mục Tiêu

- <PERSON><PERSON><PERSON> interfaces chuẩn cho WebSocket system
- Tạo common types và message structures
- Thiết lập error handling và constants
- Chuẩn bị foundation cho Hub, Connection và EventRouter

## 📁 Files Cần Tạo

```
internal/pkg/socket/
├── types.go          # Core interfaces và types
├── message.go        # Message structures
├── errors.go         # Error types và constants  
└── constants.go      # System constants
```

## 🔧 Implementation Steps

### Step 1: Tạo Core Interfaces (types.go)

```go
package socket

import (
    "context"
    "time"
    "github.com/gorilla/websocket"
)

// Connection đại diện cho một WebSocket connection
type Connection interface {
    // Connection info
    ID() string
    UserID() uint
    TenantID() uint
    WebsiteID() uint
    
    // Messaging
    Send(message *Message) error
    SendEvent(eventType string, data map[string]interface{}) error
    SendError(code, message string) error
    
    // Connection management
    Close() error
    IsAlive() bool
    
    // Room management
    JoinRoom(roomID string) error
    LeaveRoom(roomID string) error
    GetRooms() []string
    
    // Context
    Context() context.Context
    RemoteAddr() string
    LastActivity() time.Time
}

// Hub quản lý tất cả WebSocket connections
type Hub interface {
    // Lifecycle
    Start() error
    Stop() error
    
    // Connection management
    RegisterConnection(conn *websocket.Conn, userID, tenantID, websiteID uint) (Connection, error)
    UnregisterConnection(connID string) error
    GetConnection(connID string) (Connection, bool)
    GetUserConnections(userID uint) []Connection
    GetAllConnections() []Connection
    
    // Room management
    CreateRoom(roomID string, maxUsers int) error
    DeleteRoom(roomID string) error
    GetRoom(roomID string) (Room, bool)
    
    // Broadcasting
    BroadcastToRoom(roomID string, message *Message) error
    BroadcastToUser(userID uint, message *Message) error
    BroadcastToAll(message *Message) error
    
    // Statistics
    GetConnectionCount() int
    GetRoomCount() int
}

// EventHandler xử lý các WebSocket events
type EventHandler func(ctx context.Context, conn Connection, data map[string]interface{}) error

// EventRouter quản lý event routing và handling
type EventRouter interface {
    // Handler registration
    RegisterHandler(eventType string, handler EventHandler)
    UnregisterHandler(eventType string)
    GetHandler(eventType string) (EventHandler, bool)
    
    // Event routing
    RouteEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}) error
    
    // Publishing
    PublishEvent(eventType string, data map[string]interface{}) error
    PublishToUser(userID uint, eventType string, data map[string]interface{}) error
    PublishToRoom(roomID string, eventType string, data map[string]interface{}) error
}

// EventPublisher interface cho modules publish events
type EventPublisher interface {
    Publish(eventType string, data map[string]interface{}) error
    PublishToUser(userID uint, eventType string, data map[string]interface{}) error
    PublishToRoom(roomID string, eventType string, data map[string]interface{}) error
}

// Room đại diện cho một chat room hoặc topic room
type Room interface {
    ID() string
    MaxUsers() int
    CurrentUsers() int
    Users() []uint
    AddUser(userID uint) error
    RemoveUser(userID uint) error
    HasUser(userID uint) bool
    Broadcast(message *Message) error
    Close() error
}

// Middleware interface cho WebSocket middleware
type Middleware interface {
    HandleConnection(next ConnectionHandler) ConnectionHandler
    HandleMessage(next MessageHandler) MessageHandler
}

// ConnectionHandler xử lý new connections
type ConnectionHandler func(conn *websocket.Conn, userID, tenantID, websiteID uint) error

// MessageHandler xử lý incoming messages
type MessageHandler func(ctx context.Context, conn Connection, message *Message) error
```

### Step 2: Tạo Message Structures (message.go)

```go
package socket

import (
    "encoding/json"
    "time"
)

// Message là cấu trúc chuẩn cho tất cả WebSocket messages
type Message struct {
    Type      string                 `json:"type"`              // Event type
    Data      map[string]interface{} `json:"data,omitempty"`    // Event data
    Error     *ErrorDetail          `json:"error,omitempty"`    // Error details
    Timestamp int64                 `json:"timestamp"`          // Unix timestamp
    RequestID string                `json:"request_id,omitempty"` // For request tracking
    UserID    uint                  `json:"user_id,omitempty"`  // Target user ID
    RoomID    string                `json:"room_id,omitempty"`  // Target room ID
    Priority  MessagePriority       `json:"priority"`           // Message priority
}

// ErrorDetail chứa thông tin lỗi chi tiết
type ErrorDetail struct {
    Code    string                 `json:"code"`               // Error code
    Message string                 `json:"message"`            // Error message
    Details map[string]interface{} `json:"details,omitempty"`  // Additional details
}

// MessagePriority định nghĩa mức độ ưu tiên của message
type MessagePriority int

const (
    PriorityLow    MessagePriority = 1
    PriorityNormal MessagePriority = 2
    PriorityHigh   MessagePriority = 3
    PriorityCritical MessagePriority = 4
)

// IncomingMessage là message từ client gửi lên
type IncomingMessage struct {
    Type      string                 `json:"type"`
    Data      map[string]interface{} `json:"data,omitempty"`
    RequestID string                 `json:"request_id,omitempty"`
}

// OutgoingMessage là message từ server gửi xuống client
type OutgoingMessage struct {
    Type      string                 `json:"type"`
    Data      map[string]interface{} `json:"data,omitempty"`
    Error     *ErrorDetail          `json:"error,omitempty"`
    Timestamp int64                 `json:"timestamp"`
    RequestID string                 `json:"request_id,omitempty"`
}

// NewMessage tạo message mới với timestamp
func NewMessage(msgType string, data map[string]interface{}) *Message {
    return &Message{
        Type:      msgType,
        Data:      data,
        Timestamp: time.Now().Unix(),
        Priority:  PriorityNormal,
    }
}

// NewErrorMessage tạo error message
func NewErrorMessage(code, message string, details map[string]interface{}) *Message {
    return &Message{
        Type:      "error",
        Error:     &ErrorDetail{
            Code:    code,
            Message: message,
            Details: details,
        },
        Timestamp: time.Now().Unix(),
        Priority:  PriorityHigh,
    }
}

// NewSuccessMessage tạo success response message
func NewSuccessMessage(requestID string, data map[string]interface{}) *Message {
    return &Message{
        Type:      "success",
        Data:      data,
        Timestamp: time.Now().Unix(),
        RequestID: requestID,
        Priority:  PriorityNormal,
    }
}

// ToJSON chuyển message thành JSON bytes
func (m *Message) ToJSON() ([]byte, error) {
    return json.Marshal(m)
}

// FromJSON parse JSON bytes thành message
func (m *Message) FromJSON(data []byte) error {
    return json.Unmarshal(data, m)
}

// WithPriority set priority cho message
func (m *Message) WithPriority(priority MessagePriority) *Message {
    m.Priority = priority
    return m
}

// WithRequestID set request ID cho message
func (m *Message) WithRequestID(requestID string) *Message {
    m.RequestID = requestID
    return m
}

// WithUserID set target user ID
func (m *Message) WithUserID(userID uint) *Message {
    m.UserID = userID
    return m
}

// WithRoomID set target room ID
func (m *Message) WithRoomID(roomID string) *Message {
    m.RoomID = roomID
    return m
}
```

### Step 3: Tạo Error Types (errors.go)

```go
package socket

import (
    "errors"
    "fmt"
)

// Predefined error types
var (
    // Connection errors
    ErrConnectionNotFound     = errors.New("connection not found")
    ErrConnectionClosed       = errors.New("connection closed")
    ErrConnectionLimit        = errors.New("connection limit reached")
    ErrInvalidConnection      = errors.New("invalid connection")
    
    // Authentication errors
    ErrAuthenticationFailed   = errors.New("authentication failed")
    ErrAuthorizationFailed    = errors.New("authorization failed")
    ErrInvalidToken          = errors.New("invalid token")
    ErrTokenExpired          = errors.New("token expired")
    
    // Message errors
    ErrInvalidMessage        = errors.New("invalid message format")
    ErrMessageTooLarge       = errors.New("message too large")
    ErrInvalidEventType      = errors.New("invalid event type")
    ErrMissingData           = errors.New("missing required data")
    
    // Room errors
    ErrRoomNotFound          = errors.New("room not found")
    ErrRoomFull              = errors.New("room is full")
    ErrUserNotInRoom         = errors.New("user not in room")
    ErrInvalidRoomID         = errors.New("invalid room ID")
    
    // Handler errors
    ErrHandlerNotFound       = errors.New("event handler not found")
    ErrHandlerAlreadyExists  = errors.New("event handler already exists")
    ErrHandlerRegistration   = errors.New("failed to register handler")
    
    // System errors
    ErrHubNotStarted         = errors.New("hub not started")
    ErrHubAlreadyStarted     = errors.New("hub already started")
    ErrSystemOverload        = errors.New("system overload")
)

// Error codes cho client
const (
    // Connection error codes
    CodeConnectionFailed     = "CONNECTION_FAILED"
    CodeConnectionLimit      = "CONNECTION_LIMIT"
    CodeConnectionClosed     = "CONNECTION_CLOSED"
    
    // Authentication error codes
    CodeAuthFailed           = "AUTH_FAILED"
    CodeAuthRequired         = "AUTH_REQUIRED"
    CodeInvalidToken         = "INVALID_TOKEN"
    CodeTokenExpired         = "TOKEN_EXPIRED"
    
    // Message error codes
    CodeInvalidMessage       = "INVALID_MESSAGE"
    CodeMessageTooLarge      = "MESSAGE_TOO_LARGE"
    CodeInvalidEventType     = "INVALID_EVENT_TYPE"
    CodeMissingData          = "MISSING_DATA"
    
    // Room error codes
    CodeRoomNotFound         = "ROOM_NOT_FOUND"
    CodeRoomFull             = "ROOM_FULL"
    CodeUserNotInRoom        = "USER_NOT_IN_ROOM"
    CodeInvalidRoomID        = "INVALID_ROOM_ID"
    
    // Handler error codes
    CodeHandlerNotFound      = "HANDLER_NOT_FOUND"
    CodeHandlerError         = "HANDLER_ERROR"
    
    // System error codes
    CodeSystemError          = "SYSTEM_ERROR"
    CodeSystemOverload       = "SYSTEM_OVERLOAD"
    CodeInternalError        = "INTERNAL_ERROR"
)

// SocketError là custom error type với code và context
type SocketError struct {
    Code    string
    Message string
    Err     error
    Context map[string]interface{}
}

func (e *SocketError) Error() string {
    if e.Err != nil {
        return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Err)
    }
    return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

func (e *SocketError) Unwrap() error {
    return e.Err
}

// NewSocketError tạo SocketError mới
func NewSocketError(code, message string, err error) *SocketError {
    return &SocketError{
        Code:    code,
        Message: message,
        Err:     err,
        Context: make(map[string]interface{}),
    }
}

// WithContext thêm context vào error
func (e *SocketError) WithContext(key string, value interface{}) *SocketError {
    e.Context[key] = value
    return e
}

// ToErrorDetail chuyển thành ErrorDetail cho response
func (e *SocketError) ToErrorDetail() *ErrorDetail {
    return &ErrorDetail{
        Code:    e.Code,
        Message: e.Message,
        Details: e.Context,
    }
}

// Helper functions tạo common errors
func NewConnectionError(message string, err error) *SocketError {
    return NewSocketError(CodeConnectionFailed, message, err)
}

func NewAuthError(message string, err error) *SocketError {
    return NewSocketError(CodeAuthFailed, message, err)
}

func NewMessageError(message string, err error) *SocketError {
    return NewSocketError(CodeInvalidMessage, message, err)
}

func NewRoomError(code, message string, err error) *SocketError {
    return NewSocketError(code, message, err)
}

func NewSystemError(message string, err error) *SocketError {
    return NewSocketError(CodeSystemError, message, err)
}
```

### Step 4: Tạo Constants (constants.go)

```go
package socket

import "time"

// WebSocket configuration constants
const (
    // Connection limits
    MaxConnections        = 10000              // Maximum concurrent connections
    MaxConnectionsPerUser = 10                 // Maximum connections per user
    MaxRoomsPerUser      = 100                 // Maximum rooms per user
    
    // Message limits
    MaxMessageSize       = 1024 * 1024         // 1MB maximum message size
    MaxQueueSize         = 10000               // Maximum queued messages per connection
    MaxRoomSize          = 1000                // Maximum users per room
    
    // Timeouts
    WriteTimeout         = 10 * time.Second    // Write timeout
    ReadTimeout          = 60 * time.Second    // Read timeout
    PingTimeout          = 30 * time.Second    // Ping timeout
    PongTimeout          = 60 * time.Second    // Pong timeout
    
    // Heartbeat
    PingInterval         = 30 * time.Second    // Ping interval
    HeartbeatInterval    = 10 * time.Second    // Connection health check
    
    // Buffer sizes
    ReadBufferSize       = 1024                // WebSocket read buffer
    WriteBufferSize      = 1024                // WebSocket write buffer
    MessageBufferSize    = 256                 // Channel buffer size
    
    // Cleanup intervals
    CleanupInterval      = 5 * time.Minute     // Cleanup inactive connections
    StatsInterval        = 1 * time.Minute     // Statistics collection interval
)

// Event types
const (
    // System events
    EventTypeConnect     = "system.connect"
    EventTypeDisconnect  = "system.disconnect"
    EventTypePing        = "system.ping"
    EventTypePong        = "system.pong"
    EventTypeError       = "system.error"
    EventTypeSuccess     = "system.success"
    
    // Room events
    EventTypeJoinRoom    = "room.join"
    EventTypeLeaveRoom   = "room.leave"
    EventTypeRoomMessage = "room.message"
    
    // User events
    EventTypeUserOnline  = "user.online"
    EventTypeUserOffline = "user.offline"
    EventTypeUserMessage = "user.message"
)

// Room types
const (
    RoomTypeUser         = "user"              // Personal user room
    RoomTypeTenant       = "tenant"            // Tenant-wide room
    RoomTypeWebsite      = "website"           // Website-specific room
    RoomTypeTopic        = "topic"             // Topic-based room
    RoomTypeEvent        = "event"             // Event-specific room
    RoomTypeSystem       = "system"            // System-wide room
)

// Connection states
const (
    StateConnecting      = "connecting"
    StateConnected       = "connected"
    StateDisconnecting   = "disconnecting"
    StateDisconnected    = "disconnected"
    StateError           = "error"
)

// Priority levels
const (
    PrioritySystemCritical = 10               // System critical messages
    PriorityUserCritical   = 8                // User critical messages
    PriorityHighImportance = 6                // High importance messages
    PriorityNormalMessage  = 4                // Normal messages
    PriorityLowImportance  = 2                // Low importance messages
    PriorityBackgroundTask = 1                // Background tasks
)

// Rate limiting
const (
    MaxMessagesPerSecond = 10                 // Messages per second per connection
    MaxRoomsPerMinute    = 5                  // Room joins per minute per user
    MaxEventPerMinute    = 100                // Events per minute per connection
)

// HTTP headers
const (
    HeaderAuthorization  = "Authorization"
    HeaderWebSocketKey   = "Sec-WebSocket-Key"
    HeaderUserAgent      = "User-Agent"
    HeaderOrigin         = "Origin"
    HeaderForwardedFor   = "X-Forwarded-For"
    HeaderRealIP         = "X-Real-IP"
)

// Query parameters
const (
    QueryParamToken      = "token"
    QueryParamUserID     = "user_id"
    QueryParamTenantID   = "tenant_id"
    QueryParamWebsiteID  = "website_id"
    QueryParamClientType = "client_type"
)

// Metrics keys
const (
    MetricConnections    = "socket.connections"
    MetricMessages       = "socket.messages"
    MetricRooms          = "socket.rooms"
    MetricErrors         = "socket.errors"
    MetricLatency        = "socket.latency"
    MetricThroughput     = "socket.throughput"
)

// Log levels and categories
const (
    LogCategoryConnection = "connection"
    LogCategoryMessage    = "message"
    LogCategoryRoom       = "room"
    LogCategoryEvent      = "event"
    LogCategorySystem     = "system"
    LogCategoryAuth       = "auth"
)
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **types_test.go**:
   - Test interface compliance
   - Test type conversions
   - Test method signatures

2. **message_test.go**:
   - Test message creation và serialization
   - Test JSON marshal/unmarshal
   - Test message builder methods
   - Test error message creation

3. **errors_test.go**:
   - Test custom error types
   - Test error wrapping và unwrapping
   - Test error code mapping
   - Test error context

### Test Example:

```go
package socket_test

import (
    "testing"
    "encoding/json"
    "wnapi/internal/pkg/socket"
)

func TestNewMessage(t *testing.T) {
    data := map[string]interface{}{
        "test": "data",
    }
    
    msg := socket.NewMessage("test.event", data)
    
    if msg.Type != "test.event" {
        t.Errorf("Expected type 'test.event', got '%s'", msg.Type)
    }
    
    if msg.Data["test"] != "data" {
        t.Errorf("Expected data 'data', got '%v'", msg.Data["test"])
    }
    
    if msg.Timestamp == 0 {
        t.Error("Expected timestamp to be set")
    }
}

func TestMessageJSON(t *testing.T) {
    msg := socket.NewMessage("test.event", map[string]interface{}{
        "key": "value",
    })
    
    jsonData, err := msg.ToJSON()
    if err != nil {
        t.Fatalf("Failed to marshal message: %v", err)
    }
    
    var parsedMsg socket.Message
    err = parsedMsg.FromJSON(jsonData)
    if err != nil {
        t.Fatalf("Failed to unmarshal message: %v", err)
    }
    
    if parsedMsg.Type != msg.Type {
        t.Errorf("Type mismatch after JSON roundtrip")
    }
}
```

## ✅ Completion Criteria

- [ ] Tất cả interfaces được định nghĩa đầy đủ
- [ ] Message structures có đủ functionality cần thiết
- [ ] Error types cover tất cả cases có thể xảy ra
- [ ] Constants được define hợp lý
- [ ] Unit tests pass với coverage >= 80%
- [ ] Code review approved
- [ ] Documentation đầy đủ

## 🔗 Dependencies

**None** - Đây là foundation task

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 02**: WebSocket Hub Implementation
2. **Task 03**: Connection Management  
3. **Task 04**: Event Router System

## 📝 Notes

- Tất cả interfaces cần được design để hỗ trợ future extensibility
- Error handling phải consistent và informative
- Message format phải compatible với frontend clients
- Constants phải được configure từ environment variables trong future
- Code phải follow Go best practices và project conventions
