# Task 06: Room Management System

## 📋 Tổng Quan

Triển khai hệ thống quản lý Rooms cho WebSocket system với database persistence, advanced room features, và efficient room operations. System này cung cấp foundation cho group communications, topic-based messaging, và event-driven interactions.

## 🎯 Mục Tiêu

- Implement database models cho rooms và memberships
- Room lifecycle management (create, update, delete, archive)
- Advanced room features (permissions, capacity, moderation)
- Room membership management với roles và permissions
- Room message history và analytics
- Integration với notification system

## 📁 Files Cần Tạo

```
modules/integration/socket/
├── models/
│   ├── room.go                    # Room database model
│   ├── room_member.go             # Room membership model
│   ├── room_message.go            # Room message history model
│   └── room_permission.go         # Room permissions model
├── service/
│   ├── room_service.go            # Room business logic
│   ├── room_member_service.go     # Membership management
│   └── room_message_service.go    # Message history service
├── repository/
│   ├── room_repository.go         # Room data access
│   ├── room_member_repository.go  # Membership data access
│   └── room_message_repository.go # Message history data access
├── dto/
│   ├── room_dto.go                # Room DTOs
│   ├── room_member_dto.go         # Membership DTOs
│   └── room_message_dto.go        # Message DTOs
└── migrations/
    ├── 001_create_rooms_table.sql
    ├── 002_create_room_members_table.sql
    ├── 003_create_room_messages_table.sql
    └── 004_create_room_permissions_table.sql
```

## 🔧 Implementation Steps

### Step 1: Database Models (models/room.go)

```go
package models

import (
    "time"
    "gorm.io/gorm"
)

// Room đại diện cho một chat room hoặc topic room
type Room struct {
    ID          string    `gorm:"primaryKey;size:100" json:"id"`
    Name        string    `gorm:"size:255;not null" json:"name"`
    Description string    `gorm:"type:text" json:"description,omitempty"`
    Type        string    `gorm:"size:50;not null;index" json:"type"` // user, tenant, website, topic, event
    
    // Ownership và access
    OwnerID     uint      `gorm:"not null;index" json:"owner_id"`
    TenantID    uint      `gorm:"not null;index" json:"tenant_id"`
    WebsiteID   uint      `gorm:"not null;index" json:"website_id"`
    
    // Room configuration
    IsPublic    bool      `gorm:"default:false" json:"is_public"`
    IsActive    bool      `gorm:"default:true;index" json:"is_active"`
    MaxMembers  int       `gorm:"default:100" json:"max_members"`
    
    // Features
    AllowMessages   bool  `gorm:"default:true" json:"allow_messages"`
    AllowFiles      bool  `gorm:"default:true" json:"allow_files"`
    RequireApproval bool  `gorm:"default:false" json:"require_approval"`
    
    // Moderation
    IsModerated     bool  `gorm:"default:false" json:"is_moderated"`
    AutoDeleteDays  int   `gorm:"default:0" json:"auto_delete_days"` // 0 = never
    
    // Statistics
    MemberCount     int   `gorm:"default:0" json:"member_count"`
    MessageCount    int64 `gorm:"default:0" json:"message_count"`
    LastActivity    time.Time `json:"last_activity,omitempty"`
    
    // Metadata
    Metadata        JSON  `gorm:"type:json" json:"metadata,omitempty"`
    Settings        JSON  `gorm:"type:json" json:"settings,omitempty"`
    
    // Timestamps
    CreatedAt       time.Time      `json:"created_at"`
    UpdatedAt       time.Time      `json:"updated_at"`
    DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relationships
    Members    []RoomMember   `gorm:"foreignKey:RoomID;constraint:OnDelete:CASCADE" json:"members,omitempty"`
    Messages   []RoomMessage  `gorm:"foreignKey:RoomID;constraint:OnDelete:CASCADE" json:"messages,omitempty"`
    Permissions []RoomPermission `gorm:"foreignKey:RoomID;constraint:OnDelete:CASCADE" json:"permissions,omitempty"`
}

// TableName trả về tên table
func (Room) TableName() string {
    return "socket_rooms"
}

// BeforeCreate hook trước khi tạo room
func (r *Room) BeforeCreate(tx *gorm.DB) error {
    if r.ID == "" {
        r.ID = generateRoomID(r.Type)
    }
    r.LastActivity = time.Now()
    return nil
}

// BeforeUpdate hook trước khi update room
func (r *Room) BeforeUpdate(tx *gorm.DB) error {
    r.LastActivity = time.Now()
    return nil
}

// IsOwner kiểm tra user có phải owner không
func (r *Room) IsOwner(userID uint) bool {
    return r.OwnerID == userID
}

// CanJoin kiểm tra user có thể join không
func (r *Room) CanJoin(userID uint) bool {
    if !r.IsActive {
        return false
    }
    
    if r.MemberCount >= r.MaxMembers {
        return false
    }
    
    if r.IsOwner(userID) {
        return true
    }
    
    if r.IsPublic {
        return true
    }
    
    // Check permissions or invitations
    return false
}

// GetRoomPrefix trả về prefix cho room ID
func GetRoomPrefix(roomType string) string {
    prefixes := map[string]string{
        "user":    "usr",
        "tenant":  "tnt",
        "website": "web",
        "topic":   "top",
        "event":   "evt",
        "system":  "sys",
    }
    
    if prefix, exists := prefixes[roomType]; exists {
        return prefix
    }
    
    return "room"
}

// generateRoomID tạo unique room ID
func generateRoomID(roomType string) string {
    prefix := GetRoomPrefix(roomType)
    timestamp := time.Now().Unix()
    return fmt.Sprintf("%s_%d_%d", prefix, timestamp, rand.Intn(9999))
}
```

### Step 2: Room Member Model (models/room_member.go)

```go
package models

import (
    "time"
    "gorm.io/gorm"
)

// RoomMember đại diện cho membership của user trong room
type RoomMember struct {
    ID       uint   `gorm:"primaryKey" json:"id"`
    RoomID   string `gorm:"size:100;not null;index:idx_room_user,unique" json:"room_id"`
    UserID   uint   `gorm:"not null;index:idx_room_user,unique;index" json:"user_id"`
    
    // Membership info
    Role        string    `gorm:"size:50;default:'member'" json:"role"` // owner, admin, moderator, member, guest
    Status      string    `gorm:"size:50;default:'active'" json:"status"` // active, inactive, banned, pending
    
    // Permissions
    CanMessage  bool      `gorm:"default:true" json:"can_message"`
    CanInvite   bool      `gorm:"default:false" json:"can_invite"`
    CanModerate bool      `gorm:"default:false" json:"can_moderate"`
    CanKick     bool      `gorm:"default:false" json:"can_kick"`
    
    // Activity tracking
    LastSeen    time.Time `json:"last_seen,omitempty"`
    MessageCount int64    `gorm:"default:0" json:"message_count"`
    
    // Notification preferences
    NotifyOnMention  bool `gorm:"default:true" json:"notify_on_mention"`
    NotifyOnMessage  bool `gorm:"default:true" json:"notify_on_message"`
    NotifyOnJoinLeave bool `gorm:"default:false" json:"notify_on_join_leave"`
    
    // Join info
    InvitedBy   *uint     `gorm:"index" json:"invited_by,omitempty"`
    JoinedAt    time.Time `json:"joined_at"`
    
    // Timestamps
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relationships
    Room    Room `gorm:"foreignKey:RoomID;constraint:OnDelete:CASCADE" json:"room,omitempty"`
}

// TableName trả về tên table
func (RoomMember) TableName() string {
    return "socket_room_members"
}

// BeforeCreate hook trước khi tạo membership
func (rm *RoomMember) BeforeCreate(tx *gorm.DB) error {
    rm.JoinedAt = time.Now()
    rm.LastSeen = time.Now()
    return nil
}

// IsOwner kiểm tra có phải owner không
func (rm *RoomMember) IsOwner() bool {
    return rm.Role == "owner"
}

// IsAdmin kiểm tra có phải admin không
func (rm *RoomMember) IsAdmin() bool {
    return rm.Role == "admin" || rm.Role == "owner"
}

// IsModerator kiểm tra có thể moderate không
func (rm *RoomMember) IsModerator() bool {
    return rm.CanModerate || rm.IsAdmin()
}

// CanPerformAction kiểm tra có thể thực hiện action không
func (rm *RoomMember) CanPerformAction(action string) bool {
    if rm.Status != "active" {
        return false
    }
    
    switch action {
    case "message":
        return rm.CanMessage
    case "invite":
        return rm.CanInvite || rm.IsAdmin()
    case "moderate":
        return rm.CanModerate || rm.IsAdmin()
    case "kick":
        return rm.CanKick || rm.IsAdmin()
    default:
        return false
    }
}

// UpdateActivity cập nhật last seen
func (rm *RoomMember) UpdateActivity() {
    rm.LastSeen = time.Now()
}
```

### Step 3: Room Message Model (models/room_message.go)

```go
package models

import (
    "time"
    "gorm.io/gorm"
)

// RoomMessage đại diện cho message trong room
type RoomMessage struct {
    ID       uint   `gorm:"primaryKey" json:"id"`
    RoomID   string `gorm:"size:100;not null;index" json:"room_id"`
    UserID   uint   `gorm:"not null;index" json:"user_id"`
    
    // Message content
    Type        string `gorm:"size:50;default:'text'" json:"type"` // text, file, image, system
    Content     string `gorm:"type:text" json:"content"`
    
    // Metadata
    EventType   string `gorm:"size:100" json:"event_type,omitempty"`
    EventData   JSON   `gorm:"type:json" json:"event_data,omitempty"`
    
    // Threading
    ParentID    *uint  `gorm:"index" json:"parent_id,omitempty"`
    ThreadCount int    `gorm:"default:0" json:"thread_count"`
    
    // Reactions và interactions
    ReactionCount map[string]int `gorm:"type:json" json:"reaction_count,omitempty"`
    ReplyCount    int           `gorm:"default:0" json:"reply_count"`
    
    // Moderation
    IsEdited    bool      `gorm:"default:false" json:"is_edited"`
    IsDeleted   bool      `gorm:"default:false;index" json:"is_deleted"`
    EditedAt    time.Time `json:"edited_at,omitempty"`
    DeletedBy   *uint     `json:"deleted_by,omitempty"`
    
    // Delivery tracking
    DeliveredTo map[uint]time.Time `gorm:"type:json" json:"delivered_to,omitempty"`
    ReadBy      map[uint]time.Time `gorm:"type:json" json:"read_by,omitempty"`
    
    // Timestamps
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
    
    // Relationships
    Room    Room         `gorm:"foreignKey:RoomID;constraint:OnDelete:CASCADE" json:"room,omitempty"`
    Parent  *RoomMessage `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
    Replies []RoomMessage `gorm:"foreignKey:ParentID" json:"replies,omitempty"`
}

// TableName trả về tên table
func (RoomMessage) TableName() string {
    return "socket_room_messages"
}

// BeforeCreate hook trước khi tạo message
func (rm *RoomMessage) BeforeCreate(tx *gorm.DB) error {
    if rm.DeliveredTo == nil {
        rm.DeliveredTo = make(map[uint]time.Time)
    }
    if rm.ReadBy == nil {
        rm.ReadBy = make(map[uint]time.Time)
    }
    if rm.ReactionCount == nil {
        rm.ReactionCount = make(map[string]int)
    }
    return nil
}

// MarkAsRead đánh dấu message đã được read bởi user
func (rm *RoomMessage) MarkAsRead(userID uint) {
    if rm.ReadBy == nil {
        rm.ReadBy = make(map[uint]time.Time)
    }
    rm.ReadBy[userID] = time.Now()
}

// MarkAsDelivered đánh dấu message đã được delivered đến user
func (rm *RoomMessage) MarkAsDelivered(userID uint) {
    if rm.DeliveredTo == nil {
        rm.DeliveredTo = make(map[uint]time.Time)
    }
    rm.DeliveredTo[userID] = time.Now()
}

// IsReadBy kiểm tra message đã được read bởi user chưa
func (rm *RoomMessage) IsReadBy(userID uint) bool {
    if rm.ReadBy == nil {
        return false
    }
    _, exists := rm.ReadBy[userID]
    return exists
}

// AddReaction thêm reaction vào message
func (rm *RoomMessage) AddReaction(emoji string) {
    if rm.ReactionCount == nil {
        rm.ReactionCount = make(map[string]int)
    }
    rm.ReactionCount[emoji]++
}

// RemoveReaction loại bỏ reaction khỏi message
func (rm *RoomMessage) RemoveReaction(emoji string) {
    if rm.ReactionCount == nil {
        return
    }
    
    if count, exists := rm.ReactionCount[emoji]; exists && count > 0 {
        rm.ReactionCount[emoji]--
        if rm.ReactionCount[emoji] == 0 {
            delete(rm.ReactionCount, emoji)
        }
    }
}

// Edit cập nhật nội dung message
func (rm *RoomMessage) Edit(newContent string) {
    rm.Content = newContent
    rm.IsEdited = true
    rm.EditedAt = time.Now()
}

// SoftDelete xóa message (soft delete)
func (rm *RoomMessage) SoftDelete(deletedBy uint) {
    rm.IsDeleted = true
    rm.DeletedBy = &deletedBy
    rm.Content = "[Message deleted]"
}
```

### Step 4: Room Service Implementation (service/room_service.go)

```go
package service

import (
    "context"
    "fmt"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
    "wnapi/modules/integration/socket/repository"
    "wnapi/modules/integration/socket/dto"
    "wnapi/modules/integration/socket/models"
)

// RoomService interface cho room business logic
type RoomService interface {
    // Room management
    CreateRoom(ctx context.Context, req dto.CreateRoomRequest) (*dto.RoomResponse, error)
    GetRoom(ctx context.Context, roomID string) (*dto.RoomResponse, error)
    GetRooms(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]dto.RoomResponse, int64, error)
    UpdateRoom(ctx context.Context, roomID string, req dto.UpdateRoomRequest) (*dto.RoomResponse, error)
    DeleteRoom(ctx context.Context, roomID string) error
    
    // Room membership
    JoinRoom(ctx context.Context, roomID string, userID uint) error
    LeaveRoom(ctx context.Context, roomID string, userID uint) error
    KickUser(ctx context.Context, roomID string, userID, kickedBy uint) error
    InviteUser(ctx context.Context, roomID string, userID, invitedBy uint) error
    
    // Room operations
    BroadcastToRoom(ctx context.Context, roomID, eventType string, data map[string]interface{}) error
    GetRoomUsers(ctx context.Context, roomID string) ([]dto.RoomMemberResponse, error)
    GetRoomStats(ctx context.Context, roomID string) (*dto.RoomStatsResponse, error)
    
    // Message management
    SaveMessage(ctx context.Context, roomID string, userID uint, message *socket.Message) error
    GetRoomMessages(ctx context.Context, roomID string, filter dto.MessageFilter, page, limit int) ([]dto.RoomMessageResponse, int64, error)
    DeleteMessage(ctx context.Context, roomID string, messageID uint, deletedBy uint) error
}

type roomService struct {
    roomRepo       repository.RoomRepository
    memberRepo     repository.RoomMemberRepository
    messageRepo    repository.RoomMessageRepository
    hub            socket.Hub
    logger         logger.Logger
}

// NewRoomService tạo room service mới
func NewRoomService(
    roomRepo repository.RoomRepository,
    memberRepo repository.RoomMemberRepository,
    messageRepo repository.RoomMessageRepository,
    hub socket.Hub,
    logger logger.Logger,
) RoomService {
    return &roomService{
        roomRepo:    roomRepo,
        memberRepo:  memberRepo,
        messageRepo: messageRepo,
        hub:         hub,
        logger:      logger,
    }
}

// CreateRoom tạo room mới
func (s *roomService) CreateRoom(ctx context.Context, req dto.CreateRoomRequest) (*dto.RoomResponse, error) {
    // Validate request
    if err := s.validateCreateRoomRequest(req); err != nil {
        return nil, err
    }
    
    // Create room model
    room := &models.Room{
        ID:              req.ID,
        Name:            req.Name,
        Description:     req.Description,
        Type:            req.Type,
        OwnerID:         req.OwnerID,
        TenantID:        req.TenantID,
        WebsiteID:       req.WebsiteID,
        IsPublic:        req.IsPublic,
        MaxMembers:      req.MaxMembers,
        AllowMessages:   req.AllowMessages,
        AllowFiles:      req.AllowFiles,
        RequireApproval: req.RequireApproval,
        IsModerated:     req.IsModerated,
        AutoDeleteDays:  req.AutoDeleteDays,
        Metadata:        req.Metadata,
        Settings:        req.Settings,
    }
    
    // Save to database
    if err := s.roomRepo.Create(ctx, room); err != nil {
        s.logger.Error("Failed to create room",
            "room_id", req.ID,
            "error", err,
        )
        return nil, fmt.Errorf("failed to create room: %w", err)
    }
    
    // Create room in Hub
    if err := s.hub.CreateRoom(room.ID, room.MaxMembers); err != nil {
        s.logger.Error("Failed to create room in hub",
            "room_id", room.ID,
            "error", err,
        )
        // Room created in DB but not in Hub - log but don't fail
    }
    
    // Add owner as first member
    ownerMember := &models.RoomMember{
        RoomID:       room.ID,
        UserID:       room.OwnerID,
        Role:         "owner",
        Status:       "active",
        CanMessage:   true,
        CanInvite:    true,
        CanModerate:  true,
        CanKick:      true,
    }
    
    if err := s.memberRepo.Create(ctx, ownerMember); err != nil {
        s.logger.Error("Failed to add owner to room",
            "room_id", room.ID,
            "owner_id", room.OwnerID,
            "error", err,
        )
    }
    
    s.logger.Info("Room created successfully",
        "room_id", room.ID,
        "name", room.Name,
        "owner_id", room.OwnerID,
    )
    
    return s.convertToRoomResponse(room), nil
}

// GetRoom lấy thông tin room
func (s *roomService) GetRoom(ctx context.Context, roomID string) (*dto.RoomResponse, error) {
    room, err := s.roomRepo.GetByID(ctx, roomID)
    if err != nil {
        return nil, fmt.Errorf("room not found: %w", err)
    }
    
    return s.convertToRoomResponse(room), nil
}

// GetRooms lấy danh sách rooms với filtering
func (s *roomService) GetRooms(ctx context.Context, filter dto.RoomFilter, page, limit int) ([]dto.RoomResponse, int64, error) {
    rooms, total, err := s.roomRepo.GetWithFilter(ctx, filter, page, limit)
    if err != nil {
        return nil, 0, fmt.Errorf("failed to get rooms: %w", err)
    }
    
    responses := make([]dto.RoomResponse, len(rooms))
    for i, room := range rooms {
        responses[i] = *s.convertToRoomResponse(&room)
    }
    
    return responses, total, nil
}

// JoinRoom thêm user vào room
func (s *roomService) JoinRoom(ctx context.Context, roomID string, userID uint) error {
    // Get room
    room, err := s.roomRepo.GetByID(ctx, roomID)
    if err != nil {
        return fmt.Errorf("room not found: %w", err)
    }
    
    // Check if user can join
    if !room.CanJoin(userID) {
        return fmt.Errorf("user cannot join room")
    }
    
    // Check if already member
    exists, err := s.memberRepo.IsMember(ctx, roomID, userID)
    if err != nil {
        return fmt.Errorf("failed to check membership: %w", err)
    }
    
    if exists {
        return fmt.Errorf("user already member of room")
    }
    
    // Create membership
    member := &models.RoomMember{
        RoomID:     roomID,
        UserID:     userID,
        Role:       "member",
        Status:     "active",
        CanMessage: room.AllowMessages,
    }
    
    if err := s.memberRepo.Create(ctx, member); err != nil {
        return fmt.Errorf("failed to create membership: %w", err)
    }
    
    // Update room member count
    if err := s.roomRepo.IncrementMemberCount(ctx, roomID); err != nil {
        s.logger.Error("Failed to update member count",
            "room_id", roomID,
            "error", err,
        )
    }
    
    // Add to Hub room
    if hubRoom, exists := s.hub.GetRoom(roomID); exists {
        if err := hubRoom.AddUser(userID); err != nil {
            s.logger.Error("Failed to add user to hub room",
                "room_id", roomID,
                "user_id", userID,
                "error", err,
            )
        }
    }
    
    // Broadcast join event
    joinData := map[string]interface{}{
        "user_id": userID,
        "room_id": roomID,
        "action":  "joined",
    }
    
    s.BroadcastToRoom(ctx, roomID, "room.user_joined", joinData)
    
    s.logger.Info("User joined room",
        "room_id", roomID,
        "user_id", userID,
    )
    
    return nil
}

// LeaveRoom remove user khỏi room
func (s *roomService) LeaveRoom(ctx context.Context, roomID string, userID uint) error {
    // Check membership
    member, err := s.memberRepo.GetMember(ctx, roomID, userID)
    if err != nil {
        return fmt.Errorf("user not member of room: %w", err)
    }
    
    // Owner cannot leave room
    if member.IsOwner() {
        return fmt.Errorf("room owner cannot leave room")
    }
    
    // Remove membership
    if err := s.memberRepo.Delete(ctx, roomID, userID); err != nil {
        return fmt.Errorf("failed to remove membership: %w", err)
    }
    
    // Update room member count
    if err := s.roomRepo.DecrementMemberCount(ctx, roomID); err != nil {
        s.logger.Error("Failed to update member count",
            "room_id", roomID,
            "error", err,
        )
    }
    
    // Remove from Hub room
    if hubRoom, exists := s.hub.GetRoom(roomID); exists {
        if err := hubRoom.RemoveUser(userID); err != nil {
            s.logger.Error("Failed to remove user from hub room",
                "room_id", roomID,
                "user_id", userID,
                "error", err,
            )
        }
    }
    
    // Broadcast leave event
    leaveData := map[string]interface{}{
        "user_id": userID,
        "room_id": roomID,
        "action":  "left",
    }
    
    s.BroadcastToRoom(ctx, roomID, "room.user_left", leaveData)
    
    s.logger.Info("User left room",
        "room_id", roomID,
        "user_id", userID,
    )
    
    return nil
}

// BroadcastToRoom gửi message đến tất cả members của room
func (s *roomService) BroadcastToRoom(ctx context.Context, roomID, eventType string, data map[string]interface{}) error {
    message := socket.NewMessage(eventType, data).WithRoomID(roomID)
    
    return s.hub.BroadcastToRoom(roomID, message)
}

// validateCreateRoomRequest validate room creation request
func (s *roomService) validateCreateRoomRequest(req dto.CreateRoomRequest) error {
    if req.Name == "" {
        return fmt.Errorf("room name is required")
    }
    
    if req.Type == "" {
        return fmt.Errorf("room type is required")
    }
    
    if req.OwnerID == 0 {
        return fmt.Errorf("owner ID is required")
    }
    
    if req.MaxMembers <= 0 {
        req.MaxMembers = 100
    }
    
    return nil
}

// convertToRoomResponse chuyển model thành response DTO
func (s *roomService) convertToRoomResponse(room *models.Room) *dto.RoomResponse {
    return &dto.RoomResponse{
        ID:              room.ID,
        Name:            room.Name,
        Description:     room.Description,
        Type:            room.Type,
        OwnerID:         room.OwnerID,
        TenantID:        room.TenantID,
        WebsiteID:       room.WebsiteID,
        IsPublic:        room.IsPublic,
        IsActive:        room.IsActive,
        MaxMembers:      room.MaxMembers,
        MemberCount:     room.MemberCount,
        MessageCount:    room.MessageCount,
        AllowMessages:   room.AllowMessages,
        AllowFiles:      room.AllowFiles,
        RequireApproval: room.RequireApproval,
        IsModerated:     room.IsModerated,
        AutoDeleteDays:  room.AutoDeleteDays,
        LastActivity:    room.LastActivity,
        Metadata:        room.Metadata,
        Settings:        room.Settings,
        CreatedAt:       room.CreatedAt,
        UpdatedAt:       room.UpdatedAt,
    }
}
```

### Step 5: Database Migrations

```sql
-- migrations/001_create_rooms_table.sql
CREATE TABLE IF NOT EXISTS socket_rooms (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    
    owner_id INT UNSIGNED NOT NULL,
    tenant_id INT UNSIGNED NOT NULL,
    website_id INT UNSIGNED NOT NULL,
    
    is_public BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    max_members INT DEFAULT 100,
    
    allow_messages BOOLEAN DEFAULT TRUE,
    allow_files BOOLEAN DEFAULT TRUE,
    require_approval BOOLEAN DEFAULT FALSE,
    
    is_moderated BOOLEAN DEFAULT FALSE,
    auto_delete_days INT DEFAULT 0,
    
    member_count INT DEFAULT 0,
    message_count BIGINT DEFAULT 0,
    last_activity TIMESTAMP,
    
    metadata JSON,
    settings JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_rooms_type (type),
    INDEX idx_rooms_tenant (tenant_id),
    INDEX idx_rooms_website (website_id),
    INDEX idx_rooms_owner (owner_id),
    INDEX idx_rooms_active (is_active),
    INDEX idx_rooms_deleted (deleted_at)
);

-- migrations/002_create_room_members_table.sql
CREATE TABLE IF NOT EXISTS socket_room_members (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    room_id VARCHAR(100) NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    role VARCHAR(50) DEFAULT 'member',
    status VARCHAR(50) DEFAULT 'active',
    
    can_message BOOLEAN DEFAULT TRUE,
    can_invite BOOLEAN DEFAULT FALSE,
    can_moderate BOOLEAN DEFAULT FALSE,
    can_kick BOOLEAN DEFAULT FALSE,
    
    last_seen TIMESTAMP,
    message_count BIGINT DEFAULT 0,
    
    notify_on_mention BOOLEAN DEFAULT TRUE,
    notify_on_message BOOLEAN DEFAULT TRUE,
    notify_on_join_leave BOOLEAN DEFAULT FALSE,
    
    invited_by INT UNSIGNED,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    UNIQUE KEY idx_room_user (room_id, user_id),
    INDEX idx_members_user (user_id),
    INDEX idx_members_role (role),
    INDEX idx_members_status (status),
    INDEX idx_members_deleted (deleted_at),
    
    FOREIGN KEY (room_id) REFERENCES socket_rooms(id) ON DELETE CASCADE
);

-- migrations/003_create_room_messages_table.sql
CREATE TABLE IF NOT EXISTS socket_room_messages (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    room_id VARCHAR(100) NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    type VARCHAR(50) DEFAULT 'text',
    content TEXT,
    
    event_type VARCHAR(100),
    event_data JSON,
    
    parent_id INT UNSIGNED,
    thread_count INT DEFAULT 0,
    
    reaction_count JSON,
    reply_count INT DEFAULT 0,
    
    is_edited BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP,
    deleted_by INT UNSIGNED,
    
    delivered_to JSON,
    read_by JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_messages_room (room_id),
    INDEX idx_messages_user (user_id),
    INDEX idx_messages_parent (parent_id),
    INDEX idx_messages_created (created_at),
    INDEX idx_messages_deleted (is_deleted),
    
    FOREIGN KEY (room_id) REFERENCES socket_rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES socket_room_messages(id) ON DELETE SET NULL
);
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **room_service_test.go**: Test room business logic
2. **room_repository_test.go**: Test room data access
3. **room_model_test.go**: Test room models và methods
4. **room_member_service_test.go**: Test membership management

### Integration Tests:

1. **room_integration_test.go**: Test với real database
2. **room_hub_integration_test.go**: Test với WebSocket Hub

## ✅ Completion Criteria

- [ ] Database models và migrations hoạt động
- [ ] Room lifecycle management complete
- [ ] Membership management với permissions
- [ ] Message history tracking
- [ ] Integration với WebSocket Hub
- [ ] Broadcasting system functional
- [ ] Unit tests pass với coverage >= 85%
- [ ] Integration tests pass
- [ ] Performance tests với large rooms

## 🔗 Dependencies

- **Task 01-04**: Phase 1 Core Infrastructure (Complete)
- **Task 05**: Socket Module Business Logic (Complete)
- **Database**: Existing database connection

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 07**: Broadcasting Utilities
2. **Task 08**: Middleware & Authentication
3. **Task 09**: Notification Module Integration

## 📝 Notes

- Room IDs phải unique và có meaningful prefix
- Member permissions phải flexible và extensible
- Message history cần efficient pagination
- Room statistics cần real-time updates
- Integration với Hub phải reliable
- Database queries phải optimized cho performance
- Soft deletes cho rooms và messages
