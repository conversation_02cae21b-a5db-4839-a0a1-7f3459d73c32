# Task 05: Socket Module Business Logic

## 📋 Tổng Quan

Triển khai Socket Module - business logic layer cho WebSocket system. Module này cung cấp HTTP APIs, WebSocket endpoints và orchestrate interactions giữa core socket infrastructure với business modules khác.

## 🎯 Mục Tiêu

- Tạo Socket Module với đầy đủ HTTP APIs và WebSocket endpoints
- Integrate với core socket infrastructure (Hub, EventRouter)
- Provide management APIs cho connections, rooms, notifications
- Implement business logic cho socket operations
- Support admin dashboard cho monitoring và management

## 📁 Files Cần Tạo

```
modules/integration/socket/             # Socket Module
├── module.go                          # Module definition và registration
├── api/
│   ├── routes.go                      # Route definitions
│   └── handlers/
│       ├── websocket_handler.go       # WebSocket endpoint
│       ├── connection_handler.go      # Connection management API
│       ├── room_handler.go            # Room management API
│       ├── notification_handler.go    # Notification API
│       └── stats_handler.go           # Statistics API
├── service/
│   ├── socket_service.go              # Main socket service
│   ├── connection_service.go          # Connection business logic
│   ├── room_service.go                # Room business logic
│   ├── notification_service.go        # Notification management
│   └── stats_service.go               # Statistics service
├── repository/
│   ├── connection_repository.go       # Connection persistence
│   ├── room_repository.go             # Room persistence
│   └── notification_repository.go     # Notification storage
├── dto/
│   ├── connection_dto.go              # Connection DTOs
│   ├── room_dto.go                    # Room DTOs
│   ├── notification_dto.go            # Notification DTOs
│   └── stats_dto.go                   # Statistics DTOs
└── models/
    ├── connection.go                  # Connection models
    ├── room.go                        # Room models
    └── subscription.go                # Subscription models
```

## 🔧 Implementation Steps

### Step 1: Module Definition (module.go)

```go
package socket

import (
    "context"
    "go.uber.org/fx"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/database"
    "wnapi/modules/integration/socket/api"
    "wnapi/modules/integration/socket/service"
    "wnapi/modules/integration/socket/repository"
)

// Module định nghĩa Socket module
type Module struct {
    Name        string
    Version     string
    Description string
}

// NewModule tạo Socket module instance
func NewModule() *Module {
    return &Module{
        Name:        "socket",
        Version:     "1.0.0",
        Description: "WebSocket Integration Module",
    }
}

// ModuleOptions cấu hình cho module
type ModuleOptions struct {
    fx.In
    
    DB           database.Database
    Logger       logger.Logger
    Hub          socket.Hub
    EventRouter  socket.EventRouter
}

// SocketModuleParams dependencies injection
type SocketModuleParams struct {
    fx.Out
    
    Module           *Module
    SocketService    service.SocketService
    ConnectionRepo   repository.ConnectionRepository
    RoomRepo         repository.RoomRepository
    NotificationRepo repository.NotificationRepository
    WebSocketHandler api.WebSocketHandler
    Routes           api.Routes
}

// ProvideSocketModule provide Socket module với dependencies
func ProvideSocketModule(opts ModuleOptions) (SocketModuleParams, error) {
    logger := opts.Logger.Named("socket-module")
    
    // Repositories
    connectionRepo := repository.NewConnectionRepository(opts.DB, logger)
    roomRepo := repository.NewRoomRepository(opts.DB, logger)
    notificationRepo := repository.NewNotificationRepository(opts.DB, logger)
    
    // Services
    connectionService := service.NewConnectionService(connectionRepo, opts.Hub, logger)
    roomService := service.NewRoomService(roomRepo, opts.Hub, logger)
    notificationService := service.NewNotificationService(notificationRepo, opts.Hub, opts.EventRouter, logger)
    statsService := service.NewStatsService(opts.Hub, logger)
    
    socketService := service.NewSocketService(
        connectionService,
        roomService,
        notificationService,
        statsService,
        opts.Hub,
        opts.EventRouter,
        logger,
    )
    
    // API Handlers
    websocketHandler := api.NewWebSocketHandler(socketService, opts.Hub, opts.EventRouter, logger)
    connectionHandler := api.NewConnectionHandler(connectionService, logger)
    roomHandler := api.NewRoomHandler(roomService, logger)
    notificationHandler := api.NewNotificationHandler(notificationService, logger)
    statsHandler := api.NewStatsHandler(statsService, logger)
    
    // Routes
    routes := api.NewRoutes(
        websocketHandler,
        connectionHandler,
        roomHandler,
        notificationHandler,
        statsHandler,
    )
    
    module := NewModule()
    
    return SocketModuleParams{
        Module:           module,
        SocketService:    socketService,
        ConnectionRepo:   connectionRepo,
        RoomRepo:         roomRepo,
        NotificationRepo: notificationRepo,
        WebSocketHandler: websocketHandler,
        Routes:           routes,
    }, nil
}

// RegisterModule đăng ký module với FX
var RegisterModule = fx.Options(
    fx.Provide(ProvideSocketModule),
)
```

### Step 2: WebSocket Handler (api/handlers/websocket_handler.go)

```go
package api

import (
    "context"
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "github.com/gorilla/websocket"
    "wnapi/internal/pkg/socket"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/auth"
    "wnapi/modules/integration/socket/service"
)

// WebSocketHandler xử lý WebSocket connections
type WebSocketHandler interface {
    HandleWebSocket(c *gin.Context)
    HandleHealthCheck(c *gin.Context)
}

type websocketHandler struct {
    socketService service.SocketService
    hub           socket.Hub
    eventRouter   socket.EventRouter
    logger        logger.Logger
    upgrader      websocket.Upgrader
}

// NewWebSocketHandler tạo WebSocket handler mới
func NewWebSocketHandler(
    socketService service.SocketService,
    hub socket.Hub,
    eventRouter socket.EventRouter,
    logger logger.Logger,
) WebSocketHandler {
    upgrader := websocket.Upgrader{
        ReadBufferSize:  socket.ReadBufferSize,
        WriteBufferSize: socket.WriteBufferSize,
        CheckOrigin: func(r *http.Request) bool {
            // TODO: Implement proper origin checking
            return true
        },
    }
    
    return &websocketHandler{
        socketService: socketService,
        hub:           hub,
        eventRouter:   eventRouter,
        logger:        logger,
        upgrader:      upgrader,
    }
}

// HandleWebSocket xử lý WebSocket connection requests
func (h *websocketHandler) HandleWebSocket(c *gin.Context) {
    // Extract authentication info từ JWT token hoặc query params
    userID, tenantID, websiteID, err := h.extractAuthInfo(c)
    if err != nil {
        h.logger.Warn("WebSocket authentication failed",
            "remote_addr", c.ClientIP(),
            "error", err,
        )
        c.JSON(http.StatusUnauthorized, gin.H{
            "error": "Authentication required",
        })
        return
    }
    
    // Upgrade connection đến WebSocket
    conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        h.logger.Error("Failed to upgrade WebSocket connection",
            "remote_addr", c.ClientIP(),
            "error", err,
        )
        return
    }
    
    // Register connection với Hub
    socketConn, err := h.hub.RegisterConnection(conn, userID, tenantID, websiteID)
    if err != nil {
        h.logger.Error("Failed to register WebSocket connection",
            "user_id", userID,
            "tenant_id", tenantID,
            "remote_addr", c.ClientIP(),
            "error", err,
        )
        conn.Close()
        return
    }
    
    h.logger.Info("WebSocket connection established",
        "conn_id", socketConn.ID(),
        "user_id", userID,
        "tenant_id", tenantID,
        "remote_addr", c.ClientIP(),
    )
    
    // Send welcome message
    welcomeMsg := socket.NewMessage("system.connected", map[string]interface{}{
        "connection_id": socketConn.ID(),
        "user_id":      userID,
        "server_time":  socketConn.LastActivity(),
    })
    
    if err := socketConn.Send(welcomeMsg); err != nil {
        h.logger.Warn("Failed to send welcome message",
            "conn_id", socketConn.ID(),
            "error", err,
        )
    }
}

// HandleHealthCheck xử lý health check cho WebSocket endpoint
func (h *websocketHandler) HandleHealthCheck(c *gin.Context) {
    stats := h.hub.GetStats()
    
    health := gin.H{
        "status":      "healthy",
        "connections": stats["total_connections"],
        "rooms":       stats["total_rooms"],
        "uptime":      stats["uptime"],
    }
    
    c.JSON(http.StatusOK, health)
}

// extractAuthInfo extract authentication information từ request
func (h *websocketHandler) extractAuthInfo(c *gin.Context) (uint, uint, uint, error) {
    // Try to get from JWT token first
    if claims, exists := c.Get("user_claims"); exists {
        if userClaims, ok := claims.(*auth.UserClaims); ok {
            return userClaims.UserID, userClaims.TenantID, userClaims.WebsiteID, nil
        }
    }
    
    // Fallback to query parameters (for development/testing)
    userIDStr := c.Query(socket.QueryParamUserID)
    tenantIDStr := c.Query(socket.QueryParamTenantID)
    websiteIDStr := c.Query(socket.QueryParamWebsiteID)
    
    if userIDStr == "" || tenantIDStr == "" || websiteIDStr == "" {
        return 0, 0, 0, socket.NewAuthError("missing authentication parameters", socket.ErrAuthenticationFailed)
    }
    
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        return 0, 0, 0, socket.NewAuthError("invalid user ID", err)
    }
    
    tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
    if err != nil {
        return 0, 0, 0, socket.NewAuthError("invalid tenant ID", err)
    }
    
    websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
    if err != nil {
        return 0, 0, 0, socket.NewAuthError("invalid website ID", err)
    }
    
    return uint(userID), uint(tenantID), uint(websiteID), nil
}
```

### Step 3: Connection Management Handler (api/handlers/connection_handler.go)

```go
package api

import (
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/response"
    "wnapi/modules/integration/socket/service"
    "wnapi/modules/integration/socket/dto"
)

// ConnectionHandler quản lý connections qua HTTP API
type ConnectionHandler interface {
    GetConnections(c *gin.Context)
    GetConnection(c *gin.Context)
    GetUserConnections(c *gin.Context)
    CloseConnection(c *gin.Context)
    BroadcastToUser(c *gin.Context)
    GetConnectionStats(c *gin.Context)
}

type connectionHandler struct {
    connectionService service.ConnectionService
    logger           logger.Logger
}

// NewConnectionHandler tạo connection handler mới
func NewConnectionHandler(
    connectionService service.ConnectionService,
    logger logger.Logger,
) ConnectionHandler {
    return &connectionHandler{
        connectionService: connectionService,
        logger:           logger,
    }
}

// GetConnections lấy danh sách tất cả connections
func (h *connectionHandler) GetConnections(c *gin.Context) {
    // Pagination parameters
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
    
    // Filters
    filter := dto.ConnectionFilter{
        TenantID:  h.parseUintQuery(c, "tenant_id"),
        WebsiteID: h.parseUintQuery(c, "website_id"),
        Status:    c.Query("status"),
    }
    
    connections, total, err := h.connectionService.GetConnections(c.Request.Context(), filter, page, limit)
    if err != nil {
        h.logger.Error("Failed to get connections",
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get connections")
        return
    }
    
    response.SuccessWithPagination(c, connections, total, page, limit)
}

// GetConnection lấy thông tin một connection cụ thể
func (h *connectionHandler) GetConnection(c *gin.Context) {
    connID := c.Param("id")
    if connID == "" {
        response.Error(c, http.StatusBadRequest, "Connection ID is required")
        return
    }
    
    connection, err := h.connectionService.GetConnection(c.Request.Context(), connID)
    if err != nil {
        h.logger.Error("Failed to get connection",
            "conn_id", connID,
            "error", err,
        )
        response.Error(c, http.StatusNotFound, "Connection not found")
        return
    }
    
    response.Success(c, connection)
}

// GetUserConnections lấy tất cả connections của một user
func (h *connectionHandler) GetUserConnections(c *gin.Context) {
    userIDStr := c.Param("user_id")
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid user ID")
        return
    }
    
    connections, err := h.connectionService.GetUserConnections(c.Request.Context(), uint(userID))
    if err != nil {
        h.logger.Error("Failed to get user connections",
            "user_id", userID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get user connections")
        return
    }
    
    response.Success(c, connections)
}

// CloseConnection đóng một connection
func (h *connectionHandler) CloseConnection(c *gin.Context) {
    connID := c.Param("id")
    if connID == "" {
        response.Error(c, http.StatusBadRequest, "Connection ID is required")
        return
    }
    
    err := h.connectionService.CloseConnection(c.Request.Context(), connID)
    if err != nil {
        h.logger.Error("Failed to close connection",
            "conn_id", connID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to close connection")
        return
    }
    
    response.Success(c, gin.H{"status": "closed"})
}

// BroadcastToUser gửi message đến tất cả connections của user
func (h *connectionHandler) BroadcastToUser(c *gin.Context) {
    userIDStr := c.Param("user_id")
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid user ID")
        return
    }
    
    var req dto.BroadcastRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request body")
        return
    }
    
    err = h.connectionService.BroadcastToUser(c.Request.Context(), uint(userID), req.EventType, req.Data)
    if err != nil {
        h.logger.Error("Failed to broadcast to user",
            "user_id", userID,
            "event_type", req.EventType,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to broadcast message")
        return
    }
    
    response.Success(c, gin.H{"status": "sent"})
}

// GetConnectionStats lấy thống kê connections
func (h *connectionHandler) GetConnectionStats(c *gin.Context) {
    stats, err := h.connectionService.GetConnectionStats(c.Request.Context())
    if err != nil {
        h.logger.Error("Failed to get connection stats",
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get stats")
        return
    }
    
    response.Success(c, stats)
}

// parseUintQuery helper để parse uint query parameter
func (h *connectionHandler) parseUintQuery(c *gin.Context, key string) *uint {
    value := c.Query(key)
    if value == "" {
        return nil
    }
    
    parsed, err := strconv.ParseUint(value, 10, 32)
    if err != nil {
        return nil
    }
    
    result := uint(parsed)
    return &result
}
```

### Step 4: Room Management Handler (api/handlers/room_handler.go)

```go
package api

import (
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/response"
    "wnapi/modules/integration/socket/service"
    "wnapi/modules/integration/socket/dto"
)

// RoomHandler quản lý rooms qua HTTP API
type RoomHandler interface {
    CreateRoom(c *gin.Context)
    GetRooms(c *gin.Context)
    GetRoom(c *gin.Context)
    UpdateRoom(c *gin.Context)
    DeleteRoom(c *gin.Context)
    JoinRoom(c *gin.Context)
    LeaveRoom(c *gin.Context)
    BroadcastToRoom(c *gin.Context)
    GetRoomUsers(c *gin.Context)
    GetRoomStats(c *gin.Context)
}

type roomHandler struct {
    roomService service.RoomService
    logger      logger.Logger
}

// NewRoomHandler tạo room handler mới
func NewRoomHandler(
    roomService service.RoomService,
    logger logger.Logger,
) RoomHandler {
    return &roomHandler{
        roomService: roomService,
        logger:      logger,
    }
}

// CreateRoom tạo room mới
func (h *roomHandler) CreateRoom(c *gin.Context) {
    var req dto.CreateRoomRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request body")
        return
    }
    
    room, err := h.roomService.CreateRoom(c.Request.Context(), req)
    if err != nil {
        h.logger.Error("Failed to create room",
            "room_id", req.ID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to create room")
        return
    }
    
    response.Success(c, room)
}

// GetRooms lấy danh sách rooms
func (h *roomHandler) GetRooms(c *gin.Context) {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
    
    filter := dto.RoomFilter{
        Type:      c.Query("type"),
        TenantID:  h.parseUintQuery(c, "tenant_id"),
        WebsiteID: h.parseUintQuery(c, "website_id"),
        Status:    c.Query("status"),
    }
    
    rooms, total, err := h.roomService.GetRooms(c.Request.Context(), filter, page, limit)
    if err != nil {
        h.logger.Error("Failed to get rooms",
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get rooms")
        return
    }
    
    response.SuccessWithPagination(c, rooms, total, page, limit)
}

// GetRoom lấy thông tin room cụ thể
func (h *roomHandler) GetRoom(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    room, err := h.roomService.GetRoom(c.Request.Context(), roomID)
    if err != nil {
        h.logger.Error("Failed to get room",
            "room_id", roomID,
            "error", err,
        )
        response.Error(c, http.StatusNotFound, "Room not found")
        return
    }
    
    response.Success(c, room)
}

// UpdateRoom cập nhật room
func (h *roomHandler) UpdateRoom(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    var req dto.UpdateRoomRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request body")
        return
    }
    
    room, err := h.roomService.UpdateRoom(c.Request.Context(), roomID, req)
    if err != nil {
        h.logger.Error("Failed to update room",
            "room_id", roomID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to update room")
        return
    }
    
    response.Success(c, room)
}

// DeleteRoom xóa room
func (h *roomHandler) DeleteRoom(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    err := h.roomService.DeleteRoom(c.Request.Context(), roomID)
    if err != nil {
        h.logger.Error("Failed to delete room",
            "room_id", roomID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to delete room")
        return
    }
    
    response.Success(c, gin.H{"status": "deleted"})
}

// JoinRoom join user vào room
func (h *roomHandler) JoinRoom(c *gin.Context) {
    roomID := c.Param("id")
    userIDStr := c.Param("user_id")
    
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid user ID")
        return
    }
    
    err = h.roomService.JoinRoom(c.Request.Context(), roomID, uint(userID))
    if err != nil {
        h.logger.Error("Failed to join room",
            "room_id", roomID,
            "user_id", userID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to join room")
        return
    }
    
    response.Success(c, gin.H{"status": "joined"})
}

// LeaveRoom remove user khỏi room
func (h *roomHandler) LeaveRoom(c *gin.Context) {
    roomID := c.Param("id")
    userIDStr := c.Param("user_id")
    
    userID, err := strconv.ParseUint(userIDStr, 10, 32)
    if err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid user ID")
        return
    }
    
    err = h.roomService.LeaveRoom(c.Request.Context(), roomID, uint(userID))
    if err != nil {
        h.logger.Error("Failed to leave room",
            "room_id", roomID,
            "user_id", userID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to leave room")
        return
    }
    
    response.Success(c, gin.H{"status": "left"})
}

// BroadcastToRoom gửi message đến room
func (h *roomHandler) BroadcastToRoom(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    var req dto.BroadcastRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, http.StatusBadRequest, "Invalid request body")
        return
    }
    
    err := h.roomService.BroadcastToRoom(c.Request.Context(), roomID, req.EventType, req.Data)
    if err != nil {
        h.logger.Error("Failed to broadcast to room",
            "room_id", roomID,
            "event_type", req.EventType,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to broadcast message")
        return
    }
    
    response.Success(c, gin.H{"status": "sent"})
}

// GetRoomUsers lấy danh sách users trong room
func (h *roomHandler) GetRoomUsers(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    users, err := h.roomService.GetRoomUsers(c.Request.Context(), roomID)
    if err != nil {
        h.logger.Error("Failed to get room users",
            "room_id", roomID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get room users")
        return
    }
    
    response.Success(c, users)
}

// GetRoomStats lấy thống kê room
func (h *roomHandler) GetRoomStats(c *gin.Context) {
    roomID := c.Param("id")
    if roomID == "" {
        response.Error(c, http.StatusBadRequest, "Room ID is required")
        return
    }
    
    stats, err := h.roomService.GetRoomStats(c.Request.Context(), roomID)
    if err != nil {
        h.logger.Error("Failed to get room stats",
            "room_id", roomID,
            "error", err,
        )
        response.Error(c, http.StatusInternalServerError, "Failed to get room stats")
        return
    }
    
    response.Success(c, stats)
}

// parseUintQuery helper để parse uint query parameter
func (h *roomHandler) parseUintQuery(c *gin.Context, key string) *uint {
    value := c.Query(key)
    if value == "" {
        return nil
    }
    
    parsed, err := strconv.ParseUint(value, 10, 32)
    if err != nil {
        return nil
    }
    
    result := uint(parsed)
    return &result
}
```

### Step 5: Routes Definition (api/routes.go)

```go
package api

import (
    "github.com/gin-gonic/gin"
)

// Routes định nghĩa tất cả routes cho Socket module
type Routes interface {
    RegisterRoutes(router *gin.RouterGroup)
}

type routes struct {
    websocketHandler    WebSocketHandler
    connectionHandler   ConnectionHandler
    roomHandler         RoomHandler
    notificationHandler NotificationHandler
    statsHandler        StatsHandler
}

// NewRoutes tạo routes instance mới
func NewRoutes(
    websocketHandler WebSocketHandler,
    connectionHandler ConnectionHandler,
    roomHandler RoomHandler,
    notificationHandler NotificationHandler,
    statsHandler StatsHandler,
) Routes {
    return &routes{
        websocketHandler:    websocketHandler,
        connectionHandler:   connectionHandler,
        roomHandler:         roomHandler,
        notificationHandler: notificationHandler,
        statsHandler:        statsHandler,
    }
}

// RegisterRoutes đăng ký tất cả routes
func (r *routes) RegisterRoutes(router *gin.RouterGroup) {
    // WebSocket endpoint
    router.GET("/ws", r.websocketHandler.HandleWebSocket)
    router.GET("/ws/health", r.websocketHandler.HandleHealthCheck)
    
    // Connection management
    connections := router.Group("/connections")
    {
        connections.GET("", r.connectionHandler.GetConnections)
        connections.GET("/:id", r.connectionHandler.GetConnection)
        connections.DELETE("/:id", r.connectionHandler.CloseConnection)
        connections.GET("/stats", r.connectionHandler.GetConnectionStats)
        
        // User connections
        connections.GET("/users/:user_id", r.connectionHandler.GetUserConnections)
        connections.POST("/users/:user_id/broadcast", r.connectionHandler.BroadcastToUser)
    }
    
    // Room management
    rooms := router.Group("/rooms")
    {
        rooms.POST("", r.roomHandler.CreateRoom)
        rooms.GET("", r.roomHandler.GetRooms)
        rooms.GET("/:id", r.roomHandler.GetRoom)
        rooms.PUT("/:id", r.roomHandler.UpdateRoom)
        rooms.DELETE("/:id", r.roomHandler.DeleteRoom)
        rooms.GET("/:id/users", r.roomHandler.GetRoomUsers)
        rooms.GET("/:id/stats", r.roomHandler.GetRoomStats)
        rooms.POST("/:id/broadcast", r.roomHandler.BroadcastToRoom)
        
        // Room membership
        rooms.POST("/:id/users/:user_id", r.roomHandler.JoinRoom)
        rooms.DELETE("/:id/users/:user_id", r.roomHandler.LeaveRoom)
    }
    
    // Notification management
    notifications := router.Group("/notifications")
    {
        notifications.POST("/send", r.notificationHandler.SendNotification)
        notifications.POST("/broadcast", r.notificationHandler.BroadcastNotification)
        notifications.GET("/subscriptions", r.notificationHandler.GetSubscriptions)
        notifications.POST("/subscriptions", r.notificationHandler.CreateSubscription)
        notifications.PUT("/subscriptions/:id", r.notificationHandler.UpdateSubscription)
        notifications.DELETE("/subscriptions/:id", r.notificationHandler.DeleteSubscription)
    }
    
    // Statistics
    stats := router.Group("/stats")
    {
        stats.GET("", r.statsHandler.GetOverallStats)
        stats.GET("/connections", r.statsHandler.GetConnectionStats)
        stats.GET("/rooms", r.statsHandler.GetRoomStats)
        stats.GET("/events", r.statsHandler.GetEventStats)
        stats.GET("/performance", r.statsHandler.GetPerformanceStats)
    }
}
```

## 🧪 Testing Requirements

### Unit Tests Cần Tạo:

1. **module_test.go**: Test module registration và dependency injection
2. **websocket_handler_test.go**: Test WebSocket connection handling
3. **connection_handler_test.go**: Test connection management APIs
4. **room_handler_test.go**: Test room management APIs
5. **routes_test.go**: Test route registration

### Integration Tests:

1. **websocket_integration_test.go**: Test real WebSocket connections
2. **api_integration_test.go**: Test HTTP APIs với real database
3. **service_integration_test.go**: Test service layer interactions

## ✅ Completion Criteria

- [ ] Module registration hoạt động với FX dependency injection
- [ ] WebSocket endpoint accepts connections và authenticates properly
- [ ] HTTP APIs cho connection management hoạt động
- [ ] HTTP APIs cho room management hoạt động
- [ ] Route registration correct và organized
- [ ] Unit tests pass với coverage >= 80%
- [ ] Integration tests với real components
- [ ] API documentation complete
- [ ] Error handling comprehensive

## 🔗 Dependencies

- **Task 01-04**: Phase 1 Core Infrastructure (Complete)
- **Existing modules**: Auth module cho authentication
- **Database**: Models và migrations

## 📈 Next Steps

Sau khi hoàn thành task này:
1. **Task 06**: Room Management System
2. **Task 07**: Broadcasting Utilities  
3. **Task 08**: Middleware & Authentication

## 📝 Notes

- Module phải integrate cleanly với existing FX system
- Authentication integration với existing auth module
- HTTP APIs follow existing response format standards
- WebSocket endpoint phải secure và scalable
- Error responses consistent với project standards
- All handlers cần proper input validation
- Business logic trong service layer, không trong handlers
