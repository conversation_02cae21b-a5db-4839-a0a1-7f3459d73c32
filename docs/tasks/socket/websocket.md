# Centralized Socket Architecture with Event System

## 1. Kiến trúc tổng quan

```
┌─────────────────────────────────────────────────────────────┐
│                     Frontend Clients                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Blog Client │ │ Auth Client │ │ E-com Client│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────┬───────────────────────────────────────┘
                      │ WebSocket Connections
                      ▼
┌─────────────────────────────────────────────────────────────┐
│              Central Socket Service                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                WebSocket Hub                        │   │
│  │  • Connection Management                            │   │
│  │  • Room Management                                  │   │
│  │  • Message Broadcasting                             │   │
│  │  • Authentication & Authorization                   │   │
│  └─────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Event Router                           │   │
│  │  • Route messages to appropriate modules            │   │
│  │  • Handle module-specific events                    │   │
│  │  • Event transformation & filtering                 │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────┬───────────────┬───────────────┬───────────────┘
              │               │               │
              ▼               ▼               ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐
    │ Auth Module │ │ Blog Module │ │ Ecommerce Module│
    │             │ │             │ │                 │
    │ • Register  │ │ • Register  │ │ • Register      │
    │   handlers  │ │   handlers  │ │   handlers      │
    │ • Handle    │ │ • Handle    │ │ • Handle        │
    │   events    │ │   events    │ │   events        │
    └─────────────┘ └─────────────┘ └─────────────────┘
```

## 2. Cấu trúc thư mục đề xuất

```
internal/pkg/socket/                    # Shared Socket Infrastructure
├── hub.go                             # Central WebSocket Hub
├── connection.go                      # Connection management
├── event_router.go                    # Event routing system
├── middleware.go                      # Socket middleware
├── types.go                           # Common types & interfaces
└── broadcaster.go                     # Broadcasting utilities

modules/integration/socket/             # Socket Module (Business Logic)
├── module.go                          # Module registration
├── api/
│   ├── handlers/
│   │   ├── websocket_handler.go       # WebSocket endpoint
│   │   ├── notification_handler.go    # HTTP notification API
│   │   └── room_handler.go            # Room management API
│   └── routes.go
├── service/
│   ├── socket_service.go              # Socket business logic
│   ├── notification_service.go        # Notification management
│   ├── room_service.go                # Room management
│   └── event_service.go               # Event handling coordination
├── repository/
│   ├── connection_repository.go       # Connection persistence
│   ├── notification_repository.go     # Notification storage
│   └── room_repository.go             # Room storage
└── dto/                               # Socket-specific DTOs

modules/*/                             # Other Business Modules
├── socket_handlers/                   # Module-specific socket handlers
│   ├── events.go                      # Event definitions
│   ├── handlers.go                    # Event handlers
│   └── publisher.go                   # Event publishing
└── service/
    └── *_service.go                   # Register socket handlers
```

## 3. Event-Driven Communication

### Event Registration Pattern

```go
// In each module's service layer
package service

import (
    "wnapi/internal/pkg/socket"
    "wnapi/modules/auth/socket_handlers"
)

func (s *AuthService) RegisterSocketHandlers(router *socket.EventRouter) {
    // Register event handlers
    router.RegisterHandler("auth.login", socket_handlers.HandleLogin)
    router.RegisterHandler("auth.logout", socket_handlers.HandleLogout)
    router.RegisterHandler("auth.password_reset", socket_handlers.HandlePasswordReset)
    
    // Register event publishers
    router.RegisterPublisher("user.authenticated", s.publishUserAuthenticated)
    router.RegisterPublisher("user.login_failed", s.publishLoginFailed)
}

// In auth module's socket_handlers/handlers.go
func HandleLogin(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
    // Handle login via WebSocket
    var req dto.LoginRequest
    if err := mapToStruct(data, &req); err != nil {
        return err
    }
    
    // Process login...
    user, err := authService.Login(ctx, req)
    if err != nil {
        return socket.SendError(conn, "LOGIN_FAILED", err.Error())
    }
    
    // Send success response
    return socket.SendResponse(conn, "LOGIN_SUCCESS", map[string]interface{}{
        "user": user,
        "token": token,
    })
}
```

### Event Publishing Pattern

```go
// In business logic (any module)
func (s *AuthService) Login(ctx context.Context, req dto.LoginRequest) (*User, error) {
    user, err := s.authenticateUser(ctx, req)
    if err != nil {
        // Publish login failed event
        s.eventPublisher.Publish("user.login_failed", map[string]interface{}{
            "email": req.Email,
            "reason": err.Error(),
            "ip": getClientIP(ctx),
        })
        return nil, err
    }
    
    // Publish successful login event
    s.eventPublisher.Publish("user.authenticated", map[string]interface{}{
        "user_id": user.ID,
        "tenant_id": user.TenantID,
        "login_time": time.Now(),
    })
    
    return user, nil
}
```

## 4. Core Socket Implementation

### internal/pkg/socket/types.go

```go
package socket

import (
    "context"
    "github.com/gorilla/websocket"
)

// EventHandler defines interface for handling socket events
type EventHandler func(ctx context.Context, conn Connection, data map[string]interface{}) error

// EventPublisher defines interface for publishing events
type EventPublisher interface {
    Publish(eventType string, data map[string]interface{}) error
    PublishToUser(userID uint, eventType string, data map[string]interface{}) error
    PublishToRoom(roomID string, eventType string, data map[string]interface{}) error
}

// Connection represents a WebSocket connection
type Connection interface {
    ID() string
    UserID() uint
    TenantID() uint
    WebsiteID() uint
    Send(message *Message) error
    SendEvent(eventType string, data map[string]interface{}) error
    SendError(code, message string) error
    Close() error
    IsAlive() bool
    JoinRoom(roomID string) error
    LeaveRoom(roomID string) error
    GetRooms() []string
}

// EventRouter manages event routing and handling
type EventRouter interface {
    RegisterHandler(eventType string, handler EventHandler)
    RegisterPublisher(eventType string, publisher EventPublisher)
    RouteEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}) error
    PublishEvent(eventType string, data map[string]interface{}) error
}

// Hub manages all WebSocket connections
type Hub interface {
    Start() error
    Stop() error
    RegisterConnection(conn *websocket.Conn, userID, tenantID, websiteID uint) (Connection, error)
    UnregisterConnection(connID string) error
    GetConnection(connID string) (Connection, bool)
    GetUserConnections(userID uint) []Connection
    BroadcastToRoom(roomID string, message *Message) error
    BroadcastToUser(userID uint, message *Message) error
    CreateRoom(roomID string, maxUsers int) error
    DeleteRoom(roomID string) error
}

// Message represents a WebSocket message
type Message struct {
    Type      string                 `json:"type"`
    Data      map[string]interface{} `json:"data,omitempty"`
    Error     *ErrorDetail          `json:"error,omitempty"`
    Timestamp int64                 `json:"timestamp"`
    RequestID string                `json:"request_id,omitempty"`
}

type ErrorDetail struct {
    Code    string `json:"code"`
    Message string `json:"message"`
}
```

### internal/pkg/socket/event_router.go

```go
package socket

import (
    "context"
    "fmt"
    "sync"
    "wnapi/internal/pkg/logger"
)

type eventRouter struct {
    handlers   map[string]EventHandler
    publishers map[string][]EventPublisher
    hub        Hub
    logger     logger.Logger
    mu         sync.RWMutex
}

func NewEventRouter(hub Hub, logger logger.Logger) EventRouter {
    return &eventRouter{
        handlers:   make(map[string]EventHandler),
        publishers: make(map[string][]EventPublisher),
        hub:        hub,
        logger:     logger,
    }
}

func (r *eventRouter) RegisterHandler(eventType string, handler EventHandler) {
    r.mu.Lock()
    defer r.mu.Unlock()
    
    r.handlers[eventType] = handler
    r.logger.Info("Registered socket event handler", "event_type", eventType)
}

func (r *eventRouter) RegisterPublisher(eventType string, publisher EventPublisher) {
    r.mu.Lock()
    defer r.mu.Unlock()
    
    if r.publishers[eventType] == nil {
        r.publishers[eventType] = make([]EventPublisher, 0)
    }
    r.publishers[eventType] = append(r.publishers[eventType], publisher)
    r.logger.Info("Registered socket event publisher", "event_type", eventType)
}

func (r *eventRouter) RouteEvent(ctx context.Context, conn Connection, eventType string, data map[string]interface{}) error {
    r.mu.RLock()
    handler, exists := r.handlers[eventType]
    r.mu.RUnlock()
    
    if !exists {
        return fmt.Errorf("no handler registered for event type: %s", eventType)
    }
    
    // Add connection context to data
    data["_connection_id"] = conn.ID()
    data["_user_id"] = conn.UserID()
    data["_tenant_id"] = conn.TenantID()
    
    return handler(ctx, conn, data)
}

func (r *eventRouter) PublishEvent(eventType string, data map[string]interface{}) error {
    r.mu.RLock()
    publishers, exists := r.publishers[eventType]
    r.mu.RUnlock()
    
    if !exists {
        r.logger.Warn("No publishers registered for event type", "event_type", eventType)
        return nil
    }
    
    for _, publisher := range publishers {
        if err := publisher.Publish(eventType, data); err != nil {
            r.logger.Error("Failed to publish event",
                "event_type", eventType,
                "error", err,
            )
        }
    }
    
    return nil
}
```

## 5. Module Integration Pattern

### modules/auth/socket_handlers/events.go

```go
package socket_handlers

// Auth module event types
const (
    EventAuthLogin          = "auth.login"
    EventAuthLogout         = "auth.logout"
    EventAuthRefreshToken   = "auth.refresh_token"
    EventAuthPasswordReset  = "auth.password_reset"
    
    // Published events
    EventUserAuthenticated  = "user.authenticated"
    EventUserLoggedOut     = "user.logged_out"
    EventLoginFailed       = "user.login_failed"
)

// Event data structures
type LoginEventData struct {
    Email    string `json:"email"`
    Password string `json:"password"`
    Remember bool   `json:"remember"`
}

type UserAuthenticatedData struct {
    UserID     uint      `json:"user_id"`
    TenantID   uint      `json:"tenant_id"`
    Email      string    `json:"email"`
    LoginTime  time.Time `json:"login_time"`
    IPAddress  string    `json:"ip_address"`
}
```

### modules/auth/socket_handlers/handlers.go

```go
package socket_handlers

import (
    "context"
    "wnapi/internal/pkg/socket"
    "wnapi/modules/auth/dto"
    "wnapi/modules/auth/internal"
)

type AuthSocketHandlers struct {
    authService internal.AuthService
    logger      logger.Logger
}

func NewAuthSocketHandlers(authService internal.AuthService, logger logger.Logger) *AuthSocketHandlers {
    return &AuthSocketHandlers{
        authService: authService,
        logger:      logger,
    }
}

func (h *AuthSocketHandlers) HandleLogin(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
    var eventData LoginEventData
    if err := mapToStruct(data, &eventData); err != nil {
        return conn.SendError("INVALID_DATA", "Invalid login data format")
    }
    
    // Convert to service DTO
    req := dto.LoginRequest{
        Email:    eventData.Email,
        Password: eventData.Password,
    }
    
    // Perform login
    resp, err := h.authService.Login(ctx, req)
    if err != nil {
        h.logger.Error("Socket login failed",
            "user_id", conn.UserID(),
            "email", req.Email,
            "error", err,
        )
        return conn.SendError("LOGIN_FAILED", err.Error())
    }
    
    // Send success response
    return conn.SendEvent("auth.login_success", map[string]interface{}{
        "access_token":  resp.AccessToken,
        "refresh_token": resp.RefreshToken,
        "user_id":       resp.UserID,
        "expires_in":    resp.AccessTokenExpiresIn,
    })
}

func (h *AuthSocketHandlers) HandleLogout(ctx context.Context, conn socket.Connection, data map[string]interface{}) error {
    // Perform logout logic
    err := h.authService.Logout(ctx, conn.UserID())
    if err != nil {
        return conn.SendError("LOGOUT_FAILED", err.Error())
    }
    
    // Force disconnect
    return conn.Close()
}
```

### modules/auth/service/auth_service.go (Integration)

```go
package service

import (
    "wnapi/internal/pkg/socket"
    "wnapi/modules/auth/socket_handlers"
)

func (s *Service) RegisterSocketHandlers(router socket.EventRouter) {
    handlers := socket_handlers.NewAuthSocketHandlers(s, s.logger)
    
    // Register event handlers
    router.RegisterHandler(socket_handlers.EventAuthLogin, handlers.HandleLogin)
    router.RegisterHandler(socket_handlers.EventAuthLogout, handlers.HandleLogout)
    router.RegisterHandler(socket_handlers.EventAuthRefreshToken, handlers.HandleRefreshToken)
    
    // Register as publisher for cross-module events
    router.RegisterPublisher(socket_handlers.EventUserAuthenticated, s)
    router.RegisterPublisher(socket_handlers.EventLoginFailed, s)
}

// Implement EventPublisher interface
func (s *Service) Publish(eventType string, data map[string]interface{}) error {
    // Handle publishing logic
    return s.socketPublisher.PublishEvent(eventType, data)
}
```

## 6. Benefits của approach này

### ✅ **Centralized Management**
- Single WebSocket server
- Unified connection handling
- Consistent authentication & authorization
- Shared rate limiting & monitoring

### ✅ **Module Autonomy**
- Modules register their own handlers
- Business logic stays in modules
- Event-driven communication
- No tight coupling between modules

### ✅ **Scalability**
- Easy to add new modules
- Event routing can be optimized
- Connection pooling & load balancing
- Horizontal scaling support

### ✅ **Maintainability**
- Single source of truth for WebSocket logic
- Consistent error handling
- Unified logging & monitoring
- Easy to debug and test

### ✅ **Performance**
- Shared connection pool
- Efficient message routing
- Reduced memory footprint
- Better resource utilization

## 7. Frontend Usage Example

```javascript
// Frontend WebSocket client
const socket = new WebSocket('ws://localhost:8080/ws');

// Login via WebSocket
socket.send(JSON.stringify({
    type: 'auth.login',
    data: {
        email: '<EMAIL>',
        password: 'password123'
    },
    request_id: 'req_123'
}));

// Handle auth success
socket.onmessage = (event) => {
    const message = JSON.parse(event.data);
    
    switch(message.type) {
        case 'auth.login_success':
            console.log('Logged in:', message.data);
            // Store tokens, update UI
            break;
            
        case 'user.authenticated':
            console.log('User authenticated event:', message.data);
            // Update online status, etc.
            break;
            
        case 'seat.reserved':
            console.log('Seat reserved:', message.data);
            // Update seat UI
            break;
    }
};
```

Kiến trúc này cho phép **tái sử dụng infrastructure** trong khi vẫn **duy trì tính module** và **flexibility** cho từng business domain.