# Notification Module WebSocket Integration Flow

## 1. 🏗️ Kiến Trúc Tổng Quan

```
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend Clients                         │
│  ┌──────────────┐ ┌──────────────┐ ┌──────────────────────────┐  │
│  │ Web App      │ │ Mobile App   │ │ Admin Dashboard          │  │
│  │ - Blog       │ │ - Shopping   │ │ - Notification Center    │  │
│  │ - E-commerce │ │ - Notifications│ │ - User Management        │  │
│  └──────────────┘ └──────────────┘ └──────────────────────────┘  │
└─────────────────┬───────────────────┬─────────────────────────────┘
                  │ WebSocket         │ HTTP API
                  │ Connections       │ Calls
                  ▼                   ▼
┌─────────────────────────────────────────────────────────────────┐
│                 Central Socket Service                          │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │               WebSocket Hub                             │   │
│  │  • Manage 1000+ concurrent connections                 │   │
│  │  • User authentication & authorization                 │   │
│  │  • Room management (user rooms, topic rooms)          │   │
│  │  • Message routing & broadcasting                      │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │               Event Router                              │   │
│  │  • Route "notification.*" events                       │   │
│  │  • Handle subscription management                      │   │
│  │  • Transform & validate messages                       │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────┬───────────────────────────────────────────────┘
                  │ Event Distribution
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│               Notification Module                               │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │             Socket Handlers                             │   │
│  │  • Handle subscription requests                        │   │
│  │  • Process "mark as read" events                       │   │
│  │  • Send notification lists                             │   │
│  │  • Manage user preferences                             │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │           Business Logic Services                      │   │
│  │  • Create notifications                                │   │
│  │  • Template processing                                 │   │
│  │  • Multi-channel delivery                             │   │
│  │  • User subscription management                        │   │
│  └─────────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │            Delivery Channels                           │   │
│  │  • WebSocket Channel                                   │   │
│  │  • Email Channel                                       │   │
│  │  • SMS Channel                                         │   │
│  │  • Push Notification Channel                           │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────┬───────────────────────────────────────────────┘
                  │ Cross-Module Events
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Other Modules                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────────┐ │
│  │ Auth Module │ │ Blog Module │ │ E-commerce Module           │ │
│  │ • Login     │ │ • New post  │ │ • Order status             │ │
│  │ • Register  │ │ • Comments  │ │ • Payment confirmation     │ │
│  │ • Password  │ │ • Likes     │ │ • Inventory alerts         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 2. 🔄 Connection Flow - Kết Nối Ban Đầu

```
Client                 Socket Hub              Event Router           Notification Module
  │                         │                       │                         │
  │ 1. WebSocket Connect    │                       │                         │
  ├─────────────────────────▶                       │                         │
  │                         │                       │                         │
  │ 2. JWT Authentication   │                       │                         │
  ├─────────────────────────▶                       │                         │
  │                         │                       │                         │
  │ 3. Connection Success   │                       │                         │
  ◀─────────────────────────┤                       │                         │
  │                         │                       │                         │
  │ 4. Subscribe to         │                       │                         │
  │    Notifications        │                       │                         │
  ├─────────────────────────▶ 5. Route Event       │                         │
  │                         ├───────────────────────▶ 6. Handle Subscribe    │
  │                         │                       ├─────────────────────────▶
  │                         │                       │                         │
  │                         │                       │ 7. Create Subscription │
  │                         │                       ◀─────────────────────────┤
  │                         │                       │                         │
  │                         │ 8. Join User Room     │                         │
  │                         ◀───────────────────────┤                         │
  │                         │                       │                         │
  │ 9. Subscription Success │                       │                         │
  ◀─────────────────────────┴───────────────────────┤                         │
  │                                                 │                         │
  │ 10. Send Unread Count                           │                         │
  ◀─────────────────────────────────────────────────┴─────────────────────────┤
```

### 📋 Chi Tiết Từng Bước:

**Bước 1-3: Kết nối WebSocket**
- Client mở WebSocket connection đến `/ws`
- Socket Hub xác thực JWT token từ query params hoặc headers
- Tạo Connection object với thông tin user (userID, tenantID, websiteID)

**Bước 4-6: Đăng ký nhận notifications**
- Client gửi event `notification.subscribe` với preferences
- Event Router phân tích và route đến Notification Module
- Socket Handler trong Notification Module xử lý request

**Bước 7-8: Thiết lập subscription**
- Tạo/cập nhật subscription record trong database
- Join user vào room riêng: `notifications_user_{userID}`
- Có thể join thêm các topic rooms nếu cần

**Bước 9-10: Phản hồi**
- Gửi confirmation về subscription thành công
- Gửi current unread count và thống kê

## 3. 📨 Notification Sending Flow - Gửi Thông Báo

```
Trigger Source           Notification Service      Socket Publisher          Socket Hub              Client
     │                           │                       │                     │                      │
     │ 1. Create Notification    │                       │                     │                      │
     ├───────────────────────────▶                       │                     │                      │
     │   (Blog post, Order, etc) │                       │                     │                      │
     │                           │                       │                     │                      │
     │                           │ 2. Save to Database   │                     │                      │
     │                           ├─────────────────────── ▶ DB                 │                      │
     │                           │                       │                     │                      │
     │                           │ 3. Get Subscribers    │                     │                      │
     │                           ├─────────────────────── ▶ DB                 │                      │
     │                           │                       │                     │                      │
     │                           │ 4. Process Template   │                     │                      │
     │                           │    & Personalization  │                     │                      │
     │                           │                       │                     │                      │
     │                           │ 5. Multi-Channel      │                     │                      │
     │                           │    Delivery           │                     │                      │
     │                           ├───────────────────────▶                     │                      │
     │                           │                       │                     │                      │
     │                           │                       │ 6. WebSocket        │                      │
     │                           │                       │    Broadcast        │                      │
     │                           │                       ├─────────────────────▶                      │
     │                           │                       │                     │                      │
     │                           │                       │                     │ 7. Send to Client   │
     │                           │                       │                     ├──────────────────────▶
     │                           │                       │                     │                      │
     │                           │                       │                     │ 8. Update UI        │
     │                           │                       │                     │ ◀────────────────────┤
     │                           │                       │                     │                      │
     │                           │ 9. Update Unread      │                     │                      │
     │                           │    Count               │                     │                      │
     │                           ├───────────────────────▶─────────────────────▶──────────────────────▶
```

### 📋 Chi Tiết Delivery Channels:

**WebSocket Channel (Real-time)**
- Immediate delivery đến active connections
- Fallback: Store trong database nếu user offline
- Support bulk notifications

**Email Channel (Async)**
- Queue-based processing
- Template rendering với personalization
- Delivery tracking và retry logic

**SMS Channel (Critical)**
- Chỉ cho notifications quan trọng
- Rate limiting per user
- Cost optimization

**Push Channel (Mobile)**
- Firebase/APNS integration
- Device token management
- Silent vs alert notifications

## 4. 🎯 Real-time Interaction Flow - Tương Tác Thời Gian Thực

```
Client A              Socket Hub              Notification Module         Client B
   │                      │                          │                      │
   │ 1. Mark as Read      │                          │                      │
   ├──────────────────────▶ 2. Route Event          │                      │
   │                      ├──────────────────────────▶                      │
   │                      │                          │                      │
   │                      │                          │ 3. Update Database  │
   │                      │                          ├──────────────────── ▶ DB
   │                      │                          │                      │
   │                      │ 4. Broadcast Update      │                      │
   │                      ◀──────────────────────────┤                      │
   │                      │                          │                      │
   │ 5. Confirmation      │                          │                      │
   ◀──────────────────────┤                          │                      │
   │                      │                          │                      │
   │                      │ 6. Update Other Devices  │                      │
   │                      ├──────────────────────────┼──────────────────────▶
   │                      │                          │                      │
   │                      │                          │                      │ 7. Sync UI
   │                      │                          │                      │ ◀─────────┤
```

### 🔄 Multi-Device Synchronization:

**Scenario: User đọc notification trên mobile**
1. Mobile app gửi `notification.mark_read`
2. Database được update
3. Unread count được recalculate
4. Broadcast update đến tất cả devices của user:
   - Web browser: Update badge count
   - Other mobile sessions: Sync read status
   - Desktop app: Update notification list

## 5. 🏠 Room Management Strategy

```
Room Structure:
┌────────────────────────────────────────────────────────────┐
│                     Room Hierarchy                        │
│                                                            │
│  📱 User Rooms (Personal)                                 │
│  ├─ notifications_user_123                                │
│  ├─ notifications_user_456                                │
│  └─ notifications_user_789                                │
│                                                            │
│  🏢 Tenant Rooms (Organization)                           │
│  ├─ notifications_tenant_1                                │
│  ├─ notifications_tenant_2                                │
│  └─ notifications_tenant_3                                │
│                                                            │
│  🌐 Website Rooms (Multi-site)                            │
│  ├─ notifications_website_blog                            │
│  ├─ notifications_website_shop                            │
│  └─ notifications_website_admin                           │
│                                                            │
│  📂 Topic Rooms (Content-based)                           │
│  ├─ notifications_topic_orders                            │
│  ├─ notifications_topic_posts                             │
│  ├─ notifications_topic_comments                          │
│  └─ notifications_topic_system                            │
│                                                            │
│  🎯 Event Rooms (Temporary)                               │
│  ├─ notifications_event_flash_sale                        │
│  ├─ notifications_event_webinar_123                       │
│  └─ notifications_event_maintenance                       │
└────────────────────────────────────────────────────────────┘
```

### 🎯 Room Selection Logic:

**Personal Notifications**: `notifications_user_{userID}`
- Account updates, personal messages, order status

**Team/Organization**: `notifications_tenant_{tenantID}`
- Company announcements, team updates

**Content-based**: `notifications_topic_{topic}`
- Blog comments, product reviews, forum discussions

**Event-based**: `notifications_event_{eventID}`
- Live events, flash sales, maintenance windows

## 6. 📊 Subscription Management Flow

```
User Preferences          Subscription Service         Database              WebSocket
      │                          │                        │                    │
      │ 1. Update Preferences    │                        │                    │
      ├──────────────────────────▶                        │                    │
      │                          │                        │                    │
      │                          │ 2. Validate Settings  │                    │
      │                          │                        │                    │
      │                          │ 3. Save Preferences   │                    │
      │                          ├────────────────────────▶                    │
      │                          │                        │                    │
      │                          │ 4. Update Room         │                    │
      │                          │    Memberships         │                    │
      │                          ├────────────────────────┼────────────────────▶
      │                          │                        │                    │
      │ 5. Confirmation          │                        │                    │
      ◀──────────────────────────┤                        │                    │
```

### ⚙️ Subscription Options:

**Notification Types**
- `order_updates`: Order status changes
- `blog_comments`: Comments on user's posts
- `system_alerts`: System maintenance, security
- `marketing`: Promotions, newsletters
- `social`: Likes, follows, mentions

**Delivery Channels**
- `websocket`: Real-time in-app
- `email`: Email notifications
- `sms`: SMS for critical updates
- `push`: Mobile push notifications

**Timing Preferences**
- `immediate`: Send right away
- `digest_hourly`: Batch every hour
- `digest_daily`: Daily summary
- `digest_weekly`: Weekly summary

**Priority Filtering**
- `all`: All notifications
- `high_only`: Only high priority
- `critical_only`: Only critical alerts

## 7. 🔄 Cross-Module Integration

```
Auth Module              Notification Module         E-commerce Module
     │                          │                           │
     │ User Login Event         │                           │
     ├──────────────────────────▶                           │
     │                          │                           │
     │                          │ Welcome Notification     │
     │                          │                           │
     │                          │ ◀─────────── Order Created │
     │                          │                           ├─┐
     │                          │                           │ │ 1. Save Order
     │                          │ Create Notification       │ │
     │                          ◀───────────────────────────┤ │
     │                          │                           │ │
     │                          │ ─┐                        │ │
     │                          │  │ 2. Process Template    │ │
     │                          │  │ 3. Send Multi-channel  │ │
     │                          │  │ 4. WebSocket Broadcast │ │
     │                          │ ◀┘                        │ │
     │                          │                           │ │
     │ Real-time Update         │                           │ │
     ◀──────────────────────────┤                           │ │
     │                          │                           ├─┘
```

### 🔗 Event Publishing Pattern:

**From Other Modules**:
```
// Auth Module publishes
user.login_success → "Welcome back!" notification
user.password_changed → "Security alert" notification
user.account_locked → "Account security" notification

// E-commerce Module publishes  
order.created → "Order confirmation" notification
order.shipped → "Shipping update" notification
payment.failed → "Payment issue" notification

// Blog Module publishes
post.comment_added → "New comment" notification
post.published → "New post" notification
user.mentioned → "You were mentioned" notification
```

## 8. 📈 Performance & Scaling Considerations

### 🎯 Connection Management:
- **Connection Pooling**: Reuse connections efficiently
- **Heartbeat/Ping**: Keep connections alive
- **Graceful Degradation**: Handle connection drops
- **Load Balancing**: Distribute connections across servers

### 🚀 Message Broadcasting:
- **Batch Processing**: Group similar notifications
- **Priority Queues**: Critical notifications first
- **Rate Limiting**: Prevent spam/flooding
- **Memory Management**: Cleanup expired messages

### 💾 Data Management:
- **Notification Archiving**: Move old notifications
- **Connection State**: Store active connections
- **Subscription Caching**: Cache user preferences
- **Analytics Tracking**: Monitor delivery rates

### 🔧 Monitoring & Debugging:
- **Connection Metrics**: Active connections, drops
- **Delivery Metrics**: Success rates per channel
- **Performance Metrics**: Latency, throughput
- **Error Tracking**: Failed deliveries, connection issues

Kiến trúc này đảm bảo notification module có thể scale efficiently và tích hợp seamlessly với WebSocket infrastructure while maintaining clean separation of concerns.