package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// AIProvider enum để xác định loại provider
type AIProvider string

const (
	ProviderLMStudio AIProvider = "lmstudio"
	ProviderOpenAI   AIProvider = "openai"
	ProviderGemini   AIProvider = "gemini"
)

// AIConfig chứa thông tin cấu hình cho từng provider
type AIConfig struct {
	Provider AIProvider `json:"provider"`
	BaseURL  string     `json:"base_url"`
	APIKey   string     `json:"api_key"`
	Model    string     `json:"model"`
	Timeout  int        `json:"timeout"` // seconds
}

// Message cấu trúc tin nhắn chung
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// AIRequest cấu trúc request chung
type AIRequest struct {
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
}

// AIResponse cấu trúc response chung
type AIResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// AIService interface định nghĩa các method cần thiết
type AIService interface {
	GenerateText(ctx context.Context, request AIRequest) (*AIResponse, error)
	SetConfig(config AIConfig)
	GetConfig() AIConfig
}

// AIClient implementation của AIService
type AIClient struct {
	config     AIConfig
	httpClient *http.Client
}

// NewAIClient tạo client mới
func NewAIClient(config AIConfig) *AIClient {
	timeout := 30 * time.Second
	if config.Timeout > 0 {
		timeout = time.Duration(config.Timeout) * time.Second
	}

	return &AIClient{
		config: config,
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// SetConfig cập nhật cấu hình
func (c *AIClient) SetConfig(config AIConfig) {
	c.config = config
	
	timeout := 30 * time.Second
	if config.Timeout > 0 {
		timeout = time.Duration(config.Timeout) * time.Second
	}
	c.httpClient.Timeout = timeout
}

// GetConfig lấy cấu hình hiện tại
func (c *AIClient) GetConfig() AIConfig {
	return c.config
}

// GenerateText gọi API để generate text
func (c *AIClient) GenerateText(ctx context.Context, request AIRequest) (*AIResponse, error) {
	switch c.config.Provider {
	case ProviderOpenAI:
		return c.callOpenAI(ctx, request)
	case ProviderLMStudio:
		return c.callLMStudio(ctx, request)
	case ProviderGemini:
		return c.callGemini(ctx, request)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", c.config.Provider)
	}
}

// callOpenAI gọi OpenAI API
func (c *AIClient) callOpenAI(ctx context.Context, request AIRequest) (*AIResponse, error) {
	// Chuẩn bị request body
	requestBody := map[string]interface{}{
		"model":       c.config.Model,
		"messages":    request.Messages,
		"temperature": request.Temperature,
		"max_tokens":  request.MaxTokens,
		"stream":      request.Stream,
	}

	return c.makeHTTPRequest(ctx, "/v1/chat/completions", requestBody, map[string]string{
		"Authorization": "Bearer " + c.config.APIKey,
		"Content-Type":  "application/json",
	})
}

// callLMStudio gọi LM Studio API
func (c *AIClient) callLMStudio(ctx context.Context, request AIRequest) (*AIResponse, error) {
	// LM Studio thường không cần API key
	requestBody := map[string]interface{}{
		"model":       c.config.Model,
		"messages":    request.Messages,
		"temperature": request.Temperature,
		"max_tokens":  request.MaxTokens,
		"stream":      request.Stream,
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}
	
	// Thêm API key nếu có
	if c.config.APIKey != "" {
		headers["Authorization"] = "Bearer " + c.config.APIKey
	}

	return c.makeHTTPRequest(ctx, "/v1/chat/completions", requestBody, headers)
}

// callGemini gọi Google Gemini API
func (c *AIClient) callGemini(ctx context.Context, request AIRequest) (*AIResponse, error) {
	// Gemini có format khác một chút
	contents := make([]map[string]interface{}, 0)
	for _, msg := range request.Messages {
		if msg.Role == "system" {
			// Gemini không có system role, merge vào user message
			continue
		}
		
		role := msg.Role
		if role == "assistant" {
			role = "model"
		}
		
		contents = append(contents, map[string]interface{}{
			"role": role,
			"parts": []map[string]string{
				{"text": msg.Content},
			},
		})
	}

	requestBody := map[string]interface{}{
		"contents": contents,
		"generationConfig": map[string]interface{}{
			"temperature":  request.Temperature,
			"maxOutputTokens": request.MaxTokens,
		},
	}

	endpoint := fmt.Sprintf("/v1beta/models/%s:generateContent", c.config.Model)
	return c.makeHTTPRequest(ctx, endpoint, requestBody, map[string]string{
		"Content-Type": "application/json",
		"x-goog-api-key": c.config.APIKey,
	})
}

// makeHTTPRequest thực hiện HTTP request
func (c *AIClient) makeHTTPRequest(ctx context.Context, endpoint string, requestBody interface{}, headers map[string]string) (*AIResponse, error) {
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	url := c.config.BaseURL + endpoint
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error %d: %s", resp.StatusCode, string(body))
	}

	var aiResponse AIResponse
	if err := json.NewDecoder(resp.Body).Decode(&aiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &aiResponse, nil
}

// ConfigManager để quản lý nhiều config
type ConfigManager struct {
	configs map[string]AIConfig
	current string
}

// NewConfigManager tạo config manager mới
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		configs: make(map[string]AIConfig),
	}
}

// AddConfig thêm config mới
func (cm *ConfigManager) AddConfig(name string, config AIConfig) {
	cm.configs[name] = config
}

// SetCurrent đặt config hiện tại
func (cm *ConfigManager) SetCurrent(name string) error {
	if _, exists := cm.configs[name]; !exists {
		return fmt.Errorf("config %s not found", name)
	}
	cm.current = name
	return nil
}

// GetCurrent lấy config hiện tại
func (cm *ConfigManager) GetCurrent() (AIConfig, error) {
	if cm.current == "" {
		return AIConfig{}, fmt.Errorf("no current config set")
	}
	config, exists := cm.configs[cm.current]
	if !exists {
		return AIConfig{}, fmt.Errorf("current config %s not found", cm.current)
	}
	return config, nil
}

// Example usage
func main() {
	// Tạo config manager
	configManager := NewConfigManager()

	// Thêm các config cho từng provider
	configManager.AddConfig("lmstudio", AIConfig{
		Provider: ProviderLMStudio,
		BaseURL:  "http://localhost:1234",
		Model:    "mistral-7b-instruct",
		Timeout:  30,
	})

	configManager.AddConfig("openai", AIConfig{
		Provider: ProviderOpenAI,
		BaseURL:  "https://api.openai.com",
		APIKey:   "your-openai-api-key",
		Model:    "gpt-3.5-turbo",
		Timeout:  30,
	})

	configManager.AddConfig("gemini", AIConfig{
		Provider: ProviderGemini,
		BaseURL:  "https://generativelanguage.googleapis.com",
		APIKey:   "your-gemini-api-key",
		Model:    "gemini-pro",
		Timeout:  30,
	})

	// Chuyển đổi giữa các provider
	configManager.SetCurrent("lmstudio")
	
	currentConfig, err := configManager.GetCurrent()
	if err != nil {
		panic(err)
	}

	// Tạo AI client
	aiClient := NewAIClient(currentConfig)

	// Tạo request
	request := AIRequest{
		Messages: []Message{
			{Role: "user", Content: "Viết một đoạn code Go để connect database"},
		},
		Temperature: 0.7,
		MaxTokens:   1000,
	}

	// Gọi API
	ctx := context.Background()
	response, err := aiClient.GenerateText(ctx, request)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	// In kết quả
	if len(response.Choices) > 0 {
		fmt.Printf("Response: %s\n", response.Choices[0].Message.Content)
	}

	// Chuyển sang OpenAI
	fmt.Println("\n--- Switching to OpenAI ---")
	configManager.SetCurrent("openai")
	newConfig, _ := configManager.GetCurrent()
	aiClient.SetConfig(newConfig)

	// Gọi lại với provider mới
	response2, err := aiClient.GenerateText(ctx, request)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	if len(response2.Choices) > 0 {
		fmt.Printf("Response: %s\n", response2.Choices[0].Message.Content)
	}
}