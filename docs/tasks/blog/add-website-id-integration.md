# Task: Thêm Website ID Integration vào Blog Module

## 🎯 PROGRESS UPDATE
**Phase 1: Database Schema Migration - ✅ COMPLETED**

Đã hoàn thành cập nhật tất cả migration files để tích hợp website_id:
- ✅ blog_posts: Thêm website_id và cập nhật indexes
- ✅ blog_categories: Thêm website_id và cập nhật indexes  
- ✅ blog_tags: Thêm website_id và cập nhật indexes
- ✅ blog_authors: Thêm website_id và cập nhật indexes
- ✅ blog_schedules: Thêm website_id và cập nhật indexes
- ✅ blog_blocks: Thêm tenant_id, website_id và cập nhật indexes
- ✅ blog_timelines: Thêm tenant_id, website_id và cập nhật indexes

**Next Steps:** Phase 2 - Model Updates

---

## Tổng Quan
Thêm tích hợp `website_id` vào module blog để hỗ trợ multi-website architecture, cho phép mỗi bài viết blog thuộc về một website cụ thể trong tenant.

## Mục Tiêu
- Thêm `website_id` vào blog models và database schema
- Cập nhật repositories, services và APIs để hỗ trợ website context
- Đảm bảo data isolation giữa các websites
- Duy trì backward compatibility với existing data

## Phân Tích Hiện Tại

### Blog Module Structure
```
modules/blog/
├── models/           # Blog models (BlogPost, Category, Tag, Author, etc.)
├── repository/       # Data access layer  
├── service/          # Business logic
├── api/             # HTTP handlers và routes
├── migrations/      # Database migrations
└── dto/             # Request/Response DTOs
```

### Core Models Cần Cập Nhật
1. `BlogPost` - Main blog content
2. `Category` - Blog categories  
3. `Tag` - Blog tags
4. `Author` - Blog authors
5. `BlogBlock` - Blog content blocks
6. `BlogTimeline` - Blog timelines

### Database Tables Cần Migration
1. `blog_posts` - Thêm `website_id` column
2. `blog_categories` - Thêm `website_id` column  
3. `blog_tags` - Thêm `website_id` column
4. `blog_authors` - Thêm `website_id` column
5. `blog_blocks` - Thêm `website_id` column
6. `blog_timelines` - Thêm `website_id` column

## Implementation Plan

### Phase 1: Database Schema Migration (2 giờ) - ✅ COMPLETED

**Status: COMPLETED** - All migration files have been updated to include website_id integration.

#### 1.1 Tạo Migration Files
```bash
# Tạo migration cho từng table
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_posts
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_categories  
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_tags
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_authors
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_blocks
go run cmd/migrate/main.go -action=create -module=blog -name=add_website_id_to_blog_timelines
```

#### 1.2 Migration Content Strategy
**Up Migration:**
```sql
-- Thêm website_id column với default value và foreign key
ALTER TABLE blog_posts 
ADD COLUMN website_id INT UNSIGNED AFTER tenant_id,
ADD INDEX idx_blog_posts_website_tenant (website_id, tenant_id);

-- Update existing data với website default của tenant
UPDATE blog_posts bp 
JOIN (
    SELECT w.tenant_id, MIN(w.website_id) as default_website_id
    FROM site_websites w 
    WHERE w.status = 'active'
    GROUP BY w.tenant_id
) dw ON bp.tenant_id = dw.tenant_id
SET bp.website_id = dw.default_website_id;

-- Thêm NOT NULL constraint sau khi data đã được populate
ALTER TABLE blog_posts 
MODIFY COLUMN website_id INT UNSIGNED NOT NULL;

-- Thêm foreign key constraint
ALTER TABLE blog_posts
ADD CONSTRAINT fk_blog_posts_website 
FOREIGN KEY (website_id) REFERENCES site_websites(website_id) 
ON DELETE RESTRICT ON UPDATE CASCADE;
```

**Down Migration:**
```sql
-- Remove foreign key và column
ALTER TABLE blog_posts 
DROP FOREIGN KEY fk_blog_posts_website,
DROP INDEX idx_blog_posts_website_tenant,
DROP COLUMN website_id;
```

### Phase 2: Model Updates (1.5 giờ)

#### 2.1 Update BlogPost Model
```go
// modules/blog/models/blog_post.go
type BlogPost struct {
    PostID        uint                  `gorm:"column:post_id;primaryKey;autoIncrement" json:"post_id"`
    TenantID      uint                  `gorm:"column:tenant_id;not null" json:"tenant_id"`
    WebsiteID     uint                  `gorm:"column:website_id;not null" json:"website_id"` // NEW
    Title         string                `gorm:"column:title;size:255;not null" json:"title"`
    // ... existing fields
    
    // Relations
    Website       *website.Website      `gorm:"foreignKey:WebsiteID" json:"website,omitempty"` // NEW
}
```

#### 2.2 Update Các Models Khác
- Tương tự cho `Category`, `Tag`, `Author`, `BlogBlock`, `BlogTimeline`
- Thêm `WebsiteID` field và relation

### Phase 3: Repository Layer Updates (3 giờ)

#### 3.1 Update Repository Interfaces
```go
// modules/blog/repository/interfaces.go
type PostRepository interface {
    // Update existing methods để include website_id
    Create(ctx context.Context, tenantID, websiteID uint, post *models.BlogPost) error
    GetByID(ctx context.Context, tenantID, websiteID, postID uint) (*models.BlogPost, error)
    List(ctx context.Context, tenantID, websiteID uint, req request.ListPostsRequest) ([]*models.BlogPost, string, bool, error)
    
    // Migration helper methods
    GetByIDWithoutWebsite(ctx context.Context, tenantID, postID uint) (*models.BlogPost, error) // For migration
    UpdateWebsiteID(ctx context.Context, postID, websiteID uint) error // For migration
}
```

#### 3.2 Update MySQL Implementations
```go
// modules/blog/repository/mysql/post_repository.go
func (r *PostRepository) Create(ctx context.Context, tenantID, websiteID uint, post *models.BlogPost) error {
    post.TenantID = tenantID
    post.WebsiteID = websiteID
    // ... rest of implementation
}

func (r *PostRepository) List(ctx context.Context, tenantID, websiteID uint, req request.ListPostsRequest) ([]*models.BlogPost, string, bool, error) {
    query := `
        SELECT * FROM blog_posts 
        WHERE tenant_id = ? AND website_id = ?
        AND status IN (?)
        ORDER BY created_at DESC
        LIMIT ?
    `
    // ... implementation
}
```

### Phase 4: Service Layer Updates (2 giờ)

#### 4.1 Update Service Interfaces  
```go
// modules/blog/service/interfaces.go
type PostService interface {
    CreatePost(ctx context.Context, tenantID, websiteID uint, req request.CreatePostRequest) (*response.PostResponse, error)
    GetPost(ctx context.Context, tenantID, websiteID, postID uint) (*response.PostResponse, error)
    ListPosts(ctx context.Context, tenantID, websiteID uint, req request.ListPostsRequest) (*response.ListPostsResponse, error)
    
    // Migration utilities
    MigratePostsToWebsite(ctx context.Context, tenantID, websiteID uint, postIDs []uint) error
}
```

#### 4.2 Update Service Implementations
```go
// modules/blog/service/post_service.go
func (s *postService) CreatePost(ctx context.Context, tenantID, websiteID uint, req request.CreatePostRequest) (*response.PostResponse, error) {
    // Validate website exists and belongs to tenant
    if err := s.validateWebsiteAccess(ctx, tenantID, websiteID); err != nil {
        return nil, err
    }
    
    // Create post with website context
    post := &models.BlogPost{
        TenantID:  tenantID,
        WebsiteID: websiteID,
        Title:     req.Title,
        // ... map other fields
    }
    
    if err := s.postRepo.Create(ctx, tenantID, websiteID, post); err != nil {
        return nil, err
    }
    
    return s.convertToResponse(post), nil
}

func (s *postService) validateWebsiteAccess(ctx context.Context, tenantID, websiteID uint) error {
    // Call website service to verify website exists and belongs to tenant
    website, err := s.websiteService.GetWebsite(ctx, int(tenantID), int(websiteID))
    if err != nil {
        return fmt.Errorf("invalid website: %w", err)
    }
    
    if website.TenantID != int(tenantID) {
        return fmt.Errorf("website does not belong to tenant")
    }
    
    return nil
}
```

### Phase 5: API Layer Updates (2.5 giờ)

#### 5.1 Update DTOs
```go
// modules/blog/dto/request/post_request.go
type CreatePostRequest struct {
    WebsiteID   uint   `json:"website_id" binding:"required"`
    Title       string `json:"title" binding:"required"`
    // ... existing fields
}

type ListPostsRequest struct {
    WebsiteID   uint   `uri:"website_id" binding:"required"`
    // ... existing pagination fields
}
```

#### 5.2 Update Route Handlers
```go
// modules/blog/api/handlers/post_handler.go
func (h *PostHandler) CreatePost(c *gin.Context) {
    tenantID := middleware.GetTenantID(c)
    
    var req request.CreatePostRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        // handle error
        return
    }
    
    post, err := h.postService.CreatePost(c.Request.Context(), tenantID, req.WebsiteID, req)
    if err != nil {
        // handle error  
        return
    }
    
    c.JSON(http.StatusCreated, gin.H{
        "data": post,
        "message": "Post created successfully",
    })
}

func (h *PostHandler) ListPosts(c *gin.Context) {
    tenantID := middleware.GetTenantID(c)
    
    var req request.ListPostsRequest
    if err := c.ShouldBindUri(&req); err != nil {
        // handle error
        return
    }
    
    posts, err := h.postService.ListPosts(c.Request.Context(), tenantID, req.WebsiteID, req)
    // ... handle response
}
```

#### 5.3 Update Routes
```go
// modules/blog/api/routes.go
func (h *Handler) RegisterRoutesWithEngine(engine *gin.Engine) error {
    // Update routes để include website_id parameter
    adminGroup := engine.Group("/api/admin/v1/blog")
    
    // Website-scoped routes
    websiteGroup := adminGroup.Group("/websites/:website_id")
    {
        // Posts
        websiteGroup.GET("/posts", h.postHandler.ListPosts)
        websiteGroup.POST("/posts", h.postHandler.CreatePost) 
        websiteGroup.GET("/posts/:id", h.postHandler.GetPost)
        websiteGroup.PUT("/posts/:id", h.postHandler.UpdatePost)
        websiteGroup.DELETE("/posts/:id", h.postHandler.DeletePost)
        
        // Categories
        websiteGroup.GET("/categories", h.categoryHandler.ListCategories)
        websiteGroup.POST("/categories", h.categoryHandler.CreateCategory)
        // ... other category routes
        
        // Tags
        websiteGroup.GET("/tags", h.tagHandler.ListTags)
        websiteGroup.POST("/tags", h.tagHandler.CreateTag)
        // ... other tag routes
    }
    
    // Backward compatibility routes (deprecated)
    legacyGroup := adminGroup.Group("/legacy")
    {
        legacyGroup.GET("/posts", h.postHandler.ListPostsLegacy) // Without website_id
        // ... other legacy routes
    }
    
    return nil
}
```

### Phase 6: Dependencies Integration (1 giờ)

#### 6.1 Add Website Service Dependency
```go
// modules/blog/providers.go
func NewPostService(
    postRepo repository.PostRepository,
    websiteService website.WebsiteService, // NEW DEPENDENCY
    // ... other deps
) service.PostService {
    return &postService{
        postRepo:       postRepo,
        websiteService: websiteService,
        // ... other fields
    }
}
```

#### 6.2 Update FX Module
```go
// modules/blog/fx.go
func (m *BlogModule) Dependencies() []string {
    return []string{"tenant", "auth", "rbac", "socket", "website"} // Add website
}

func (m *BlogModule) Module() fx.Option {
    return fx.Module("blog",
        fx.Provide(
            // ... existing providers
            website.NewWebsiteService, // Provide website service
        ),
        // ... rest of module
    )
}
```

### Phase 7: Migration Utilities (1.5 giờ)

#### 7.1 Data Migration Scripts
```go
// modules/blog/cmd/migrate-website-id/main.go
func main() {
    // Script để migrate existing blog data to websites
    // 1. Get all tenants
    // 2. For each tenant, get default website
    // 3. Update all blog posts with default website_id
    // 4. Validate data integrity
}
```

#### 7.2 Migration Service Methods
```go
// modules/blog/service/migration_service.go
type MigrationService interface {
    MigrateTenantsToDefaultWebsite(ctx context.Context) error
    ValidateWebsiteIntegrity(ctx context.Context, tenantID uint) error
}
```

### Phase 8: Testing & Validation (2 giờ)

#### 8.1 Unit Tests
- Repository layer tests với website_id
- Service layer tests với website validation
- Handler tests với website context

#### 8.2 Integration Tests  
- End-to-end API tests
- Migration scripts validation
- Cross-website data isolation tests

#### 8.3 Performance Tests
- Query performance với website_id indexes
- Migration performance với large datasets

## Migration Strategy

### Rollout Plan
1. **Pre-Migration:** Backup database
2. **Schema Migration:** Run migrations to add website_id columns
3. **Data Migration:** Populate website_id với default values
4. **Code Deployment:** Deploy updated application code
5. **Validation:** Verify data integrity và API functionality
6. **Cleanup:** Remove legacy/temporary migration code

### Rollback Strategy
1. **Code Rollback:** Deploy previous version
2. **Schema Rollback:** Run down migrations
3. **Data Validation:** Verify original data integrity

## Considerations

### Performance Impact
- **Indexes:** Thêm composite indexes (tenant_id, website_id) cho query performance
- **Query Changes:** Tất cả queries sẽ include website_id filter
- **Migration Time:** Large datasets có thể cần maintenance window

### Backward Compatibility
- **Legacy APIs:** Maintain legacy endpoints trong transition period
- **Default Website:** Auto-assign default website cho requests không specify
- **Migration Period:** Support both old và new API patterns

### Data Integrity
- **Foreign Keys:** Ensure referential integrity với website table
- **Validation:** Validate website belongs to tenant trong all operations
- **Constraints:** Prevent orphaned blog data

### Security Considerations  
- **Website Access:** Validate user có permission trên website
- **Cross-Website Access:** Prevent data leakage giữa websites
- **Tenant Isolation:** Maintain existing tenant isolation

## Success Criteria

### Functional Requirements
- ✅ All blog content được scoped to specific websites
- ✅ APIs support website_id parameter
- ✅ Existing data được migrated successfully
- ✅ Website validation hoạt động correctly

### Non-Functional Requirements  
- ✅ Query performance maintained hoặc improved
- ✅ Migration completed without data loss
- ✅ Backward compatibility maintained during transition
- ✅ Security isolation between websites maintained

## Files Modified/Created

### Files Modified/Created

### Modified Migration Files (Phase 1 - COMPLETED)
- ✅ `modules/blog/migrations/001_create_blog_categories.up.sql` - Added website_id column and updated indexes
- ✅ `modules/blog/migrations/002_create_blog_posts.up.sql` - Added website_id column and updated indexes
- ✅ `modules/blog/migrations/004_create_blog_tags.up.sql` - Added website_id column and updated indexes
- ✅ `modules/blog/migrations/010_create_blog_authors.up.sql` - Added website_id column and updated indexes
- ✅ `modules/blog/migrations/011_create_blog_schedules.up.sql` - Added website_id column and updated indexes
- ✅ `modules/blog/migrations/013_create_blog_blocks.up.sql` - Added tenant_id, website_id columns and updated indexes
- ✅ `modules/blog/migrations/015_create_blog_timelines.up.sql` - Added tenant_id, website_id columns and updated indexes

### Modified Model Files
- `modules/blog/models/blog_post.go`
- `modules/blog/models/category.go`  
- `modules/blog/models/tag.go`
- `modules/blog/models/author.go`
- `modules/blog/models/blog_block.go`
- `modules/blog/models/blog_timeline.go`

### Modified Repository Files
- `modules/blog/repository/interfaces.go`
- `modules/blog/repository/mysql/post_repository.go`
- `modules/blog/repository/mysql/category_repository.go`
- (Similar files for other repositories)

### Modified Service Files  
- `modules/blog/service/interfaces.go`
- `modules/blog/service/post_service.go`
- `modules/blog/service/category_service.go`
- (Similar files for other services)

### Modified API Files
- `modules/blog/dto/request/*.go`
- `modules/blog/dto/response/*.go`
- `modules/blog/api/handlers/*.go`
- `modules/blog/api/routes.go`

### Modified Configuration Files
- `modules/blog/providers.go`
- `modules/blog/fx.go`

### New Utility Files
- `modules/blog/cmd/migrate-website-id/main.go`
- `modules/blog/service/migration_service.go`

## Timeline
**Total Estimated Time: 14 giờ**

- Phase 1: Database Schema Migration - 2 giờ
- Phase 2: Model Updates - 1.5 giờ  
- Phase 3: Repository Layer Updates - 3 giờ
- Phase 4: Service Layer Updates - 2 giờ
- Phase 5: API Layer Updates - 2.5 giờ
- Phase 6: Dependencies Integration - 1 giờ
- Phase 7: Migration Utilities - 1.5 giờ
- Phase 8: Testing & Validation - 2 giờ

## Dependencies
- Website module phải hoạt động và stable
- Database migration system
- Testing infrastructure
- Monitoring và logging cho migration process

## Risk Mitigation
- **Data Loss:** Comprehensive backup strategy
- **Performance Degradation:** Staged rollout với monitoring
- **Integration Issues:** Thorough testing với website module
- **Migration Failures:** Rollback procedures và validation checks
