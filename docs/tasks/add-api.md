- thêm migrations
- thêm model
- thêm router
- thêm dto
- thêm service
- thêm repository
- thêm handle
- thêm docs-api bruno
- luôn kiểm tra trước các file đã tồn tại
- nếu là module mới thì:
+ đăng ký cmd/fx-server/main.go
+ update .env : MODULES_ENABLED
+ đăng ký cmd/cli/main.go nếu có
+ đăng ký cmd/migrate/main.go
+ đăng ký cmd/seed/main.go nếu có
- testing:
+ phải kiểm tra route đã đăng ký thành công hiện ở console chưa
+ phải build được dự án: go build cmd/fx-server/main.go
+ phải chạy được dự án: go run cmd/fx-server/main.go
+ phải có thể chạy được các command
+ go run cmd/migrate/main.go -action=up -module=[ten_module]