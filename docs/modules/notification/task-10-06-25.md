# Task 10-06-25: Refactor Test Endpoints và Template API

## Mục tiêu
1. <PERSON> chuyển các test endpoints từ `modules/notification/providers.go` sang `modules/notification/api/handler.go`
2. T<PERSON> chức lại cấu trúc API để dễ bảo trì và mở rộng
3. Hoàn thiện API để test template functionality

## Tình trạng hiện tại

### Test Endpoints trong providers.go
Hiện tại có các test endpoints được định nghĩa trực tiếp trong `RegisterNotificationRoutes()` function:

- `/test-queue-simple` - Test queue đơn giản
- `/test-email-template` - Test gửi email với template
- `/test-email-direct` - Test gửi email trực tiếp (không template)
- `/test-sms` - Test gửi SMS
- `/test-push` - Test gửi push notification

### Template API đã có
Template API đã được implement đầy đủ trong `modules/notification/api/handlers/template_handler.go`:

- `POST /templates` - Tạo template mới
- `GET /templates` - <PERSON><PERSON><PERSON> danh sách templates với pagination
- `GET /templates/:id` - Lấy template theo ID
- `PUT /templates/:id` - Cập nhật template
- `DELETE /templates/:id` - Xóa template
- `GET /templates/code/:code` - Lấy template theo code

## Kế hoạch thực hiện

### Bước 1: Di chuyển test endpoints từ providers.go sang handler.go ✅

**Đã hoàn thành:**
- Thêm import `"wnapi/modules/notification/types"` vào `api/handler.go`
- Bắt đầu di chuyển các test methods vào Handler struct

**Cần hoàn thành:**
- Di chuyển tất cả test endpoints còn lại
- Đăng ký routes trong `RegisterRoutes()` và `RegisterRoutesWithEngine()`
- Xóa test endpoints khỏi `providers.go`

### Bước 2: Tổ chức lại cấu trúc test endpoints

**Cấu trúc mới:**
```
/api/v1/notifications/test/
├── queue-simple          # Test queue đơn giản
├── email-template        # Test email với template
├── email-direct          # Test email trực tiếp
├── sms                   # Test SMS
└── push                  # Test push notification
```

### Bước 3: Hoàn thiện Template Testing API

**Template Test Endpoints cần thêm:**
- `POST /api/v1/notifications/test/template-render` - Test render template với variables
- `POST /api/v1/notifications/test/template-validate` - Validate template syntax
- `GET /api/v1/notifications/test/template-preview/:code` - Preview template

## Chi tiết implementation

### Test Methods cần thêm vào Handler

```go
// Test methods trong Handler struct
func (h *Handler) testQueueSimple(c *gin.Context)     // ✅ Đã thêm
func (h *Handler) testEmailTemplate(c *gin.Context)  // ✅ Đã thêm
func (h *Handler) testEmailDirect(c *gin.Context)    // 🔄 Đang thêm
func (h *Handler) testSMS(c *gin.Context)            // ⏳ Chưa thêm
func (h *Handler) testPush(c *gin.Context)           // ⏳ Chưa thêm
func (h *Handler) testTemplateRender(c *gin.Context) // ⏳ Chưa thêm
func (h *Handler) testTemplateValidate(c *gin.Context) // ⏳ Chưa thêm
func (h *Handler) testTemplatePreview(c *gin.Context) // ⏳ Chưa thêm
```

### Route Registration

```go
// Trong RegisterRoutesWithEngine()
testRoutes := apiGroup.Group("/test")
testRoutes.POST("/queue-simple", h.testQueueSimple)
testRoutes.POST("/email-template", h.testEmailTemplate)
testRoutes.POST("/email-direct", h.testEmailDirect)
testRoutes.POST("/sms", h.testSMS)
testRoutes.POST("/push", h.testPush)
testRoutes.POST("/template-render", h.testTemplateRender)
testRoutes.POST("/template-validate", h.testTemplateValidate)
testRoutes.GET("/template-preview/:code", h.testTemplatePreview)
```

## Tiến độ thực hiện

### ✅ Đã hoàn thành

1. **Thêm import types** - Đã thêm `"wnapi/modules/notification/types"` vào `api/handler.go`
2. **Test queue simple** - Đã implement `testQueueSimple()` method
3. **Test email template** - Đã implement `testEmailTemplate()` method
4. **Template API cơ bản** - Đã có đầy đủ CRUD operations cho templates

### 🔄 Đang thực hiện

1. **Test email direct** - Đang implement `testEmailDirect()` method
2. **Route registration** - Cần đăng ký routes mới trong handler

### ⏳ Chưa thực hiện

1. **Test SMS method** - Implement `testSMS()` method
2. **Test Push method** - Implement `testPush()` method
3. **Template testing methods** - Implement template render/validate/preview
4. **Cleanup providers.go** - Xóa test endpoints khỏi providers.go
5. **Update documentation** - Cập nhật API docs và Bruno collections

## Request/Response Schemas

### Test Email Template Request
```json
{
  "tenant_id": 1,
  "user_id": 1,
  "to": "<EMAIL>",
  "subject": "Test Email Template",
  "template_id": "welcome_email",
  "variables": {
    "user_name": "John Doe",
    "activation_link": "https://example.com/activate"
  }
}
```

### Test Email Direct Request
```json
{
  "tenant_id": 1,
  "user_id": 1,
  "to": "<EMAIL>",
  "subject": "Test Direct Email",
  "body": "Hello, this is a test email.",
  "cc": ["<EMAIL>"],
  "bcc": ["<EMAIL>"],
  "variables": {}
}
```

### Test SMS Request
```json
{
  "tenant_id": 1,
  "user_id": 1,
  "to": "+84901234567",
  "message": "Test SMS message",
  "template_id": "sms_welcome",
  "from": "COMPANY",
  "variables": {
    "user_name": "John Doe"
  }
}
```

### Test Push Request
```json
{
  "tenant_id": 1,
  "user_id": 1,
  "device_id": "device_123",
  "device_type": "android",
  "title": "New Message",
  "body": "You have received a new message",
  "device_tokens": ["fcm_token_1", "fcm_token_2"],
  "icon": "notification_icon",
  "sound": "default",
  "badge": "1",
  "click_action": "OPEN_CHAT",
  "data": {
    "chat_id": "chat_123",
    "sender_id": "user_456"
  },
  "options": {
    "priority": "high",
    "time_to_live": 3600
  }
}
```

### Template Render Test Request
```json
{
  "template_code": "welcome_email",
  "variables": {
    "user_name": "John Doe",
    "activation_link": "https://example.com/activate",
    "company_name": "ACME Corp"
  }
}
```

## API Response Format

Tất cả test endpoints sẽ trả về response theo format chuẩn:

### Success Response
```json
{
  "status": "ok",
  "task_id": "task_12345",
  "task_type": "notification.send.email_template",
  "message": "Email template task enqueued successfully",
  "payload": {
    "tenant_id": 1,
    "user_id": 1,
    "to": "<EMAIL>",
    "template_id": "welcome_email"
  }
}
```

### Error Response
```json
{
  "error": "Queue manager not available",
  "details": "Additional error details if available"
}
```

## Testing Strategy

### Unit Tests
- Test từng method riêng biệt
- Mock queue manager và services
- Validate request/response schemas

### Integration Tests
- Test end-to-end flow từ API đến queue
- Test với real queue manager
- Verify task được enqueue đúng cách

### Manual Testing
- Sử dụng Bruno API collections
- Test với real email/SMS providers
- Verify notifications được gửi thành công

## Acceptance Criteria

### ✅ Hoàn thành khi:

1. **Code Organization**
   - [ ] Tất cả test endpoints đã được di chuyển từ providers.go sang handler.go
   - [ ] Routes được đăng ký đúng cách trong cả 2 methods RegisterRoutes và RegisterRoutesWithEngine
   - [ ] Code được tổ chức theo cấu trúc module chuẩn

2. **Functionality**
   - [ ] Tất cả test endpoints hoạt động đúng như cũ
   - [ ] Template API hoạt động đầy đủ (CRUD + test methods)
   - [ ] Queue integration hoạt động bình thường

3. **Documentation**
   - [ ] API documentation được cập nhật
   - [ ] Bruno collections được cập nhật với endpoints mới
   - [ ] README được cập nhật với thông tin mới

4. **Testing**
   - [ ] Unit tests pass
   - [ ] Integration tests pass
   - [ ] Manual testing successful

## Notes

- Giữ nguyên functionality của các test endpoints
- Đảm bảo backward compatibility
- Sử dụng response format chuẩn của hệ thống
- Follow coding standards và naming conventions
- Proper error handling và validation

