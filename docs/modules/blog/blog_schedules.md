# Blog Schedules - <PERSON><PERSON> thống Lập lịch Xu<PERSON>t bản Bài viết

## Tổng quan

Blog Schedules là tính năng cho phép lập lịch xuất bản bài viết tự động tại thời điểm đã định trước. Hệ thống này được tích hợp với cron jobs để tự động xử lý việc xuất bản các bài viết đã được lên lịch.

## Kiến trúc System

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  API Handler    │    │ Schedule Service │    │   Cron Jobs     │
│                 │────│                 │────│                 │
│ - Create        │    │ - Process       │    │ - Auto Publish  │
│ - List          │    │ - Execute       │    │ - Cleanup       │
│ - Cancel        │    │ - Retry         │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Database      │
                    │                 │
                    │ - blog_schedules│
                    │ - blog_posts    │
                    └─────────────────┘
```

### Database Schema

```sql
CREATE TABLE blog_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tenant_id INT NOT NULL,
    blog_id INT NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'published', 'failed', 'cancelled') DEFAULT 'pending',
    error_message TEXT NULL,
    retry_count TINYINT UNSIGNED DEFAULT 0,
    max_retries TINYINT UNSIGNED DEFAULT 3,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    
    INDEX idx_tenant_status (tenant_id, status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_blog_id (blog_id),
    FOREIGN KEY (blog_id) REFERENCES blog_posts(post_id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

## Data Models

### BlogSchedule Model

```go
type BlogSchedule struct {
    ID           uint               `json:"id"`
    TenantID     uint               `json:"tenant_id"`
    BlogID       uint               `json:"blog_id"`
    ScheduledAt  time.Time          `json:"scheduled_at"`
    Status       BlogScheduleStatus `json:"status"`
    ErrorMessage *string            `json:"error_message,omitempty"`
    RetryCount   uint8              `json:"retry_count"`
    MaxRetries   uint8              `json:"max_retries"`
    PublishedAt  *time.Time         `json:"published_at,omitempty"`
    CreatedAt    time.Time          `json:"created_at"`
    UpdatedAt    time.Time          `json:"updated_at"`
    CreatedBy    uint               `json:"created_by"`
    
    // Relations
    Post    *Post `json:"post,omitempty"`
    Creator *User `json:"creator,omitempty"`
}
```

### BlogScheduleStatus

```go
type BlogScheduleStatus string

const (
    ScheduleStatusPending   BlogScheduleStatus = "pending"   // Chờ xuất bản
    ScheduleStatusPublished BlogScheduleStatus = "published" // Đã xuất bản
    ScheduleStatusFailed    BlogScheduleStatus = "failed"    // Lỗi khi xuất bản
    ScheduleStatusCancelled BlogScheduleStatus = "cancelled" // Đã hủy
)
```

## API Endpoints

### 1. Tạo Schedule mới

**POST** `/api/v1/blog/schedules`

```json
{
    "tenant_id": 1,
    "blog_id": 123,
    "scheduled_at": "2025-07-06T15:30:00Z",
    "max_retries": 3,
    "created_by": 1
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 456,
        "tenant_id": 1,
        "blog_id": 123,
        "scheduled_at": "2025-07-06T15:30:00Z",
        "status": "pending",
        "retry_count": 0,
        "max_retries": 3,
        "created_at": "2025-07-06T10:00:00Z",
        "created_by": 1
    }
}
```

### 2. Lấy thông tin Schedule

**GET** `/api/v1/blog/schedules/{id}?tenant_id=1`

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 456,
        "tenant_id": 1,
        "blog_id": 123,
        "scheduled_at": "2025-07-06T15:30:00Z",
        "status": "pending",
        "retry_count": 0,
        "max_retries": 3,
        "created_at": "2025-07-06T10:00:00Z",
        "created_by": 1,
        "post": {
            "post_id": 123,
            "title": "Bài viết được lên lịch",
            "status": "draft"
        }
    }
}
```

### 3. Danh sách Schedules

**GET** `/api/v1/blog/schedules?tenant_id=1&status=pending&limit=10&offset=0`

**Response:**
```json
{
    "success": true,
    "data": {
        "schedules": [
            {
                "id": 456,
                "tenant_id": 1,
                "blog_id": 123,
                "scheduled_at": "2025-07-06T15:30:00Z",
                "status": "pending",
                "retry_count": 0,
                "max_retries": 3
            }
        ],
        "total": 1,
        "limit": 10,
        "offset": 0
    }
}
```

### 4. Hủy Schedule

**POST** `/api/v1/blog/schedules/{id}/cancel`

```json
{
    "tenant_id": 1
}
```

### 5. Retry Schedule (thử lại nếu failed)

**POST** `/api/v1/blog/schedules/{id}/retry`

```json
{
    "tenant_id": 1
}
```

## Service Layer

### ScheduleService Interface

```go
type ScheduleService interface {
    // Tạo schedule mới
    CreateSchedule(ctx context.Context, tenantID, blogID, createdBy uint, 
                  scheduledAt time.Time, maxRetries uint8) (*models.BlogSchedule, error)
    
    // Lấy thông tin schedule
    GetSchedule(ctx context.Context, tenantID, scheduleID uint) (*models.BlogSchedule, error)
    
    // Danh sách schedules
    ListSchedules(ctx context.Context, tenantID uint, status *models.BlogScheduleStatus, 
                 limit, offset int) ([]*models.BlogSchedule, int64, error)
    
    // Hủy schedule
    CancelSchedule(ctx context.Context, tenantID, scheduleID uint) error
    
    // Retry schedule
    RetrySchedule(ctx context.Context, tenantID, scheduleID uint) error
    
    // Xử lý schedules pending (được gọi bởi cron)
    ProcessPendingSchedules(ctx context.Context) error
    
    // Thực thi schedule cụ thể
    ExecuteSchedule(ctx context.Context, scheduleID uint) error
}
```

### Key Methods

#### CreateSchedule
- Kiểm tra blog post tồn tại
- Validate thời gian schedule phải trong tương lai
- Kiểm tra không có schedule trùng lặp
- Tạo bản ghi trong database
- Enqueue task vào queue system

#### ProcessPendingSchedules
- Tìm tất cả schedules có status "pending" và thời gian <= hiện tại
- Xử lý từng schedule một cách tuần tự
- Cập nhật status và retry count
- Ghi log chi tiết

#### ExecuteSchedule
- Kiểm tra schedule có thể thực thi
- Cập nhật blog post status thành "published"
- Cập nhật schedule status thành "published"
- Ghi log kết quả

## Cron Jobs Integration

### 1. Auto Publish Job

```go
{
    Name:        "blog_process_pending_schedules",
    Schedule:    "*/5 * * * *", // Mỗi 5 phút
    Description: "Process pending blog schedules",
    Enabled:     true,
    Handler:     ProcessPendingSchedules,
}
```

**Chức năng:**
- Chạy mỗi 5 phút
- Tìm và xử lý các schedules đến thời gian xuất bản
- Tự động retry nếu có lỗi (theo max_retries)

### 2. Cleanup Job

```go
{
    Name:        "blog_cleanup_old_schedules",
    Schedule:    "0 2 * * *", // Hàng ngày lúc 2h sáng
    Description: "Cleanup old completed blog schedules",
    Enabled:     true,
    Handler:     CleanupOldSchedules,
}
```

**Chức năng:**
- Dọn dẹp các schedules cũ đã hoàn thành
- Giữ lại records trong 30 ngày
- Tối ưu performance database

## Queue Integration

### Schedule Task Structure

```go
type ScheduleTask struct {
    ScheduleID uint      `json:"schedule_id"`
    TenantID   uint      `json:"tenant_id"`
    BlogID     uint      `json:"blog_id"`
    ScheduledAt time.Time `json:"scheduled_at"`
    Attempt    int       `json:"attempt"`
}
```

### Task Processing Flow

1. **Enqueue Task**: Khi tạo schedule, task được đưa vào queue
2. **Delay Execution**: Task được delay đến thời gian scheduled_at
3. **Execute**: Worker xử lý task và publish blog post
4. **Retry Logic**: Nếu fail, task được retry theo max_retries
5. **Complete**: Cập nhật status và ghi log

## Business Logic

### Validation Rules

1. **Scheduled Time**: Phải trong tương lai (> current time)
2. **Blog Post**: Phải tồn tại và thuộc về tenant
3. **Duplicate Check**: Không cho phép schedule trùng blog + time
4. **Status Check**: Chỉ pending schedules mới có thể được execute
5. **Retry Limit**: Số lần retry không vượt quá max_retries

### State Transitions

```
pending → published (success)
pending → failed (error, có thể retry)
pending → cancelled (manual cancel)
failed → pending (manual retry, nếu chưa vượt max_retries)
```

### Error Handling

1. **Database Errors**: Transaction rollback, log error
2. **Blog Post Not Found**: Mark schedule as failed
3. **Permission Errors**: Mark schedule as failed
4. **Network Errors**: Retry với exponential backoff
5. **Queue Errors**: Log và alert admin

## Configuration

### Environment Variables

```bash
# Schedule Processing
BLOG_SCHEDULE_PROCESS_INTERVAL=5m
BLOG_SCHEDULE_CLEANUP_RETENTION=30d
BLOG_SCHEDULE_MAX_RETRIES=3
BLOG_SCHEDULE_RETRY_DELAY=5m

# Queue Configuration
BLOG_SCHEDULE_QUEUE_NAME=blog_schedules
BLOG_SCHEDULE_WORKER_CONCURRENCY=5

# Cron Configuration
BLOG_CRON_SCHEDULE_ENABLED=true
BLOG_CRON_CLEANUP_ENABLED=true
```

### Feature Flags

```go
type ScheduleConfig struct {
    Enabled                bool          `yaml:"enabled"`
    ProcessInterval        time.Duration `yaml:"process_interval"`
    CleanupRetention       time.Duration `yaml:"cleanup_retention"`
    DefaultMaxRetries      uint8         `yaml:"default_max_retries"`
    QueueWorkerConcurrency int          `yaml:"queue_worker_concurrency"`
}
```

## Usage Examples

### 1. Tạo Schedule cho bài viết

```go
ctx := context.Background()
tenantID := uint(1)
blogID := uint(123)
createdBy := uint(1)
scheduledAt := time.Now().Add(24 * time.Hour) // 24 giờ sau
maxRetries := uint8(3)

schedule, err := scheduleService.CreateSchedule(
    ctx, tenantID, blogID, createdBy, scheduledAt, maxRetries,
)
if err != nil {
    log.Printf("Failed to create schedule: %v", err)
    return
}

fmt.Printf("Schedule created with ID: %d\n", schedule.ID)
```

### 2. Xử lý schedules pending (Cron job)

```go
func (s *ScheduleCronJobs) ProcessPendingSchedules(ctx context.Context) error {
    err := s.scheduleService.ProcessPendingSchedules(ctx)
    if err != nil {
        s.logger.Error("Failed to process pending schedules", "error", err)
        return err
    }
    
    s.logger.Info("Successfully processed pending schedules")
    return nil
}
```

### 3. API Request Example

```bash
# Tạo schedule mới
curl -X POST http://localhost:9033/api/v1/blog/schedules \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: 1" \
  -d '{
    "tenant_id": 1,
    "blog_id": 123,
    "scheduled_at": "2025-07-07T10:00:00Z",
    "max_retries": 3,
    "created_by": 1
  }'

# Lấy danh sách schedules
curl -X GET "http://localhost:9033/api/v1/blog/schedules?tenant_id=1&status=pending&limit=10"

# Hủy schedule
curl -X POST http://localhost:9033/api/v1/blog/schedules/456/cancel \
  -H "Content-Type: application/json" \
  -d '{"tenant_id": 1}'
```

## Monitoring và Logging

### Metrics cần theo dõi

1. **Schedule Creation Rate**: Số lượng schedules được tạo
2. **Execution Success Rate**: Tỷ lệ thành công của việc execute
3. **Retry Rate**: Tỷ lệ schedules cần retry
4. **Queue Depth**: Số lượng tasks trong queue
5. **Processing Time**: Thời gian xử lý mỗi schedule

### Log Events

```go
// Schedule created
logger.Info("Schedule created", 
    "schedule_id", schedule.ID,
    "blog_id", blogID,
    "scheduled_at", scheduledAt,
    "tenant_id", tenantID,
)

// Schedule executed
logger.Info("Schedule executed successfully",
    "schedule_id", scheduleID,
    "blog_id", schedule.BlogID,
    "execution_time", time.Since(start),
)

// Schedule failed
logger.Error("Schedule execution failed",
    "schedule_id", scheduleID,
    "error", err,
    "retry_count", schedule.RetryCount,
)
```

## Testing

### Unit Tests

```go
func TestCreateSchedule(t *testing.T) {
    // Setup
    service := setupScheduleService(t)
    
    // Test data
    tenantID := uint(1)
    blogID := uint(123)
    scheduledAt := time.Now().Add(1 * time.Hour)
    
    // Execute
    schedule, err := service.CreateSchedule(
        context.Background(), 
        tenantID, blogID, 1, scheduledAt, 3,
    )
    
    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, schedule)
    assert.Equal(t, models.ScheduleStatusPending, schedule.Status)
}
```

### Integration Tests

```go
func TestScheduleE2E(t *testing.T) {
    // Tạo blog post
    // Tạo schedule
    // Trigger cron job
    // Verify blog được publish
}
```

## Performance Considerations

### Database Optimization

1. **Indexes**: Tối ưu queries theo tenant_id, status, scheduled_at
2. **Partitioning**: Phân vùng table theo thời gian
3. **Cleanup**: Dọn dẹp dữ liệu cũ định kỳ

### Queue Optimization

1. **Batch Processing**: Xử lý nhiều schedules cùng lúc
2. **Delay Queues**: Sử dụng delay queues cho future schedules
3. **Dead Letter Queues**: Xử lý failed tasks

### Caching Strategy

1. **Schedule Lookup**: Cache thông tin schedules thường xuyên truy cập
2. **Blog Post Cache**: Cache blog post data để giảm DB queries
3. **Configuration Cache**: Cache config để tránh reload nhiều lần

## Security Considerations

1. **Tenant Isolation**: Đảm bảo schedules chỉ truy cập được trong tenant
2. **Permission Check**: Kiểm tra quyền user trước khi tạo/sửa schedule
3. **Input Validation**: Validate tất cả input từ API
4. **Rate Limiting**: Giới hạn số lượng schedules có thể tạo

## Troubleshooting

### Common Issues

1. **Schedule không execute**: Kiểm tra cron job, queue worker
2. **Failed schedules**: Xem logs, kiểm tra blog post status
3. **Performance issues**: Monitor queue depth, database performance
4. **Memory leaks**: Kiểm tra worker processes, cleanup jobs

### Debug Commands

```bash
# Kiểm tra pending schedules
./build/wnapi cron run blog_process_pending_schedules

# Xem queue status
redis-cli LLEN queue:blog_schedules

# Check database
mysql> SELECT * FROM blog_schedules WHERE status = 'pending' AND scheduled_at <= NOW();
```

## Future Enhancements

1. **Recurring Schedules**: Hỗ trợ schedule lặp lại
2. **Batch Operations**: Tạo nhiều schedules cùng lúc
3. **Advanced Scheduling**: Timezone support, business hours
4. **Notification Integration**: Thông báo khi schedule execute
5. **Analytics Dashboard**: Báo cáo và thống kê schedules