**Không**, không phải toàn bộ bảng đều cần lưu cả `tenant_id` và `website_id`. Cần phân loại dựa trên **tính chất dữ liệu** và **phạm vi sử dụng**:

## 1. <PERSON>ân loại dữ liệu theo scope

### **Global Data** (Không cần tenant_id, website_id)
```
- users (thông tin user toàn hệ thống)
- roles (định nghĩa vai trò chung)
- permissions (định nghĩa quyền hạn chung)
- countries, currencies (dữ liệu tham chiếu)
- system_settings (cài đặt hệ thống)
```

### **Tenant-scoped Data** (Chỉ cần tenant_id)
```
- tenants (thông tin tenant)
- tenant_settings (cài đặt riêng của tenant)
- tenant_plans (gói dịch vụ của tenant)
- tenant_billing (thanh toán c<PERSON> tenant)
- user_tenants (quan hệ user-tenant)
```

### **Website-scoped Data** (Cần cả tenant_id + website_id)
```
- websites (website_id + tenant_id)
- website_settings (cài đặt website)
- website_themes (theme của website)
- website_menus (menu của website)
- user_website_roles (vai trò user trên website)
```

### **Content Data** (Cần cả tenant_id + website_id)
```
- blog_posts (bài viết thuộc website nào)
- products (sản phẩm thuộc website nào)
- categories (danh mục của website)
- orders (đơn hàng từ website nào)
- customers (khách hàng của website)
```

## 2. Chiến lược Composite Keys

### **Không dùng composite key trên tất cả bảng**, mà dùng **reference chain**:

```sql
-- Bảng gốc (không cần tenant_id, website_id)
users: id, email, name

-- Bảng quan hệ (có tenant_id)
user_tenants: user_id, tenant_id, role

-- Bảng quan hệ (có website_id, kế thừa tenant_id từ websites)
user_website_roles: user_id, website_id, role_id

-- Websites (có tenant_id)
websites: id, tenant_id, domain, name

-- Content (có website_id, kế thừa tenant_id từ websites)
blog_posts: id, website_id, title, content
```

## 3. Pattern thiết kế bảng

### **Pattern 1: Reference Chain** (Khuyến nghị)
```sql
-- User không thuộc tenant cụ thể
users: id, email, password

-- Website thuộc tenant
websites: id, tenant_id, domain

-- Content thuộc website (kế thừa tenant qua website)
blog_posts: id, website_id, title, content

-- Query: Lấy posts của tenant
SELECT p.* FROM blog_posts p
JOIN websites w ON p.website_id = w.id
WHERE w.tenant_id = ?
```

### **Pattern 2: Redundant Keys** (Khi cần performance)
```sql
-- Với dữ liệu truy vấn nhiều theo tenant
blog_posts: id, website_id, tenant_id, title, content

-- Index compound cho performance
INDEX idx_tenant_website (tenant_id, website_id)
INDEX idx_website (website_id)
```

## 4. Quy tắc thiết kế cụ thể

### **Bảng cần tenant_id + website_id:**
- Dữ liệu content (posts, products, categories)
- Dữ liệu giao dịch (orders, payments)
- Dữ liệu user-generated (comments, reviews)
- Cài đặt cấp website

### **Bảng chỉ cần tenant_id:**
- Thông tin tenant
- Cài đặt cấp tenant
- Billing và subscription
- User-tenant relationships

### **Bảng không cần tenant_id/website_id:**
- Master data (users, roles, permissions)
- Reference data (countries, currencies)
- System configuration
- Audit logs (có thể có tenant_id để tracking)

## 5. Ví dụ thiết kế cụ thể

### **Bảng Users (Global)**
```sql
CREATE TABLE users (
    id INT PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255),
    full_name VARCHAR(255),
    created_at TIMESTAMP
);
```

### **Bảng Websites (Tenant-scoped)**
```sql
CREATE TABLE websites (
    id INT PRIMARY KEY,
    tenant_id INT NOT NULL,
    domain VARCHAR(255) UNIQUE,
    name VARCHAR(255),
    created_at TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);
```

### **Bảng Blog Posts (Website-scoped)**
```sql
CREATE TABLE blog_posts (
    id INT PRIMARY KEY,
    website_id INT NOT NULL,
    -- tenant_id INT, -- Tùy chọn: thêm để query nhanh
    title VARCHAR(255),
    content TEXT,
    author_id INT,
    created_at TIMESTAMP,
    FOREIGN KEY (website_id) REFERENCES websites(id),
    FOREIGN KEY (author_id) REFERENCES users(id)
);
```

### **Bảng User Website Roles (Cross-reference)**
```sql
CREATE TABLE user_website_roles (
    id INT PRIMARY KEY,
    user_id INT NOT NULL,
    website_id INT NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (website_id) REFERENCES websites(id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    UNIQUE KEY (user_id, website_id, role_id)
);
```

## 6. Query Patterns

### **Query theo Tenant:**
```sql
-- Lấy tất cả posts của tenant (qua website)
SELECT p.* FROM blog_posts p
JOIN websites w ON p.website_id = w.id
WHERE w.tenant_id = ?;

-- Nếu có redundant tenant_id trong posts
SELECT * FROM blog_posts WHERE tenant_id = ?;
```

### **Query theo Website:**
```sql
-- Lấy posts của website cụ thể
SELECT * FROM blog_posts WHERE website_id = ?;
```

### **Query permissions:**
```sql
-- Kiểm tra user có quyền gì trên website
SELECT r.name FROM user_website_roles uwr
JOIN roles r ON uwr.role_id = r.id
WHERE uwr.user_id = ? AND uwr.website_id = ?;
```

## 7. Performance Considerations

### **Khi nào thêm redundant keys:**
- Bảng có volume lớn và query nhiều theo tenant
- Cần partition dữ liệu theo tenant
- Query performance quan trọng hơn storage

### **Indexes cần thiết:**
```sql
-- Trên bảng content
INDEX idx_website_id (website_id)
INDEX idx_tenant_website (tenant_id, website_id) -- nếu có redundant tenant_id

-- Trên bảng relationships  
INDEX idx_user_website (user_id, website_id)
INDEX idx_website_role (website_id, role_id)
```

## Kết luận

**Không** cần lưu `tenant_id` và `website_id` trên tất cả bảng. Thay vào đó:

1. **Phân loại dữ liệu** theo scope (global/tenant/website)
2. **Sử dụng reference chain** cho relationship
3. **Thêm redundant keys** chỉ khi cần performance
4. **Thiết kế indexes** phù hợp với query patterns
5. **Maintain data consistency** qua foreign keys và constraints

Cách này vừa đảm bảo tính linh hoạt, vừa tối ưu performance và không làm phức tạp schema không cần thiết.