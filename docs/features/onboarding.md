Đây là workflow chi tiết của quá trình onboarding từ khi đăng ký đến khi hoàn thành. Dưới đây tôi sẽ giải thích các bước chính:


flowchart TD
    A[User đăng ký tài khoản] --> B{Tạo User thành công?}
    B -->|No| A1[Hiển thị lỗi]
    B -->|Yes| C[Tạo User record]
    
    C --> D[Set onboarding_status = 'not_started']
    D --> E[Set onboarding_step = 'email_verification']
    E --> F[Gửi email xác thực]
    F --> G[Hiển thị: 'Kiểm tra email để kích hoạt']
    
    G --> H[User click link trong email]
    H --> I{Email hợp lệ?}
    I -->|No| I1[Hiển thị lỗi: Link không hợp lệ]
    I -->|Yes| J[Update email_verified = true]
    
    J --> K[Update onboarding_status = 'in_progress']
    K --> L[Update onboarding_step = 'tenant_setup']
    L --> M[Tạo record trong user_onboarding_progress]
    M --> N[Mark 'email_verification' = completed]
    
    N --> O[Redirect đến trang tạo Tenant]
    O --> P[Form chọn loại Tenant]
    P --> P1{User chọn loại Tenant}
    P1 -->|Cá nhân| P2[Tạo Tenant với thông tin mặc định]
    P1 -->|Công ty| P3[Form điền thông tin công ty]
    
    P3 --> Q[User điền thông tin Tenant công ty]
    Q --> R{Validate dữ liệu Tenant?}
    R -->|No| R1[Hiển thị lỗi validation]
    R1 --> P3
    R -->|Yes| S[Tạo Tenant record]
    P2 --> S
    
    S --> T[Update user.tenant_id]
    T --> U[Update onboarding_step = 'website_setup']
    U --> V[Mark 'tenant_setup' = completed]
    
    V --> W[Redirect đến trang tạo Website]
    W --> X[Form tạo Website]
    X --> Y[User điền thông tin Website]
    
    Y --> Z{Validate dữ liệu Website?}
    Z -->|No| Z1[Hiển thị lỗi validation]
    Z1 --> X
    Z -->|Yes| AA[Tạo Website record]
    
    AA --> BB[Update onboarding_step = 'profile_setup']
    BB --> CC[Mark 'website_setup' = completed]
    
    CC --> DD[Redirect đến trang Profile Setup]
    DD --> EE[Form cập nhật Profile]
    EE --> FF[User điền thông tin cá nhân]
    
    FF --> GG{Validate Profile?}
    GG -->|No| GG1[Hiển thị lỗi validation]
    GG1 --> EE
    GG -->|Yes| HH[Update User profile]
    
    HH --> II[Update onboarding_step = 'tutorial']
    II --> JJ[Mark 'profile_setup' = completed]
    
    JJ --> KK[Hiển thị Tutorial/Dashboard tour]
    KK --> LL[User hoàn thành tutorial]
    
    LL --> MM[Update onboarding_status = 'completed']
    MM --> NN[Update onboarding_step = null]
    NN --> OO[Mark 'tutorial' = completed]
    
    OO --> PP[Redirect đến Dashboard chính]
    PP --> QQ[🎉 Onboarding hoàn thành!]
    
    %% Skip options
    O --> O2[Skip button]
    W --> W2[Skip button]
    DD --> DD2[Skip button]
    KK --> KK2[Skip button]
    
    O2 --> O3[Mark 'tenant_setup' = skipped]
    W2 --> W3[Mark 'website_setup' = skipped]
    DD2 --> DD3[Mark 'profile_setup' = skipped]
    KK2 --> KK3[Mark 'tutorial' = skipped]
    
    O3 --> W
    W3 --> DD
    DD3 --> KK
    KK3 --> MM
    
    %% Database updates
    style C fill:#e1f5fe
    style J fill:#e1f5fe
    style S fill:#e1f5fe
    style AA fill:#e1f5fe
    style HH fill:#e1f5fe
    
    %% Status updates
    style D fill:#f3e5f5
    style K fill:#f3e5f5
    style MM fill:#f3e5f5
    
    %% Step updates
    style E fill:#fff3e0
    style L fill:#fff3e0
    style U fill:#fff3e0
    style BB fill:#fff3e0
    style II fill:#fff3e0
    style NN fill:#fff3e0

## 🔄 **Các giai đoạn chính:**

### 1️⃣ **Đăng ký tài khoản**
- Tạo user record với `onboarding_status = 'not_started'`
- Set `onboarding_step = 'email_verification'`
- Gửi email xác thực

### 2️⃣ **Kích hoạt email**
- User click link xác thực
- Update `email_verified = true`
- Chuyển `onboarding_status = 'in_progress'`
- Chuyển `onboarding_step = 'tenant_setup'`

### 3️⃣ **Tạo Tenant**
- Hiển thị form chọn loại tenant (Cá nhân/Công ty)
- Nếu chọn **Cá nhân**: Tự động tạo tenant với thông tin mặc định
- Nếu chọn **Công ty**: Hiển thị form điền thông tin công ty
- Validate và tạo tenant record
- Liên kết user với tenant
- Chuyển `onboarding_step = 'website_setup'`

### 4️⃣ **Tạo Website**
- Hiển thị form tạo website
- Validate và tạo website record
- Chuyển `onboarding_step = 'profile_setup'`

### 5️⃣ **Setup Profile**
- Form cập nhật thông tin cá nhân
- Update user profile
- Chuyển `onboarding_step = 'tutorial'`

### 6️⃣ **Tutorial hoàn thành**
- Hiển thị hướng dẫn sử dụng
- Set `onboarding_status = 'completed'`
- Set `onboarding_step = null`

## 📊 **Database Updates:**

### Bảng `users`:
```sql
-- Khởi tạo
onboarding_status = 'not_started'
onboarding_step = 'email_verification'

-- Sau khi verify email
onboarding_status = 'in_progress'
onboarding_step = 'tenant_setup'

-- Sau khi tạo tenant
onboarding_step = 'website_setup'

-- Sau khi tạo website
onboarding_step = 'profile_setup'

-- Sau khi setup profile
onboarding_step = 'tutorial'

-- Hoàn thành
onboarding_status = 'completed'
onboarding_step = null
```

### Bảng `tenants`:
```sql
-- Tenant Cá nhân
tenant_type = 'individual'
tenant_name = user.full_name -- Tên hiển thị (VD: "Nguyễn Văn A")
company_name = NULL -- Không có tên công ty chính thức
tax_code = NULL
legal_representative = user.full_name
company_address = NULL
company_phone = NULL
company_email = NULL

-- Tenant Công ty
tenant_type = 'company'
tenant_name = 'ABC Tech' -- Tên hiển thị ngắn gọn
company_name = 'Công ty TNHH Công nghệ ABC' -- Tên chính thức cho hóa đơn
tax_code = 'Mã số thuế'
legal_representative = 'Người đại diện pháp luật'
company_address = 'Địa chỉ công ty'
company_phone = 'SĐT công ty'
company_email = 'Email công ty'
```
company_email = 'Email công ty'
```

### Bảng `user_onboarding_progress`:
Mỗi step hoàn thành sẽ tạo/update record với status tương ứng:
- `email_verification` → `completed`
- `tenant_setup` → `completed` hoặc `skipped`
- `website_setup` → `completed` hoặc `skipped`
- `profile_setup` → `completed` hoặc `skipped`
- `tutorial` → `completed` hoặc `skipped`

### Bảng `website_websites`:
```sql
-- Được tạo trong step website_setup
tenant_id = user.tenant_id
name = 'Tên website user nhập'
subdomain = 'subdomain tự động hoặc user chọn'
status = 'draft' (ban đầu)
```

## 🗄️ **Migrations đã cập nhật:**

### 1. **modules/tenant/migrations/001_create_tenants.up.sql**
- ✅ Thêm `tenant_type` để phân biệt individual/company
- ✅ Thêm các trường thông tin công ty (có thể NULL)
- ✅ Thêm indexes cho tenant_type và tax_code

### 2. **modules/auth/migrations/001_create_users.up.sql**
- ✅ Thêm `onboarding_status` và `onboarding_step`
- ✅ Thêm indexes cho onboarding tracking

### 3. **modules/auth/migrations/008_create_user_onboarding_progress.up.sql** (MỚI)
- ✅ Tạo bảng theo dõi chi tiết tiến trình onboarding
- ✅ Hỗ trợ JSON data cho mỗi step
- ✅ Foreign key constraint với users table

### 4. **modules/website/migrations/mysql/001_create_website_websites.up.sql** (ĐÃ CÓ)
- ✅ Đã sẵn sàng cho onboarding flow
- ✅ Liên kết với tenant_id

## 🔧 **Cách chạy migrations:**

```bash
# Chạy tất cả migrations
make migrate-up

# Hoặc chạy từng module
./bin/migrate -path ./modules/tenant/migrations -database "mysql://..." up
./bin/migrate -path ./modules/auth/migrations -database "mysql://..." up
./bin/migrate -path ./modules/website/migrations/mysql -database "mysql://..." up
```

## 🎯 **Validation Rules:**

### Tenant Cá nhân:
- `tenant_name`: Bắt buộc (tên hiển thị - có thể dùng full_name)
- `tenant_code`: Tự động generate từ email/name
- `company_name`: NULL (không có tên công ty chính thức)
- Tất cả company_* fields = NULL

### Tenant Công ty:
- `tenant_name`: Bắt buộc (tên hiển thị ngắn gọn)
- `company_name`: Bắt buộc (tên chính thức cho hóa đơn/pháp lý)
- `tax_code`: Bắt buộc (unique validation)
- `legal_representative`: Bắt buộc
- `company_address`: Tùy chọn
- `company_phone`: Tùy chọn  
- `company_email`: Tùy chọn (email format validation)

### 💡 **Ví dụ sự khác biệt:**
```sql
-- Tenant Công ty
tenant_name = "Shopee VN"  -- Tên hiển thị ngắn
company_name = "Công ty TNHH Sea Limited Việt Nam"  -- Tên chính thức

-- Tenant Cá nhân  
tenant_name = "Nguyễn Văn A"  -- Tên hiển thị
company_name = NULL  -- Không có
```
- `legal_representative`: Bắt buộc
- `company_address`: Tùy chọn
- `company_phone`: Tùy chọn  
- `company_email`: Tùy chọn (email format validation)