# Thiết Kế Module API Key - WNAPI

## 1. <PERSON><PERSON><PERSON> tr<PERSON><PERSON> thư mục

```
modules/api-key/
├── fx.go                           # Dependency injection với Fx
├── providers.go                    # Provider definitions
├── internal/
│   ├── config.go                   # Cấu hình module
│   ├── types.go                    # Đ<PERSON><PERSON> nghĩa types và interfaces
│   ├── errors.go                   # Định nghĩa lỗi
│   └── permission.go               # Định nghĩa permissions
├── models/
│   ├── api_key.go                  # Model API Key
│   ├── api_key_scope.go            # Model API Key Scope
│   ├── api_key_usage.go            # Model API Key Usage Log
│   └── api_key_rate_limit.go       # Model Rate Limiting
├── dto/
│   ├── request/
│   │   ├── create_api_key.go       # DTO tạo API key
│   │   ├── update_api_key.go       # DTO cập nhật API key
│   │   ├── list_api_key.go         # DTO list API key
│   │   └── rotate_api_key.go       # DTO rotate API key
│   └── response/
│       ├── api_key_response.go     # Response API key
│       ├── api_key_list.go         # Response list API key
│       └── api_key_usage.go        # Response usage statistics
├── repository/
│   ├── repository.go               # Interface definitions
│   └── mysql/
│       ├── api_key_repository.go   # Repository implementation
│       ├── api_key_scope_repository.go
│       ├── api_key_usage_repository.go
│       └── api_key_rate_limit_repository.go
├── service/
│   ├── api_key_service.go          # Business logic
│   ├── api_key_validator.go        # Validation logic
│   ├── rate_limiter.go             # Rate limiting logic
│   └── usage_tracker.go            # Usage tracking
├── api/
│   ├── routes.go                   # Route definitions
│   ├── middleware/
│   │   ├── api_key_auth.go         # API key authentication
│   │   └── rate_limit.go           # Rate limiting middleware
│   └── handlers/
│       ├── api_key_handler.go      # HTTP handlers
│       └── utils.go                # Handler utilities
├── queue/
│   ├── handlers.go                 # Queue handlers
│   ├── usage_aggregator.go         # Usage aggregation job
│   └── cleanup_expired.go          # Cleanup expired keys job
└── migrations/
    ├── 001_create_api_keys.up.sql
    ├── 001_create_api_keys.down.sql
    ├── 002_create_api_key_scopes.up.sql
    ├── 002_create_api_key_scopes.down.sql
    ├── 003_create_api_key_usage.up.sql
    ├── 003_create_api_key_usage.down.sql
    ├── 004_create_api_key_rate_limits.up.sql
    └── 004_create_api_key_rate_limits.down.sql
```

## 2. Database Schema

### 2.1 Bảng `api_keys`

```sql
-- 001_create_api_keys.up.sql
CREATE TABLE api_keys (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Thông tin cơ bản
    name VARCHAR(255) NOT NULL COMMENT 'Tên API key',
    description TEXT COMMENT 'Mô tả mục đích sử dụng',
    
    -- Key info
    key_id VARCHAR(64) NOT NULL UNIQUE COMMENT 'Public key identifier (ak_xxxxx)',
    secret_hash VARCHAR(255) NOT NULL COMMENT 'Hashed secret key',
    key_prefix VARCHAR(16) NOT NULL DEFAULT 'ak' COMMENT 'Key prefix',
    
    -- Ownership
    tenant_id INT UNSIGNED NOT NULL COMMENT 'Tenant sở hữu',
    user_id INT UNSIGNED NOT NULL COMMENT 'User tạo key',
    
    -- Permissions & Scopes
    scopes JSON COMMENT 'Danh sách scopes được phép (["read:posts", "write:users"])',
    allowed_ips JSON COMMENT 'Danh sách IP được phép truy cập',
    allowed_domains JSON COMMENT 'Danh sách domains được phép',
    
    -- Status & Lifecycle
    status ENUM('active', 'inactive', 'revoked', 'expired') NOT NULL DEFAULT 'active',
    is_master BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Có phải master key không',
    
    -- Rate limiting
    rate_limit_requests INT UNSIGNED DEFAULT NULL COMMENT 'Số request tối đa per minute',
    rate_limit_window ENUM('minute', 'hour', 'day') DEFAULT 'minute',
    
    -- Expiration
    expires_at TIMESTAMP NULL COMMENT 'Thời gian hết hạn',
    
    -- Usage tracking
    last_used_at TIMESTAMP NULL COMMENT 'Lần sử dụng cuối',
    total_requests INT UNSIGNED DEFAULT 0 COMMENT 'Tổng số request',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_used_at (last_used_at),
    
    -- Foreign keys
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES auth_users(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Bảng lưu trữ API keys';
```

### 2.2 Bảng `api_key_scopes`

```sql
-- 002_create_api_key_scopes.up.sql
CREATE TABLE api_key_scopes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Basic info
    name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Tên scope (vd: read:posts)',
    display_name VARCHAR(255) NOT NULL COMMENT 'Tên hiển thị',
    description TEXT COMMENT 'Mô tả scope',
    
    -- Grouping
    category VARCHAR(100) NOT NULL COMMENT 'Nhóm scope (posts, users, orders)',
    
    -- Permission mapping
    resource VARCHAR(100) NOT NULL COMMENT 'Resource được truy cập',
    action VARCHAR(50) NOT NULL COMMENT 'Action được phép (read, write, delete)',
    
    -- Metadata
    is_sensitive BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Có phải scope nhạy cảm',
    requires_approval BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Cần approval để sử dụng',
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_category (category),
    INDEX idx_resource_action (resource, action),
    INDEX idx_is_active (is_active),
    
    -- Unique constraint
    UNIQUE KEY uk_resource_action (resource, action)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Bảng định nghĩa các scopes cho API keys';
```

### 2.3 Bảng `api_key_usage`

```sql
-- 003_create_api_key_usage.up.sql
CREATE TABLE api_key_usage (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- API Key reference
    api_key_id INT UNSIGNED NOT NULL,
    key_id VARCHAR(64) NOT NULL COMMENT 'Key ID for quick lookup',
    
    -- Request info
    endpoint VARCHAR(500) NOT NULL COMMENT 'API endpoint được gọi',
    method VARCHAR(10) NOT NULL COMMENT 'HTTP method',
    
    -- Response info
    status_code SMALLINT UNSIGNED NOT NULL COMMENT 'HTTP status code',
    response_time_ms INT UNSIGNED COMMENT 'Thời gian response (ms)',
    
    -- Client info
    ip_address VARCHAR(45) COMMENT 'IP address của client',
    user_agent TEXT COMMENT 'User agent string',
    referer VARCHAR(500) COMMENT 'Referer header',
    
    -- Request metadata
    request_size INT UNSIGNED COMMENT 'Kích thước request (bytes)',
    response_size INT UNSIGNED COMMENT 'Kích thước response (bytes)',
    
    -- Error tracking
    error_code VARCHAR(50) COMMENT 'Mã lỗi nếu có',
    error_message TEXT COMMENT 'Chi tiết lỗi',
    
    -- Tenant context
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Timestamp
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_id (api_key_id),
    INDEX idx_key_id (key_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_created_at (created_at),
    INDEX idx_endpoint (endpoint(100)),
    INDEX idx_status_code (status_code),
    INDEX idx_ip_address (ip_address),
    
    -- Composite indexes for analytics
    INDEX idx_key_date (api_key_id, created_at),
    INDEX idx_tenant_date (tenant_id, created_at),
    
    -- Foreign key
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Bảng log sử dụng API keys'
PARTITION BY RANGE (UNIX_TIMESTAMP(created_at)) (
    PARTITION p202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01')),
    PARTITION p202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01')),
    PARTITION p202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01')),
    PARTITION p202504 VALUES LESS THAN (UNIX_TIMESTAMP('2025-05-01')),
    PARTITION p202505 VALUES LESS THAN (UNIX_TIMESTAMP('2025-06-01')),
    PARTITION p202506 VALUES LESS THAN (UNIX_TIMESTAMP('2025-07-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 2.4 Bảng `api_key_rate_limits`

```sql
-- 004_create_api_key_rate_limits.up.sql
CREATE TABLE api_key_rate_limits (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- API Key reference
    api_key_id INT UNSIGNED NOT NULL,
    key_id VARCHAR(64) NOT NULL COMMENT 'Key ID for quick lookup',
    
    -- Rate limit window
    window_start TIMESTAMP NOT NULL COMMENT 'Bắt đầu time window',
    window_end TIMESTAMP NOT NULL COMMENT 'Kết thúc time window',
    window_type ENUM('minute', 'hour', 'day') NOT NULL DEFAULT 'minute',
    
    -- Counter
    request_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Số request trong window',
    limit_exceeded_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Số lần vượt limit',
    
    -- Metadata
    tenant_id INT UNSIGNED NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_api_key_id (api_key_id),
    INDEX idx_key_id (key_id),
    INDEX idx_window (window_start, window_end),
    INDEX idx_tenant_id (tenant_id),
    
    -- Unique constraint để tránh duplicate windows
    UNIQUE KEY uk_key_window (api_key_id, window_start, window_type),
    
    -- Foreign key
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Bảng tracking rate limiting cho API keys';
```

## 3. Seed Data cho Scopes

```sql
-- Seed data for api_key_scopes
INSERT INTO api_key_scopes (name, display_name, description, category, resource, action, is_sensitive, requires_approval) VALUES
-- Auth scopes
('read:users', 'Read Users', 'Đọc thông tin người dùng', 'auth', 'users', 'read', FALSE, FALSE),
('write:users', 'Write Users', 'Tạo và cập nhật người dùng', 'auth', 'users', 'write', TRUE, TRUE),
('delete:users', 'Delete Users', 'Xóa người dùng', 'auth', 'users', 'delete', TRUE, TRUE),

-- Blog scopes
('read:posts', 'Read Posts', 'Đọc bài viết', 'blog', 'posts', 'read', FALSE, FALSE),
('write:posts', 'Write Posts', 'Tạo và cập nhật bài viết', 'blog', 'posts', 'write', FALSE, FALSE),
('delete:posts', 'Delete Posts', 'Xóa bài viết', 'blog', 'posts', 'delete', FALSE, TRUE),
('read:categories', 'Read Categories', 'Đọc danh mục', 'blog', 'categories', 'read', FALSE, FALSE),
('write:categories', 'Write Categories', 'Tạo và cập nhật danh mục', 'blog', 'categories', 'write', FALSE, FALSE),

-- Product scopes
('read:products', 'Read Products', 'Đọc sản phẩm', 'ecommerce', 'products', 'read', FALSE, FALSE),
('write:products', 'Write Products', 'Tạo và cập nhật sản phẩm', 'ecommerce', 'products', 'write', FALSE, FALSE),
('delete:products', 'Delete Products', 'Xóa sản phẩm', 'ecommerce', 'products', 'delete', FALSE, TRUE),

-- Order scopes
('read:orders', 'Read Orders', 'Đọc đơn hàng', 'ecommerce', 'orders', 'read', TRUE, FALSE),
('write:orders', 'Write Orders', 'Tạo và cập nhật đơn hàng', 'ecommerce', 'orders', 'write', TRUE, TRUE),

-- Media scopes
('read:media', 'Read Media', 'Đọc file media', 'media', 'media', 'read', FALSE, FALSE),
('write:media', 'Write Media', 'Upload file media', 'media', 'media', 'write', FALSE, FALSE),
('delete:media', 'Delete Media', 'Xóa file media', 'media', 'media', 'delete', FALSE, TRUE),

-- Admin scopes
('admin:all', 'Admin All', 'Quyền admin toàn bộ hệ thống', 'admin', 'all', 'all', TRUE, TRUE),
('admin:tenants', 'Admin Tenants', 'Quản lý tenants', 'admin', 'tenants', 'write', TRUE, TRUE),
('admin:api-keys', 'Admin API Keys', 'Quản lý API keys', 'admin', 'api-keys', 'write', TRUE, TRUE);
```

## 4. Key Features

### 4.1 Format API Key
- **Key ID**: `ak_1234567890abcdef` (public identifier)
- **Secret**: `sk_1234567890abcdef1234567890abcdef12345678` (private key)
- **Full Key**: `ak_1234567890abcdef.sk_1234567890abcdef1234567890abcdef12345678`

### 4.2 Security Features
- **Hashed Storage**: Chỉ lưu hash của secret key
- **IP Restrictions**: Giới hạn truy cập theo IP
- **Domain Restrictions**: Giới hạn truy cập theo domain
- **Scope-based Permissions**: Kiểm soát quyền truy cập chi tiết
- **Rate Limiting**: Giới hạn số request per minute/hour/day
- **Expiration**: Tự động hết hạn
- **Usage Tracking**: Log chi tiết mọi request

### 4.3 Management Features
- **Master Keys**: Key có quyền quản lý các key khác
- **Key Rotation**: Tạo key mới và vô hiệu hóa key cũ
- **Bulk Operations**: Vô hiệu hóa nhiều key cùng lúc
- **Usage Analytics**: Thống kê sử dụng chi tiết
- **Alert System**: Cảnh báo khi có hoạt động bất thường

## 5. Migration Files

### 5.1 Down Migration Files

```sql
-- 004_create_api_key_rate_limits.down.sql
DROP TABLE IF EXISTS api_key_rate_limits;

-- 003_create_api_key_usage.down.sql
DROP TABLE IF EXISTS api_key_usage;

-- 002_create_api_key_scopes.down.sql
DROP TABLE IF EXISTS api_key_scopes;

-- 001_create_api_keys.down.sql
DROP TABLE IF EXISTS api_keys;
```

## 6. Indexes và Performance

### 6.1 Các Index quan trọng
- **api_keys**: `idx_key_id` (unique), `idx_tenant_status`, `idx_expires_at`
- **api_key_usage**: `idx_key_date` (composite), `idx_tenant_date`, `idx_endpoint`
- **api_key_rate_limits**: `idx_key_window` (composite), `uk_key_window` (unique)

### 6.2 Partitioning
- Bảng `api_key_usage` được partition theo tháng để tối ưu performance
- Tự động tạo partition mới mỗi tháng
- Cleanup partition cũ sau 12 tháng

### 6.3 Caching Strategy
- Cache API key info trong Redis với TTL 15 phút
- Cache rate limit counters trong Redis
- Cache scope definitions
- Invalidate cache khi key bị revoke

## 7. API Endpoints

```
GET    /api/v1/api-keys              # List API keys
POST   /api/v1/api-keys              # Create new API key
GET    /api/v1/api-keys/{id}         # Get API key details
PATCH  /api/v1/api-keys/{id}         # Update API key
DELETE /api/v1/api-keys/{id}         # Revoke API key
POST   /api/v1/api-keys/{id}/rotate  # Rotate API key
GET    /api/v1/api-keys/{id}/usage   # Get usage statistics

GET    /api/v1/api-key-scopes        # List available scopes
POST   /api/v1/api-key-scopes        # Create new scope (admin only)
```

## 8. Error Codes

```go
const (
    ErrCodeAPIKeyNotFound      = "API_KEY_NOT_FOUND"
    ErrCodeAPIKeyExpired       = "API_KEY_EXPIRED"
    ErrCodeAPIKeyRevoked       = "API_KEY_REVOKED"
    ErrCodeAPIKeyInactive      = "API_KEY_INACTIVE"
    ErrCodeInvalidAPIKey       = "INVALID_API_KEY"
    ErrCodeRateLimitExceeded   = "RATE_LIMIT_EXCEEDED"
    ErrCodeIPNotAllowed        = "IP_NOT_ALLOWED"
    ErrCodeDomainNotAllowed    = "DOMAIN_NOT_ALLOWED"
    ErrCodeScopeNotAllowed     = "SCOPE_NOT_ALLOWED"
    ErrCodeAPIKeyLimitReached  = "API_KEY_LIMIT_REACHED"
)
```

Thiết kế này cung cấp một hệ thống API Key management hoàn chỉnh với đầy đủ tính năng bảo mật, monitoring và quản lý cần thiết cho một ứng dụng production.