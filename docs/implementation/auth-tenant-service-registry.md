# Auth-Tenant ServiceRegistry Integration Implementation

## Tổng quan

Tài liệu này mô tả việc triển khai ServiceRegistry pattern để cho phép auth module gọi trực tiếp tenant module service thông qua dependency injection, thay vì sử dụng HTTP API calls.

## Kiến trúc

### 1. ServiceRegistry Pattern

ServiceRegistry hoạt động như một DI container đơn giản, cho phép:
- **Type-safe service resolution** với `core.GetServiceTyped[T]()`
- **Graceful degradation** khi service không có sẵn
- **Centralized service management** 
- **Module independence** với loose coupling

### 2. Service Name Constants

```go
// internal/core/service_names.go
const (
    TenantServiceName      = "tenant_service"
    AuthServiceName        = "auth_service"
    NotificationServiceName = "notification_service"
    // ... other services
)
```

### 3. Module Loading Order

Modules được load theo thứ tự dependency:
```
1. tenant (cung cấp multi-tenant support)
2. auth (phụ thuộc tenant)
3. rbac (phụ thuộc auth + tenant)
4. notification (độc lập)
5. business modules
```

## Implementation Details

### 1. Tenant Module Registration

```go
// modules/tenant/module.go
func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    // ... khởi tạo tenant service
    
    // Register tenant service in ServiceRegistry
    err = app.GetServiceRegistry().Register(core.TenantServiceName, result.TenantService)
    if err != nil {
        logger.Error("Failed to register tenant service", "error", err.Error())
    } else {
        logger.Info("Tenant service registered successfully", "service_name", core.TenantServiceName)
    }
    
    return module, nil
}
```

### 2. Auth Module Service Resolution

```go
// modules/auth/module.go
func NewModule(app *core.AppBootstrap, config map[string]interface{}) (core.Module, error) {
    // ... khởi tạo auth service
    
    // Try to get tenant service from ServiceRegistry using type-safe approach
    tenantSvc, err := core.GetServiceTyped[tenantService.TenantService](
        app.GetServiceRegistry(),
        core.TenantServiceName,
    )
    
    if err != nil {
        logger.Warn("Tenant service not found in registry, multi-tenant features will be disabled",
            "error", err.Error())
    } else {
        logger.Info("Tenant service found in registry, configuring multi-tenant support")
        // Configure auth service with tenant service
        if s, ok := authService.(*service.Service); ok {
            s.SetTenantService(tenantSvc)
            logger.Info("Auth service configured with tenant service", 
                "service_type", fmt.Sprintf("%T", tenantSvc))
        }
    }
    
    return module, nil
}
```

### 3. Direct Service Calls

```go
// modules/auth/service/auth_service.go
func (s *Service) performMultiTenantRegistration(ctx context.Context, user *internal.User, req dto.RegisterRequest, createdUser **internal.User, createdTenantID *uint) error {
    // Step 1: Create user
    // ...
    
    // Step 2: Create tenant automatically using direct service call
    if s.tenantService != nil {
        err = tracing.WithSpan(ctx, "auth-service", "create_tenant", func(ctx context.Context) error {
            tenantCode := s.generateTenantCode((*createdUser).Username, (*createdUser).Email)
            
            // Call tenant service directly with proper interface
            tenantResp, err := s.callTenantServiceCreate(ctx, (*createdUser), tenantCode)
            if err != nil {
                return fmt.Errorf("failed to create tenant: %w", err)
            }
            
            // Extract tenant ID from response
            *createdTenantID = s.extractTenantIDFromResponse(tenantResp)
            return nil
        })
    }
    
    return nil
}
```

## Benefits

### 1. Performance
- **No network overhead**: Direct Go method calls thay vì HTTP requests
- **Faster execution**: Không có serialization/deserialization
- **Lower latency**: Loại bỏ network round-trips

### 2. Type Safety
- **Compile-time checking**: Interface validation tại compile time
- **IDE support**: Auto-completion và refactoring
- **Error detection**: Sớm phát hiện interface mismatches

### 3. Transaction Consistency
- **Shared transactions**: Có thể share database transactions
- **Atomic operations**: Rollback mechanisms hoạt động tốt
- **Data consistency**: Đảm bảo ACID properties

### 4. Error Handling
- **Direct error propagation**: Stack traces rõ ràng
- **Structured errors**: Type-safe error handling
- **Better debugging**: Easier to trace issues

### 5. Graceful Degradation
- **Service availability**: Hệ thống vẫn hoạt động khi service không có
- **Feature toggles**: Tự động disable features khi dependencies thiếu
- **Resilience**: Robust error handling

## Testing

### 1. Unit Tests
```go
func TestServiceRegistryIntegration(t *testing.T) {
    registry := core.NewServiceRegistry()
    mockTenantService := &MockTenantServiceForTest{}
    
    err := registry.Register(core.TenantServiceName, mockTenantService)
    assert.NoError(t, err)
    
    retrievedSvc, exists := registry.Get(core.TenantServiceName)
    assert.True(t, exists)
    assert.Equal(t, mockTenantService, retrievedSvc)
}
```

### 2. Integration Tests
- Test module loading order
- Test service registration và resolution
- Test graceful degradation scenarios
- Test transaction consistency

## Configuration

### 1. Module Loading Order
```env
# .env
MODULES_ENABLED=hello,tenant,auth,rbac,notification,product,blog,media
```

### 2. Service Dependencies
- Tenant module phải load trước auth module
- ServiceRegistry được khởi tạo trong AppBootstrap
- Services được register trong module initialization

## Best Practices

### 1. Service Interface Design
- Định nghĩa clear interfaces cho services
- Sử dụng proper DTOs cho request/response
- Implement graceful error handling

### 2. Dependency Management
- Sử dụng service name constants
- Implement proper module loading order
- Handle missing dependencies gracefully

### 3. Testing Strategy
- Mock services cho unit tests
- Integration tests cho service interactions
- Test graceful degradation scenarios

### 4. Error Handling
- Log service registration failures
- Implement fallback mechanisms
- Provide clear error messages

## Troubleshooting

### 1. Service Not Found
- Kiểm tra module loading order
- Verify service registration
- Check service name constants

### 2. Interface Mismatch
- Verify interface implementations
- Check import paths
- Validate method signatures

### 3. Circular Dependencies
- Review module dependencies
- Use lazy loading if needed
- Consider event-driven patterns

## Future Enhancements

1. **Advanced DI Features**: Constructor injection, lifecycle management
2. **Service Discovery**: Dynamic service registration
3. **Health Checks**: Service availability monitoring
4. **Metrics**: Service usage tracking
5. **Configuration**: Runtime service configuration
