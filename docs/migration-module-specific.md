# Migration cho Module Cụ Thể

## Tổng Quan

Hệ thống migration đã được mở rộng để hỗ trợ chạy migration cho từng module riêng lẻ. Tính năng này hữu ích khi phát triển và debug từng module mà không ảnh hưởng đến các module khác.

## Cú Pháp Lệnh

### Migration Up cho Module Cụ Thể
```bash
# Chạy migration up cho module blog
go run cmd/migrate/main.go -action=up -module=blog

# Chạy migration up cho module auth
go run cmd/migrate/main.go -action=up -module=auth

# Chạy migration up cho module tenant
go run cmd/migrate/main.go -action=up -module=tenant
```

### Migration Down cho Module Cụ Thể
```bash
# Rollback migration cho module blog
go run cmd/migrate/main.go -action=down -module=blog

# Rollback migration cho module auth
go run cmd/migrate/main.go -action=down -module=auth

# Rollback migration cho module tenant
go run cmd/migrate/main.go -action=down -module=tenant
```

### Migration cho Tất C<PERSON> (<PERSON><PERSON><PERSON>)
```bash
# Chạy migration up cho tất cả modules
go run cmd/migrate/main.go -action=up

# Rollback migration cho tất cả modules
go run cmd/migrate/main.go -action=down
```

## Cách Hoạt Động

### 1. Module Registry
- Hệ thống sử dụng `modules.GlobalRegistry` để tìm module
- Mỗi module phải implement interface `ModuleInfo` với:
  - `GetMigrationPath()`: Trả về đường dẫn migrations
  - `GetMigrationOrder()`: Trả về thứ tự ưu tiên

### 2. Migration Table Riêng Biệt
- Mỗi module có migration table riêng: `module_{name}_schema_migrations`
- Ví dụ:
  - Blog module: `module_blog_schema_migrations`
  - Auth module: `module_auth_schema_migrations`
  - Tenant module: `module_tenant_schema_migrations`

### 3. Validation
- Kiểm tra module có tồn tại trong registry
- Kiểm tra module có migration path không
- Nếu module không có migrations, hiển thị thông báo và hoàn thành thành công

## Ví Dụ Sử Dụng

### Phát Triển Module Mới
```bash
# 1. Tạo migration mới cho module
go run cmd/migrate/main.go -action=create -module=blog -name=add_new_field

# 2. Chạy migration up cho module đó
go run cmd/migrate/main.go -action=up -module=blog

# 3. Nếu có lỗi, rollback
go run cmd/migrate/main.go -action=down -module=blog
```

### Debug Migration Issues
```bash
# Rollback module có vấn đề
go run cmd/migrate/main.go -action=down -module=blog

# Fix migration files

# Chạy lại migration
go run cmd/migrate/main.go -action=up -module=blog
```

## Lưu Ý Quan Trọng

### 1. Foreign Key Dependencies
- Khi rollback module, cần chú ý các foreign key constraints
- Ví dụ: Không thể rollback `auth` module nếu `blog` module đang reference `users` table
- Cần rollback theo thứ tự ngược lại với dependency

### 2. Module Dependencies
- Blog module phụ thuộc vào Auth và Tenant modules
- Khi migration up, đảm bảo các dependency modules đã được migrate
- Khi migration down, rollback module phụ thuộc trước

### 3. Data Consistency
- Migration cho module cụ thể chỉ ảnh hưởng đến tables của module đó
- Không ảnh hưởng đến system tables hoặc modules khác
- Đảm bảo data consistency khi có cross-module references

## Troubleshooting

### Dirty Database State
Nếu gặp lỗi "Dirty database version", có thể fix bằng cách:

```sql
-- Kiểm tra trạng thái migration
SELECT * FROM module_blog_schema_migrations;

-- Reset về version sạch (thay X bằng version muốn reset)
UPDATE module_blog_schema_migrations SET version = X, dirty = 0;
```

### Module Not Found
- Đảm bảo module đã được import trong `cmd/migrate/main.go`
- Kiểm tra module có implement đúng interface `ModuleInfo`
- Verify module đã được register trong `modules.GlobalRegistry`

### No Migrations
Nếu module không có migrations:
- Hệ thống sẽ hiển thị "Module has no migrations"
- Đây là trạng thái bình thường, không phải lỗi
- Module vẫn có thể hoạt động mà không cần migrations

## Kết Luận

Tính năng migration cho module cụ thể giúp:
- Phát triển module độc lập
- Debug migration issues dễ dàng hơn
- Tránh ảnh hưởng đến modules khác
- Quản lý database schema theo module

Sử dụng tính năng này một cách thận trọng và luôn backup database trước khi thực hiện migration.
