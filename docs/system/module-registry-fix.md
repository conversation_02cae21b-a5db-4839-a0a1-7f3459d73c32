# Sửa Lỗi Module Registry - Hướng Dẫn Chi Tiết

## Vấn Đề

Lỗi `undefined: module.GlobalRegistry` xuất hiện trong file `modules/marketing/fx.go` khi build dự án.

### Nguyên Nhân

1. **Sử dụng sai package**: Module marketing đang import `internal/pkg/module` thay vì `internal/fx/modules`
2. **Sử dụng sai method**: Đang gọi `module.GlobalRegistry.RegisterModule()` thay vì `modules.GlobalRegistry.Register()`
3. **Implement sai interface**: Module implement interface cũ thay vì interface `FXModule` mới

### Chi Tiết Lỗi

```go
// TRƯỚC - Sai
package marketing

import (
	"wnapi/internal/pkg/module"  // ❌ Package sai
)

func (m *MarketingModule) Enabled() bool {  // ❌ Method signature sai
	return true
}

func init() {
	module.GlobalRegistry.RegisterModule(&MarketingModule{})  // ❌ Method không tồn tại
}
```

## G<PERSON>ải Pháp

### 1. Sửa Import Package

```go
// SAU - Đúng
package marketing

import (
	"go.uber.org/fx"
	"wnapi/internal/fx/modules"  // ✅ Package đúng
)
```

### 2. Sửa Method Implementation

```go
// TRƯỚC - Sai
func (m *MarketingModule) Enabled() bool {
	return true
}

// SAU - Đúng
func (m *MarketingModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}
```

### 3. Thêm Method Module()

Interface `FXModule` yêu cầu method `Module()` trả về `fx.Option`:

```go
// Module returns FX options for the module
func (m *MarketingModule) Module() fx.Option {
	return fx.Module("marketing",
		// Providers
		fx.Provide(
			// Configuration
			NewMarketingConfig,

			// Repositories
			NewAdsPositionRepository,
			NewAdsBannerRepository,

			// Services
			NewAdsPositionService,
			NewAdsBannerService,

			// API Handlers
			NewAdsPositionHandler,
			NewAdsBannerHandler,
		),

		// Route registration
		fx.Invoke(RegisterMarketingRoutes),
	)
}
```

### 4. Sửa Registry Call

```go
// TRƯỚC - Sai
func init() {
	module.GlobalRegistry.RegisterModule(&MarketingModule{})
}

// SAU - Đúng
func init() {
	modules.GlobalRegistry.Register(&MarketingModule{})
}
```

## Interface FXModule

Dự án sử dụng hệ thống module dựa trên Uber FX với interface sau:

```go
type FXModule interface {
	// Thông tin module
	Name() string
	Dependencies() []string
	Priority() int
	
	// Kiểm tra enable với config
	Enabled(config map[string]interface{}) bool
	
	// Migration
	GetMigrationPath() string
	GetMigrationOrder() int
	
	// FX module definition
	Module() fx.Option  // ✅ Method bắt buộc cho FX
}
```

## Hệ Thống Registry

### Global Registry Location

```go
// File: internal/fx/modules/discovery.go
var GlobalRegistry = NewModuleRegistry()

// File: internal/fx/modules/interface.go  
type FXModule interface { /* ... */ }
```

### Cách Đăng Ký Module

```go
// Trong init() function của mỗi module
func init() {
	modules.GlobalRegistry.Register(&ModuleName{})
}
```

## So Sánh Với Các Module Khác

### Module Blog (Đúng)
```go
import "wnapi/internal/fx/modules"

func (m *BlogModule) Enabled(config map[string]interface{}) bool { /* ... */ }
func (m *BlogModule) Module() fx.Option { /* ... */ }

func init() {
	modules.GlobalRegistry.Register(&BlogModule{})
}
```

### Module Cart (Đúng)
```go
import "wnapi/internal/fx/modules"

func (m *CartModule) Enabled(config map[string]interface{}) bool { /* ... */ }
func (m *CartModule) Module() fx.Option { /* ... */ }

func init() {
	modules.GlobalRegistry.Register(&CartModule{})
}
```

## Kiểm Tra Sau Sửa

### 1. Build Test
```bash
go build -o /tmp/build-check ./cmd/fx-server/main.go
```

### 2. Module Registry Test
```bash
# Kiểm tra module có được đăng ký
go run ./cmd/fx-server/main.go --list-modules
```

### 3. Lint Check
```bash
golangci-lint run modules/marketing/
```

## Best Practices

### 1. Tuân Thủ Interface
- Luôn implement đầy đủ interface `FXModule`
- Method `Enabled()` phải nhận `config map[string]interface{}`
- Method `Module()` phải trả về `fx.Option`

### 2. Dependency Management
- Khai báo dependencies trong `Dependencies()` 
- Thiết lập priority hợp lý trong `Priority()`

### 3. FX Module Structure
```go
func (m *ModuleName) Module() fx.Option {
	return fx.Module("module-name",
		// Providers - dependency injection
		fx.Provide(
			NewConfig,
			NewRepository,
			NewService,
			NewHandler,
		),
		
		// Invokes - initialization
		fx.Invoke(RegisterRoutes),
	)
}
```

### 4. Migration Support
- Implement `GetMigrationPath()` và `GetMigrationOrder()`
- Migration path: `"modules/module-name/migrations"`
- Migration order: số nguyên (thứ tự chạy migration)

## Kết Luận

Lỗi `undefined: module.GlobalRegistry` đã được giải quyết bằng cách:

1. ✅ Sửa import package từ `internal/pkg/module` sang `internal/fx/modules`
2. ✅ Sửa method `Enabled()` để nhận parameter `config`
3. ✅ Thêm method `Module()` trả về `fx.Option`
4. ✅ Sửa registry call từ `RegisterModule()` sang `Register()`

Tất cả module trong dự án giờ đây đều tuân thủ interface `FXModule` và sử dụng cùng một hệ thống registry toàn cục.
