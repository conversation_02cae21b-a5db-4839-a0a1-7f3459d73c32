# Hướng dẫn Testing

Tài liệu này mô tả cách chạy test và các lệnh liên quan đến testing trong dự án.

## M<PERSON><PERSON> lục

- [Chạy Unit Test](#chạy-unit-test)
- [Chạy Test với Coverage](#chạy-test-với-coverage)
- [Xem báo cáo Coverage](#xem-báo-cáo-coverage)
- [Chạy Test cho một package cụ thể](#chạy-test-cho-một-package-cụ-thể)
- [Chạy một test case cụ thể](#chạy-một-test-case-cụ-thể)
- [Các lệnh Makefile liên quan](#các-lệnh-makefile-liên-quan)

## Chạy Unit Test

Chạy toàn bộ test trong dự án:

```bash
go test ./...
```

## Chạy Test với Coverage

Tạo báo cáo coverage:

```bash
make test-coverage
```

<PERSON><PERSON><PERSON> này sẽ:

1. Ch<PERSON>y toàn bộ test
2. Tạo file `coverage.out`
3. Tạo báo cáo HTML tại `coverage.html`

## Xem báo cáo Coverage

Sau khi chạy `make test-coverage`, mở file `coverage.html` bằng trình duyệt:

```bash
open coverage.html
```

## Chạy Test cho một package cụ thể

```bash
go test ./path/to/package
```

Ví dụ:

```bash
go test ./internal/pkg/utils
```

## Chạy một test case cụ thể

```bash
go test -run TestFunctionName
```

Ví dụ:

```bash
go test -run TestUserValidation
```

## Các lệnh Makefile liên quan

- `make test`: Chạy toàn bộ test
- `make test-coverage`: Chạy test và tạo báo cáo coverage
- `make clean`: Xóa các file build và test

## Best Practices

1. Đặt tên test file với hậu tố `_test.go`
2. Sử dụng table-driven tests cho các test case tương tự
3. Đặt tên test function với tiền tố `Test`
4. Sử dụng package `testify/assert` cho các assertion
5. Giữ test độc lập và có thể chạy song song

## Debug Test

Để debug test bằng Delve:

```bash
dlv test -- -test.run TestName
```

## Chạy Test với Race Detector

```bash
go test -race ./...
```

## Chạy Test với Verbose Output

```bash
go test -v ./...
```

## Tự động chạy test khi code thay đổi

Cài đặt `air` và chạy:

```bash
air -c .air.toml
```

## Ghi chú

- Đảm bảo tất cả test đều pass trước khi tạo pull request
- Cập nhật test case khi thay đổi logic
- Giữ test nhanh và đáng tin cậy