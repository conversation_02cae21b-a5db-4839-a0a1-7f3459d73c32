# Hướng dẫn chạy Cron ở môi trường Local Development

## Tổng quan

Ở môi trường local, bạn có nhiều cách để chạy và test cron jobs của WNAPI. Hệ thống cron được tích hợp sẵn với CLI commands và có thể chạy độc lập hoặc cùng với main application.

## 1. <PERSON><PERSON><PERSON> cách chạy Cron Local

### Cách 1: Chạy cron jobs thủ công (Recommended cho testing)

#### Khởi động services cần thiết
```bash
# Khởi động MySQL và Redis
docker-compose up -d mysql redis

# Hoặc khởi động tất cả services
docker-compose up -d
```

#### Build ứng dụng
```bash
# Build ứng dụng
make build

# Hoặc build trực tiếp
go build -o build/wnapi cmd/fx-server/main.go
```

#### Chạy cron jobs thủ công
```bash
# Xem danh sách tất cả cron jobs
./build/wnapi cron list

# Xem status của cron system
./build/wnapi cron status

# Chạy một job cụ thể
./build/wnapi cron run system_cleanup
./build/wnapi cron run auth_session_cleanup
./build/wnapi cron run media_image_optimization

# Chạy với format JSON
./build/wnapi cron list --json
./build/wnapi cron status --json
```

### Cách 2: Khởi động Cron Scheduler (Chạy tự động theo lịch)

#### Khởi động cron scheduler
```bash
# Khởi động cron scheduler
./build/wnapi cron start

# Hoặc với project cụ thể
./build/wnapi cron start --project=sample
```

#### Cấu hình Environment cho local
Tạo file `.env.local`:
```bash
# Database
DB_HOST=127.0.0.1
DB_PORT=3307
DB_NAME=wnapi
DB_USER=wnapi
DB_PASSWORD=password

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Cron Configuration
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh
CRON_LOG_LEVEL=debug

# System Jobs (chạy thường xuyên hơn cho testing)
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="*/5 * * * *"  # Mỗi 5 phút
CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/2 * * * *"  # Mỗi 2 phút
CRON_SYSTEM_BACKUP_ENABLED=false  # Tắt backup cho local

# Auth Module Jobs
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="*/10 * * * *"  # Mỗi 10 phút
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=24h

# Media Module Jobs
CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="*/15 * * * *"  # Mỗi 15 phút
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=1h

# Blog Module Jobs
CRON_BLOG_AUTO_PUBLISH_ENABLED=true
CRON_BLOG_AUTO_PUBLISH_SCHEDULE="*/5 * * * *"  # Mỗi 5 phút
```

#### Chạy với config local
```bash
# Sử dụng config local
./build/wnapi cron start --config=.env.local

# Hoặc export environment
export CONFIG_FILE=.env.local
./build/wnapi cron start
```

### Cách 3: Chạy trong Docker (Giống production)

#### Sử dụng docker-compose development
```bash
# Khởi động tất cả services bao gồm cron
docker-compose up -d

# Xem logs của main application (có cron)
docker-compose logs -f api

# Vào container để chạy cron commands
docker-compose exec api bash
./wnapi cron list
./wnapi cron status
```

#### Tạo separate cron container cho local
Thêm vào `docker-compose.yml`:
```yaml
  # Cron Worker cho development
  cron-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wnapi-cron-local
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./projects:/app/projects
      - ./.env.docker:/app/.env
    environment:
      - CRON_ENABLED=true
      - CRON_TIME_ZONE=Asia/Ho_Chi_Minh
      - APP_MODE=cron-only
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - wnapi-network
    command: ["/app/main", "cron", "start", "--project=sample"]
```

```bash
# Khởi động cron worker
docker-compose up -d cron-worker

# Xem logs cron worker
docker-compose logs -f cron-worker

# Vào container cron worker
docker-compose exec cron-worker bash
```

## 2. Testing và Debugging

### Kiểm tra cron jobs hoạt động

#### Xem logs chi tiết
```bash
# Xem logs trong terminal
./build/wnapi cron start --verbose

# Xem logs file
tail -f logs/app.log | grep cron

# Filter logs theo job cụ thể
tail -f logs/app.log | grep "system_cleanup"
```

#### Test từng job riêng lẻ
```bash
# Test system cleanup
./build/wnapi cron run system_cleanup --dry-run

# Test với debug mode
LOG_LEVEL=debug ./build/wnapi cron run auth_session_cleanup

# Test với specific project
./build/wnapi cron run media_temp_cleanup --project=sample
```

#### Monitor Redis queue
```bash
# Kết nối Redis
redis-cli -h localhost -p 6379

# Xem keys liên quan đến cron
KEYS *cron*

# Xem queue jobs
LLEN queue:cron:default
LRANGE queue:cron:default 0 -1

# Monitor real-time
MONITOR
```

### Tạo custom cron job cho testing

#### Tạo test job
```bash
# Tạo file test-cron.go
mkdir -p test/cron
```

Tạo file `test/cron/test-job.go`:
```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "wnapi/internal/pkg/queue/cron"
    "wnapi/internal/pkg/queue/cron/types"
)

type TestHandler struct{}

func (h *TestHandler) GetTaskType() types.CronTaskType {
    return "test:hello_world"
}

func (h *TestHandler) GetDescription() string {
    return "Test cron job for development"
}

func (h *TestHandler) Handle(ctx context.Context, payload types.CronTaskPayload) (*types.CronTaskResult, error) {
    fmt.Printf("Hello from test cron job at: %s\n", time.Now().Format(time.RFC3339))
    
    return &types.CronTaskResult{
        TaskType:    h.GetTaskType(),
        Success:     true,
        Message:     "Test job completed successfully",
        ExecutedAt:  time.Now(),
        CompletedAt: time.Now(),
    }, nil
}
```

#### Register test job trong development
Thêm vào initialization code cho development mode.

## 3. Development Workflow

### Workflow hàng ngày

1. **Khởi động services:**
```bash
docker-compose up -d mysql redis
```

2. **Build ứng dụng:**
```bash
make build
```

3. **Test cron jobs:**
```bash
# Test jobs thủ công
./build/wnapi cron run system_cleanup
./build/wnapi cron run auth_session_cleanup

# Hoặc khởi động scheduler
./build/wnapi cron start
```

4. **Monitor và debug:**
```bash
# Xem status
./build/wnapi cron status

# Xem logs
tail -f logs/app.log | grep cron
```

### Quick testing script

Tạo file `scripts/test-cron.sh`:
```bash
#!/bin/bash

echo "🚀 Testing WNAPI Cron Jobs..."

# Build app
echo "📦 Building application..."
make build

# Test connectivity
echo "🔗 Testing database connection..."
./build/wnapi health db

echo "🔗 Testing Redis connection..."
./build/wnapi health redis

# List all jobs
echo "📋 Available cron jobs:"
./build/wnapi cron list

# Test each job
echo "🧪 Testing system cleanup..."
./build/wnapi cron run system_cleanup

echo "🧪 Testing auth session cleanup..."
./build/wnapi cron run auth_session_cleanup

echo "🧪 Testing media temp cleanup..."
./build/wnapi cron run media_temp_cleanup

echo "✅ All tests completed!"
```

```bash
chmod +x scripts/test-cron.sh
./scripts/test-cron.sh
```

## 4. Common Issues và Solutions

### Issue 1: Cron jobs không chạy

**Nguyên nhân và giải pháp:**
```bash
# Kiểm tra config
./build/wnapi cron status

# Kiểm tra database connection
./build/wnapi health db

# Kiểm tra Redis connection
./build/wnapi health redis

# Kiểm tra timezone
date
./build/wnapi cron status | grep timezone
```

### Issue 2: Jobs chạy nhưng có lỗi

**Debug steps:**
```bash
# Chạy với debug level
LOG_LEVEL=debug ./build/wnapi cron run job_name

# Xem detailed logs
tail -f logs/app.log | grep -A 5 -B 5 "ERROR"

# Test với dry-run mode
./build/wnapi cron run job_name --dry-run
```

### Issue 3: Performance issues

**Optimization cho local:**
```bash
# Giảm batch size
export CRON_AUTH_SESSION_CLEANUP_BATCH_SIZE=10
export CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=5

# Tăng thời gian chạy jobs
export CRON_SYSTEM_CLEANUP_SCHEDULE="*/30 * * * *"  # 30 phút thay vì 5 phút
```

## 5. Tips và Best Practices

### Development Tips

1. **Sử dụng shorter schedules cho testing:**
   - Production: `0 2 * * *` (daily 2AM)
   - Local: `*/5 * * * *` (every 5 minutes)

2. **Enable debug logging:**
   ```bash
   export LOG_LEVEL=debug
   export CRON_LOG_LEVEL=debug
   ```

3. **Test jobs individually trước khi chạy scheduler:**
   ```bash
   ./build/wnapi cron run job_name
   ```

4. **Sử dụng dry-run mode:**
   ```bash
   ./build/wnapi cron run job_name --dry-run
   ```

5. **Monitor Redis queue:**
   ```bash
   watch "redis-cli LLEN queue:cron:default"
   ```

### Tạo aliases tiện dụng

Thêm vào `.bashrc` hoặc `.zshrc`:
```bash
# WNAPI Cron aliases
alias wcron-build="make build"
alias wcron-list="./build/wnapi cron list"
alias wcron-status="./build/wnapi cron status"
alias wcron-start="./build/wnapi cron start"
alias wcron-logs="tail -f logs/app.log | grep cron"

# Test specific jobs
alias wcron-test-cleanup="./build/wnapi cron run system_cleanup"
alias wcron-test-auth="./build/wnapi cron run auth_session_cleanup"
alias wcron-test-media="./build/wnapi cron run media_temp_cleanup"
```

Reload shell và sử dụng:
```bash
source ~/.bashrc  # hoặc ~/.zshrc

wcron-build
wcron-list
wcron-test-cleanup
```

## Tóm tắt

Để chạy cron ở local, bạn có 3 options chính:

1. **Thủ công** (Recommended cho development): `./build/wnapi cron run job_name`
2. **Tự động với scheduler**: `./build/wnapi cron start`
3. **Docker**: Sử dụng container giống production

Workflow thông thường:
1. Start services (MySQL, Redis)
2. Build app
3. Test jobs manually
4. Run scheduler nếu cần
5. Monitor logs và debug

Điều quan trọng là cấu hình schedule ngắn hơn cho local testing và enable debug logging để dễ troubleshoot.
