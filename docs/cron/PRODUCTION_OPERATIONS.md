# WNAPI Production Operations Guide

## Hướng dẫn Triển khai và Vận hành Production

### 1. Khởi tạo lần đầ<PERSON> (Initial Setup)

#### Bước 1: Chuẩn bị môi trường
```bash
# Clone repository
git clone <repository-url>
cd blog-api-v1

# Copy và cấu hình environment
cp .env.production.example .env.production
nano .env.production  # Cập nhật các giá trị cần thiết
```

#### Bước 2: Cấu hình SSL/TLS (Optional)
```bash
# Tạo thư mục SSL
mkdir -p ssl

# Copy certificates
cp your-domain.crt ssl/
cp your-domain.key ssl/
```

#### Bước 3: Build và Deploy
```bash
# Build Docker image
./scripts/deploy-production.sh build v1.0.0

# Deploy services
./scripts/deploy-production.sh deploy

# Kiểm tra status
./scripts/deploy-production.sh status
```

### 2. <PERSON><PERSON><PERSON> hành hàng ngà<PERSON> (Daily Operations)

#### Kiểm tra tình trạng hệ thống
```bash
# Xem tổng quan
./scripts/deploy-production.sh status

# Xem logs
./scripts/deploy-production.sh logs

# Xem logs của cron worker
./scripts/deploy-production.sh logs cron-worker

# Kiểm tra health
./scripts/deploy-production.sh health
```

#### Quản lý Cron Jobs
```bash
# Vào container cron worker
docker-compose -f docker-compose.production.yml exec cron-worker bash

# Xem danh sách cron jobs
./wnapi cron list

# Xem status cron system
./wnapi cron status

# Chạy job thủ công
./wnapi cron run system_cleanup
./wnapi cron run auth_session_cleanup
./wnapi cron run media_image_optimization
```

#### Monitoring Cron Jobs
```bash
# Xem logs cron jobs
docker logs wnapi-cron-worker | grep "cron"

# Xem metrics (nếu có Prometheus)
curl http://localhost:9090/metrics | grep cron

# Kiểm tra Redis queue
docker-compose -f docker-compose.production.yml exec redis redis-cli
> KEYS *cron*
> LLEN queue:cron:default
```

### 3. Backup và Recovery

#### Backup tự động
Backup được thực hiện tự động qua cron job:
```bash
# Kiểm tra backup job
./wnapi cron status | grep backup

# Chạy backup thủ công
./scripts/deploy-production.sh backup
```

#### Backup thủ công
```bash
# Database backup
docker-compose -f docker-compose.production.yml exec mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD $MYSQL_DATABASE > backups/manual_backup_$(date +%Y%m%d).sql

# Cron data backup
tar -czf backups/cron-data_$(date +%Y%m%d).tar.gz -C / app/cron-data

# Upload data backup
tar -czf backups/uploads_$(date +%Y%m%d).tar.gz uploads/
```

#### Recovery
```bash
# Restore database
docker-compose -f docker-compose.production.yml exec -T mysql mysql -u root -p$MYSQL_ROOT_PASSWORD $MYSQL_DATABASE < backups/backup_file.sql

# Restore cron data
tar -xzf backups/cron-data_backup.tar.gz -C /

# Restart services
./scripts/deploy-production.sh restart
```

### 4. Troubleshooting

#### Cron Jobs không chạy

**Kiểm tra cron worker:**
```bash
docker logs wnapi-cron-worker

# Vào container
docker exec -it wnapi-cron-worker bash
./wnapi cron status
```

**Kiểm tra cấu hình:**
```bash
# Kiểm tra environment variables
docker exec wnapi-cron-worker env | grep CRON

# Kiểm tra timezone
docker exec wnapi-cron-worker date
```

**Restart cron worker:**
```bash
docker-compose -f docker-compose.production.yml restart cron-worker
```

#### Database Connection Issues

**Kiểm tra database:**
```bash
docker logs wnapi-mysql-prod

# Test connection
docker exec wnapi-cron-worker ./wnapi health db
```

**Kiểm tra network:**
```bash
docker network ls
docker network inspect blog-api-v1_wnapi-network
```

#### Redis Connection Issues

**Kiểm tra Redis:**
```bash
docker logs wnapi-redis-prod

# Test connection
docker exec wnapi-cron-worker redis-cli -h redis ping
```

#### Performance Issues

**Kiểm tra resource usage:**
```bash
docker stats --no-stream

# Kiểm tra memory usage
docker exec wnapi-cron-worker free -h

# Kiểm tra disk usage
docker exec wnapi-cron-worker df -h
```

**Optimize cron jobs:**
```bash
# Giảm batch size nếu jobs chạy quá lâu
# Cập nhật .env.production:
CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=25
CRON_AUTH_SESSION_CLEANUP_BATCH_SIZE=500

# Restart services
./scripts/deploy-production.sh restart
```

### 5. Scaling và Optimization

#### Horizontal Scaling
```bash
# Scale cron workers
docker-compose -f docker-compose.production.yml up -d --scale cron-worker=3

# Load balancing với multiple workers
# Cần implement Redis-based job locking
```

#### Performance Tuning
```bash
# Tăng worker concurrency
CRON_WORKER_CONCURRENCY=10

# Optimize database connections
DB_MAX_CONNECTIONS=50
DB_MAX_IDLE_CONNECTIONS=10

# Tăng Redis memory
# Cập nhật redis-production.conf:
maxmemory 512mb
```

### 6. Security Best Practices

#### Regular Security Updates
```bash
# Update base images
docker pull mysql:8.0
docker pull redis:7.2-alpine
docker pull alpine:3.20

# Rebuild application image
./scripts/deploy-production.sh build latest

# Deploy updates
./scripts/deploy-production.sh restart
```

#### Monitor Security
```bash
# Check for failed authentication attempts
docker logs wnapi-mysql-prod | grep "Access denied"

# Monitor unusual cron activity
docker logs wnapi-cron-worker | grep -E "(ERROR|WARN)"

# Review backup integrity
ls -la backups/
```

### 7. Monitoring và Alerting

#### Setup Monitoring
```bash
# Prometheus metrics endpoint
curl http://localhost:9090/metrics

# Grafana dashboard
# Import dashboard từ monitoring/grafana/

# Setup alerting
# Configure AlertManager rules
```

#### Key Metrics to Monitor
- Cron job execution rate
- Job failure rate
- Job execution duration
- Queue depth
- Database performance
- Memory/CPU usage
- Disk space

### 8. Maintenance Schedule

#### Daily
- Kiểm tra service status
- Review error logs
- Check backup completion

#### Weekly
- Review performance metrics
- Check disk space usage
- Cleanup old logs
- Update documentation

#### Monthly
- Security updates
- Performance optimization review
- Capacity planning
- Backup testing

### 9. Emergency Procedures

#### Service Down
```bash
# Quick restart
./scripts/deploy-production.sh restart

# Full recovery
./scripts/deploy-production.sh stop
./scripts/deploy-production.sh start
```

#### Data Corruption
```bash
# Stop services
./scripts/deploy-production.sh stop

# Restore from backup
# (follow recovery procedures above)

# Start services
./scripts/deploy-production.sh start
```

#### High CPU/Memory Usage
```bash
# Identify problematic containers
docker stats

# Scale down temporarily
docker-compose -f docker-compose.production.yml up -d --scale cron-worker=1

# Investigate and optimize
```

### 10. Contacts và Support

#### Emergency Contacts
- System Administrator: <EMAIL>
- Development Team: <EMAIL>
- Infrastructure Team: <EMAIL>

#### Useful Links
- Monitoring Dashboard: http://monitoring.yourdomain.com
- Log Aggregation: http://logs.yourdomain.com
- Documentation: http://docs.yourdomain.com
- Status Page: http://status.yourdomain.com
