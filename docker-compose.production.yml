version: '3.8'

services:
  # Main API service
  api:
    image: wnapi:latest
    container_name: wnapi-api
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
      - cron-data:/app/cron-data
      - uploads:/app/uploads
    environment:
      # Database
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${MYSQL_DATABASE:-wnapi_prod}
      - DB_USER=${MYSQL_USER:-wnapi}
      - DB_PASSWORD=${MYSQL_PASSWORD:-secure_password}
      - DB_MAX_CONNECTIONS=25
      
      # Redis
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # Application
      - APP_ENV=production
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      
      # Cron (disabled for main API)
      - CRON_ENABLED=false
      
      # JWT
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret_here}
      - JWT_ACCESS_EXPIRY=15m
      - JWT_REFRESH_EXPIRY=7d
      
    depends_on:
      - mysql
      - redis
    networks:
      - wnapi-network
    command: ["--project=production"]
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "/app/wnapi", "health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"

  # Dedicated Cron Worker Service
  cron-worker:
    image: wnapi:latest
    container_name: wnapi-cron-worker
    restart: always
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
      - cron-data:/app/cron-data
      - uploads:/app/uploads
    environment:
      # Database
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${MYSQL_DATABASE:-wnapi_prod}
      - DB_USER=${MYSQL_USER:-wnapi}
      - DB_PASSWORD=${MYSQL_PASSWORD:-secure_password}
      
      # Redis
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # Application
      - APP_ENV=production
      - APP_MODE=cron-only
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      
      # Cron Configuration
      - CRON_ENABLED=true
      - CRON_TIME_ZONE=Asia/Ho_Chi_Minh
      
      # System Jobs
      - CRON_SYSTEM_CLEANUP_ENABLED=true
      - CRON_SYSTEM_CLEANUP_SCHEDULE="0 2 * * *"
      - CRON_SYSTEM_CLEANUP_RETENTION_DAYS=30
      - CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
      - CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/5 * * * *"
      - CRON_SYSTEM_BACKUP_ENABLED=true
      - CRON_SYSTEM_BACKUP_SCHEDULE="0 3 * * 0"
      
      # Auth Module Jobs
      - CRON_AUTH_SESSION_CLEANUP_ENABLED=true
      - CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
      - CRON_AUTH_SESSION_CLEANUP_MAX_AGE=168h
      - CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
      - CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
      
      # Media Module Jobs
      - CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=true
      - CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
      - CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
      - CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 1 * * *"
      
      # Blog Module Jobs
      - CRON_BLOG_AUTO_PUBLISH_ENABLED=true
      - CRON_BLOG_AUTO_PUBLISH_SCHEDULE="*/10 * * * *"
      - CRON_BLOG_SITEMAP_UPDATE_ENABLED=true
      - CRON_BLOG_SITEMAP_UPDATE_SCHEDULE="0 4 * * *"
      
    depends_on:
      - mysql
      - redis
    networks:
      - wnapi-network
    command: ["cron", "start", "--project=production"]
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.2'
          memory: 256M
    healthcheck:
      test: ["CMD", "/app/wnapi", "cron", "status"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "10"

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wnapi-mysql-prod
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root_password}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-wnapi_prod}
      MYSQL_USER: ${MYSQL_USER:-wnapi}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-secure_password}
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - ./infrastructure/docker/mysql/mysql-production.cnf:/etc/mysql/conf.d/custom.cnf
      - ./backups/mysql:/backups
    networks:
      - wnapi-network
    command: --default-authentication-plugin=mysql_native_password
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Redis for Queue System
  redis:
    image: redis:7.2-alpine
    container_name: wnapi-redis-prod
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./infrastructure/docker/redis/redis-production.conf:/usr/local/etc/redis/redis.conf
    networks:
      - wnapi-network
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: wnapi-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/docker/nginx/nginx.production.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - wnapi-network
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
  cron-data:
    driver: local
  uploads:
    driver: local

networks:
  wnapi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
