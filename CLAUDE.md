# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development Commands
- `make run` - Run the application with default project
- `make run PROJECT=<project>` - Run with specific project configuration
- `make build` - Build the application
- `make dev` - Run with debugger support (uses dlv on port 9034)
- `make test` - Run all tests
- `make test-coverage` - Run tests with coverage report

### Database & Migration Commands
- `make migrate` - Run database migrations for default project
- `make migrate PROJECT=<project>` - Run migrations for specific project
- `make migrate-down PROJECT=<project>` - Rollback migrations
- `make create-migration MODULE=<module> NAME=<name>` - Create new migration

### Module & Plugin Development
- `make create-module name=<module-name>` - Create new module skeleton
- `make create-plugin name=<plugin-name>` - Create new plugin skeleton

### Seeding Commands
- `make build-seed` - Build seed command
- `make seed-dev` - Seed development environment
- `make seed-module MODULE=<module>` - Seed specific module

### Process Management
- `make kill` - Kill all wnapi processes and ports 9033, 9034
- `make ps` - Show all related processes

## Architecture Overview

This is a modular Go microservices framework using **uber-go/fx** for dependency injection and lifecycle management. The architecture has been migrated from a traditional bootstrap pattern to FX-based dependency injection.

### Key Components

1. **FX-based Core** (`internal/fx/`)
   - `fx.App` manages application lifecycle
   - `fx.Provide` registers dependencies
   - `fx.Invoke` handles startup hooks
   - `fx.Module` for modular architecture

2. **Module System** (`modules/`)
   - Each module is an `fx.Module` with its own providers and invokes
   - Modules register via `init()` functions with global registry
   - Module structure: `api/`, `dto/`, `models/`, `repository/`, `service/`, `migrations/`

3. **Multi-tenant Support**
   - Tenant-aware middleware and context
   - Tenant-specific data isolation
   - Website-aware request handling

4. **Queue System**
   - Asynq-based task queue with Redis backend
   - Background job processing
   - Cron job scheduling

### Current Modules
- `auth` - Authentication and user management
- `blog` - Blog posts, categories, tags, timelines
- `media` - File upload and media management
- `notification` - Multi-channel notifications
- `marketing` - Ads banners and positions
- `website` - Multi-site management
- `agent-ai` - AI-powered content generation
- `cart` - Shopping cart functionality
- `crawl` - Web scraping and content extraction

## Important Implementation Details

### Main Entry Point
- Primary entry: `cmd/fx-server/main.go` (FX-based)
- Legacy entry: `cmd/server/main.go` (deprecated)

### Module Registration
Modules use the FX pattern with factory registration:
```go
// In module's fx.go file
func Module() fx.Option {
    return fx.Module("module_name",
        fx.Provide(
            // providers
        ),
        fx.Invoke(
            // startup hooks
        ),
    )
}
```

### Database Migrations
- System migrations: `migrations/`
- Module-specific migrations: `modules/{module}/migrations/`
- Migration order controlled by module priority

### Configuration
- Project-specific configs in `projects/` directory
- YAML-based configuration with environment variable support
- Multi-tenant configuration support

### Testing
- Integration tests in `tests/` directory
- Module-specific tests alongside source code
- Use `go test -v ./...` for running tests

## Development Notes

### Creating New Modules
Use `make create-module name=<module-name>` which creates:
- Module directory structure
- FX module template
- Migration directory
- Basic interfaces and types

### Database Schema Changes
1. Create migration: `make create-migration MODULE=<module> NAME=<description>`
2. Write up/down migration files
3. Test migration: `make migrate PROJECT=<project>`

### Working with FX
- All dependencies should be provided via fx.Provide
- Use fx.Invoke for startup hooks (like route registration)
- Leverage fx.Module for organizing related functionality
- Follow constructor injection pattern for dependencies

### Code Organization
- Follow domain-driven design within modules
- Use repository pattern for data access
- Implement service layer for business logic
- Use DTOs for API request/response

### Important Files
- `internal/fx/app.go` - Main FX application setup
- `internal/fx/providers/` - Core dependency providers
- `internal/fx/modules/` - Module loading and discovery
- `Makefile` - Build and development commands
- `go.mod` - Go module dependencies