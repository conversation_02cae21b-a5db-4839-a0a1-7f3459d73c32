#!/bin/bash

# =============================================================================
# WNAPI Production Deployment Script
# =============================================================================
# Script này sẽ deploy WNAPI lên production environment với cron system
# Sử dụng: ./deploy-production.sh [command]
# Commands: build, deploy, start, stop, restart, logs, status

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="wnapi"
DOCKER_COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_DIR="./logs"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed!"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed!"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        log_error "Environment file $ENV_FILE not found!"
        log_info "Please copy .env.production.example to $ENV_FILE and configure it."
        exit 1
    fi
    
    log_success "Requirements check passed"
}

create_directories() {
    log_info "Creating required directories..."
    
    mkdir -p $BACKUP_DIR/{mysql,cron-data,uploads}
    mkdir -p $LOG_DIR
    mkdir -p ./uploads/{temp,avatars,media}
    mkdir -p ./config/production
    
    log_success "Directories created"
}

build_image() {
    log_info "Building Docker image..."
    
    # Build the image
    docker build -t wnapi:latest -f Dockerfile .
    
    # Tag with version if provided
    if [ ! -z "$1" ]; then
        docker tag wnapi:latest wnapi:$1
        log_success "Image tagged as wnapi:$1"
    fi
    
    log_success "Docker image built successfully"
}

deploy_services() {
    log_info "Deploying services..."
    
    # Pull latest images
    docker-compose -f $DOCKER_COMPOSE_FILE pull
    
    # Start services
    docker-compose -f $DOCKER_COMPOSE_FILE up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_service_health
    
    log_success "Services deployed successfully"
}

start_services() {
    log_info "Starting services..."
    
    docker-compose -f $DOCKER_COMPOSE_FILE up -d
    
    # Wait for services
    sleep 15
    check_service_health
    
    log_success "Services started successfully"
}

stop_services() {
    log_info "Stopping services..."
    
    docker-compose -f $DOCKER_COMPOSE_FILE down
    
    log_success "Services stopped successfully"
}

restart_services() {
    log_info "Restarting services..."
    
    docker-compose -f $DOCKER_COMPOSE_FILE down
    sleep 5
    docker-compose -f $DOCKER_COMPOSE_FILE up -d
    
    # Wait for services
    sleep 15
    check_service_health
    
    log_success "Services restarted successfully"
}

check_service_health() {
    log_info "Checking service health..."
    
    # Check API service
    if docker-compose -f $DOCKER_COMPOSE_FILE ps | grep -q "wnapi-api.*Up"; then
        log_success "API service is running"
    else
        log_error "API service is not running"
        return 1
    fi
    
    # Check Cron Worker
    if docker-compose -f $DOCKER_COMPOSE_FILE ps | grep -q "wnapi-cron-worker.*Up"; then
        log_success "Cron worker is running"
    else
        log_error "Cron worker is not running"
        return 1
    fi
    
    # Check Database
    if docker-compose -f $DOCKER_COMPOSE_FILE ps | grep -q "wnapi-mysql-prod.*Up"; then
        log_success "Database is running"
    else
        log_error "Database is not running"
        return 1
    fi
    
    # Check Redis
    if docker-compose -f $DOCKER_COMPOSE_FILE ps | grep -q "wnapi-redis-prod.*Up"; then
        log_success "Redis is running"
    else
        log_error "Redis is not running"
        return 1
    fi
    
    # Test API endpoint
    log_info "Testing API endpoint..."
    if curl -f http://localhost:8080/health >/dev/null 2>&1; then
        log_success "API endpoint is responding"
    else
        log_warning "API endpoint is not responding yet"
    fi
}

show_logs() {
    if [ -z "$1" ]; then
        docker-compose -f $DOCKER_COMPOSE_FILE logs -f
    else
        docker-compose -f $DOCKER_COMPOSE_FILE logs -f $1
    fi
}

show_status() {
    log_info "Service Status:"
    docker-compose -f $DOCKER_COMPOSE_FILE ps
    
    echo
    log_info "Resource Usage:"
    docker stats --no-stream $(docker-compose -f $DOCKER_COMPOSE_FILE ps -q)
    
    echo
    log_info "Cron Status:"
    docker-compose -f $DOCKER_COMPOSE_FILE exec cron-worker /app/wnapi cron status || log_warning "Cannot get cron status"
}

run_migrations() {
    log_info "Running database migrations..."
    
    docker-compose -f $DOCKER_COMPOSE_FILE exec api /app/wnapi migrate up
    
    log_success "Migrations completed"
}

backup_database() {
    log_info "Creating database backup..."
    
    BACKUP_FILE="$BACKUP_DIR/mysql/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    docker-compose -f $DOCKER_COMPOSE_FILE exec mysql mysqldump -u root -p\$MYSQL_ROOT_PASSWORD \$MYSQL_DATABASE > $BACKUP_FILE
    
    # Compress backup
    gzip $BACKUP_FILE
    
    log_success "Database backup created: ${BACKUP_FILE}.gz"
}

cleanup_old_backups() {
    log_info "Cleaning up old backups..."
    
    # Keep last 7 days of backups
    find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete
    
    log_success "Old backups cleaned up"
}

show_help() {
    echo "WNAPI Production Deployment Script"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  build [version]    - Build Docker image (optional version tag)"
    echo "  deploy             - Deploy all services"
    echo "  start              - Start all services"
    echo "  stop               - Stop all services"
    echo "  restart            - Restart all services"
    echo "  logs [service]     - Show logs (optional service name)"
    echo "  status             - Show service status"
    echo "  migrate            - Run database migrations"
    echo "  backup             - Create database backup"
    echo "  cleanup            - Clean up old backups"
    echo "  health             - Check service health"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build v1.0.0"
    echo "  $0 deploy"
    echo "  $0 logs cron-worker"
    echo "  $0 status"
}

# Main script
main() {
    case "${1:-help}" in
        "build")
            check_requirements
            build_image "$2"
            ;;
        "deploy")
            check_requirements
            create_directories
            deploy_services
            run_migrations
            ;;
        "start")
            check_requirements
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$2"
            ;;
        "status")
            show_status
            ;;
        "migrate")
            run_migrations
            ;;
        "backup")
            backup_database
            ;;
        "cleanup")
            cleanup_old_backups
            ;;
        "health")
            check_service_health
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function
main "$@"
