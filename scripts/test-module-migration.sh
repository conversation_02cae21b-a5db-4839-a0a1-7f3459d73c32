#!/bin/bash

# Test script for module-specific migration functionality
# This script demonstrates the new module migration features

set -e

echo "🚀 Testing Module-Specific Migration Functionality"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run migration command and check result
run_migration() {
    local action=$1
    local module=$2
    local description=$3
    
    print_status "$description"
    
    if [ -n "$module" ]; then
        cmd="go run cmd/migrate/main.go -action=$action -module=$module"
        echo "Command: $cmd"
    else
        cmd="go run cmd/migrate/main.go -action=$action"
        echo "Command: $cmd"
    fi
    
    if $cmd; then
        print_success "✅ $description completed successfully"
    else
        print_error "❌ $description failed"
        return 1
    fi
    echo ""
}

echo ""
print_status "Testing module-specific migration functionality..."
echo ""

# Test 1: Migration up for specific module
echo "📋 Test 1: Migration UP for specific module"
run_migration "up" "blog" "Running migration UP for blog module"

# Test 2: Migration down for specific module
echo "📋 Test 2: Migration DOWN for specific module"
run_migration "down" "blog" "Running migration DOWN for blog module"

# Test 3: Migration up again to restore state
echo "📋 Test 3: Restore blog module state"
run_migration "up" "blog" "Restoring blog module with migration UP"

# Test 4: Test with module that has no migrations
echo "📋 Test 4: Module with no migrations"
run_migration "up" "hello" "Testing module with no migrations (hello)"

# Test 5: Test with non-existent module (should fail)
echo "📋 Test 5: Non-existent module (should fail)"
print_status "Testing non-existent module (expected to fail)"
if go run cmd/migrate/main.go -action=up -module=nonexistent 2>/dev/null; then
    print_error "❌ Expected failure but command succeeded"
else
    print_success "✅ Correctly failed for non-existent module"
fi
echo ""

# Test 6: Test migration for multiple modules individually
echo "📋 Test 6: Multiple modules individually"
modules=("media" "notification" "rbac")

for module in "${modules[@]}"; do
    print_status "Testing migration for $module module"
    if go run cmd/migrate/main.go -action=up -module=$module; then
        print_success "✅ $module module migration successful"
    else
        print_warning "⚠️  $module module migration had issues (might be expected)"
    fi
    echo ""
done

# Test 7: Compare with full migration (all modules)
echo "📋 Test 7: Full migration (all modules) for comparison"
print_status "Running full migration (all modules) to ensure consistency"
if go run cmd/migrate/main.go -action=up; then
    print_success "✅ Full migration completed successfully"
else
    print_warning "⚠️  Full migration had issues"
fi
echo ""

echo "🎉 Module Migration Testing Complete!"
echo "====================================="
echo ""
echo "Summary of tested features:"
echo "✅ Module-specific migration UP"
echo "✅ Module-specific migration DOWN"
echo "✅ Module with no migrations handling"
echo "✅ Non-existent module error handling"
echo "✅ Multiple individual module migrations"
echo "✅ Full migration compatibility"
echo ""
echo "📚 For more information, see: docs/migration-module-specific.md"
