#!/bin/bash

# Test runner script for Blog API v1
# Supports unit tests, integration tests, and benchmarks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    echo "Usage: $0 [unit|integration|all|benchmark|coverage]"
    echo ""
    echo "Commands:"
    echo "  unit        Run unit tests only"
    echo "  integration Run integration tests only" 
    echo "  all         Run both unit and integration tests"
    echo "  benchmark   Run performance benchmarks"
    echo "  coverage    Run tests with coverage report"
    echo "  help        Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  TEST_ENV    Test environment (test|integration)"
    echo "  TEST_TAGS   Build tags for tests"
    echo "  COVERAGE    Enable coverage (true|false)"
    echo ""
}

# Run unit tests
run_unit_tests() {
    print_info "Running unit tests..."
    
    # Ensure we're in the right directory
    if [ ! -f "go.mod" ]; then
        print_error "go.mod not found. Please run from project root."
        exit 1
    fi
    
    # Run unit tests
    go test -v -race -coverprofile=unit.out ./internal/... ./modules/... || {
        print_error "Unit tests failed"
        return 1
    }
    
    print_success "Unit tests completed"
}

# Run integration tests
run_integration_tests() {
    print_info "Running integration tests..."
    
    # Check if integration test files exist
    if [ ! -d "tests/integration" ]; then
        print_warning "Integration tests directory not found"
        return 0
    fi
    
    # Load integration test environment
    if [ -f ".env.integration" ]; then
        export $(cat .env.integration | grep -v '^#' | xargs)
        print_info "Loaded integration test configuration"
    else
        print_warning "Integration test config not found, using defaults"
    fi
    
    # Setup integration test environment
    if [ -f "scripts/setup-integration-test.sh" ]; then
        print_info "Setting up integration test environment..."
        chmod +x scripts/setup-integration-test.sh
        ./scripts/setup-integration-test.sh
    else
        print_warning "Integration test setup script not found"
    fi
    
    # Run integration tests
    cd tests
    go test -v -tags=integration -coverprofile=integration.out ./integration/... || {
        print_error "Integration tests failed"
        cd ..
        return 1
    }
    cd ..
    
    print_success "Integration tests completed"
}

# Run benchmark tests
run_benchmarks() {
    print_info "Running performance benchmarks..."
    
    # Run benchmarks on main modules
    go test -bench=. -benchmem -run=^$ ./internal/... ./modules/... || {
        print_error "Benchmarks failed"
        return 1
    }
    
    print_success "Benchmarks completed"
}

# Generate coverage report
generate_coverage() {
    print_info "Generating coverage report..."
    
    # Combine unit and integration coverage if both exist
    if [ -f "unit.out" ] && [ -f "tests/integration.out" ]; then
        print_info "Combining unit and integration coverage..."
        
        # Create combined coverage
        echo "mode: set" > combined.out
        tail -n +2 unit.out >> combined.out
        tail -n +2 tests/integration.out >> combined.out
        
        # Generate HTML report
        go tool cover -html=combined.out -o coverage.html
        go tool cover -func=combined.out | tail -1
        
        print_success "Combined coverage report generated: coverage.html"
        
    elif [ -f "unit.out" ]; then
        go tool cover -html=unit.out -o coverage.html
        go tool cover -func=unit.out | tail -1
        
        print_success "Unit test coverage report generated: coverage.html"
        
    elif [ -f "tests/integration.out" ]; then
        go tool cover -html=tests/integration.out -o coverage.html
        go tool cover -func=tests/integration.out | tail -1
        
        print_success "Integration test coverage report generated: coverage.html"
        
    else
        print_warning "No coverage files found"
        return 1
    fi
}

# Main script logic
main() {
    case "${1:-all}" in
        unit)
            run_unit_tests
            ;;
        integration)
            run_integration_tests
            ;;
        all)
            print_info "Running all tests..."
            run_unit_tests && run_integration_tests
            ;;
        benchmark)
            run_benchmarks
            ;;
        coverage)
            run_unit_tests
            run_integration_tests
            generate_coverage
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
