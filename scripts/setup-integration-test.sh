#!/bin/bash

# Integration Test Setup Script
# Sử dụng để setup môi trường integration test trong CI/CD

set -e

echo "🚀 Setting up integration test environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check required environment variables
check_env() {
    print_status "Checking environment variables..."
    
    required_vars=(
        "DB_HOST"
        "DB_PORT"
        "DB_USERNAME"
        "DB_PASSWORD"
        "DB_DATABASE"
        "REDIS_ADDR"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            print_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    print_status "Environment variables OK"
}

# Wait for database to be ready
wait_for_database() {
    print_status "Waiting for database to be ready..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1" >/dev/null 2>&1; then
            print_status "Database is ready!"
            return 0
        fi
        
        print_warning "Database not ready, attempt $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    done
    
    print_error "Database is not ready after $max_attempts attempts"
    exit 1
}

# Wait for Redis to be ready
wait_for_redis() {
    print_status "Waiting for Redis to be ready..."
    
    redis_host=$(echo "$REDIS_ADDR" | cut -d':' -f1)
    redis_port=$(echo "$REDIS_ADDR" | cut -d':' -f2)
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if redis-cli -h "$redis_host" -p "$redis_port" ping >/dev/null 2>&1; then
            print_status "Redis is ready!"
            return 0
        fi
        
        print_warning "Redis not ready, attempt $attempt/$max_attempts"
        sleep 2
        ((attempt++))
    done
    
    print_error "Redis is not ready after $max_attempts attempts"
    exit 1
}

# Create test database
create_test_database() {
    print_status "Creating test database..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "
        DROP DATABASE IF EXISTS $DB_DATABASE;
        CREATE DATABASE $DB_DATABASE CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        GRANT ALL PRIVILEGES ON $DB_DATABASE.* TO '$DB_USERNAME'@'%';
        FLUSH PRIVILEGES;
    " || {
        print_error "Failed to create test database"
        exit 1
    }
    
    print_status "Test database created successfully"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if [ ! -f "./cmd/migrate/main.go" ]; then
        print_error "Migration command not found"
        exit 1
    fi
    
    go run cmd/migrate/main.go up || {
        print_error "Failed to run migrations"
        exit 1
    }
    
    print_status "Migrations completed successfully"
}

# Seed test data
seed_test_data() {
    print_status "Seeding test data..."
    
    # Seed permissions (required for RBAC)
    if [ -f "./cmd/seed-permissions/main.go" ]; then
        go run cmd/seed-permissions/main.go || {
            print_warning "Failed to seed permissions (this might be expected)"
        }
    fi
    
    # Seed demo data
    if [ -f "./cmd/seed/main.go" ]; then
        go run cmd/seed/main.go || {
            print_warning "Failed to seed demo data (this might be expected)"
        }
    fi
    
    print_status "Test data seeding completed"
}

# Create test directories
create_test_directories() {
    print_status "Creating test directories..."
    
    mkdir -p test_uploads
    mkdir -p logs
    mkdir -p tmp
    
    print_status "Test directories created"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    if [ "$CLEANUP_ON_EXIT" = "true" ]; then
        print_status "Dropping test database..."
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "DROP DATABASE IF EXISTS $DB_DATABASE;" || true
        
        print_status "Cleaning up test files..."
        rm -rf test_uploads || true
        rm -rf tmp/test* || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    print_status "Starting integration test setup..."
    
    # Load environment if file exists
    if [ -f ".env.integration" ]; then
        print_status "Loading .env.integration file..."
        export $(cat .env.integration | grep -v '^#' | xargs)
    fi
    
    check_env
    wait_for_database
    wait_for_redis
    create_test_database
    create_test_directories
    run_migrations
    seed_test_data
    
    print_status "Integration test environment setup completed successfully! 🎉"
}

# Run main function
main "$@"
