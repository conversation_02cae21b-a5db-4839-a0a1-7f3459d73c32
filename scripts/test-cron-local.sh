#!/bin/bash

# =============================================================================
# WNAPI Local Cron Testing Script
# =============================================================================
# Script này giúp test cron jobs ở môi trường local development
# Sử dụng: ./scripts/test-cron-local.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_BINARY="./build/wnapi"
LOG_FILE="./logs/app.log"
ENV_FILE=".env.local"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check if build exists
    if [ ! -f "$APP_BINARY" ]; then
        log_warning "Application binary not found. Building..."
        make build
        if [ ! -f "$APP_BINARY" ]; then
            log_error "Failed to build application!"
            exit 1
        fi
    fi
    
    # Check services
    log_info "Checking database connection..."
    if ! $APP_BINARY health db >/dev/null 2>&1; then
        log_error "Database connection failed!"
        log_info "Please start database: docker-compose up -d mysql"
        exit 1
    fi
    
    log_info "Checking Redis connection..."
    if ! $APP_BINARY health redis >/dev/null 2>&1; then
        log_error "Redis connection failed!"
        log_info "Please start Redis: docker-compose up -d redis"
        exit 1
    fi
    
    log_success "Requirements check passed"
}

create_local_env() {
    log_info "Creating local environment configuration..."
    
    if [ -f "$ENV_FILE" ]; then
        log_warning "Local environment file already exists: $ENV_FILE"
        return
    fi
    
    cat > $ENV_FILE << 'EOF'
# Local Development Cron Configuration
# Database
DB_HOST=127.0.0.1
DB_PORT=3307
DB_NAME=wnapi
DB_USER=wnapi
DB_PASSWORD=password

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Cron Configuration
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh
CRON_LOG_LEVEL=debug

# System Jobs (frequent for testing)
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="*/5 * * * *"
CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="*/2 * * * *"
CRON_SYSTEM_BACKUP_ENABLED=false

# Auth Module Jobs
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="*/10 * * * *"
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=24h

# Media Module Jobs
CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="*/15 * * * *"
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=1h

# Blog Module Jobs
CRON_BLOG_AUTO_PUBLISH_ENABLED=true
CRON_BLOG_AUTO_PUBLISH_SCHEDULE="*/5 * * * *"
EOF
    
    log_success "Local environment file created: $ENV_FILE"
}

build_app() {
    log_info "Building application..."
    make build
    log_success "Application built successfully"
}

list_jobs() {
    log_info "Available cron jobs:"
    $APP_BINARY cron list
}

show_status() {
    log_info "Cron system status:"
    $APP_BINARY cron status
}

test_individual_jobs() {
    log_info "Testing individual cron jobs..."
    
    # Define jobs to test
    jobs=(
        "system_cleanup"
        "auth_session_cleanup"
        "media_temp_cleanup"
        "system_health_check"
    )
    
    for job in "${jobs[@]}"; do
        log_info "Testing job: $job"
        if $APP_BINARY cron run $job; then
            log_success "✅ Job $job completed successfully"
        else
            log_error "❌ Job $job failed"
        fi
        echo
    done
}

test_with_dry_run() {
    log_info "Testing jobs with dry-run mode..."
    
    jobs=(
        "system_cleanup"
        "auth_session_cleanup"
        "media_temp_cleanup"
    )
    
    for job in "${jobs[@]}"; do
        log_info "Dry-run testing job: $job"
        if $APP_BINARY cron run $job --dry-run; then
            log_success "✅ Dry-run for $job completed successfully"
        else
            log_error "❌ Dry-run for $job failed"
        fi
        echo
    done
}

start_scheduler() {
    log_info "Starting cron scheduler..."
    log_warning "This will run continuously. Press Ctrl+C to stop."
    
    if [ -f "$ENV_FILE" ]; then
        export CONFIG_FILE=$ENV_FILE
    fi
    
    $APP_BINARY cron start
}

monitor_logs() {
    log_info "Monitoring cron logs..."
    log_info "Press Ctrl+C to stop monitoring"
    
    if [ -f "$LOG_FILE" ]; then
        tail -f $LOG_FILE | grep --line-buffered "cron"
    else
        log_warning "Log file not found: $LOG_FILE"
        log_info "Logs will appear here when cron jobs run..."
        sleep 1
        monitor_logs
    fi
}

check_redis_queue() {
    log_info "Checking Redis queue status..."
    
    if command -v redis-cli &> /dev/null; then
        echo "Redis Cron Keys:"
        redis-cli -h localhost -p 6379 KEYS "*cron*"
        echo
        echo "Queue Length:"
        redis-cli -h localhost -p 6379 LLEN "queue:cron:default"
        echo
        echo "Queue Contents:"
        redis-cli -h localhost -p 6379 LRANGE "queue:cron:default" 0 -1
    else
        log_error "redis-cli not found. Please install Redis tools."
    fi
}

run_performance_test() {
    log_info "Running performance test..."
    
    start_time=$(date +%s)
    
    # Test multiple jobs
    jobs=(
        "system_cleanup"
        "auth_session_cleanup"
        "media_temp_cleanup"
        "system_health_check"
    )
    
    for job in "${jobs[@]}"; do
        log_info "Performance testing job: $job"
        job_start=$(date +%s)
        
        if $APP_BINARY cron run $job >/dev/null 2>&1; then
            job_end=$(date +%s)
            job_duration=$((job_end - job_start))
            log_success "✅ Job $job completed in ${job_duration}s"
        else
            log_error "❌ Job $job failed"
        fi
    done
    
    end_time=$(date +%s)
    total_duration=$((end_time - start_time))
    log_success "Performance test completed in ${total_duration}s"
}

cleanup_logs() {
    log_info "Cleaning up old logs..."
    
    if [ -f "$LOG_FILE" ]; then
        # Keep last 1000 lines
        tail -n 1000 $LOG_FILE > ${LOG_FILE}.tmp
        mv ${LOG_FILE}.tmp $LOG_FILE
        log_success "Logs cleaned up"
    else
        log_warning "Log file not found: $LOG_FILE"
    fi
}

show_help() {
    echo "WNAPI Local Cron Testing Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  build              - Build the application"
    echo "  setup              - Create local environment configuration"
    echo "  list               - List all available cron jobs"
    echo "  status             - Show cron system status"
    echo "  test               - Test individual cron jobs"
    echo "  test-dry           - Test jobs with dry-run mode"
    echo "  start              - Start cron scheduler"
    echo "  logs               - Monitor cron logs"
    echo "  redis              - Check Redis queue status"
    echo "  performance        - Run performance test"
    echo "  cleanup            - Clean up old logs"
    echo "  all                - Run complete test suite"
    echo "  help               - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup           # Create local config"
    echo "  $0 test            # Test all jobs"
    echo "  $0 start           # Start scheduler"
    echo "  $0 logs            # Monitor logs"
    echo ""
    echo "Prerequisites:"
    echo "  - MySQL running on localhost:3307"
    echo "  - Redis running on localhost:6379"
    echo "  - Application built (or use 'build' command)"
}

# Main script
main() {
    case "${1:-help}" in
        "build")
            build_app
            ;;
        "setup")
            create_local_env
            ;;
        "list")
            check_requirements
            list_jobs
            ;;
        "status")
            check_requirements
            show_status
            ;;
        "test")
            check_requirements
            test_individual_jobs
            ;;
        "test-dry")
            check_requirements
            test_with_dry_run
            ;;
        "start")
            check_requirements
            start_scheduler
            ;;
        "logs")
            monitor_logs
            ;;
        "redis")
            check_redis_queue
            ;;
        "performance")
            check_requirements
            run_performance_test
            ;;
        "cleanup")
            cleanup_logs
            ;;
        "all")
            check_requirements
            list_jobs
            echo
            show_status
            echo
            test_individual_jobs
            echo
            log_info "Complete test suite finished!"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Execute main function
main "$@"
