package main

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"wnapi/modules/rbac/commands"
)

func main() {
	fmt.Println("🚀 WNAPI Permission Seeding Tool")
	fmt.Println("=================================")

	parser := commands.NewPermissionParser()

	// Scan all modules for permission files
	modulePermissions, err := scanModulesForPermissions(parser, "")
	if err != nil {
		fmt.Printf("❌ Error scanning modules: %v\n", err)
		os.Exit(1)
	}

	if len(modulePermissions) == 0 {
		fmt.Println("⚠️  No permission definitions found in modules")
		return
	}

	// Display statistics
	totalPermissions := 0
	for _, mp := range modulePermissions {
		totalPermissions += len(mp.Permissions)
	}

	fmt.Printf("📊 Found %d modules with %d total permissions\n\n", len(modulePermissions), totalPermissions)

	// Display detailed permissions
	for _, mp := range modulePermissions {
		fmt.Printf("📁 Module: %s (%d permissions)\n", mp.ModuleName, len(mp.Permissions))

		// Group permissions by category
		categories := make(map[string][]commands.PermissionDefinition)
		for _, perm := range mp.Permissions {
			categories[perm.Category] = append(categories[perm.Category], perm)
		}

		for category, perms := range categories {
			fmt.Printf("  📂 %s:\n", strings.Title(strings.ReplaceAll(category, "_", " ")))
			for _, perm := range perms {
				fmt.Printf("    ├─ %s: %s\n", perm.Code, perm.Name)
			}
		}
		fmt.Println()
	}

	fmt.Println("✅ Permission scanning completed successfully!")
	fmt.Println("\n💡 To seed these permissions to database:")
	fmt.Println("   1. Ensure database is running and migrated")
	fmt.Println("   2. Use: go run cmd/cli/main.go seed-permissions")
	fmt.Println("   3. Or implement the RBAC console module registration")
}

// ModulePermissions contains permissions for a module
type ModulePermissions struct {
	ModuleName  string
	Permissions []commands.PermissionDefinition
}

// scanModulesForPermissions scans all modules for permission definitions
func scanModulesForPermissions(parser *commands.PermissionParser, targetModule string) ([]ModulePermissions, error) {
	var result []ModulePermissions

	// Path to modules directory
	modulesPath := "modules"

	// Read all directories in modules
	entries, err := os.ReadDir(modulesPath)
	if err != nil {
		return nil, fmt.Errorf("cannot read modules directory: %w", err)
	}

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		moduleName := entry.Name()

		// If specific module is targeted, only process that module
		if targetModule != "" && moduleName != targetModule {
			continue
		}

		// Check for permission.go file
		permissionFile := filepath.Join(modulesPath, moduleName, "internal", "permission.go")
		if _, err := os.Stat(permissionFile); os.IsNotExist(err) {
			fmt.Printf("⏭️  Module %s has no permission.go file, skipping\n", moduleName)
			continue
		}

		// Parse permission.go file
		permissions, err := parser.ParsePermissionFile(permissionFile, moduleName)
		if err != nil {
			fmt.Printf("⚠️  Error parsing permission.go for module %s: %v\n", moduleName, err)
			continue
		}

		if len(permissions) > 0 {
			result = append(result, ModulePermissions{
				ModuleName:  moduleName,
				Permissions: permissions,
			})
		}
	}

	return result, nil
}
