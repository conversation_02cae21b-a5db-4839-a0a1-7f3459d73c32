# Integration Test Configuration for CI/CD
# Sử dụng cho CI/CD pipeline integration testing

# Application
APP_NAME=wnapi-integration
APP_VERSION=integration
APP_ENV=integration

# Server
SERVER_ADDR=0.0.0.0:8080
GIN_MODE=test
SERVER_SHUTDOWN_TIMEOUT=5s

# Database (CI Services)
DB_TYPE=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=wnapi_integration
DB_MAX_OPEN_CONNS=10
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
DB_MIGRATION_PATH=./migrations

# Redis/Queue (CI Services)
REDIS_ADDR=127.0.0.1:6379
REDIS_PASSWORD=
REDIS_DB=2
QUEUE_CONCURRENCY=5

# JWT (Integration Test Keys)
JWT_ACCESS_SIGNING_KEY=integration-secret-key-access-12345678901234567890
JWT_REFRESH_SIGNING_KEY=integration-secret-key-refresh-12345678901234567890
JWT_ACCESS_TOKEN_EXPIRATION=1h
JWT_REFRESH_TOKEN_EXPIRATION=24h

# Logging (Quiet for CI)
LOG_LEVEL=error
LOGGER_FORMAT=json

# Email (Disabled in integration)
EMAIL_SMTP_HOST=localhost
EMAIL_SMTP_PORT=1025
EMAIL_SMTP_USERNAME=
EMAIL_SMTP_PASSWORD=
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Integration Test WNAPI

# SMS (Disabled in integration)
SMS_ENABLED=false

# Push Notifications (Disabled in integration)
PUSH_ENABLED=false

# File Upload (Local for integration)
FILE_STORAGE_TYPE=local
FILE_UPLOAD_MAX_SIZE=10MB
FILE_UPLOAD_PATH=./tmp/uploads/integration

# Multi-tenant (Integration mode)
TENANT_MODE=single
DEFAULT_TENANT_ID=integration-tenant

# Cache (Redis for integration)
CACHE_TYPE=redis
CACHE_TTL=5m

# Security (Relaxed for integration)
RATE_LIMIT_ENABLED=false
CORS_ENABLED=true
CORS_ORIGINS=*

# Queue Settings (Fast processing for tests)
QUEUE_RETRY_MAX=1
QUEUE_RETRY_DELAY=1s
QUEUE_WORKER_POOL_SIZE=2

# Test-specific settings
TEST_MODE=true
TEST_DATA_CLEANUP=true
TEST_TIMEOUT=30s
