# golangci-lint configuration for Blog API v1
# https://golangci-lint.run/usage/configuration/

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - build
    - tmp
    - .git
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_gen\\.go$"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  uniq-by-line: true

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true
    
  govet:
    check-shadowing: true
    enable-all: true
    
  golint:
    min-confidence: 0.8
    
  gofmt:
    simplify: true
    
  goimports:
    local-prefixes: wnapi
    
  gocyclo:
    min-complexity: 15
    
  gocognit:
    min-complexity: 15
    
  maligned:
    suggest-new: true
    
  dupl:
    threshold: 100
    
  goconst:
    min-len: 3
    min-occurrences: 3
    
  misspell:
    locale: US
    
  lll:
    line-length: 120
    
  unparam:
    check-exported: false
    
  nakedret:
    max-func-lines: 30
    
  prealloc:
    simple: true
    range-loops: true
    for-loops: false
    
  gocritic:
    enabled-tags:
      - performance
      - style
      - experimental
    disabled-checks:
      - wrapperFunc
      - commentedOutCode
      - sloppyReassign

  revive:
    rules:
      - name: var-naming
        severity: warning
        disabled: false
        arguments:
          - ["ID", "API", "URL", "HTTP", "JSON", "XML", "SQL", "UUID", "JWT", "FX"]
      - name: exported
        severity: warning
        disabled: false
      - name: package-comments
        severity: warning
        disabled: false

linters:
  enable:
    # Default linters
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused
    
    # Additional linters
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - exportloopref
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - goprintffuncname
    - gosec
    - interfacer
    - lll
    - maligned
    - misspell
    - nakedret
    - prealloc
    - revive
    - rowserrcheck
    - scopelint
    - structcheck
    - stylecheck
    - unconvert
    - unparam
    - varcheck
    - whitespace
    
  disable:
    - gochecknoglobals  # Chúng ta sử dụng global variables cho FX registry
    - funlen           # Một số function có thể dài do business logic
    - godot            # Không bắt buộc dấu chấm cuối comment

issues:
  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
        - lll
        
    # Exclude some issues in generated files
    - path: ".*\\.pb\\.go"
      linters:
        - all
        
    # Exclude init functions check for module registration
    - path: modules/.*/fx\.go
      text: "don't use `init` function"
      linters:
        - gochecknoinits
        
    # Exclude global variables for module registry
    - path: internal/fx/modules/discovery\.go
      text: "GlobalRegistry is a global variable"
      linters:
        - gochecknoglobals
        
    # Exclude some issues from third party libraries
    - path: vendor/
      linters:
        - all

  exclude:
    # Default excludes from golangci-lint
    - "Error return value of .((os\\.)?std(out|err)\\..*|.*Close|.*Flush|os\\.Remove(All)?|.*printf?|os\\.(Un)?Setenv). is not checked"
    - "func name will be used as test\\.Test.* by other packages, and that stutters; consider calling this"
    - "G104: Errors unhandled"
    
  max-issues-per-linter: 0
  max-same-issues: 0
  new: false

severity:
  default-severity: error
  case-sensitive: false
  rules:
    - linters:
        - dupl
      severity: info
    - linters:
        - gocritic
      severity: warning
