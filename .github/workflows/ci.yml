# CI/CD Pipeline cho Blog API v1
name: CI/CD Pipeline

on:
  push:
    branches: [ main, dev ]
  pull_request:
    branches: [ main, dev ]

env:
  GO_VERSION: '1.21'

jobs:
  # Job 1: Lint và Format Check
  lint:
    name: Lint và Code Quality
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Verify dependencies
      run: go mod verify

    - name: Check go fmt
      run: |
        if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
          echo "Code không được format đúng:"
          gofmt -s -l .
          exit 1
        fi

    - name: Run go vet
      run: go vet ./...

    - name: Install golangci-lint
      run: |
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2

    - name: Run golangci-lint
      run: $(go env GOPATH)/bin/golangci-lint run --timeout=5m

  # Job 2: Unit Tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wnapi_test
          MYSQL_USER: test
          MYSQL_PASSWORD: test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Wait for MySQL
      run: |
        until mysql -h127.0.0.1 -P3306 -uroot -proot -e "SELECT 1"; do
          echo "Waiting for MySQL..."
          sleep 2
        done

    - name: Wait for Redis
      run: |
        until redis-cli -h 127.0.0.1 -p 6379 ping; do
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Create test environment file
      run: |
        cat > .env.test << EOF
        APP_ENV=test
        DB_TYPE=mysql
        DB_HOST=127.0.0.1
        DB_PORT=3306
        DB_USERNAME=root
        DB_PASSWORD=root
        DB_DATABASE=wnapi_test
        DB_MAX_OPEN_CONNS=10
        DB_MAX_IDLE_CONNS=5
        DB_CONN_MAX_LIFETIME=5m
        
        REDIS_ADDR=127.0.0.1:6379
        REDIS_PASSWORD=
        REDIS_DB=1
        
        JWT_ACCESS_SIGNING_KEY=test-secret-key-access
        JWT_REFRESH_SIGNING_KEY=test-secret-key-refresh
        JWT_ACCESS_TOKEN_EXPIRATION=1h
        JWT_REFRESH_TOKEN_EXPIRATION=24h
        
        LOG_LEVEL=error
        GIN_MODE=test
        
        MODULES_ENABLED=tenant,auth,rbac,notification,media,blog
        EOF

    - name: Run database migrations
      run: |
        export $(cat .env.test | xargs)
        go run cmd/migrate/main.go up
      env:
        CONFIG_FILE: .env.test

    - name: Run unit tests
      run: |
        export $(cat .env.test | xargs)
        go test -v -race -coverprofile=coverage.out -covermode=atomic ./...
      env:
        CONFIG_FILE: .env.test

    - name: Generate coverage report
      run: go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      if: github.event_name == 'push'
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella

    - name: Upload coverage artifact
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage.html

  # Job 3: Integration Tests
  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [lint, test]
    if: github.event_name == 'push' || github.event.pull_request.base.ref == 'main'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wnapi_integration
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Wait for services
      run: |
        until mysql -h127.0.0.1 -P3306 -uroot -proot -e "SELECT 1"; do
          sleep 2
        done
        until redis-cli -h 127.0.0.1 -p 6379 ping; do
          sleep 2
        done

    - name: Create integration test environment
      run: |
        cat > .env.integration << EOF
        APP_ENV=integration
        SERVER_ADDR=0.0.0.0:8080
        DB_TYPE=mysql
        DB_HOST=127.0.0.1
        DB_PORT=3306
        DB_USERNAME=root
        DB_PASSWORD=root
        DB_DATABASE=wnapi_integration
        REDIS_ADDR=127.0.0.1:6379
        JWT_ACCESS_SIGNING_KEY=integration-test-key
        JWT_REFRESH_SIGNING_KEY=integration-test-refresh-key
        LOG_LEVEL=error
        GIN_MODE=test
        MODULES_ENABLED=tenant,auth,rbac,notification,media,blog,marketing,agent-ai
        EOF

    - name: Run migrations for integration tests
      run: |
        export $(cat .env.integration | xargs)
        go run cmd/migrate/main.go up

    - name: Seed test data
      run: |
        export $(cat .env.integration | xargs)
        go run cmd/seed/main.go

    - name: Run integration tests
      run: |
        export $(cat .env.integration | xargs)
        go test -v -tags=integration ./tests/integration/...
      env:
        CONFIG_FILE: .env.integration

  # Job 4: Build
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [lint, test]
    strategy:
      matrix:
        os: [linux, darwin, windows]
        arch: [amd64, arm64]
        exclude:
          - os: windows
            arch: arm64

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Set build variables
      id: vars
      run: |
        echo "binary_name=wnapi-${{ matrix.os }}-${{ matrix.arch }}" >> $GITHUB_OUTPUT
        if [ "${{ matrix.os }}" = "windows" ]; then
          echo "binary_name=wnapi-${{ matrix.os }}-${{ matrix.arch }}.exe" >> $GITHUB_OUTPUT
        fi
        echo "build_time=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_OUTPUT
        echo "git_commit=${GITHUB_SHA:0:7}" >> $GITHUB_OUTPUT

    - name: Build binary
      env:
        GOOS: ${{ matrix.os }}
        GOARCH: ${{ matrix.arch }}
        CGO_ENABLED: 0
      run: |
        go build \
          -ldflags="-s -w -X main.version=${{ github.ref_name }} -X main.commit=${{ steps.vars.outputs.git_commit }} -X main.buildTime=${{ steps.vars.outputs.build_time }}" \
          -o build/${{ steps.vars.outputs.binary_name }} \
          cmd/fx-server/main.go

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: binaries
        path: build/

  # Job 5: Docker Build
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [lint, test]
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Docker Hub
      if: github.ref == 'refs/heads/main'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ${{ secrets.DOCKER_USERNAME }}/blog-api-v1
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.ref == 'refs/heads/main' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          VERSION=${{ github.ref_name }}
          COMMIT=${{ github.sha }}
          BUILD_TIME=${{ steps.vars.outputs.build_time }}

  # Job 6: Security Scan
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec.sarif ./...'

    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: gosec.sarif

    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/golang@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # Job 7: Deployment (chỉ chạy trên main branch)
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [integration-test, build, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: binaries
        path: build/

    - name: Deploy to staging server
      run: |
        echo "🚀 Deploying to staging environment..."
        echo "Binary: build/wnapi-linux-amd64"
        echo "Docker image: ${{ secrets.DOCKER_USERNAME }}/blog-api-v1:${{ github.sha }}"
        # Thêm deployment script ở đây
        
    - name: Run health check
      run: |
        echo "🔍 Running health check..."
        # Thêm health check script ở đây
        
    - name: Notify deployment status
      if: always()
      run: |
        if [ "${{ job.status }}" = "success" ]; then
          echo "✅ Deployment thành công!"
        else
          echo "❌ Deployment thất bại!"
        fi

  # Job 8: Performance Tests
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [integration-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Download dependencies
      run: go mod download

    - name: Run benchmark tests
      run: |
        go test -bench=. -benchmem -count=3 ./... > benchmark.txt
        cat benchmark.txt

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark.txt

# Notification về status
  notify:
    name: Notify Build Status
    runs-on: ubuntu-latest
    needs: [lint, test, integration-test, build, docker]
    if: always()

    steps:
    - name: Notify success
      if: ${{ needs.lint.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' }}
      run: |
        echo "🎉 Build pipeline hoàn thành thành công!"
        echo "✅ Lint: ${{ needs.lint.result }}"
        echo "✅ Tests: ${{ needs.test.result }}"
        echo "✅ Integration Tests: ${{ needs.integration-test.result }}"
        echo "✅ Build: ${{ needs.build.result }}"
        echo "✅ Docker: ${{ needs.docker.result }}"

    - name: Notify failure
      if: ${{ needs.lint.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' }}
      run: |
        echo "❌ Build pipeline thất bại!"
        echo "Lint: ${{ needs.lint.result }}"
        echo "Tests: ${{ needs.test.result }}"
        echo "Integration Tests: ${{ needs.integration-test.result }}"
        echo "Build: ${{ needs.build.result }}"
        echo "Docker: ${{ needs.docker.result }}"
        exit 1
