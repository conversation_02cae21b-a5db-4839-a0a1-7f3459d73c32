# =============================================================================
# WNAPI Configuration File (.env)
# =============================================================================
# Copy this file to .env and modify the values according to your environment
# All values shown here are the default values from setGlobalDefaultConfig()

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
APP_NAME=wnapi
APP_VERSION=0.1.0
APP_ENV=development

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
SERVER_ADDR=0.0.0.0:9033
GIN_MODE=debug
SERVER_SHUTDOWN_TIMEOUT=10s
SERVER_HOST=0.0.0.0
SERVER_PORT=9033
SERVER_TIMEOUT=30s
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_MAX_HEADER_BYTES=1048576

# WEB
WEB_URL=http://localhost:9200

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_TYPE=mysql
DB_HOST=mysql
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=root
DB_DATABASE=wnapi
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m
DB_MIGRATION_PATH=./migrations

# =============================================================================
# LOGGER CONFIGURATION
# =============================================================================
LOG_LEVEL=debug
LOGGER_FORMAT=console
LOGGER_FILE=

# =============================================================================
# RBAC CONFIGURATION
# =============================================================================
RBAC_CACHE_PERMISSION_TTL=5m

# =============================================================================
# QUEUE CONFIGURATION (Redis-based with Asynq)
# =============================================================================
QUEUE_ENABLED=true
QUEUE_BACKEND=asynq
QUEUE_REDIS_ADDR=redis:6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_DB=0
QUEUE_REDIS_KEY_PREFIX=wnapi:queue:
QUEUE_WORKER_CONCURRENCY=10
QUEUE_WORKER_SHUTDOWN_TIMEOUT=30s
QUEUE_DEFAULT_QUEUE_NAME=default
QUEUE_DEFAULT_QUEUE_PRIORITY=10
QUEUE_DEFAULT_QUEUE_MAX_RETRY=3
QUEUE_SCHEDULER_ENABLED=true
QUEUE_SCHEDULER_CHECK_INTERVAL=1m
QUEUE_SCHEDULER_TIME_ZONE=Asia/Ho_Chi_Minh
QUEUE_START_WORKER=true

# =============================================================================
# JWT CONFIGURATION (Global)
# =============================================================================
JWT_ACCESS_SIGNING_KEY=super_secret_access_key_change_me_in_production
JWT_REFRESH_SIGNING_KEY=super_secret_refresh_key_change_me_in_production
JWT_ACCESS_TOKEN_EXPIRATION=168h
JWT_REFRESH_TOKEN_EXPIRATION=168h
JWT_ISSUER=wnapi

# =============================================================================
# MODULES CONFIGURATION
# =============================================================================
MODULES_ENABLED=hello,tenant,auth,rbac,notification,media,blog
PLUGINS_ENABLED=logger

# =============================================================================
# TENANT MODULE CONFIGURATION
# =============================================================================
TENANT_MAX_USERS_PER_TENANT=100
TENANT_MAX_TENANTS_PER_ACCOUNT=5
TENANT_DEFAULT_PLAN_ID=1
TENANT_ENABLE_MULTI_TENANCY=true
TENANT_DEFAULT_PAGE_SIZE=10
TENANT_MAX_PAGE_SIZE=100
TENANT_DEFAULT_SORT_FIELD=created_at
TENANT_DEFAULT_SORT_ORDER=desc
TENANT_DEFAULT_TENANT_TIMEZONE=Asia/Ho_Chi_Minh
TENANT_MESSAGE=Xin chào từ module Tenant!

# =============================================================================
# AUTH MODULE CONFIGURATION
# =============================================================================
AUTH_ACCESS_TOKEN_EXPIRY=168h
AUTH_REFRESH_TOKEN_EXPIRY=168h
AUTH_JWT_SECRET=auth_super_secret_key_change_me
AUTH_JWT_ISSUER=wnapi
AUTH_MESSAGE=Xin chào từ module Auth!

# Hello Module Configuration
HELLO_MESSAGE=Xin chào từ module Hello!

# =============================================================================
# NOTIFICATION MODULE CONFIGURATION
# =============================================================================
NOTIFICATION_EMAIL_HOST=mailcatcher
NOTIFICATION_EMAIL_PORT=1025
NOTIFICATION_EMAIL_USERNAME=
NOTIFICATION_EMAIL_PASSWORD=
NOTIFICATION_EMAIL_FROM=<EMAIL>
NOTIFICATION_SMS_ENABLED=false
NOTIFICATION_PUSH_ENABLED=false
NOTIFICATION_CACHE_ENABLED=false
NOTIFICATION_CACHE_TTL=300
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5s
NOTIFICATION_WORKER_ENABLED=true

# =============================================================================
# PRODUCT MODULE CONFIGURATION
# =============================================================================
PRODUCT_DEFAULT_PAGE_SIZE=10
PRODUCT_MAX_PAGE_SIZE=100
PRODUCT_MAX_TITLE_LENGTH=200
PRODUCT_MAX_CONTENT_LENGTH=50000
PRODUCT_DEFAULT_SORT_FIELD=created_at
PRODUCT_DEFAULT_SORT_ORDER=desc
PRODUCT_ALLOWED_SORT_FIELDS=id,name,base_price,created_at,updated_at

# =============================================================================
# SEO MODULE CONFIGURATION
# =============================================================================
SEO_MESSAGE=Xin chào từ module SEO!
SEO_DEBUG=false

# =============================================================================
# BLOG MODULE CONFIGURATION
# =============================================================================
BLOG_MAX_TITLE_LENGTH=200
BLOG_MAX_CONTENT_LENGTH=50000
BLOG_DEFAULT_PAGE_SIZE=10
BLOG_MAX_PAGE_SIZE=100
BLOG_DEFAULT_SORT_FIELD=created_at
BLOG_DEFAULT_SORT_ORDER=desc
BLOG_ALLOWED_SORT_FIELDS=id,title,created_at,updated_at
BLOG_MESSAGE=Xin chào từ module Blog!

# =============================================================================
# WEBSITE MODULE CONFIGURATION
# =============================================================================
WEBSITE_SITE_NAME=My Website
WEBSITE_SITE_DESCRIPTION=Website description
WEBSITE_CONTACT_EMAIL=<EMAIL>
WEBSITE_MAX_MENU_ITEMS=10
WEBSITE_MAX_BANNERS=5
WEBSITE_ENABLE_CONTACT_FORM=true
WEBSITE_CACHE_TIMEOUT_MINUTES=10
WEBSITE_MAX_PAGE_SIZE=100
WEBSITE_DEFAULT_PAGE_SIZE=10
WEBSITE_MESSAGE=Xin chào từ module Website!
WEBSITE_TRACING_ENABLED=false
WEBSITE_TRACING_JAEGER_HOST=localhost
WEBSITE_TRACING_JAEGER_PORT=14268
WEBSITE_TRACING_SIGNOZ_ENDPOINT=http://localhost:4317

# =============================================================================
# EVENT SYSTEM CONFIGURATION (Redis Streams)
# =============================================================================
EVENT_ENABLED=true
EVENT_REDIS_HOST=redis
EVENT_REDIS_PORT=6379
EVENT_REDIS_DB=0
EVENT_REDIS_PASSWORD=
EVENT_PUBLISHER_MAX_LEN=10000
EVENT_DEBUG=true
EVENT_CONSUMER_GROUP=wnapi_consumers
EVENT_CONSUMER_ID=wnapi_consumer_1
EVENT_ROUTER_CLOSE_TIMEOUT=30s
EVENT_ROUTER_MIDDLEWARE_TIMEOUT=30s
EVENT_RETRY_MAX_ATTEMPTS=3
EVENT_RETRY_INITIAL_INTERVAL=1s
EVENT_RETRY_MAX_INTERVAL=30s
EVENT_LOGGING_ENABLED=true
EVENT_METRICS_ENABLED=true

# =============================================================================
# TRACING CONFIGURATION
# =============================================================================
# Basic service information
SERVICE_NAME=wnapi
SERVICE_VERSION=0.1.0
ENVIRONMENT=development

# Tracing settings
TRACING_ENABLED=true
TRACING_SERVICE_NAME=wnapi
TRACING_SERVICE_VERSION=0.1.0
TRACING_ENVIRONMENT=development
TRACING_EXPORTER_TYPE=jaeger

# OTLP Configuration (for OTLP exporter)
TRACING_OTLP_ENDPOINT=http://jaeger:4317
TRACING_OTLP_INSECURE=true
TRACING_OTLP_TIMEOUT=10s

# Jaeger Configuration (for Jaeger exporter)
TRACING_JAEGER_ENDPOINT=http://jaeger:14268/api/traces
TRACING_JAEGER_USERNAME=
TRACING_JAEGER_PASSWORD=

# Sampling Configuration
TRACING_SAMPLING_TYPE=ratio
TRACING_SAMPLING_RATIO=0.1

# Legacy variables (for backward compatibility)
EXPORTER_TYPE=jaeger
OTLP_ENDPOINT=http://jaeger:4317
OTLP_INSECURE=true
OTLP_TIMEOUT=10s
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
JAEGER_USERNAME=
JAEGER_PASSWORD=
SAMPLING_TYPE=ratio
SAMPLING_RATIO=0.1

# =============================================================================
# MEDIA MODULE CONFIGURATION
# =============================================================================
MEDIA_STORAGE_TYPE=minio
MEDIA_MAX_FILE_SIZE=52428800
MEDIA_IMAGE_QUALITY=85
MEDIA_ENABLE_PROCESSING=true
MEDIA_MESSAGE=Xin chào từ module Media!

# Local Storage Configuration (fallback)
STORAGE_LOCAL_PATH=./uploads
STORAGE_LOCAL_URL=http://localhost:9033/uploads

# MinIO/S3 Storage Configuration
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minio
MINIO_SECRET_KEY=minio123
MINIO_BUCKET=media
MINIO_REGION=us-east-1
MINIO_USE_SSL=false

# CDN Configuration (optional)
CDN_DOMAIN=

# =============================================================================
# REDIS CONFIGURATION (Legacy/Additional)
# =============================================================================
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Enable/disable cron system
CRON_ENABLED=true
CRON_TIME_ZONE=Asia/Ho_Chi_Minh

# System jobs
CRON_SYSTEM_CLEANUP_ENABLED=true
CRON_SYSTEM_CLEANUP_SCHEDULE="0 2 * * *"
CRON_SYSTEM_HEALTH_CHECK_ENABLED=true
CRON_SYSTEM_HEALTH_CHECK_SCHEDULE="1 * * * *"
CRON_SYSTEM_BACKUP_ENABLED=false
CRON_SYSTEM_BACKUP_SCHEDULE="0 3 * * 0"
CRON_SYSTEM_DEMO_ENABLED=true
CRON_SYSTEM_DEMO_SCHEDULE="* * * * *"

# Auth module jobs
CRON_AUTH_SESSION_CLEANUP_ENABLED=true
CRON_AUTH_SESSION_CLEANUP_SCHEDULE="0 2 * * *"
CRON_AUTH_SESSION_CLEANUP_MAX_AGE=24h
CRON_AUTH_PASSWORD_EXPIRY_ENABLED=true
CRON_AUTH_PASSWORD_EXPIRY_SCHEDULE="0 9 * * *"
CRON_AUTH_PASSWORD_EXPIRY_NOTIFY_DAYS="7,3,1"
CRON_AUTH_PASSWORD_EXPIRY_TEMPLATE_ID=password_expiry

# Media module jobs
CRON_MEDIA_IMAGE_OPTIMIZATION_ENABLED=true
CRON_MEDIA_IMAGE_OPTIMIZATION_SCHEDULE="0 3 * * *"
CRON_MEDIA_IMAGE_OPTIMIZATION_QUALITY=85
CRON_MEDIA_IMAGE_OPTIMIZATION_BATCH_SIZE=100
CRON_MEDIA_TEMP_CLEANUP_ENABLED=true
CRON_MEDIA_TEMP_CLEANUP_SCHEDULE="0 1 * * *"
CRON_MEDIA_TEMP_CLEANUP_MAX_AGE=24h