# Dockerfile cho Blog API v1
# Multi-stage build để tối ưu kích thước image

# Build stage
FROM golang:1.21-alpine AS builder

# Cài đặt dependencies cần thiết
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Build arguments
ARG VERSION=dev
ARG COMMIT=unknown
ARG BUILD_TIME=unknown

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w -X main.version=${VERSION} -X main.commit=${COMMIT} -X main.buildTime=${BUILD_TIME}" \
    -o /app/build/wnapi \
    cmd/fx-server/main.go

# Final stage
FROM alpine:3.18

# Cài đặt certificates và timezone
RUN apk --no-cache add ca-certificates tzdata

# Tạo user non-root
RUN adduser -D -s /bin/sh -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy binary từ builder stage
COPY --from=builder /app/build/wnapi /app/wnapi

# Copy migration files
COPY --from=builder /app/migrations /app/migrations

# Copy config files
COPY --from=builder /app/.env.docker /app/.env

# Tạo thư mục uploads
RUN mkdir -p /app/uploads && chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 9033

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:9033/api/v1/hello/ || exit 1

# Default command
CMD ["./wnapi", "-config", ".env"]
